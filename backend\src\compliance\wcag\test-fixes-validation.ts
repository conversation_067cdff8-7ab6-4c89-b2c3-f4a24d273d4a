/**
 * WCAG System Fixes Validation Script
 * Tests the implemented fixes for CMS detection, Form analyzer, and 75% threshold scoring
 */

import puppeteer from 'puppeteer';
import { HeadlessCMSDetector } from './utils/headless-cms-detector';
import { FormAccessibilityAnalyzer } from './utils/form-accessibility-analyzer';
import { CheckTemplate } from './utils/check-template';
import { SCORING_CONFIG } from './constants';
import logger from '../../utils/logger';

interface TestResult {
  testName: string;
  passed: boolean;
  details: string;
  error?: string;
}

class WCAGFixesValidator {
  private results: TestResult[] = [];

  /**
   * Test CMS Detection Utility Fix
   */
  async testCMSDetectionFix(): Promise<TestResult> {
    const testName = 'CMS Detection Utility Fix';
    
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      
      // Navigate to a test page
      await page.goto('https://example.com', { waitUntil: 'networkidle0' });
      
      const cmsDetector = HeadlessCMSDetector.getInstance();
      const result = await cmsDetector.analyzeHeadlessCMS(page);
      
      await browser.close();
      
      // Validate result structure
      const isValid = result && 
                     Array.isArray(result.detectedCMS) && 
                     result.primaryCMS && 
                     typeof result.overallAccessibilityScore === 'number';
      
      return {
        testName,
        passed: isValid,
        details: isValid 
          ? `✅ CMS detection completed successfully. Detected ${result.detectedCMS.length} CMS platforms.`
          : '❌ CMS detection returned invalid result structure'
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ CMS detection failed with error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test Form Analyzer Utility Fix
   */
  async testFormAnalyzerFix(): Promise<TestResult> {
    const testName = 'Form Analyzer Utility Fix';
    
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      
      // Create a test page with a form
      await page.setContent(`
        <html>
          <body>
            <form id="test-form">
              <label for="email">Email:</label>
              <input type="email" id="email" name="email" required>
              <button type="submit">Submit</button>
            </form>
          </body>
        </html>
      `);
      
      const formAnalyzer = FormAccessibilityAnalyzer.getInstance();
      const result = await formAnalyzer.analyzeFormAccessibility(page);
      
      await browser.close();
      
      // Validate result structure
      const isValid = result && 
                     Array.isArray(result.forms) && 
                     typeof result.overallScore === 'number';
      
      return {
        testName,
        passed: isValid,
        details: isValid 
          ? `✅ Form analysis completed successfully. Analyzed ${result.forms.length} forms.`
          : '❌ Form analysis returned invalid result structure'
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ Form analysis failed with error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test 75% Threshold Scoring System
   */
  async test75ThresholdScoring(): Promise<TestResult> {
    const testName = '75% Threshold Scoring System';
    
    try {
      const checkTemplate = new CheckTemplate();
      
      // Test different score scenarios
      const testCases = [
        { score: 100, maxScore: 100, expectedStatus: 'passed', description: '100% score' },
        { score: 85, maxScore: 100, expectedStatus: 'passed', description: '85% score (above 75%)' },
        { score: 75, maxScore: 100, expectedStatus: 'passed', description: '75% score (at threshold)' },
        { score: 70, maxScore: 100, expectedStatus: 'failed', description: '70% score (below 75%)' },
        { score: 50, maxScore: 100, expectedStatus: 'failed', description: '50% score (below 75%)' },
      ];
      
      const results = [];
      
      for (const testCase of testCases) {
        // Use reflection to access private method for testing
        const scoringResult = (checkTemplate as any).calculateWcagCompliance({
          score: testCase.score,
          maxScore: testCase.maxScore,
          evidence: [],
          recommendations: []
        });
        
        const passed = scoringResult.status === testCase.expectedStatus;
        results.push({
          ...testCase,
          actualStatus: scoringResult.status,
          passed,
          details: scoringResult.details
        });
      }
      
      const allPassed = results.every(r => r.passed);
      const passedCount = results.filter(r => r.passed).length;
      
      return {
        testName,
        passed: allPassed,
        details: allPassed 
          ? `✅ All ${testCases.length} threshold scoring tests passed`
          : `❌ ${passedCount}/${testCases.length} threshold scoring tests passed`
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ Threshold scoring test failed with error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test Scoring Configuration
   */
  async testScoringConfiguration(): Promise<TestResult> {
    const testName = 'Scoring Configuration';
    
    try {
      // Validate scoring configuration
      const configValid = SCORING_CONFIG.DEFAULT_PASS_THRESHOLD === 75 &&
                         SCORING_CONFIG.STRICT_MODE === false &&
                         SCORING_CONFIG.ENABLE_GRADUAL_SCORING === true;
      
      return {
        testName,
        passed: configValid,
        details: configValid 
          ? `✅ Scoring configuration is correct: ${SCORING_CONFIG.DEFAULT_PASS_THRESHOLD}% threshold`
          : '❌ Scoring configuration has incorrect values'
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ Scoring configuration test failed',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Run all validation tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting WCAG System Fixes Validation...\n');
    
    // Run all tests
    this.results.push(await this.testScoringConfiguration());
    this.results.push(await this.test75ThresholdScoring());
    this.results.push(await this.testCMSDetectionFix());
    this.results.push(await this.testFormAnalyzerFix());
    
    // Print results
    this.printResults();
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('\n📊 WCAG System Fixes Validation Results:');
    console.log('=' .repeat(60));
    
    let passedCount = 0;
    
    for (const result of this.results) {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} | ${result.testName}`);
      console.log(`     ${result.details}`);
      
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
      
      if (result.passed) passedCount++;
      console.log('');
    }
    
    console.log('=' .repeat(60));
    console.log(`📈 Overall Results: ${passedCount}/${this.results.length} tests passed`);
    
    if (passedCount === this.results.length) {
      console.log('🎉 All fixes validated successfully!');
    } else {
      console.log('⚠️  Some fixes need attention.');
    }
  }
}

// Export for use in other files
export { WCAGFixesValidator };

// Run validation if this file is executed directly
if (require.main === module) {
  const validator = new WCAGFixesValidator();
  validator.runAllTests().catch(console.error);
}
