/**
 * Comprehensive Pattern Detection Validation Tests
 * Tests the fixes for "elements.map is not a function" errors
 */

import { Page } from 'puppeteer';
import puppeteer from 'puppeteer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';

describe('Pattern Detection Validation', () => {
  let browser: any;
  let page: Page;
  let patternLibrary: AccessibilityPatternLibrary;

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    page = await browser.newPage();
    patternLibrary = AccessibilityPatternLibrary.getInstance();
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Create a test HTML page with various elements
    await page.setContent(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <title>Pattern Detection Test Page</title>
      </head>
      <body>
        <header>
          <h1>Test Page</h1>
          <nav>
            <ul>
              <li><a href="#section1">Section 1</a></li>
              <li><a href="#section2">Section 2</a></li>
            </ul>
          </nav>
        </header>
        
        <main>
          <section id="section1">
            <h2>Form Section</h2>
            <form>
              <label for="username">Username:</label>
              <input type="text" id="username" name="username" required>
              
              <label for="password">Password:</label>
              <input type="password" id="password" name="password" required>
              
              <button type="submit">Submit</button>
            </form>
          </section>
          
          <section id="section2">
            <h2>Content Section</h2>
            <p>This is a test paragraph with some content.</p>
            <img src="test.jpg" alt="Test image">
            
            <div class="interactive-element" tabindex="0">
              Interactive content
            </div>
          </section>
        </main>
        
        <footer>
          <p>&copy; 2024 Test Site</p>
        </footer>
      </body>
      </html>
    `);
  });

  describe('Pattern Detection Core Functionality', () => {
    test('should inject pattern detection functions without errors', async () => {
      const result = await patternLibrary.injectPatternDetectionFunctions(page);
      expect(result).toBe(true);
      
      // Verify functions are available in page context
      const functionsAvailable = await page.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        return detection && 
               typeof detection.findPatternElements === 'function' &&
               typeof detection.isElementVisible === 'function' &&
               typeof detection.getElementSelector === 'function';
      });
      
      expect(functionsAvailable).toBe(true);
    });

    test('should handle valid selector arrays correctly', async () => {
      await patternLibrary.injectPatternDetectionFunctions(page);
      
      const elements = await page.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        return detection.findPatternElements(['input[type="text"]', 'input[type="password"]']);
      });
      
      expect(Array.isArray(elements)).toBe(true);
      expect(elements.length).toBeGreaterThan(0);
    });

    test('should handle invalid selector arrays gracefully', async () => {
      await patternLibrary.injectPatternDetectionFunctions(page);
      
      // Test with non-array input
      const result1 = await page.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        return detection.findPatternElements('not-an-array');
      });
      
      expect(Array.isArray(result1)).toBe(true);
      expect(result1.length).toBe(0);
      
      // Test with null input
      const result2 = await page.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        return detection.findPatternElements(null);
      });
      
      expect(Array.isArray(result2)).toBe(true);
      expect(result2.length).toBe(0);
    });

    test('should handle invalid selectors gracefully', async () => {
      await patternLibrary.injectPatternDetectionFunctions(page);
      
      const elements = await page.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        return detection.findPatternElements(['invalid[[[selector', 'input[type="text"]']);
      });
      
      expect(Array.isArray(elements)).toBe(true);
      // Should still find valid elements despite invalid selector
      expect(elements.length).toBeGreaterThan(0);
    });
  });

  describe('Pattern Analysis Integration', () => {
    test('should analyze patterns without throwing errors', async () => {
      const pattern = {
        name: 'Form Input Pattern',
        selectors: ['input[type="text"]', 'input[type="password"]'],
        description: 'Test pattern for form inputs',
      };
      
      const result = await patternLibrary.analyzePattern(page, pattern);
      
      expect(result).toBeDefined();
      expect(result.patternName).toBe(pattern.name);
      expect(Array.isArray(result.elements)).toBe(true);
      expect(result.elements.length).toBeGreaterThan(0);
    });

    test('should handle complex pattern analysis', async () => {
      const patterns = [
        {
          name: 'Navigation Pattern',
          selectors: ['nav', 'nav ul', 'nav li', 'nav a'],
          description: 'Navigation structure pattern',
        },
        {
          name: 'Form Pattern',
          selectors: ['form', 'input', 'label', 'button[type="submit"]'],
          description: 'Form structure pattern',
        },
        {
          name: 'Content Pattern',
          selectors: ['h1', 'h2', 'p', 'img'],
          description: 'Content structure pattern',
        },
      ];
      
      const results = await patternLibrary.analyzePatterns(page, patterns);
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(patterns.length);
      
      results.forEach((result, index) => {
        expect(result.patternName).toBe(patterns[index].name);
        expect(Array.isArray(result.elements)).toBe(true);
      });
    });
  });

  describe('Error Recovery and Fallbacks', () => {
    test('should recover from page context errors', async () => {
      // Inject functions first
      await patternLibrary.injectPatternDetectionFunctions(page);
      
      // Simulate error condition by corrupting the detection object
      await page.evaluate(() => {
        (window as any).accessibilityPatternDetection = null;
      });
      
      const pattern = {
        name: 'Test Pattern',
        selectors: ['input'],
        description: 'Test pattern',
      };
      
      // Should not throw error, should return empty result
      const result = await patternLibrary.analyzePattern(page, pattern);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result.elements)).toBe(true);
      expect(result.elements.length).toBe(0);
    });

    test('should handle malformed pattern data', async () => {
      await patternLibrary.injectPatternDetectionFunctions(page);
      
      const malformedPattern = {
        name: 'Malformed Pattern',
        selectors: null, // Invalid selectors
        description: 'Test malformed pattern',
      };
      
      const result = await patternLibrary.analyzePattern(page, malformedPattern as any);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result.elements)).toBe(true);
      expect(result.elements.length).toBe(0);
    });
  });
});
