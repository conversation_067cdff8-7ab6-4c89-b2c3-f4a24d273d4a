import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface DataRightsCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class DataRightsCheck {
  /**
   * Check for data subject rights information and links
   * REAL ANALYSIS - scans actual webpage content
   */
  async performCheck(config: DataRightsCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Search for data subject rights information
      const rightsAnalysis = await this.analyzeDataSubjectRights(page);

      // Score based on rights found
      const totalRights = 8; // GDPR defines 8 key rights
      const foundRights = rightsAnalysis.rightsFound.length;
      score = Math.round((foundRights / totalRights) * 100);

      // Add evidence for found rights
      for (const right of rightsAnalysis.rightsFound) {
        evidence.push({
          type: 'text',
          description: `Data subject right found: ${right.name}`,
          location: right.location,
          value: right.description,
        });
      }

      // Add evidence for missing rights
      for (const missingRight of rightsAnalysis.missingRights) {
        evidence.push({
          type: 'text',
          description: `Missing data subject right: ${missingRight}`,
          value: 'Required by GDPR',
        });
      }

      // Check for contact mechanisms
      const contactMechanisms = await this.findContactMechanisms(page);
      if (contactMechanisms.length > 0) {
        score += 20; // Bonus for contact mechanisms
        evidence.push({
          type: 'element',
          description: 'Contact mechanisms found for exercising rights',
          value: `${contactMechanisms.length} contact methods available`,
        });
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-013',
        ruleName: 'Data Subject Rights',
        category: 'data_rights',
        passed,
        score: Math.min(100, score),
        weight: 7,
        severity: 'high',
        evidence,
        recommendations: this.generateDataRightsRecommendations(rightsAnalysis, contactMechanisms),
        manualReviewRequired: false,
      };
    } catch (error) {
      return {
        ruleId: 'GDPR-013',
        ruleName: 'Data Subject Rights',
        category: 'data_rights',
        passed: false,
        score: 0,
        weight: 7,
        severity: 'high',
        evidence: [
          {
            type: 'text',
            description: 'Data rights check failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Add data subject rights information',
            description: 'Include comprehensive information about GDPR data subject rights',
            implementation: 'Add section explaining user rights and how to exercise them',
            effort: 'moderate',
            impact: 'high',
          },
        ],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze data subject rights mentioned on the page
   */
  private async analyzeDataSubjectRights(page: Page): Promise<{
    rightsFound: Array<{ name: string; description: string; location: string }>;
    missingRights: string[];
  }> {
    return await page.evaluate(() => {
      const gdprRights = [
        { name: 'Right to Access', keywords: ['access', 'view', 'obtain', 'copy'] },
        {
          name: 'Right to Rectification',
          keywords: ['rectification', 'correct', 'update', 'amend'],
        },
        {
          name: 'Right to Erasure',
          keywords: ['erasure', 'delete', 'removal', 'right to be forgotten'],
        },
        { name: 'Right to Restrict Processing', keywords: ['restrict', 'limit', 'suspend'] },
        { name: 'Right to Data Portability', keywords: ['portability', 'transfer', 'export'] },
        { name: 'Right to Object', keywords: ['object', 'opt-out', 'refuse'] },
        { name: 'Right to Withdraw Consent', keywords: ['withdraw', 'revoke', 'consent'] },
        {
          name: 'Right not to be subject to Automated Decision-making',
          keywords: ['automated', 'profiling', 'algorithmic'],
        },
      ];

      const pageText = document.body.textContent?.toLowerCase() || '';
      const rightsFound: Array<{ name: string; description: string; location: string }> = [];
      const missingRights: string[] = [];

      for (const right of gdprRights) {
        const found = right.keywords.some((keyword) => pageText.includes(keyword.toLowerCase()));

        if (found) {
          // Try to find the specific section
          const elements = document.querySelectorAll('*');
          let location = 'Page content';

          for (const element of Array.from(elements)) {
            const elementText = element.textContent?.toLowerCase() || '';
            if (right.keywords.some((keyword) => elementText.includes(keyword.toLowerCase()))) {
              const parent = element.closest(
                'section, div[class*="privacy"], div[class*="rights"]',
              );
              if (parent) {
                location = parent.tagName.toLowerCase();
                break;
              }
            }
          }

          rightsFound.push({
            name: right.name,
            description: `Found references to ${right.keywords.join(', ')}`,
            location,
          });
        } else {
          missingRights.push(right.name);
        }
      }

      return { rightsFound, missingRights };
    });
  }

  /**
   * Find contact mechanisms for exercising rights
   */
  private async findContactMechanisms(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const mechanisms: string[] = [];

      // Look for email addresses
      const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
      const pageText = document.body.textContent || '';
      const emails = pageText.match(emailPattern);

      if (emails && emails.length > 0) {
        mechanisms.push('Email contact');
      }

      // Look for contact forms
      const forms = document.querySelectorAll('form');
      for (const form of Array.from(forms)) {
        const formText = form.textContent?.toLowerCase() || '';
        if (
          formText.includes('contact') ||
          formText.includes('privacy') ||
          formText.includes('rights')
        ) {
          mechanisms.push('Contact form');
          break;
        }
      }

      // Look for phone numbers
      const phonePattern = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
      const phones = pageText.match(phonePattern);

      if (phones && phones.length > 0) {
        mechanisms.push('Phone contact');
      }

      // Look for postal addresses
      const addressKeywords = ['address', 'street', 'avenue', 'road', 'suite', 'floor'];
      const hasAddress = addressKeywords.some((keyword) =>
        pageText.toLowerCase().includes(keyword),
      );

      if (hasAddress) {
        mechanisms.push('Postal address');
      }

      return mechanisms;
    });
  }

  /**
   * Generate recommendations for data rights compliance
   */
  private generateDataRightsRecommendations(
    rightsAnalysis: {
      missingRights: string[];
    },
    contactMechanisms: string[],
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (rightsAnalysis.missingRights.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Add missing data subject rights information',
        description: `Include information about: ${rightsAnalysis.missingRights.join(', ')}`,
        implementation: 'Add comprehensive section explaining all GDPR data subject rights',
        effort: 'moderate',
        impact: 'high',
      });
    }

    if (contactMechanisms.length === 0) {
      recommendations.push({
        priority: 2,
        title: 'Provide contact mechanisms',
        description: 'Add clear contact information for exercising data subject rights',
        implementation: 'Include email, contact form, or other means for users to contact you',
        effort: 'minimal',
        impact: 'high',
      });
    }

    return recommendations;
  }
}
