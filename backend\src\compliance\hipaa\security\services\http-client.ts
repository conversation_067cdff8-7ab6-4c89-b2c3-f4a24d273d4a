import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { URL } from 'url';

export interface HttpResponse {
  statusCode: number;
  responseHeaders: Record<string, string>;
  body: string;
  url: string;
  redirectChain: string[];
}

export interface HttpClientConfig {
  timeout: number;
  maxRedirects: number;
  userAgent: string;
  retryAttempts: number;
}

export class HttpClient {
  private client: AxiosInstance;
  private config: HttpClientConfig;

  constructor(config: HttpClientConfig) {
    this.config = config;
    this.client = axios.create({
      timeout: config.timeout,
      maxRedirects: config.maxRedirects,
      headers: {
        'User-Agent': config.userAgent,
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        Connection: 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      validateStatus: () => true, // Accept all status codes
    });
  }

  /**
   * Fetch a URL and return detailed response information
   */
  async fetchUrl(url: string): Promise<HttpResponse> {
    try {
      console.log(`🌐 Fetching URL: ${url}`);

      const response = await this.client.get(url);

      return {
        statusCode: response.status,
        responseHeaders: this.normalizeHeaders(response.headers),
        body: response.data || '',
        url: response.config.url || url,
        redirectChain: this.extractRedirectChain(response),
      };
    } catch (error) {
      console.warn(
        `⚠️ Failed to fetch ${url}:`,
        error instanceof Error ? error.message : 'Unknown error',
      );

      // Return a minimal response for failed requests
      return {
        statusCode: 0,
        responseHeaders: {},
        body: '',
        url,
        redirectChain: [],
      };
    }
  }

  /**
   * Discover internal links from a webpage
   */
  async discoverLinks(url: string): Promise<string[]> {
    try {
      const response = await this.fetchUrl(url);

      if (response.statusCode !== 200 || !response.body) {
        console.warn(`⚠️ Cannot discover links from ${url} (status: ${response.statusCode})`);
        return [];
      }

      const links = this.extractLinksFromHtml(response.body, url);
      const internalLinks = this.filterInternalLinks(links, url);

      console.log(`🔗 Discovered ${internalLinks.length} internal links from ${url}`);
      return internalLinks;
    } catch (error) {
      console.error(`❌ Link discovery failed for ${url}:`, error);
      return [];
    }
  }

  /**
   * Extract links from HTML content
   */
  private extractLinksFromHtml(html: string, baseUrl: string): string[] {
    const links: string[] = [];

    // Simple regex patterns for different link types
    const patterns = [
      /<a[^>]+href=["']([^"']+)["']/gi, // <a href="...">
      /<link[^>]+href=["']([^"']+)["']/gi, // <link href="...">
      /<form[^>]+action=["']([^"']+)["']/gi, // <form action="...">
      /<iframe[^>]+src=["']([^"']+)["']/gi, // <iframe src="...">
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        try {
          const href = match[1];

          // Skip javascript:, mailto:, tel:, etc.
          if (
            href.startsWith('javascript:') ||
            href.startsWith('mailto:') ||
            href.startsWith('tel:') ||
            href.startsWith('#')
          ) {
            continue;
          }

          // Convert relative URLs to absolute
          const absoluteUrl = new URL(href, baseUrl).toString();
          links.push(absoluteUrl);
        } catch (error) {
          // Invalid URL, skip it
          continue;
        }
      }
    }

    return Array.from(new Set(links)); // Remove duplicates
  }

  /**
   * Filter to keep only internal links (same domain)
   */
  private filterInternalLinks(links: string[], baseUrl: string): string[] {
    try {
      // Extract domain from base URL
      const baseDomain = new URL(baseUrl).hostname;

      return links.filter((link) => {
        try {
          const linkUrlObj = new URL(link);
          return linkUrlObj.hostname === baseDomain;
        } catch {
          return false;
        }
      });
    } catch {
      return [];
    }
  }

  /**
   * Normalize response headers to a consistent format
   */
  private normalizeHeaders(headers: Record<string, unknown>): Record<string, string> {
    const normalized: Record<string, string> = {};

    for (const [key, value] of Object.entries(headers)) {
      if (typeof value === 'string') {
        normalized[key.toLowerCase()] = value;
      } else if (Array.isArray(value)) {
        normalized[key.toLowerCase()] = value.join(', ');
      }
    }

    return normalized;
  }

  /**
   * Extract redirect chain from response
   */
  private extractRedirectChain(response: AxiosResponse): string[] {
    const redirectChain: string[] = [];

    // Axios doesn't provide direct access to redirect chain
    // This is a simplified implementation
    if (response.request && response.request.res && response.request.res.responseUrl) {
      redirectChain.push(response.request.res.responseUrl);
    }

    return redirectChain;
  }

  /**
   * Check if a URL is accessible
   */
  async isUrlAccessible(url: string): Promise<boolean> {
    try {
      const response = await this.client.head(url);
      return response.status >= 200 && response.status < 400;
    } catch {
      return false;
    }
  }

  /**
   * Get basic URL information without fetching full content
   */
  async getUrlInfo(url: string): Promise<{ statusCode: number; headers: Record<string, string> }> {
    try {
      const response = await this.client.head(url);
      return {
        statusCode: response.status,
        headers: this.normalizeHeaders(response.headers),
      };
    } catch (error) {
      return {
        statusCode: 0,
        headers: {},
      };
    }
  }
}
