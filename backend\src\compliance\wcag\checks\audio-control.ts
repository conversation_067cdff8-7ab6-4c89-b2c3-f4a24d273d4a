/**
 * WCAG-050: Audio Control Check
 * Success Criterion: 1.4.2 Audio Control (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import MultimediaAccessibilityTester, {
  MultimediaAccessibilityReport,
} from '../utils/multimedia-accessibility-tester';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';

interface AudioElement {
  tagName: string;
  src?: string;
  hasLoop?: boolean;
  autoplay?: boolean;
  controls?: boolean;
  muted?: boolean;
  duration?: number;
}

export interface AudioControlConfig extends EnhancedCheckConfig {
  enableMultimediaAccessibilityTesting?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAdvancedAudioDetection?: boolean;
}

export class AudioControlCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private multimediaAccessibilityTester = MultimediaAccessibilityTester.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();

  async performCheck(config: AudioControlConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: AudioControlConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableMultimediaAccessibilityTesting: true,
      enableAccessibilityPatterns: true,
      enableAdvancedAudioDetection: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-050',
      'Audio Control',
      'perceivable',
      0.0458,
      'A',
      enhancedConfig,
      this.executeAudioControlCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with audio control analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-050',
        ruleName: 'Audio Control',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'audio-control-analysis',
          autoplayDetection: true,
          audioControlValidation: true,
          multimediaAccessibilityTesting: enhancedConfig.enableMultimediaAccessibilityTesting,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 20,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeAudioControlCheck(
    page: Page,
    _config: AudioControlConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced multimedia accessibility testing
    const multimediaReport =
      await this.multimediaAccessibilityTester.testMultimediaAccessibility(page);

    // Analyze audio control requirements using MultimediaAccessibilityTester
    const audioControlAnalysis = await this.analyzeAudioControlRequirements(page, multimediaReport);

    // Combine analysis results
    const totalChecks = audioControlAnalysis.totalChecks;
    const passedChecks = audioControlAnalysis.passedChecks;

    evidence.push(...audioControlAnalysis.evidence);
    issues.push(...audioControlAnalysis.issues);
    recommendations.push(...audioControlAnalysis.recommendations);

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze audio control requirements using MultimediaAccessibilityTester
   */
  private async analyzeAudioControlRequirements(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Combine audio and video elements for analysis
    const allMediaElements = [...multimediaReport.audioElements, ...multimediaReport.videoElements];
    const totalChecks = allMediaElements.length;
    let passedChecks = 0;

    allMediaElements.forEach((media, index) => {
      const mediaType = media.type;

      // Check for autoplay issues
      if (media.autoplay.autoplays && media.autoplay.isProblematic) {
        issues.push(`${mediaType} ${index + 1} autoplays without user control`);
        evidence.push({
          type: 'code',
          description: `${mediaType} ${index + 1} violates audio control requirements`,
          value: `${media.selector} - autoplays: ${media.autoplay.autoplays}, hasUserControl: ${media.autoplay.hasUserControl}`,
          selector: media.selector,
          severity: 'error',
        });
        recommendations.push(`Add user controls or remove autoplay from ${mediaType} ${index + 1}`);
      } else {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `${mediaType} ${index + 1} has proper audio control`,
          value: `${media.selector} - autoplays: ${media.autoplay.autoplays}, hasUserControl: ${media.autoplay.hasUserControl}`,
          selector: media.selector,
          severity: 'info',
        });
      }

      // Check for accessible controls
      if (!media.controls.areAccessible && media.autoplay.autoplays) {
        issues.push(`${mediaType} ${index + 1} lacks accessible controls`);
        evidence.push({
          type: 'code',
          description: `${mediaType} ${index + 1} needs accessible controls`,
          value: `${media.selector} - hasControls: ${media.controls.hasControls}, areAccessible: ${media.controls.areAccessible}`,
          selector: media.selector,
          severity: 'warning',
        });
        recommendations.push(`Add accessible controls to ${mediaType} ${index + 1}`);
      }

      // Add issues from MultimediaAccessibilityTester
      if (media.issues.length > 0) {
        media.issues.forEach((issue) => {
          issues.push(`${mediaType} ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (media.recommendations.length > 0) {
        media.recommendations.forEach((recommendation) => {
          recommendations.push(`${mediaType} ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(audio: AudioElement): string {
    if (audio.tagName === 'audio') {
      return `<audio src="${audio.src || ''}" autoplay${audio.hasLoop ? ' loop' : ''}>`;
    }
    if (audio.tagName === 'video') {
      return `<video src="${audio.src || ''}" autoplay${audio.hasLoop ? ' loop' : ''}>`;
    }
    if (audio.tagName === 'iframe') {
      return `<iframe src="${audio.src || ''}"></iframe>`;
    }
    return `<${audio.tagName} autoplay>Audio content</${audio.tagName}>`;
  }

  private getAfterExample(audio: AudioElement): string {
    if (audio.tagName === 'audio') {
      return `<audio src="${audio.src || ''}" controls>\n  <p>Your browser doesn't support audio.</p>\n</audio>`;
    }
    if (audio.tagName === 'video') {
      return `<video src="${audio.src || ''}" controls>\n  <p>Your browser doesn't support video.</p>\n</video>`;
    }
    if (audio.tagName === 'iframe') {
      const src = audio.src || '';
      return `<iframe src="${src.replace('autoplay=1', 'autoplay=0')}"></iframe>`;
    }
    return `<${audio.tagName} controls>Audio content with controls</${audio.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('without user controls')) {
      return 'Add controls attribute to provide user audio controls';
    }
    if (issues.includes('longer than 3 seconds')) {
      return 'Remove autoplay or ensure audio is shorter than 3 seconds';
    }
    if (issues.includes('with loop')) {
      return 'Remove autoplay from looping audio or provide stop controls';
    }
    if (issues.includes('high volume')) {
      return 'Reduce default volume or provide volume controls';
    }
    return 'Provide user controls for audio playback';
  }

  private getCodeExample(tagName: string): string {
    switch (tagName) {
      case 'audio':
        return `
<!-- Before: Autoplay audio without controls -->
<audio src="background-music.mp3" autoplay loop>
  Your browser doesn't support audio.
</audio>

<!-- After: Audio with user controls -->
<audio src="background-music.mp3" controls>
  <p>Your browser doesn't support audio.</p>
  <a href="background-music.mp3">Download audio file</a>
</audio>

<!-- JavaScript controls for better accessibility -->
<script>
function toggleAudio() {
  const audio = document.getElementById('background-audio');
  if (audio.paused) {
    audio.play();
  } else {
    audio.pause();
  }
}

function setVolume(value) {
  document.getElementById('background-audio').volume = value;
}
</script>
        `;
      case 'video':
        return `
<!-- Before: Autoplay video with audio -->
<video src="presentation.mp4" autoplay>
  Your browser doesn't support video.
</video>

<!-- After: Video with user controls -->
<video src="presentation.mp4" controls>
  <p>Your browser doesn't support video.</p>
  <a href="presentation.mp4">Download video file</a>
</video>
        `;
      case 'iframe':
        return `
<!-- Before: Embedded player with autoplay -->
<iframe src="https://example.com/player?autoplay=1"></iframe>

<!-- After: Embedded player without autoplay -->
<iframe src="https://example.com/player?autoplay=0"></iframe>
        `;
      default:
        return `
<!-- Provide user controls for audio content -->
<${tagName} controls>
  Audio content with accessible controls
</${tagName}>
        `;
    }
  }
}
