/**
 * Test for Enhanced Evidence Generator
 */

import { EnhancedEvidenceGenerator, ElementIssue } from './enhanced-evidence-generator';

describe('EnhancedEvidenceGenerator', () => {
  describe('generateEnhancedEvidence', () => {
    it('should generate enhanced evidence for missing alt text issues', () => {
      const issues: ElementIssue[] = [
        {
          selector: 'img.logo',
          element: '<img src="logo.png" class="logo">',
          issues: ['Missing alt attribute'],
          severity: 'error',
          fixSuggestion: 'Add descriptive alt text',
        },
        {
          selector: 'img.banner',
          element: '<img src="banner.jpg" class="banner">',
          issues: ['Missing alt attribute'],
          severity: 'error',
          fixSuggestion: 'Add descriptive alt text',
        },
      ];

      const evidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
        'WCAG-001',
        'Non-text Content',
        issues
      );

      expect(evidence).toHaveLength(3); // 1 main + 2 individual elements
      expect(evidence[0].description).toContain('Images missing alternative text');
      expect(evidence[0].elementCount).toBe(2);
      expect(evidence[0].affectedSelectors).toEqual(['img.logo', 'img.banner']);
      expect(evidence[0].fixExample).toBeDefined();
    });

    it('should generate passing evidence when no issues found', () => {
      const evidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
        'WCAG-001',
        'Non-text Content',
        []
      );

      expect(evidence).toHaveLength(1);
      expect(evidence[0].description).toContain('No accessibility issues found');
      expect(evidence[0].severity).toBe('info');
      expect(evidence[0].elementCount).toBe(0);
    });

    it('should group issues by type', () => {
      const issues: ElementIssue[] = [
        {
          selector: 'img.logo',
          element: '<img src="logo.png">',
          issues: ['Missing alt attribute'],
          severity: 'error',
          fixSuggestion: 'Add alt text',
        },
        {
          selector: 'button.submit',
          element: '<button onclick="submit()">Submit</button>',
          issues: ['Missing keyboard handler'],
          severity: 'error',
          fixSuggestion: 'Add keyboard support',
        },
      ];

      const evidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
        'WCAG-005',
        'Keyboard',
        issues
      );

      // Should have separate groups for alt text and keyboard issues
      expect(evidence.length).toBeGreaterThan(2);
      
      const altTextEvidence = evidence.find(e => e.description.includes('alternative text'));
      const keyboardEvidence = evidence.find(e => e.description.includes('keyboard'));
      
      expect(altTextEvidence).toBeDefined();
      expect(keyboardEvidence).toBeDefined();
    });

    it('should limit elements shown based on maxElementsToShow config', () => {
      const issues: ElementIssue[] = Array.from({ length: 15 }, (_, i) => ({
        selector: `img.image-${i}`,
        element: `<img src="image-${i}.jpg">`,
        issues: ['Missing alt attribute'],
        severity: 'error' as const,
        fixSuggestion: 'Add alt text',
      }));

      const evidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
        'WCAG-001',
        'Non-text Content',
        issues,
        { maxElementsToShow: 3 }
      );

      // Should have: 1 main + 3 individual + 1 "and X more" indicator
      expect(evidence).toHaveLength(5);
      
      const moreIndicator = evidence.find(e => e.description.includes('and 12 more'));
      expect(moreIndicator).toBeDefined();
    });
  });

  describe('analyzeElementIssues', () => {
    it('should analyze non-text content issues correctly', () => {
      const element = {
        tagName: 'IMG',
        alt: undefined,
        outerHTML: '<img src="test.jpg">',
      };

      const issue = EnhancedEvidenceGenerator.analyzeElementIssues(
        element,
        'img.test',
        'WCAG-001',
        'non-text-content'
      );

      expect(issue).toBeDefined();
      expect(issue!.issues).toContain('Missing alt attribute');
      expect(issue!.severity).toBe('error');
    });

    it('should return null for decorative images with empty alt', () => {
      const element = {
        tagName: 'IMG',
        alt: '',
        outerHTML: '<img src="decorative.jpg" alt="">',
      };

      const issue = EnhancedEvidenceGenerator.analyzeElementIssues(
        element,
        'img.decorative',
        'WCAG-001',
        'non-text-content'
      );

      expect(issue).toBeNull();
    });

    it('should detect short alt text as warning', () => {
      const element = {
        tagName: 'IMG',
        alt: 'x',
        outerHTML: '<img src="test.jpg" alt="x">',
      };

      const issue = EnhancedEvidenceGenerator.analyzeElementIssues(
        element,
        'img.test',
        'WCAG-001',
        'non-text-content'
      );

      expect(issue).toBeDefined();
      expect(issue!.issues).toContain('Alt text too short');
      expect(issue!.severity).toBe('warning');
    });
  });

  describe('extractElementHTML', () => {
    it('should handle missing elements gracefully', async () => {
      const mockPage = {
        evaluate: jest.fn().mockResolvedValue(''),
      };

      const html = await EnhancedEvidenceGenerator.extractElementHTML(
        mockPage,
        'non-existent-selector'
      );

      expect(html).toBe('<element selector="non-existent-selector">');
    });

    it('should extract element HTML successfully', async () => {
      const mockPage = {
        evaluate: jest.fn().mockResolvedValue('<img src="test.jpg" alt="Test">'),
      };

      const html = await EnhancedEvidenceGenerator.extractElementHTML(
        mockPage,
        'img.test'
      );

      expect(html).toBe('<img src="test.jpg" alt="Test">');
      expect(mockPage.evaluate).toHaveBeenCalledWith(expect.any(Function), 'img.test');
    });
  });
});
