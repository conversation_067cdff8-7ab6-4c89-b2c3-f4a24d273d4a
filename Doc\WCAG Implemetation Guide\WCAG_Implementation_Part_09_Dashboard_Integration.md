# WCAG Implementation Part 09: Dashboard Integration & Navigation

## Overview

This document implements the complete WCAG dashboard integration with the main application, including navigation, routing, state management, and seamless user experience. The dashboard follows established UI patterns and integrates with existing authentication.

## ⚠️ CRITICAL REQUIREMENTS

### 🚫 NO ANY[] TYPES
**STRICTLY PROHIBITED**: All dashboard implementations must use strict TypeScript typing.

### ✅ DEPENDENCIES
- **Parts 01-08 Complete**: Backend API and frontend components ready
- **Existing Dashboard**: Integration with current dashboard structure
- **Authentication**: Keycloak integration for secure access
- **Navigation**: Consistent with existing navigation patterns

## Prerequisites

- Parts 01-08 completed successfully
- Main dashboard application structure available
- React Router for navigation
- State management solution (Redux/Context API)
- Existing authentication system

## Step 1: Dashboard State Management

### 1.1 Create WCAG State Management

Create `frontend/store/wcag/wcagSlice.ts`:

```typescript
/**
 * WCAG Redux Slice
 * State management for WCAG compliance functionality
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  WcagScanResult,
  WcagScanFormData,
  ScanProgressInfo,
  QueueStatusInfo,
  WcagDashboardState
} from '../../types/wcag';
import { wcagApiService } from '../../services/wcag-api';

// Async thunks for API calls
export const startWcagScan = createAsyncThunk(
  'wcag/startScan',
  async (formData: WcagScanFormData, { rejectWithValue }) => {
    try {
      const result = await wcagApiService.startScan(formData);
      return result;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to start scan');
    }
  }
);

export const fetchWcagScans = createAsyncThunk(
  'wcag/fetchScans',
  async (params: { page?: number; limit?: number; status?: string } = {}, { rejectWithValue }) => {
    try {
      const result = await wcagApiService.getScans(params);
      return result;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch scans');
    }
  }
);

export const fetchScanDetails = createAsyncThunk(
  'wcag/fetchScanDetails',
  async (scanId: string, { rejectWithValue }) => {
    try {
      const result = await wcagApiService.getScanDetails(scanId);
      return result;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch scan details');
    }
  }
);

export const deleteScan = createAsyncThunk(
  'wcag/deleteScan',
  async (scanId: string, { rejectWithValue }) => {
    try {
      await wcagApiService.deleteScan(scanId);
      return scanId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete scan');
    }
  }
);

export const exportScan = createAsyncThunk(
  'wcag/exportScan',
  async (
    { scanId, format, options }: {
      scanId: string;
      format: 'pdf' | 'json' | 'csv';
      options?: { includeEvidence?: boolean; includeRecommendations?: boolean; includeManualReviewItems?: boolean };
    },
    { rejectWithValue }
  ) => {
    try {
      const blob = await wcagApiService.exportScan(scanId, format, options);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `wcag-report-${scanId}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return { scanId, format };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to export scan');
    }
  }
);

// Initial state
const initialState: WcagDashboardState & {
  scans: WcagScanResult[];
  scanProgress: Record<string, ScanProgressInfo>;
  queueStatus: QueueStatusInfo | null;
  loading: {
    scanning: boolean;
    fetching: boolean;
    exporting: boolean;
    deleting: boolean;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
} = {
  currentScan: undefined,
  recentScans: [],
  isScanning: false,
  scanProgress: 0,
  error: undefined,
  selectedScanId: undefined,
  scans: [],
  scanProgress: {},
  queueStatus: null,
  loading: {
    scanning: false,
    fetching: false,
    exporting: false,
    deleting: false
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  }
};

// WCAG slice
const wcagSlice = createSlice({
  name: 'wcag',
  initialState,
  reducers: {
    // Synchronous actions
    setSelectedScan: (state, action: PayloadAction<string | undefined>) => {
      state.selectedScanId = action.payload;
      if (action.payload) {
        state.currentScan = state.scans.find(scan => scan.scanId === action.payload);
      } else {
        state.currentScan = undefined;
      }
    },
    
    updateScanProgress: (state, action: PayloadAction<ScanProgressInfo>) => {
      const { scanId } = action.payload;
      state.scanProgress[scanId] = action.payload;
      
      // Update overall scanning state
      const hasRunningScans = Object.values(state.scanProgress).some(
        progress => progress.status === 'running' || progress.status === 'pending'
      );
      state.isScanning = hasRunningScans;
    },
    
    updateQueueStatus: (state, action: PayloadAction<QueueStatusInfo>) => {
      state.queueStatus = action.payload;
    },
    
    clearError: (state) => {
      state.error = undefined;
    },
    
    setPagination: (state, action: PayloadAction<{ page?: number; limit?: number }>) => {
      if (action.payload.page !== undefined) {
        state.pagination.page = action.payload.page;
      }
      if (action.payload.limit !== undefined) {
        state.pagination.limit = action.payload.limit;
      }
    },
    
    resetWcagState: () => initialState
  },
  
  extraReducers: (builder) => {
    // Start scan
    builder
      .addCase(startWcagScan.pending, (state) => {
        state.loading.scanning = true;
        state.error = undefined;
      })
      .addCase(startWcagScan.fulfilled, (state, action) => {
        state.loading.scanning = false;
        state.currentScan = action.payload;
        state.isScanning = true;
        
        // Add to recent scans if not already there
        const existingIndex = state.recentScans.findIndex(
          scan => scan.scanId === action.payload.scanId
        );
        if (existingIndex === -1) {
          state.recentScans.unshift(action.payload);
          // Keep only last 10 recent scans
          state.recentScans = state.recentScans.slice(0, 10);
        }
      })
      .addCase(startWcagScan.rejected, (state, action) => {
        state.loading.scanning = false;
        state.error = action.payload as string;
        state.isScanning = false;
      });

    // Fetch scans
    builder
      .addCase(fetchWcagScans.pending, (state) => {
        state.loading.fetching = true;
        state.error = undefined;
      })
      .addCase(fetchWcagScans.fulfilled, (state, action) => {
        state.loading.fetching = false;
        state.scans = action.payload.scans.map(scan => ({
          ...scan,
          checks: [],
          recommendations: [],
          automatedSummary: {
            totalAutomatedChecks: scan.totalAutomatedChecks || 0,
            passedAutomatedChecks: scan.passedAutomatedChecks || 0,
            failedAutomatedChecks: scan.failedAutomatedChecks || 0,
            automatedScore: scan.automatedScore || 0,
            categoryScores: { perceivable: 0, operable: 0, understandable: 0, robust: 0 },
            versionScores: { wcag21: 0, wcag22: 0, wcag30: 0 },
            automationRate: 0.87
          },
          manualReviewSummary: {
            totalManualItems: scan.totalManualItems || 0,
            estimatedReviewTime: scan.estimatedReviewTime || 0
          },
          metadata: {
            scanId: scan.scanId,
            userId: '',
            requestId: scan.scanId,
            startTime: scan.scanTimestamp,
            endTime: scan.completionTimestamp,
            userAgent: '',
            viewport: { width: 1920, height: 1080 },
            environment: 'production',
            version: '1.0.0'
          }
        }));
        
        state.pagination = action.payload.pagination;
        
        // Update recent scans
        state.recentScans = state.scans
          .filter(scan => scan.status === 'completed')
          .slice(0, 10);
      })
      .addCase(fetchWcagScans.rejected, (state, action) => {
        state.loading.fetching = false;
        state.error = action.payload as string;
      });

    // Fetch scan details
    builder
      .addCase(fetchScanDetails.pending, (state) => {
        state.loading.fetching = true;
        state.error = undefined;
      })
      .addCase(fetchScanDetails.fulfilled, (state, action) => {
        state.loading.fetching = false;
        state.currentScan = action.payload;
        
        // Update scan in scans array
        const index = state.scans.findIndex(scan => scan.scanId === action.payload.scanId);
        if (index !== -1) {
          state.scans[index] = action.payload;
        }
      })
      .addCase(fetchScanDetails.rejected, (state, action) => {
        state.loading.fetching = false;
        state.error = action.payload as string;
      });

    // Delete scan
    builder
      .addCase(deleteScan.pending, (state) => {
        state.loading.deleting = true;
        state.error = undefined;
      })
      .addCase(deleteScan.fulfilled, (state, action) => {
        state.loading.deleting = false;
        const scanId = action.payload;
        
        // Remove from scans array
        state.scans = state.scans.filter(scan => scan.scanId !== scanId);
        
        // Remove from recent scans
        state.recentScans = state.recentScans.filter(scan => scan.scanId !== scanId);
        
        // Clear current scan if it was deleted
        if (state.currentScan?.scanId === scanId) {
          state.currentScan = undefined;
          state.selectedScanId = undefined;
        }
        
        // Remove progress tracking
        delete state.scanProgress[scanId];
      })
      .addCase(deleteScan.rejected, (state, action) => {
        state.loading.deleting = false;
        state.error = action.payload as string;
      });

    // Export scan
    builder
      .addCase(exportScan.pending, (state) => {
        state.loading.exporting = true;
        state.error = undefined;
      })
      .addCase(exportScan.fulfilled, (state) => {
        state.loading.exporting = false;
      })
      .addCase(exportScan.rejected, (state, action) => {
        state.loading.exporting = false;
        state.error = action.payload as string;
      });
  }
});

// Export actions
export const {
  setSelectedScan,
  updateScanProgress,
  updateQueueStatus,
  clearError,
  setPagination,
  resetWcagState
} = wcagSlice.actions;

// Selectors
export const selectWcagState = (state: { wcag: typeof initialState }) => state.wcag;
export const selectCurrentScan = (state: { wcag: typeof initialState }) => state.wcag.currentScan;
export const selectRecentScans = (state: { wcag: typeof initialState }) => state.wcag.recentScans;
export const selectIsScanning = (state: { wcag: typeof initialState }) => state.wcag.isScanning;
export const selectWcagError = (state: { wcag: typeof initialState }) => state.wcag.error;
export const selectWcagLoading = (state: { wcag: typeof initialState }) => state.wcag.loading;
export const selectScanProgress = (state: { wcag: typeof initialState }) => state.wcag.scanProgress;
export const selectQueueStatus = (state: { wcag: typeof initialState }) => state.wcag.queueStatus;

export default wcagSlice.reducer;
```

## Step 2: Navigation Integration

### 2.1 Create WCAG Navigation Items

Create `frontend/components/navigation/WcagNavigation.tsx`:

```typescript
/**
 * WCAG Navigation Integration
 * Navigation items for WCAG compliance features
 */

import React from 'react';
import {
  ListItem,
  ListItemIcon,
  ListItemText,
  Badge,
  Tooltip
} from '@mui/material';
import {
  Assessment,
  PlayArrow,
  History,
  GetApp,
  Settings
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { selectIsScanning, selectScanProgress } from '../../store/wcag/wcagSlice';

interface WcagNavigationProps {
  onNavigate?: (path: string) => void;
  collapsed?: boolean;
}

const WcagNavigation: React.FC<WcagNavigationProps> = ({
  onNavigate,
  collapsed = false
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isScanning = useSelector(selectIsScanning);
  const scanProgress = useSelector(selectScanProgress);
  
  // Count running scans
  const runningScansCount = Object.values(scanProgress).filter(
    progress => progress.status === 'running' || progress.status === 'pending'
  ).length;

  /**
   * Handle navigation
   */
  const handleNavigate = (path: string) => {
    navigate(path);
    if (onNavigate) {
      onNavigate(path);
    }
  };

  /**
   * Check if path is active
   */
  const isActive = (path: string): boolean => {
    return location.pathname.startsWith(path);
  };

  const navigationItems = [
    {
      path: '/compliance/wcag',
      label: 'WCAG Overview',
      icon: <Assessment />,
      tooltip: 'WCAG compliance dashboard and overview'
    },
    {
      path: '/compliance/wcag/scan',
      label: 'Start Scan',
      icon: (
        <Badge
          badgeContent={runningScansCount}
          color="primary"
          invisible={runningScansCount === 0}
        >
          <PlayArrow />
        </Badge>
      ),
      tooltip: isScanning 
        ? `${runningScansCount} scan(s) running`
        : 'Start new WCAG compliance scan'
    },
    {
      path: '/compliance/wcag/history',
      label: 'Scan History',
      icon: <History />,
      tooltip: 'View previous WCAG scan results'
    },
    {
      path: '/compliance/wcag/reports',
      label: 'Reports',
      icon: <GetApp />,
      tooltip: 'Export and download WCAG reports'
    },
    {
      path: '/compliance/wcag/settings',
      label: 'Settings',
      icon: <Settings />,
      tooltip: 'Configure WCAG scan preferences'
    }
  ];

  return (
    <>
      {navigationItems.map((item) => (
        <Tooltip
          key={item.path}
          title={collapsed ? item.tooltip : ''}
          placement="right"
        >
          <ListItem
            button
            selected={isActive(item.path)}
            onClick={() => handleNavigate(item.path)}
            sx={{
              borderRadius: 1,
              mb: 0.5,
              '&.Mui-selected': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                '&:hover': {
                  backgroundColor: 'primary.dark',
                },
                '& .MuiListItemIcon-root': {
                  color: 'primary.contrastText',
                }
              }
            }}
          >
            <ListItemIcon>
              {item.icon}
            </ListItemIcon>
            {!collapsed && (
              <ListItemText 
                primary={item.label}
                primaryTypographyProps={{
                  variant: 'body2',
                  fontWeight: isActive(item.path) ? 'medium' : 'normal'
                }}
              />
            )}
          </ListItem>
        </Tooltip>
      ))}
    </>
  );
};

export default WcagNavigation;
```

## Step 3: Route Configuration

### 3.1 Create WCAG Routes

Create `frontend/routes/WcagRoutes.tsx`:

```typescript
/**
 * WCAG Routes Configuration
 * Route definitions for WCAG compliance features
 */

import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { ErrorBoundary } from 'react-error-boundary';

// Lazy load components for better performance
const WcagDashboard = React.lazy(() => import('../pages/wcag/WcagDashboard'));
const WcagScanPage = React.lazy(() => import('../pages/wcag/WcagScanPage'));
const WcagHistoryPage = React.lazy(() => import('../pages/wcag/WcagHistoryPage'));
const WcagReportsPage = React.lazy(() => import('../pages/wcag/WcagReportsPage'));
const WcagSettingsPage = React.lazy(() => import('../pages/wcag/WcagSettingsPage'));
const WcagScanDetailsPage = React.lazy(() => import('../pages/wcag/WcagScanDetailsPage'));

/**
 * Loading component for route transitions
 */
const RouteLoading: React.FC = () => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    minHeight="400px"
    gap={2}
  >
    <CircularProgress size={40} />
    <Typography variant="body2" color="text.secondary">
      Loading WCAG compliance tools...
    </Typography>
  </Box>
);

/**
 * Error fallback component
 */
const RouteErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({
  error,
  resetErrorBoundary
}) => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    minHeight="400px"
    gap={2}
    p={3}
  >
    <Typography variant="h6" color="error">
      Something went wrong
    </Typography>
    <Typography variant="body2" color="text.secondary" textAlign="center">
      {error.message}
    </Typography>
    <button onClick={resetErrorBoundary}>
      Try again
    </button>
  </Box>
);

/**
 * WCAG Routes Component
 */
const WcagRoutes: React.FC = () => {
  return (
    <ErrorBoundary FallbackComponent={RouteErrorFallback}>
      <Suspense fallback={<RouteLoading />}>
        <Routes>
          {/* Main WCAG Dashboard */}
          <Route path="/" element={<WcagDashboard />} />

          {/* Start New Scan */}
          <Route path="/scan" element={<WcagScanPage />} />

          {/* Scan History */}
          <Route path="/history" element={<WcagHistoryPage />} />

          {/* Scan Details */}
          <Route path="/scan/:scanId" element={<WcagScanDetailsPage />} />

          {/* Reports and Exports */}
          <Route path="/reports" element={<WcagReportsPage />} />

          {/* Settings */}
          <Route path="/settings" element={<WcagSettingsPage />} />

          {/* Redirect unknown paths to main dashboard */}
          <Route path="*" element={<Navigate to="/compliance/wcag" replace />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export default WcagRoutes;
```

### 3.2 Create Main WCAG Dashboard Page

Create `frontend/pages/wcag/WcagDashboard.tsx`:

```typescript
/**
 * WCAG Dashboard Page
 * Main dashboard for WCAG compliance overview
 */

import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Tabs,
  Tab,
  Chip
} from '@mui/material';
import {
  PlayArrow,
  Assessment,
  History,
  TrendingUp,
  Warning
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  selectCurrentScan,
  selectRecentScans,
  selectIsScanning,
  selectWcagError,
  selectWcagLoading,
  fetchWcagScans,
  clearError
} from '../../store/wcag/wcagSlice';
import WcagScanOverview from '../../components/wcag/WcagScanOverview';
import WcagScanProgress from '../../components/wcag/WcagScanProgress';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`wcag-tabpanel-${index}`}
    aria-labelledby={`wcag-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
  </div>
);

const WcagDashboard: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const currentScan = useSelector(selectCurrentScan);
  const recentScans = useSelector(selectRecentScans);
  const isScanning = useSelector(selectIsScanning);
  const error = useSelector(selectWcagError);
  const loading = useSelector(selectWcagLoading);

  const [activeTab, setActiveTab] = useState(0);

  /**
   * Load initial data
   */
  useEffect(() => {
    dispatch(fetchWcagScans({ limit: 10 }) as any);
  }, [dispatch]);

  /**
   * Handle tab change
   */
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  /**
   * Handle navigation to scan page
   */
  const handleStartScan = () => {
    navigate('/compliance/wcag/scan');
  };

  /**
   * Handle navigation to history
   */
  const handleViewHistory = () => {
    navigate('/compliance/wcag/history');
  };

  /**
   * Handle scan completion
   */
  const handleScanComplete = () => {
    // Refresh scans list
    dispatch(fetchWcagScans({ limit: 10 }) as any);
  };

  /**
   * Calculate dashboard statistics
   */
  const dashboardStats = React.useMemo(() => {
    const completedScans = recentScans.filter(scan => scan.status === 'completed');
    const averageScore = completedScans.length > 0
      ? Math.round(completedScans.reduce((sum, scan) => sum + scan.overallScore, 0) / completedScans.length)
      : 0;

    const highRiskScans = completedScans.filter(scan =>
      scan.riskLevel === 'high' || scan.riskLevel === 'critical'
    ).length;

    const aaCompliantScans = completedScans.filter(scan =>
      scan.levelAchieved === 'AA' || scan.levelAchieved === 'AAA'
    ).length;

    return {
      totalScans: recentScans.length,
      completedScans: completedScans.length,
      averageScore,
      highRiskScans,
      aaCompliantScans,
      complianceRate: completedScans.length > 0
        ? Math.round((aaCompliantScans / completedScans.length) * 100)
        : 0
    };
  }, [recentScans]);

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          WCAG Compliance Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Monitor and improve your website's accessibility compliance
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          onClose={() => dispatch(clearError())}
          sx={{ mb: 3 }}
        >
          {error}
        </Alert>
      )}

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="div">
                {dashboardStats.averageScore}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Average Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="div">
                {dashboardStats.complianceRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                AA Compliance Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <History color="info" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="div">
                {dashboardStats.totalScans}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Scans
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Warning color="warning" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="div">
                {dashboardStats.highRiskScans}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                High Risk Sites
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<PlayArrow />}
              onClick={handleStartScan}
              disabled={loading.scanning}
            >
              Start New Scan
            </Button>
            <Button
              variant="outlined"
              startIcon={<History />}
              onClick={handleViewHistory}
            >
              View Scan History
            </Button>
            <Button
              variant="outlined"
              startIcon={<Assessment />}
              onClick={() => navigate('/compliance/wcag/reports')}
            >
              Generate Report
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  Current Scan
                  {isScanning && <Chip label="Running" color="primary" size="small" />}
                </Box>
              }
            />
            <Tab label="Recent Scans" />
            <Tab label="Trends" />
          </Tabs>
        </Box>

        <TabPanel value={activeTab} index={0}>
          {/* Current Scan Tab */}
          {isScanning && currentScan ? (
            <WcagScanProgress
              scanId={currentScan.scanId}
              onComplete={handleScanComplete}
            />
          ) : currentScan ? (
            <WcagScanOverview scanResult={currentScan} />
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" gutterBottom>
                No Active Scan
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Start a new WCAG compliance scan to analyze your website
              </Typography>
              <Button
                variant="contained"
                startIcon={<PlayArrow />}
                onClick={handleStartScan}
              >
                Start New Scan
              </Button>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {/* Recent Scans Tab */}
          {recentScans.length > 0 ? (
            <Grid container spacing={2}>
              {recentScans.slice(0, 6).map((scan) => (
                <Grid item xs={12} md={6} key={scan.scanId}>
                  <Card
                    sx={{ cursor: 'pointer' }}
                    onClick={() => navigate(`/compliance/wcag/scan/${scan.scanId}`)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="h6" noWrap>
                          {new URL(scan.targetUrl).hostname}
                        </Typography>
                        <Chip
                          label={scan.levelAchieved}
                          color={scan.levelAchieved === 'FAIL' ? 'error' : 'success'}
                          size="small"
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Score: {scan.overallScore}/100
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(scan.metadata.startTime).toLocaleDateString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" gutterBottom>
                No Recent Scans
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Your recent WCAG scans will appear here
              </Typography>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          {/* Trends Tab */}
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" gutterBottom>
              Trends Analysis
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Trend analysis will be available after multiple scans
            </Typography>
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default WcagDashboard;
```

## Step 4: Integration with Main App

### 4.1 Update Main App Router

Update your main app router to include WCAG routes:

```typescript
/**
 * Main App Router Update
 * Add WCAG routes to existing application
 */

import React from 'react';
import { Routes, Route } from 'react-router-dom';
import WcagRoutes from './routes/WcagRoutes';
import { ProtectedRoute } from './components/auth/ProtectedRoute';

// Existing imports...

const AppRouter: React.FC = () => {
  return (
    <Routes>
      {/* Existing routes... */}

      {/* WCAG Compliance Routes */}
      <Route
        path="/compliance/wcag/*"
        element={
          <ProtectedRoute requiredPermissions={['wcag:view', 'wcag:scan']}>
            <WcagRoutes />
          </ProtectedRoute>
        }
      />

      {/* Other routes... */}
    </Routes>
  );
};

export default AppRouter;
```

### 4.2 Update Store Configuration

Update your Redux store to include WCAG slice:

```typescript
/**
 * Store Configuration Update
 * Add WCAG reducer to existing store
 */

import { configureStore } from '@reduxjs/toolkit';
import wcagReducer from './wcag/wcagSlice';
// Other reducers...

export const store = configureStore({
  reducer: {
    // Existing reducers...
    wcag: wcagReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['wcag/exportScan/fulfilled'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

## Validation Checklist

- [ ] WCAG state management with Redux Toolkit
- [ ] Navigation integration with existing patterns
- [ ] Route configuration with lazy loading
- [ ] Main dashboard page with overview
- [ ] Error boundaries and loading states
- [ ] Integration with main app router
- [ ] Store configuration updates
- [ ] Protected routes with permissions
- [ ] Consistent UI patterns and styling
- [ ] Ready for remaining page implementations

## Next Steps

Continue with **Part 10: Export & Reporting** to implement comprehensive report generation and export functionality.

---

*This dashboard integration provides seamless access to WCAG compliance tools within the existing application structure while maintaining consistent user experience and proper state management.*
