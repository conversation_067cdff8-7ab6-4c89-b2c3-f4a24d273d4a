/**
 * Test suite for Form Analysis Injection Timeout Fixes
 * Validates that the enhanced session management and retry mechanisms work correctly
 */

import { Page } from 'puppeteer';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { SessionManager } from '../utils/session-manager';
import { FocusVisibleCheck } from '../checks/focus-visible';
import { LabelInNameCheck } from '../checks/label-in-name';
import { LabelsInstructionsCheck } from '../checks/labels-instructions';
import { ErrorPreventionCheck } from '../checks/error-prevention';
import { ErrorSuggestionCheck } from '../checks/error-suggestion';
import { HeadingsLabelsCheck } from '../checks/headings-labels';
import { HelpCheck } from '../checks/help';
import { ErrorIdentificationCheck } from '../checks/error-identification';

describe('Form Analysis Injection Timeout Fixes', () => {
  let mockPage: jest.Mocked<Page>;
  let formAnalyzer: FormAccessibilityAnalyzer;
  let sessionManager: SessionManager;

  beforeEach(() => {
    // Mock Page object
    mockPage = {
      isClosed: jest.fn().mockReturnValue(false),
      url: jest.fn().mockReturnValue('https://test.example.com'),
      evaluate: jest.fn(),
      waitForFunction: jest.fn(),
      close: jest.fn(),
    } as any;

    formAnalyzer = FormAccessibilityAnalyzer.getInstance();
    sessionManager = SessionManager.getInstance();
  });

  afterEach(() => {
    // Clean up state
    sessionManager.cleanupAllSessions();
    formAnalyzer.cleanupAllStates();
  });

  describe('FormAccessibilityAnalyzer Enhanced Session Management', () => {
    test('should handle page closure gracefully', async () => {
      // Simulate page closure during analysis
      mockPage.isClosed.mockReturnValue(true);

      await expect(
        formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-1')
      ).rejects.toThrow('Page session is not safe for form accessibility analysis');
    });

    test('should prevent concurrent injections on same page', async () => {
      const mockEvaluate = jest.fn().mockResolvedValue(undefined);
      const mockWaitForFunction = jest.fn().mockResolvedValue(undefined);
      
      mockPage.evaluate.mockImplementation(mockEvaluate);
      mockPage.waitForFunction.mockImplementation(mockWaitForFunction);

      // Start two concurrent analyses
      const promise1 = formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-1');
      const promise2 = formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-1');

      await Promise.all([promise1, promise2]);

      // Should only inject once due to state tracking
      expect(mockEvaluate).toHaveBeenCalledTimes(1);
    });

    test('should retry injection on timeout with progressive timeouts', async () => {
      let attemptCount = 0;
      mockPage.waitForFunction.mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          return Promise.reject(new Error('Waiting failed: 5000ms exceeded'));
        }
        return Promise.resolve(undefined);
      });

      mockPage.evaluate.mockResolvedValue({
        hasGetElementSelector: true,
        hasAnalyzeFormElement: true,
        hasGetFormValidation: true,
        hasGetFormLabels: true,
        hasGetFormErrors: true,
        hasGetFormGrouping: true,
        hasGetKeyboardAccess: true,
      });

      await formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-retry');

      // Should have attempted 3 times
      expect(mockPage.waitForFunction).toHaveBeenCalledTimes(3);
      expect(attemptCount).toBe(3);
    });

    test('should use progressive timeouts (3s, 5s, 7s)', async () => {
      const timeouts: number[] = [];
      
      mockPage.waitForFunction.mockImplementation((fn, options) => {
        timeouts.push(options.timeout);
        return Promise.reject(new Error('Timeout'));
      });

      try {
        await formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-timeouts');
      } catch (error) {
        // Expected to fail after all retries
      }

      expect(timeouts).toEqual([3000, 5000, 7000]);
    });
  });

  describe('SessionManager Coordination', () => {
    test('should register and track sessions', () => {
      sessionManager.registerSession(mockPage, 'test-scan-session');
      
      const stats = sessionManager.getSessionStats();
      expect(stats.activeSessions).toBe(1);
    });

    test('should detect unsafe sessions', async () => {
      mockPage.isClosed.mockReturnValue(true);
      
      const isSafe = await sessionManager.isSessionSafe(mockPage, 'test-scan-unsafe');
      expect(isSafe).toBe(false);
    });

    test('should coordinate session locks', async () => {
      let operationOrder: string[] = [];
      
      const operation1 = () => {
        operationOrder.push('op1-start');
        return new Promise(resolve => {
          setTimeout(() => {
            operationOrder.push('op1-end');
            resolve('result1');
          }, 100);
        });
      };

      const operation2 = () => {
        operationOrder.push('op2-start');
        return new Promise(resolve => {
          setTimeout(() => {
            operationOrder.push('op2-end');
            resolve('result2');
          }, 50);
        });
      };

      // Start both operations concurrently
      const [result1, result2] = await Promise.all([
        sessionManager.lockSession(mockPage, 'test-scan-lock', operation1),
        sessionManager.lockSession(mockPage, 'test-scan-lock', operation2)
      ]);

      // Operations should be serialized
      expect(operationOrder).toEqual(['op1-start', 'op1-end', 'op2-start', 'op2-end']);
      expect(result1).toBe('result1');
      expect(result2).toBe('result2');
    });
  });

  describe('WCAG Check Integration', () => {
    test('should pass scanId to form analysis in all affected checks', async () => {
      const mockConfig = { scanId: 'test-scan-integration', page: mockPage };
      
      // Mock successful form analysis
      jest.spyOn(formAnalyzer, 'analyzeFormAccessibility').mockResolvedValue({
        totalForms: 1,
        accessibleForms: 1,
        totalFields: 5,
        accessibleFields: 5,
        overallScore: 100,
        forms: [],
        issues: [],
        recommendations: []
      });

      const checks = [
        new FocusVisibleCheck(),
        new LabelInNameCheck(),
        new LabelsInstructionsCheck(),
        new ErrorPreventionCheck(),
        new ErrorSuggestionCheck(),
        new HeadingsLabelsCheck(),
        new HelpCheck(),
        new ErrorIdentificationCheck()
      ];

      for (const check of checks) {
        try {
          await (check as any).executeCheck?.(mockPage, mockConfig);
        } catch (error) {
          // Some checks might fail due to missing dependencies, but we're testing the scanId passing
        }
      }

      // Verify that analyzeFormAccessibility was called with scanId
      expect(formAnalyzer.analyzeFormAccessibility).toHaveBeenCalledWith(
        mockPage,
        expect.any(Object),
        expect.stringContaining('test-scan-integration')
      );
    });
  });

  describe('Error Recovery and Fallback', () => {
    test('should handle browser frame detachment gracefully', async () => {
      mockPage.evaluate.mockRejectedValue(
        new Error('Protocol error (Runtime.callFunctionOn): Target closed')
      );

      await expect(
        formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-detached')
      ).rejects.toThrow('Form analysis injection process failed');
    });

    test('should handle session closed errors', async () => {
      mockPage.waitForFunction.mockRejectedValue(
        new Error('Protocol error (Page.addScriptToEvaluateOnNewDocument): Session closed')
      );

      await expect(
        formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-closed')
      ).rejects.toThrow('Form analysis injection failed after 3 attempts');
    });

    test('should clean up state on errors', async () => {
      const pageUrl = 'https://test.example.com';
      
      // Simulate error during analysis
      mockPage.evaluate.mockRejectedValue(new Error('Test error'));

      try {
        await formAnalyzer.analyzeFormAccessibility(mockPage, {}, 'test-scan-cleanup');
      } catch (error) {
        // Expected error
      }

      // Clean up should work without errors
      expect(() => {
        formAnalyzer.cleanupPageState(pageUrl);
        sessionManager.cleanupPageState(pageUrl);
      }).not.toThrow();
    });
  });

  describe('Performance and Memory Management', () => {
    test('should clean up stale sessions', () => {
      // Register a session
      sessionManager.registerSession(mockPage, 'test-scan-stale');
      
      // Manually set old timestamp to simulate stale session
      const sessionKey = `test-scan-stale:${mockPage.url()}`;
      const sessions = (sessionManager as any).activeSessions;
      if (sessions.has(sessionKey)) {
        sessions.get(sessionKey).lastActivity = Date.now() - (11 * 60 * 1000); // 11 minutes ago
      }

      sessionManager.cleanupStaleSessions();
      
      const stats = sessionManager.getSessionStats();
      expect(stats.activeSessions).toBe(0);
    });

    test('should prevent memory leaks from injection state', () => {
      const initialStates = (formAnalyzer as any).injectionStateMap.size;
      
      // Simulate multiple failed injections
      for (let i = 0; i < 10; i++) {
        formAnalyzer.cleanupPageState(`https://test${i}.example.com`);
      }

      const finalStates = (formAnalyzer as any).injectionStateMap.size;
      expect(finalStates).toBe(initialStates); // Should not grow
    });
  });
});
