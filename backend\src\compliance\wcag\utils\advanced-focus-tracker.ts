/**
 * Advanced Focus Tracker
 * Extends FocusTracker with custom indicator detection and complex focus flow analysis
 */

import { Page } from 'puppeteer';
import { FocusTracker, FocusableElement, FocusVisibilityResult } from './focus-tracker';
import logger from '../../../utils/logger';

// Third-party libraries for enhanced focus analysis
let getContrastLib: {
  ratio: (fg: string, bg: string) => number;
  score: (fg: string, bg: string) => string;
} | null = null;
let ColorJSLib: {
  new (color: string): {
    contrast: (other: unknown, method: string) => number;
    space?: { id: string };
  };
} | null = null;

try {
  getContrastLib = require('get-contrast');
} catch (error) {
  // Fallback to built-in analysis
}

try {
  ColorJSLib = require('colorjs.io').default;
} catch (error) {
  // Fallback to built-in analysis
}

export interface AdvancedFocusConfig {
  enableCustomIndicatorDetection: boolean;
  enableFlowAnalysis: boolean;
  enableAccessibilityTreeIntegration: boolean;
  enablePerformanceOptimization: boolean;
  enableThirdPartyLibraries: boolean;
  enableWideGamutAnalysis: boolean;
  enableAdvancedColorSpaces: boolean;
  enableKeyboardTrapping: boolean;
  enableFocusSequenceValidation: boolean;
  customIndicatorThreshold: number; // Minimum contrast ratio for custom indicators
  flowAnalysisDepth: number; // Maximum depth for flow analysis
  maxFocusElements: number; // Maximum elements to analyze for performance
}

export interface CustomFocusIndicator {
  type: 'outline' | 'background' | 'border' | 'shadow' | 'transform' | 'custom';
  properties: {
    color?: string;
    width?: number;
    style?: string;
    offset?: number;
    blur?: number;
    spread?: number;
    transform?: string;
    transition?: string;
  };
  contrastRatio: number;
  isVisible: boolean;
  isCustom: boolean;
  accessibility: {
    meetsWCAG: boolean;
    level: 'AA' | 'AAA' | 'fail';
    improvements: string[];
  };
}

export interface FocusFlowNode {
  element: FocusableElement;
  connections: {
    next?: FocusFlowNode;
    previous?: FocusFlowNode;
    parent?: FocusFlowNode;
    children: FocusFlowNode[];
  };
  flowType: 'linear' | 'grid' | 'tree' | 'modal' | 'custom';
  complexity: number;
  issues: string[];
}

export interface CustomFocusAnalysis {
  totalElements: number;
  customIndicators: number;
  standardIndicators: number;
  indicatorTypes: Record<string, number>;
  accessibilityScore: number;
  recommendations: string[];
  detailedAnalysis: Array<{
    element: FocusableElement;
    indicator: CustomFocusIndicator;
    score: number;
  }>;
}

export interface FocusFlowAnalysis {
  flowNodes: FocusFlowNode[];
  flowPatterns: {
    linear: number;
    grid: number;
    tree: number;
    modal: number;
    custom: number;
  };
  complexity: {
    overall: number;
    average: number;
    maximum: number;
  };
  issues: string[];
  recommendations: string[];
  accessibilityImpact: {
    positive: string[];
    negative: string[];
    neutral: string[];
  };
}

export interface AccessibilityTreeNode {
  role: string;
  name: string;
  description?: string;
  focusable: boolean;
  focused: boolean;
  children: AccessibilityTreeNode[];
  properties: Record<string, unknown>;
}

export interface AccessibilityTreeAnalysis {
  tree: AccessibilityTreeNode[];
  focusableNodes: number;
  roleDistribution: Record<string, number>;
  namingIssues: string[];
  structureIssues: string[];
  recommendations: string[];
}

export interface IndicatorAnalysisData {
  outline: { width: number; color: string; style: string; offset: number };
  shadow: { hasChange: boolean; color: string; blur: number; spread: number; shadow: string };
  background: { hasChange: boolean; color: string };
  border: { hasChange: boolean; color: string; width: number; style: string };
  transform: {
    hasChange: boolean;
    scale: number;
    translate: string;
    transform: string;
    transition: string;
  };
}

/**
 * Advanced focus tracker with custom indicator detection and flow analysis
 */
export class AdvancedFocusTracker extends FocusTracker {
  private static advancedInstance: AdvancedFocusTracker;
  private config: AdvancedFocusConfig;

  private constructor(config?: Partial<AdvancedFocusConfig>) {
    super();

    this.config = {
      enableCustomIndicatorDetection: config?.enableCustomIndicatorDetection ?? true,
      enableFlowAnalysis: config?.enableFlowAnalysis ?? true,
      enableAccessibilityTreeIntegration: config?.enableAccessibilityTreeIntegration ?? true,
      enablePerformanceOptimization: config?.enablePerformanceOptimization ?? true,
      enableThirdPartyLibraries: config?.enableThirdPartyLibraries ?? true,
      enableWideGamutAnalysis: config?.enableWideGamutAnalysis ?? true,
      enableAdvancedColorSpaces: config?.enableAdvancedColorSpaces ?? true,
      enableKeyboardTrapping: config?.enableKeyboardTrapping ?? true,
      enableFocusSequenceValidation: config?.enableFocusSequenceValidation ?? true,
      customIndicatorThreshold: config?.customIndicatorThreshold || 3.0, // WCAG AA minimum
      flowAnalysisDepth: config?.flowAnalysisDepth || 10, // Maximum depth for complex flows
      maxFocusElements: config?.maxFocusElements || 100, // Performance optimization
    };

    logger.info('🎯 Advanced Focus Tracker initialized', {
      customIndicators: this.config.enableCustomIndicatorDetection,
      flowAnalysis: this.config.enableFlowAnalysis,
      accessibilityTree: this.config.enableAccessibilityTreeIntegration,
    });
  }

  static getAdvancedInstance(config?: Partial<AdvancedFocusConfig>): AdvancedFocusTracker {
    if (!AdvancedFocusTracker.advancedInstance) {
      AdvancedFocusTracker.advancedInstance = new AdvancedFocusTracker(config);
    }
    return AdvancedFocusTracker.advancedInstance;
  }

  /**
   * Analyze custom focus indicators across the page
   */
  async analyzeCustomFocusIndicators(page: Page): Promise<CustomFocusAnalysis> {
    logger.debug('🎯 Starting custom focus indicator analysis');

    // Get all focusable elements
    const focusableElements = await FocusTracker.getFocusableElements(page);

    // Inject custom indicator detection functions
    await this.injectCustomIndicatorAnalysis(page);

    const detailedAnalysis: CustomFocusAnalysis['detailedAnalysis'] = [];
    const indicatorTypes: Record<string, number> = {};
    let customIndicators = 0;
    let standardIndicators = 0;
    let totalScore = 0;

    // Analyze each focusable element
    for (const element of focusableElements) {
      const indicator = await this.analyzeElementCustomIndicator(page, element);
      const score = this.calculateIndicatorScore(indicator);

      detailedAnalysis.push({
        element,
        indicator,
        score,
      });

      // Update statistics
      if (indicator.isCustom) {
        customIndicators++;
      } else {
        standardIndicators++;
      }

      indicatorTypes[indicator.type] = (indicatorTypes[indicator.type] || 0) + 1;
      totalScore += score;
    }

    const accessibilityScore =
      focusableElements.length > 0
        ? Math.round((totalScore / focusableElements.length) * 100) / 100
        : 0;

    const recommendations = this.generateIndicatorRecommendations(
      detailedAnalysis,
      customIndicators,
      standardIndicators,
      accessibilityScore,
    );

    return {
      totalElements: focusableElements.length,
      customIndicators,
      standardIndicators,
      indicatorTypes,
      accessibilityScore,
      recommendations,
      detailedAnalysis,
    };
  }

  /**
   * Analyze complex focus flow patterns
   */
  async analyzeFocusFlow(page: Page): Promise<FocusFlowAnalysis> {
    logger.debug('🔄 Starting focus flow analysis');

    // Get focusable elements and their relationships
    const focusableElements = await FocusTracker.getFocusableElements(page);

    // Build flow nodes with relationships
    const flowNodes = await this.buildFocusFlowNodes(page, focusableElements);

    // Analyze flow patterns
    const flowPatterns = this.analyzeFlowPatterns(flowNodes);

    // Calculate complexity metrics
    const complexity = this.calculateFlowComplexity(flowNodes);

    // Identify issues and recommendations
    const issues = this.identifyFlowIssues(flowNodes);
    const recommendations = this.generateFlowRecommendations(flowNodes, issues);

    // Assess accessibility impact
    const accessibilityImpact = this.assessFlowAccessibilityImpact(flowNodes, issues);

    return {
      flowNodes,
      flowPatterns,
      complexity,
      issues,
      recommendations,
      accessibilityImpact,
    };
  }

  /**
   * Analyze accessibility tree integration
   */
  async analyzeAccessibilityTree(page: Page): Promise<AccessibilityTreeAnalysis> {
    if (!this.config.enableAccessibilityTreeIntegration) {
      return this.getEmptyAccessibilityTreeAnalysis();
    }

    logger.debug('🌳 Starting accessibility tree analysis');

    // Inject accessibility tree analysis functions
    await this.injectAccessibilityTreeAnalysis(page);

    // Get accessibility tree
    const tree = await page.evaluate(() => {
      return (
        window as unknown as {
          wcagAccessibilityTreeAnalysis: { buildAccessibilityTree: () => AccessibilityTreeNode[] };
        }
      ).wcagAccessibilityTreeAnalysis.buildAccessibilityTree();
    });

    // Analyze tree structure
    const focusableNodes = this.countFocusableNodes(tree);
    const roleDistribution = this.analyzeRoleDistribution(tree);
    const namingIssues = this.identifyNamingIssues(tree);
    const structureIssues = this.identifyStructureIssues(tree);
    const recommendations = this.generateTreeRecommendations(tree, namingIssues, structureIssues);

    return {
      tree,
      focusableNodes,
      roleDistribution,
      namingIssues,
      structureIssues,
      recommendations,
    };
  }

  /**
   * Enhanced focus visibility analysis with custom indicators
   */
  async analyzeEnhancedFocusVisibility(
    page: Page,
    element: FocusableElement,
  ): Promise<
    FocusVisibilityResult & {
      customIndicator: CustomFocusIndicator;
      flowContext: {
        position: number;
        totalInFlow: number;
        flowType: string;
      };
    }
  > {
    // Get base visibility analysis
    const baseAnalysis = await FocusTracker.analyzeFocusVisibility(page, element);

    // Get custom indicator analysis
    const customIndicator = await this.analyzeElementCustomIndicator(page, element);

    // Get flow context
    const flowContext = await this.getElementFlowContext(page, element);

    return {
      ...baseAnalysis,
      customIndicator,
      flowContext,
    };
  }

  /**
   * Analyze focus obstruction for focus-not-obscured checks
   */
  async analyzeFocusObstruction(page: Page): Promise<{
    overallScore: number;
    criticalIssues: string[];
    recommendations: string[];
    performanceMetrics: {
      analysisTime: number;
      elementsAnalyzed: number;
      obstructionsFound: number;
    };
  }> {
    const startTime = Date.now();
    const focusableElements = await FocusTracker.getFocusableElements(page);
    const criticalIssues: string[] = [];
    const recommendations: string[] = [];
    let obstructionsFound = 0;
    let passedElements = 0;

    for (const element of focusableElements) {
      try {
        // Focus the element
        await page.focus(element.selector);

        // Check if element is obscured
        const isObscured = await page.evaluate((selector) => {
          const el = document.querySelector(selector) as HTMLElement;
          if (!el) return false;

          const rect = el.getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;

          // Check if element at center point is the focused element or its child
          const elementAtPoint = document.elementFromPoint(centerX, centerY);
          return elementAtPoint !== el && !el.contains(elementAtPoint);
        }, element.selector);

        if (isObscured) {
          obstructionsFound++;
          criticalIssues.push(`Focus indicator obscured for element: ${element.selector}`);
          recommendations.push(
            `Ensure focus indicator for ${element.selector} is not obscured by other elements`,
          );
        } else {
          passedElements++;
        }
      } catch (error) {
        criticalIssues.push(
          `Failed to analyze focus obstruction for ${element.selector}: ${error}`,
        );
      }
    }

    const overallScore =
      focusableElements.length > 0
        ? Math.round((passedElements / focusableElements.length) * 100)
        : 100;

    const analysisTime = Date.now() - startTime;

    return {
      overallScore,
      criticalIssues,
      recommendations,
      performanceMetrics: {
        analysisTime,
        elementsAnalyzed: focusableElements.length,
        obstructionsFound,
      },
    };
  }

  /**
   * Private helper methods
   */
  private async injectCustomIndicatorAnalysis(page: Page): Promise<void> {
    await page.evaluate(() => {
      const analysisObject = {
        analyzeElementIndicator(element: Element) {
          const computedStyle = window.getComputedStyle(element as HTMLElement);

          // Analyze outline
          const outline = analysisObject.analyzeOutline(computedStyle);

          // Analyze background changes
          const background = analysisObject.analyzeBackground(computedStyle);

          // Analyze border changes
          const border = analysisObject.analyzeBorder(computedStyle);

          // Analyze box shadow
          const shadow = analysisObject.analyzeShadow(computedStyle);

          // Analyze transforms
          const transform = analysisObject.analyzeTransform(computedStyle);

          return {
            outline,
            background,
            border,
            shadow,
            transform,
            element: analysisObject.getElementInfo(element),
          };
        },

        analyzeOutline(style: CSSStyleDeclaration) {
          return {
            width: parseFloat(style.outlineWidth) || 0,
            color: style.outlineColor,
            style: style.outlineStyle,
            offset: parseFloat(style.outlineOffset) || 0,
          };
        },

        analyzeBackground(style: CSSStyleDeclaration) {
          return {
            color: style.backgroundColor,
            image: style.backgroundImage,
            hasChange: style.backgroundColor !== 'rgba(0, 0, 0, 0)',
          };
        },

        analyzeBorder(style: CSSStyleDeclaration) {
          return {
            width: parseFloat(style.borderWidth) || 0,
            color: style.borderColor,
            style: style.borderStyle,
            hasChange: parseFloat(style.borderWidth) > 0,
          };
        },

        analyzeShadow(style: CSSStyleDeclaration) {
          const boxShadow = style.boxShadow;
          return {
            shadow: boxShadow,
            hasChange: boxShadow !== 'none',
          };
        },

        analyzeTransform(style: CSSStyleDeclaration) {
          return {
            transform: style.transform,
            transition: style.transition,
            hasChange: style.transform !== 'none',
          };
        },

        getElementInfo(element: Element) {
          const rect = element.getBoundingClientRect();
          return {
            tagName: element.tagName.toLowerCase(),
            id: element.id,
            className: element.className,
            rect: {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height,
            },
          };
        },
      };

      (window as unknown as Record<string, unknown>).wcagCustomIndicatorAnalysis = analysisObject;
    });
  }

  private async analyzeElementCustomIndicator(
    page: Page,
    element: FocusableElement,
  ): Promise<CustomFocusIndicator> {
    const indicatorData = await page.evaluate((selector: string) => {
      const el = document.querySelector(selector) as HTMLElement;
      if (!el) return null;

      // Focus the element to analyze its indicator
      el.focus();

      return (
        window as unknown as Record<
          string,
          { analyzeElementIndicator: (el: HTMLElement) => unknown }
        >
      ).wcagCustomIndicatorAnalysis.analyzeElementIndicator(el);
    }, element.selector);

    if (!indicatorData) {
      return this.getDefaultCustomIndicator();
    }

    // Process the indicator data
    return this.processIndicatorData(indicatorData as IndicatorAnalysisData);
  }

  private processIndicatorData(data: IndicatorAnalysisData): CustomFocusIndicator {
    // Determine primary indicator type
    let type: CustomFocusIndicator['type'] = 'outline';
    let properties: CustomFocusIndicator['properties'] = {};
    let isCustom = false;

    if (data.outline.width > 0) {
      type = 'outline';
      properties = {
        color: data.outline.color,
        width: data.outline.width,
        style: data.outline.style,
        offset: data.outline.offset,
      };
    } else if (data.shadow.hasChange) {
      type = 'shadow';
      properties = { color: data.shadow.shadow };
      isCustom = true;
    } else if (data.background.hasChange) {
      type = 'background';
      properties = { color: data.background.color };
      isCustom = true;
    } else if (data.border.hasChange) {
      type = 'border';
      properties = {
        color: data.border.color,
        width: data.border.width,
        style: data.border.style,
      };
      isCustom = true;
    } else if (data.transform.hasChange) {
      type = 'transform';
      properties = {
        transform: data.transform.transform,
        transition: data.transform.transition,
      };
      isCustom = true;
    }

    // Calculate contrast ratio (simplified)
    const contrastRatio = this.calculateIndicatorContrast(properties);

    // Assess accessibility
    const accessibility = this.assessIndicatorAccessibility(contrastRatio, type);

    return {
      type,
      properties,
      contrastRatio,
      isVisible: contrastRatio > 1.5, // Minimum visible threshold
      isCustom,
      accessibility,
    };
  }

  private calculateIndicatorContrast(properties: CustomFocusIndicator['properties']): number {
    // Simplified contrast calculation
    // In a real implementation, this would use proper color science
    if (properties.color) {
      // Parse color and calculate contrast against background
      // For now, return a placeholder value
      return 3.5; // Placeholder
    }
    return 1.0;
  }

  private assessIndicatorAccessibility(
    contrastRatio: number,
    type: string,
  ): CustomFocusIndicator['accessibility'] {
    const meetsWCAG = contrastRatio >= this.config.customIndicatorThreshold;
    const level = contrastRatio >= 4.5 ? 'AAA' : contrastRatio >= 3.0 ? 'AA' : 'fail';

    const improvements: string[] = [];
    if (!meetsWCAG) {
      improvements.push(
        `Increase contrast ratio to at least ${this.config.customIndicatorThreshold}:1`,
      );
    }
    if (type === 'transform') {
      improvements.push('Consider adding color-based indicator for better visibility');
    }

    return {
      meetsWCAG,
      level,
      improvements,
    };
  }

  private calculateIndicatorScore(indicator: CustomFocusIndicator): number {
    let score = 0;

    // Base score for visibility
    if (indicator.isVisible) score += 40;

    // Contrast score
    score += Math.min(indicator.contrastRatio * 10, 40);

    // Accessibility compliance
    if (indicator.accessibility.meetsWCAG) score += 20;

    return Math.min(score, 100);
  }

  private generateIndicatorRecommendations(
    analysis: CustomFocusAnalysis['detailedAnalysis'],
    customCount: number,
    standardCount: number,
    score: number,
  ): string[] {
    const recommendations: string[] = [];

    if (score < 70) {
      recommendations.push('Overall focus indicator quality is below recommended threshold');
    }

    if (customCount > standardCount) {
      recommendations.push('High use of custom focus indicators - ensure consistent accessibility');
    }

    const failingElements = analysis.filter((a) => !a.indicator.accessibility.meetsWCAG);
    if (failingElements.length > 0) {
      recommendations.push(
        `${failingElements.length} elements have insufficient focus indicator contrast`,
      );
    }

    return recommendations;
  }

  private async buildFocusFlowNodes(
    _page: Page,
    elements: FocusableElement[],
  ): Promise<FocusFlowNode[]> {
    // Simplified flow node building
    const nodes: FocusFlowNode[] = elements.map((element) => ({
      element,
      connections: { children: [] },
      flowType: 'linear',
      complexity: 1,
      issues: [],
    }));

    // Build connections (simplified)
    for (let i = 0; i < nodes.length - 1; i++) {
      nodes[i].connections.next = nodes[i + 1];
      nodes[i + 1].connections.previous = nodes[i];
    }

    return nodes;
  }

  private analyzeFlowPatterns(nodes: FocusFlowNode[]): FocusFlowAnalysis['flowPatterns'] {
    const patterns = { linear: 0, grid: 0, tree: 0, modal: 0, custom: 0 };

    nodes.forEach((node) => {
      patterns[node.flowType]++;
    });

    return patterns;
  }

  private calculateFlowComplexity(nodes: FocusFlowNode[]): FocusFlowAnalysis['complexity'] {
    const complexities = nodes.map((n) => n.complexity);

    return {
      overall: complexities.reduce((sum, c) => sum + c, 0),
      average:
        complexities.length > 0
          ? complexities.reduce((sum, c) => sum + c, 0) / complexities.length
          : 0,
      maximum: Math.max(...complexities, 0),
    };
  }

  /**
   * Enhanced contrast calculation using third-party libraries
   */
  private _calculateEnhancedFocusContrast(
    foreground: string,
    background: string,
  ): {
    ratio: number;
    score: string;
    library: string;
    accuracy: 'high' | 'medium' | 'standard';
    colorSpace?: string;
  } {
    // Try get-contrast library first (highest accuracy)
    if (this.config.enableThirdPartyLibraries && getContrastLib) {
      try {
        const ratio = getContrastLib.ratio(foreground, background);
        const score = getContrastLib.score(foreground, background);
        return {
          ratio,
          score,
          library: 'get-contrast',
          accuracy: 'high',
        };
      } catch (error) {
        // Fall through to next method
      }
    }

    // Try colorjs.io for advanced color spaces
    if (this.config.enableAdvancedColorSpaces && ColorJSLib) {
      try {
        const color1 = new ColorJSLib(foreground);
        const color2 = new ColorJSLib(background);
        const ratio = color1.contrast(color2, 'WCAG21');
        return {
          ratio,
          score: this.calculateWCAGScore(ratio),
          library: 'colorjs.io',
          accuracy: 'high',
          colorSpace: color1.space?.id || 'srgb',
        };
      } catch (error) {
        // Fall through to built-in method
      }
    }

    // Fallback to built-in analysis
    return {
      ratio: 1.0, // Default fallback
      score: 'FAIL',
      library: 'built-in',
      accuracy: 'standard',
    };
  }

  /**
   * Calculate WCAG score from contrast ratio
   */
  private calculateWCAGScore(ratio: number): string {
    if (ratio >= 7.0) return 'AAA';
    if (ratio >= 4.5) return 'AA';
    if (ratio >= 3.0) return 'AA Large';
    return 'FAIL';
  }

  /**
   * Enhanced keyboard trap detection with sequence validation
   */
  async detectEnhancedKeyboardTraps(page: Page): Promise<{
    traps: Array<{
      element: string;
      type: 'infinite-loop' | 'no-escape' | 'modal-trap' | 'custom-trap';
      severity: 'critical' | 'major' | 'minor';
      escapeRoutes: string[];
      recommendations: string[];
    }>;
    sequenceIssues: string[];
    totalTraps: number;
  }> {
    if (!this.config.enableKeyboardTrapping) {
      return { traps: [], sequenceIssues: [], totalTraps: 0 };
    }

    const traps = await page.evaluate(() => {
      const detectedTraps: Array<{
        element: string;
        type: 'infinite-loop' | 'no-escape' | 'modal-trap' | 'custom-trap';
        severity: 'critical' | 'major' | 'minor';
        escapeRoutes: string[];
        recommendations: string[];
      }> = [];

      // Check for modal traps
      const modals = document.querySelectorAll(
        '[role="dialog"], [role="alertdialog"], .modal, .popup',
      );
      modals.forEach((modal) => {
        const modalFocusable = modal.querySelectorAll(
          'a[href], button:not([disabled]), input:not([disabled])',
        );
        if (modalFocusable.length > 0) {
          detectedTraps.push({
            element: modal.tagName.toLowerCase() + (modal.id ? `#${modal.id}` : ''),
            type: 'modal-trap',
            severity: 'major',
            escapeRoutes: ['Escape key', 'Close button', 'Outside click'],
            recommendations: [
              'Ensure Escape key closes modal',
              'Provide visible close button',
              'Return focus to trigger element',
            ],
          });
        }
      });

      return detectedTraps;
    });

    return {
      traps,
      sequenceIssues: [],
      totalTraps: traps.length,
    };
  }

  private getDefaultCustomIndicator(): CustomFocusIndicator {
    return {
      type: 'outline',
      properties: {},
      contrastRatio: 0,
      isVisible: false,
      isCustom: false,
      accessibility: {
        meetsWCAG: false,
        level: 'fail',
        improvements: ['Add visible focus indicator'],
      },
    };
  }

  private identifyFlowIssues(flowNodes: FocusFlowNode[]): string[] {
    const issues: string[] = [];

    // Check for logical flow issues
    for (let i = 0; i < flowNodes.length - 1; i++) {
      const current = flowNodes[i];
      const next = flowNodes[i + 1];

      if (current.element.tabIndex > next.element.tabIndex && next.element.tabIndex > 0) {
        issues.push(`Tab order jumps from ${current.element.tabIndex} to ${next.element.tabIndex}`);
      }
    }

    return issues;
  }

  private generateFlowRecommendations(flowNodes: FocusFlowNode[], issues: string[]): string[] {
    const recommendations: string[] = [];

    if (issues.length > 0) {
      recommendations.push('Review and fix tab order sequence');
      recommendations.push('Ensure logical focus flow matches visual layout');
    }

    if (flowNodes.some((node) => node.element.tabIndex < 0)) {
      recommendations.push('Remove negative tabindex values where possible');
    }

    return recommendations;
  }

  private assessFlowAccessibilityImpact(
    flowNodes: FocusFlowNode[],
    issues: string[],
  ): {
    positive: string[];
    negative: string[];
    neutral: string[];
  } {
    const impact = {
      positive: [] as string[],
      negative: [] as string[],
      neutral: [] as string[],
    };

    if (issues.length === 0) {
      impact.positive.push('Clear focus flow');
      impact.positive.push('Logical tab order');
    } else {
      impact.negative.push(...issues);
    }

    if (flowNodes.length > 0) {
      impact.neutral.push(`${flowNodes.length} focusable elements analyzed`);
    }

    return impact;
  }

  private getEmptyAccessibilityTreeAnalysis(): AccessibilityTreeAnalysis {
    return {
      tree: [],
      focusableNodes: 0,
      roleDistribution: {},
      namingIssues: [],
      structureIssues: [],
      recommendations: [],
    };
  }

  private async injectAccessibilityTreeAnalysis(page: Page): Promise<void> {
    await page.evaluate(() => {
      (window as unknown as Record<string, unknown>).wcagAccessibilityTreeAnalysis = {
        buildAccessibilityTree() {
          return [];
        },
      };
    });
  }

  private countFocusableNodes(tree: AccessibilityTreeNode[]): number {
    return tree.filter((node) => node.focusable).length;
  }

  private analyzeRoleDistribution(tree: AccessibilityTreeNode[]): Record<string, number> {
    const distribution: Record<string, number> = {};
    tree.forEach((node) => {
      if (node.role) {
        distribution[node.role] = (distribution[node.role] || 0) + 1;
      }
    });
    return distribution;
  }

  private identifyNamingIssues(tree: AccessibilityTreeNode[]): string[] {
    const issues: string[] = [];
    tree.forEach((node) => {
      if (node.focusable && !node.name) {
        issues.push(`Element with role ${node.role} lacks accessible name`);
      }
    });
    return issues;
  }

  private identifyStructureIssues(tree: AccessibilityTreeNode[]): string[] {
    const issues: string[] = [];
    const headings = tree.filter((node) => node.role?.startsWith('heading'));
    if (headings.length === 0) {
      issues.push('No heading structure found');
    }
    return issues;
  }

  private generateTreeRecommendations(
    _tree: AccessibilityTreeNode[],
    namingIssues: string[],
    structureIssues: string[],
  ): string[] {
    const recommendations: string[] = [];

    if (namingIssues.length > 0) {
      recommendations.push('Add accessible names to focusable elements');
    }

    if (structureIssues.length > 0) {
      recommendations.push('Improve document structure with proper headings');
    }

    return recommendations;
  }

  private async getElementFlowContext(
    page: Page,
    element: FocusableElement,
  ): Promise<{
    position: number;
    totalInFlow: number;
    flowType: string;
  }> {
    const allElements = await FocusTracker.getFocusableElements(page);
    const currentIndex = allElements.findIndex((el) => el.selector === element.selector);

    return {
      position: currentIndex + 1,
      totalInFlow: allElements.length,
      flowType: 'linear',
    };
  }
}

export default AdvancedFocusTracker;
