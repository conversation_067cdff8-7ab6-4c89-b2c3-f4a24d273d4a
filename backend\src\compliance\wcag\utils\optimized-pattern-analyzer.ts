/**
 * Optimized Pattern Analysis Utility
 * Reduces repeated content analysis, pattern validation, and other operations across checks
 * Implements shared caching and batch processing for improved performance
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import SmartCache from './smart-cache';
import { PageStructure } from './unified-dom-extractor';

export interface PatternAnalysisResult {
  contentQuality: {
    score: number;
    readabilityScore: number;
    structureScore: number;
    accessibilityScore: number;
    issues: string[];
    recommendations: string[];
  };
  patternValidation: {
    ariaPatterns: {
      valid: number;
      invalid: number;
      issues: string[];
    };
    semanticPatterns: {
      valid: number;
      invalid: number;
      issues: string[];
    };
    navigationPatterns: {
      valid: number;
      invalid: number;
      issues: string[];
    };
  };
  performanceMetrics: {
    analysisTime: number;
    elementsAnalyzed: number;
    cacheHits: number;
    cacheMisses: number;
  };
}

export interface OptimizedAnalysisConfig {
  enableContentQuality: boolean;
  enablePatternValidation: boolean;
  enableSemanticAnalysis: boolean;
  enablePerformanceTracking: boolean;
  cacheResults: boolean;
  batchSize: number;
}

/**
 * Optimized Pattern Analyzer - Singleton for shared analysis across all checks
 */
export class OptimizedPatternAnalyzer {
  private static instance: OptimizedPatternAnalyzer;
  private smartCache: SmartCache;
  private analysisCache: Map<string, PatternAnalysisResult> = new Map();
  private performanceMetrics = {
    totalAnalyses: 0,
    totalTime: 0,
    cacheHits: 0,
    cacheMisses: 0,
  };

  private constructor() {
    this.smartCache = SmartCache.getInstance();
  }

  static getInstance(): OptimizedPatternAnalyzer {
    if (!OptimizedPatternAnalyzer.instance) {
      OptimizedPatternAnalyzer.instance = new OptimizedPatternAnalyzer();
    }
    return OptimizedPatternAnalyzer.instance;
  }

  /**
   * Perform optimized pattern analysis using pre-extracted page structure
   */
  async analyzePatterns(
    pageStructure: PageStructure,
    config: OptimizedAnalysisConfig = this.getDefaultConfig()
  ): Promise<PatternAnalysisResult> {
    const cacheKey = this.generateAnalysisCacheKey(pageStructure.performance.cacheKey, config);
    
    // Check cache first
    if (config.cacheResults) {
      const cached = this.analysisCache.get(cacheKey) || 
                    await this.smartCache.get<PatternAnalysisResult>(cacheKey, 'pattern');
      
      if (cached) {
        this.performanceMetrics.cacheHits++;
        logger.debug('⚡ Using cached pattern analysis result');
        return cached;
      }
    }

    this.performanceMetrics.cacheMisses++;
    const startTime = Date.now();

    try {
      logger.debug('⚡ Starting optimized pattern analysis...');

      const result: PatternAnalysisResult = {
        contentQuality: config.enableContentQuality 
          ? await this.analyzeContentQuality(pageStructure)
          : this.getEmptyContentQuality(),
        
        patternValidation: config.enablePatternValidation
          ? await this.validatePatterns(pageStructure)
          : this.getEmptyPatternValidation(),
        
        performanceMetrics: {
          analysisTime: 0, // Will be set below
          elementsAnalyzed: this.countAnalyzedElements(pageStructure),
          cacheHits: this.performanceMetrics.cacheHits,
          cacheMisses: this.performanceMetrics.cacheMisses,
        },
      };

      const analysisTime = Date.now() - startTime;
      result.performanceMetrics.analysisTime = analysisTime;

      // Update performance metrics
      this.performanceMetrics.totalAnalyses++;
      this.performanceMetrics.totalTime += analysisTime;

      // Cache the result
      if (config.cacheResults) {
        this.analysisCache.set(cacheKey, result);
        await this.smartCache.set(cacheKey, result, 'pattern');
      }

      logger.debug(`⚡ Pattern analysis completed in ${analysisTime}ms (${result.performanceMetrics.elementsAnalyzed} elements)`);
      return result;

    } catch (error) {
      logger.error('❌ Pattern analysis failed:', error);
      throw new Error(`Pattern analysis failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Analyze content quality using pre-extracted elements
   */
  private async analyzeContentQuality(pageStructure: PageStructure): Promise<PatternAnalysisResult['contentQuality']> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Analyze headings structure
    const headings = pageStructure.elements.headings;
    let structureScore = 100;
    
    if (headings.length === 0) {
      issues.push('No headings found on the page');
      structureScore -= 30;
    } else {
      const h1Count = headings.filter(h => h.level === 1).length;
      if (h1Count === 0) {
        issues.push('No H1 heading found');
        structureScore -= 20;
      } else if (h1Count > 1) {
        issues.push('Multiple H1 headings found');
        structureScore -= 10;
      }
      
      // Check heading hierarchy
      for (let i = 1; i < headings.length; i++) {
        const current = headings[i];
        const previous = headings[i - 1];
        if (current.level > previous.level + 1) {
          issues.push(`Heading hierarchy skip detected: ${previous.tagName} to ${current.tagName}`);
          structureScore -= 5;
        }
      }
    }

    // Analyze images
    const images = pageStructure.elements.images;
    let accessibilityScore = 100;
    
    const imagesWithoutAlt = images.filter(img => !img.hasAltText && !img.isDecorative);
    if (imagesWithoutAlt.length > 0) {
      issues.push(`${imagesWithoutAlt.length} images missing alt text`);
      accessibilityScore -= Math.min(30, imagesWithoutAlt.length * 5);
      recommendations.push('Add descriptive alt text to all informative images');
    }

    // Analyze links
    const links = pageStructure.elements.links;
    const linksWithoutText = links.filter(link => !link.hasText);
    if (linksWithoutText.length > 0) {
      issues.push(`${linksWithoutText.length} links without accessible text`);
      accessibilityScore -= Math.min(20, linksWithoutText.length * 3);
      recommendations.push('Ensure all links have descriptive text');
    }

    // Analyze forms
    const forms = pageStructure.elements.forms;
    forms.forEach(form => {
      const fieldsWithoutLabels = form.fields.filter(field => !field.hasLabel);
      if (fieldsWithoutLabels.length > 0) {
        issues.push(`Form has ${fieldsWithoutLabels.length} fields without labels`);
        accessibilityScore -= Math.min(15, fieldsWithoutLabels.length * 2);
        recommendations.push('Associate labels with all form fields');
      }
    });

    // Calculate readability score based on content structure
    const readabilityScore = Math.max(0, Math.min(100, 
      (headings.length > 0 ? 30 : 0) +
      (pageStructure.metadata.title ? 20 : 0) +
      (pageStructure.metadata.description ? 20 : 0) +
      (pageStructure.accessibility.landmarks.length > 0 ? 30 : 0)
    ));

    const overallScore = Math.round((structureScore + accessibilityScore + readabilityScore) / 3);

    return {
      score: overallScore,
      readabilityScore,
      structureScore,
      accessibilityScore,
      issues,
      recommendations,
    };
  }

  /**
   * Validate accessibility patterns using pre-extracted elements
   */
  private async validatePatterns(pageStructure: PageStructure): Promise<PatternAnalysisResult['patternValidation']> {
    const ariaIssues: string[] = [];
    const semanticIssues: string[] = [];
    const navigationIssues: string[] = [];

    // Validate ARIA patterns
    let validAria = 0;
    let invalidAria = 0;

    pageStructure.accessibility.ariaElements.forEach(element => {
      if (element.hasValidAria) {
        validAria++;
      } else {
        invalidAria++;
        ariaIssues.push(`Invalid ARIA attributes on ${element.selector}`);
      }
    });

    // Validate semantic patterns
    let validSemantic = 0;
    let invalidSemantic = 0;

    const requiredLandmarks = ['main', 'nav', 'header', 'footer'];
    const foundLandmarks = pageStructure.accessibility.landmarks.map(l => l.role);
    
    requiredLandmarks.forEach(required => {
      if (foundLandmarks.includes(required)) {
        validSemantic++;
      } else {
        invalidSemantic++;
        semanticIssues.push(`Missing ${required} landmark`);
      }
    });

    // Validate navigation patterns
    let validNavigation = 0;
    let invalidNavigation = 0;

    const skipLinks = pageStructure.elements.links.filter(link => 
      link.href.startsWith('#') && link.text.toLowerCase().includes('skip')
    );
    
    if (skipLinks.length > 0) {
      validNavigation++;
    } else {
      invalidNavigation++;
      navigationIssues.push('No skip links found');
    }

    // Check keyboard navigation
    const focusableElements = pageStructure.accessibility.focusableElements;
    const keyboardAccessible = focusableElements.filter(el => el.isKeyboardAccessible);
    
    if (keyboardAccessible.length === focusableElements.length) {
      validNavigation++;
    } else {
      invalidNavigation++;
      navigationIssues.push(`${focusableElements.length - keyboardAccessible.length} elements not keyboard accessible`);
    }

    return {
      ariaPatterns: { valid: validAria, invalid: invalidAria, issues: ariaIssues },
      semanticPatterns: { valid: validSemantic, invalid: invalidSemantic, issues: semanticIssues },
      navigationPatterns: { valid: validNavigation, invalid: invalidNavigation, issues: navigationIssues },
    };
  }

  /**
   * Generate cache key for analysis results
   */
  private generateAnalysisCacheKey(pageKey: string, config: OptimizedAnalysisConfig): string {
    const configHash = JSON.stringify(config);
    return `pattern-analysis:${pageKey}:${Buffer.from(configHash).toString('base64').substring(0, 16)}`;
  }

  /**
   * Count total elements analyzed
   */
  private countAnalyzedElements(pageStructure: PageStructure): number {
    return Object.values(pageStructure.performance.elementCounts).reduce((sum, count) => sum + count, 0);
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): OptimizedAnalysisConfig {
    return {
      enableContentQuality: true,
      enablePatternValidation: true,
      enableSemanticAnalysis: true,
      enablePerformanceTracking: true,
      cacheResults: true,
      batchSize: 50,
    };
  }

  /**
   * Get empty content quality result
   */
  private getEmptyContentQuality(): PatternAnalysisResult['contentQuality'] {
    return {
      score: 0,
      readabilityScore: 0,
      structureScore: 0,
      accessibilityScore: 0,
      issues: [],
      recommendations: [],
    };
  }

  /**
   * Get empty pattern validation result
   */
  private getEmptyPatternValidation(): PatternAnalysisResult['patternValidation'] {
    return {
      ariaPatterns: { valid: 0, invalid: 0, issues: [] },
      semanticPatterns: { valid: 0, invalid: 0, issues: [] },
      navigationPatterns: { valid: 0, invalid: 0, issues: [] },
    };
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats() {
    return {
      ...this.performanceMetrics,
      averageAnalysisTime: this.performanceMetrics.totalAnalyses > 0 
        ? this.performanceMetrics.totalTime / this.performanceMetrics.totalAnalyses 
        : 0,
      cacheHitRate: this.performanceMetrics.totalAnalyses > 0
        ? (this.performanceMetrics.cacheHits / this.performanceMetrics.totalAnalyses) * 100
        : 0,
    };
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
    logger.debug('⚡ Pattern analysis cache cleared');
  }
}

export default OptimizedPatternAnalyzer;
