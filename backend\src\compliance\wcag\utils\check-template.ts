/**
 * WCAG Check Template System - Fixed Version
 * Provides consistent structure for all WCAG checks
 */

import { Page } from 'puppeteer';
import {
  WcagCheckResult,
  WcagEvidence,
  WcagCategory,
  WcagVersion,
  WcagLevel,
} from '../types';
import {
  SCORING_CONFIG,
  ScoringConfig,
  PENALTY_TIERS,
  CATEGORY_THRESHOLDS,
  LEVEL_THRESHOLDS,
  CONFIDENCE_ADJUSTMENTS,
  EnhancedScoringResult,
  ScoringStatus,
} from '../constants';
import { UnifiedDOMExtractor, PageStructure } from './unified-dom-extractor';
import logger from '../../../utils/logger';

// Define WcagCheckTemplateResult interface first to avoid forward reference issues
export interface WcagCheckTemplateResult {
  score: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
  confidence?: number; // ✅ Detection confidence (0.0 to 1.0) for scoring adjustments
}

export interface CheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
  page?: Page;
  pageStructure?: PageStructure; // ✅ Unified DOM structure for all checks
}

export interface EnhancedCheckConfig extends CheckConfig {
  retryAttempts: number;
  enableJavaScript: boolean;
  enableImages: boolean;
  followRedirects: boolean;
}

export type CheckFunction<T extends CheckConfig> = (
  page: Page,
  config: T,
) => Promise<WcagCheckTemplateResult>;

export class CheckTemplate {
  private domExtractor: UnifiedDOMExtractor;

  constructor() {
    this.domExtractor = UnifiedDOMExtractor.getInstance();
  }

  /**
   * ✅ ENHANCED WCAG COMPLIANCE CALCULATION: Graduated Penalty System
   * Implements partial credit, confidence weighting, and category-specific thresholds
   */
  private calculateWcagCompliance(
    result: WcagCheckTemplateResult,
    category?: WcagCategory,
    level?: WcagLevel,
    confidence?: number,
    config: ScoringConfig = {
      passThreshold: SCORING_CONFIG.DEFAULT_PASS_THRESHOLD,
      strictMode: SCORING_CONFIG.STRICT_MODE,
      enableGradualScoring: SCORING_CONFIG.ENABLE_GRADUAL_SCORING,
      enableThresholdLogging: SCORING_CONFIG.ENABLE_THRESHOLD_LOGGING,
      enableGraduatedPenalties: SCORING_CONFIG.ENABLE_GRADUATED_PENALTIES,
      enableConfidenceWeighting: SCORING_CONFIG.ENABLE_CONFIDENCE_WEIGHTING,
      enableCategoryThresholds: SCORING_CONFIG.ENABLE_CATEGORY_THRESHOLDS,
    },
  ): EnhancedScoringResult {
    const originalScore = result.score;
    const maxScore = result.maxScore;
    const scorePercentage = maxScore > 0 ? (originalScore / maxScore) * 100 : 0;

    // ✅ STEP 1: Determine threshold (category/level specific or default)
    let threshold = config.passThreshold;

    if (config.enableCategoryThresholds && category) {
      threshold = CATEGORY_THRESHOLDS[category] || threshold;
    }

    if (config.enableCategoryThresholds && level) {
      const levelThreshold = LEVEL_THRESHOLDS[level];
      if (levelThreshold) {
        threshold = Math.max(threshold, levelThreshold); // Use higher threshold
      }
    }

    // ✅ STEP 2: Apply confidence weighting if enabled
    let adjustedScore = originalScore;
    let confidenceAdjustment = 1.0;

    if (config.enableConfidenceWeighting && confidence !== undefined) {
      const adjustment = CONFIDENCE_ADJUSTMENTS.find(adj => confidence >= adj.min);
      if (adjustment) {
        confidenceAdjustment = adjustment.multiplier;
        adjustedScore = Math.round(originalScore * confidenceAdjustment);
      }
    }

    const adjustedPercentage = maxScore > 0 ? (adjustedScore / maxScore) * 100 : 0;

    // ✅ STEP 3: Apply graduated penalty system or legacy scoring
    let finalScore: number;
    let status: ScoringStatus;
    let penaltyTier: typeof PENALTY_TIERS[number] | undefined;

    if (config.strictMode) {
      // Legacy strict mode: binary scoring
      const isPassed = adjustedPercentage >= threshold;
      finalScore = isPassed ? adjustedScore : 0;
      status = isPassed ? 'passed' : 'failed';
    } else if (config.enableGraduatedPenalties) {
      // ✅ NEW: Graduated penalty system with partial credit
      penaltyTier = PENALTY_TIERS.find(tier => adjustedPercentage >= tier.min);

      if (penaltyTier) {
        finalScore = Math.round(adjustedScore * penaltyTier.multiplier);
        status = penaltyTier.status;
      } else {
        // Fallback (should not happen)
        finalScore = 0;
        status = 'failed';
      }
    } else {
      // Legacy mode: binary scoring with threshold
      const isPassed = adjustedPercentage >= threshold;
      if (config.enableGradualScoring && isPassed) {
        finalScore = adjustedScore;
        status = 'passed';
      } else {
        finalScore = isPassed ? maxScore : 0;
        status = isPassed ? 'passed' : 'failed';
      }
    }

    // ✅ STEP 4: Generate detailed explanation
    const details = this.generateScoringDetails(
      scorePercentage,
      adjustedPercentage,
      threshold,
      status,
      penaltyTier,
      confidenceAdjustment,
      confidence
    );

    return {
      status,
      score: finalScore,
      originalScore,
      adjustedScore,
      confidence,
      threshold,
      penaltyTier,
      confidenceAdjustment,
      details
    };
  }

  /**
   * ✅ GENERATE DETAILED SCORING EXPLANATION
   */
  private generateScoringDetails(
    originalPercentage: number,
    adjustedPercentage: number,
    threshold: number,
    status: ScoringStatus,
    penaltyTier?: typeof PENALTY_TIERS[number],
    confidenceAdjustment?: number,
    confidence?: number
  ): string {
    let details = `${originalPercentage.toFixed(1)}%`;

    // Add confidence adjustment info
    if (confidenceAdjustment && confidenceAdjustment !== 1.0 && confidence !== undefined) {
      details += ` → ${adjustedPercentage.toFixed(1)}% (confidence: ${(confidence * 100).toFixed(0)}%)`;
    }

    details += ` (threshold: ${threshold}%)`;

    // Add penalty tier info
    if (penaltyTier && penaltyTier.multiplier !== 1.0) {
      details += ` → ${(penaltyTier.multiplier * 100).toFixed(0)}% credit`;
    }

    details += ` - ${status.toUpperCase()}`;

    return details;
  }

  /**
   * Execute a WCAG check with consistent error handling and logging
   */
  async executeCheck<T extends CheckConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    config: T,
    checkFunction: CheckFunction<T>,
    requiresBrowser: boolean = true,
    requiresManualReview: boolean = false,
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();

    try {
      logger.info(`🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName}`);

      if (requiresManualReview) {
        throw new Error('Manual review checks should not use fully automated template');
      }

      try {
        if (requiresBrowser && !config.page) {
          throw new Error('Browser instance required - page not provided in config');
        }

        // ✅ UNIFIED DOM EXTRACTION: Extract page structure once for all checks
        if (requiresBrowser && config.page && !config.pageStructure) {
          // Validate page state before attempting extraction
          if (config.page.isClosed()) {
            logger.warn(`⚠️ [${config.scanId}] Page closed before DOM extraction for ${ruleId}, skipping structure extraction`);
          } else {
            logger.debug(`📋 [${config.scanId}] Extracting unified page structure for ${ruleId}`);
            try {
              config.pageStructure = await this.domExtractor.extractPageStructure(config.page, config.targetUrl);
              const elementCount = Object.keys(config.pageStructure.performance.elementCounts).reduce(
                (sum, key) => sum + config.pageStructure!.performance.elementCounts[key],
                0
              );
              logger.debug(`📋 [${config.scanId}] Page structure extracted: ${elementCount} elements`);
            } catch (extractionError) {
              logger.warn(`⚠️ [${config.scanId}] DOM extraction failed for ${ruleId}, proceeding without structure:`, {
                error: extractionError instanceof Error ? extractionError.message : String(extractionError)
              });
              // Continue without page structure - checks should handle gracefully
            }
          }
        }

        // Execute the specific check function with enhanced config
        const result = await checkFunction(config.page!, config);

        const executionTime = Date.now() - startTime;

        // ✅ ENHANCED WCAG Compliance: Graduated Penalty Scoring System
        // Apply enhanced scoring with partial credit, confidence weighting, and category thresholds
        const scoringResult = this.calculateWcagCompliance(
          result,
          category as WcagCategory,
          level as WcagLevel,
          result.confidence // Pass confidence if available in result
        );

        const { status, score: finalScore, details, originalScore, adjustedScore, threshold, penaltyTier } = scoringResult;

        // Enhanced logging with detailed scoring information
        if (status === 'failed') {
          logger.warn(
            `⚠️ [${config.scanId}] ${ruleId} failed: ${details}`,
          );
        } else if (status === 'partial') {
          logger.info(
            `🔶 [${config.scanId}] ${ruleId} partial: ${details}`,
          );
        } else {
          logger.info(
            `✅ [${config.scanId}] ${ruleId} passed: ${details}`,
          );
        }

        logger.info(
          `🎯 [${config.scanId}] Completed ${ruleId} in ${executionTime}ms - Status: ${status} (${finalScore}/${result.maxScore})`,
        );

        return {
          ruleId,
          ruleName,
          category: category as WcagCategory,
          wcagVersion: this.getVersionFromRuleId(ruleId),
          successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
          level: level as WcagLevel,
          status: status as 'passed' | 'failed', // Convert to legacy status for compatibility
          score: finalScore, // ✅ Enhanced score with graduated penalties
          maxScore: result.maxScore,
          weight,
          automated: true,
          evidence: result.evidence,
          recommendations: result.recommendations,
          executionTime,
          // ✅ Enhanced scoring metadata for analysis and debugging
          originalScore: originalScore, // Original score before any adjustments
          adjustedScore: adjustedScore, // Score after confidence weighting
          thresholdApplied: threshold, // Actual threshold used (may be category-specific)
          scoringDetails: details, // Full scoring explanation
          penaltyTier: penaltyTier?.multiplier, // Penalty multiplier applied
          confidenceAdjustment: scoringResult.confidenceAdjustment, // Confidence adjustment factor
          enhancedStatus: status, // Full status including 'partial'
        };
      } finally {
        // Browser cleanup will be handled by orchestrator
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`❌ [${config.scanId}] Error in ${ruleId}`, {
        error: {
          message: errorMessage,
          stack: errorStack,
          name: error instanceof Error ? error.name : 'UnknownError',
        },
        ruleId,
        ruleName,
        executionTime,
      });

      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [
          {
            type: 'text',
            description: 'Technical error during check execution',
            value: errorMessage,
            severity: 'error',
          },
        ],
        recommendations: [
          'Check failed due to technical error - manual review recommended',
          `Error details: ${errorMessage}`,
          'Check browser console and server logs for more information',
        ],
        executionTime,
        errorMessage,
      };
    }
  }

  /**
   * Get WCAG version from rule ID
   */
  protected getVersionFromRuleId(ruleId: string): WcagVersion {
    const ruleNumber = parseInt(ruleId.split('-')[1]);

    if (ruleNumber <= 9) return '2.1';
    if (ruleNumber <= 16) return '2.2';
    return '3.0';
  }

  /**
   * Get success criterion from rule ID
   */
  protected getSuccessCriterionFromRuleId(ruleId: string): string {
    const criterionMap: Record<string, string> = {
      'WCAG-001': '1.1.1',
      'WCAG-002': '1.2.2',
      'WCAG-003': '1.3.1',
      'WCAG-004': '1.4.3',
      'WCAG-005': '2.1.1',
      'WCAG-006': '2.4.3',
      'WCAG-007': '2.4.7',
      'WCAG-008': '3.3.1',
      'WCAG-009': '4.1.2',
      'WCAG-010': '2.4.11',
      'WCAG-011': '2.4.12',
      'WCAG-012': '2.4.13',
      'WCAG-013': '2.5.7',
      'WCAG-014': '2.5.8',
      'WCAG-015': '3.2.6',
      'WCAG-016': '3.3.7',
      'WCAG-017': '2.1',
      'WCAG-018': '2.2',
      'WCAG-019': '2.4',
      'WCAG-020': '2.5',
      'WCAG-021': '3.1',
    };

    return criterionMap[ruleId] || 'Unknown';
  }
}
