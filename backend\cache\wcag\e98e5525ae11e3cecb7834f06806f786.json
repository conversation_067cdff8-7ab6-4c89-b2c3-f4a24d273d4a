{"data": [{"type": "text", "description": "Error identification analysis summary", "value": "0/0 checks pass automated tests, 0 require manual review", "severity": "info", "elementCount": 1, "affectedSelectors": ["checks", "pass", "automated", "tests", "require", "manual", "review"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.9, "checkType": "form-validation-analysis", "manualReviewRequired": false, "formAnalysis": true, "errorMessageValidation": true, "evidenceIndex": 0, "ruleId": "WCAG-008", "ruleName": "Error Identification", "timestamp": "2025-07-13T03:28:17.028Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "No testable forms found on page", "value": "No forms with required fields and submit buttons detected", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "forms", "with", "required", "fields", "and", "submit", "buttons", "detected"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 2, "checkSpecificData": {"automationRate": 0.9, "checkType": "form-validation-analysis", "manualReviewRequired": false, "formAnalysis": true, "errorMessageValidation": true, "evidenceIndex": 1, "ruleId": "WCAG-008", "ruleName": "Error Identification", "timestamp": "2025-07-13T03:28:17.028Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752377297028, "hash": "160b3f090c7f4c28581b72e2a3973167", "accessCount": 1, "lastAccessed": 1752377297028, "size": 1534, "metadata": {"originalKey": "WCAG-008:WCAG-008", "normalizedKey": "wcag-008_wcag-008", "savedAt": 1752377297028, "version": "1.1", "keyHash": "5c720996384c96f4e5bf139c6fb24381"}}