{"data": [{"type": "text", "description": "Interactive element 1 has consistent label and accessible name", "value": "a:nth-of-type(1) - visible: \"Skip to content\", accessible: \"Skip to content\"", "selector": "a:nth-of-type(1)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(1)", "a", "nth-of-type", "visible", "<PERSON><PERSON>", "to", "content", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 2 has consistent label and accessible name", "value": "a:nth-of-type(2) - visible: \"Contact Sales: (800) 572-0470\", accessible: \"Contact Sales: (800) 572-0470\"", "selector": "a:nth-of-type(2)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "a", "nth-of-type", "visible", "Contact", "Sales", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 1, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 3 has consistent label and accessible name", "value": "a:nth-of-type(3) - visible: \"Contact Support\", accessible: \"Contact Support\"", "selector": "a:nth-of-type(3)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "a", "nth-of-type", "visible", "Contact", "Support", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 2, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 4 has consistent label and accessible name", "value": "a:nth-of-type(4) - visible: \"LoginExpand\", accessible: \"LoginExpand\"", "selector": "a:nth-of-type(4)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(4)", "a", "nth-of-type", "visible", "LoginExpand", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 3, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 6 has consistent label and accessible name", "value": "a:nth-of-type(6) - visible: \"TigerConnect\", accessible: \"TigerConnect\"", "selector": "a:nth-of-type(6)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "a", "nth-of-type", "visible", "TigerConnect", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 4, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 7 has consistent label and accessible name", "value": "a:nth-of-type(7) - visible: \"Physician Scheduling\", accessible: \"Physician Scheduling\"", "selector": "a:nth-of-type(7)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(7)", "a", "nth-of-type", "visible", "Physician", "Scheduling", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 5, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 8 has consistent label and accessible name", "value": "a:nth-of-type(8) - visible: \"TigerConnect Community\", accessible: \"TigerConnect Community\"", "selector": "a:nth-of-type(8)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(8)", "a", "nth-of-type", "visible", "TigerConnect", "Community", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 6, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 10 has consistent label and accessible name", "value": "button:nth-of-type(10) - visible: \"Search Button\", accessible: \"Search Button\"", "selector": "button:nth-of-type(10)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(10)", "button", "nth-of-type", "visible", "Search", "<PERSON><PERSON>", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 7, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 12 has consistent label and accessible name", "value": "a:nth-of-type(12) - visible: \"Home\", accessible: \"Home\"", "selector": "a:nth-of-type(12)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(12)", "a", "nth-of-type", "visible", "Home", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 8, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 13 has consistent label and accessible name", "value": "a:nth-of-type(13) - visible: \"Who We ServeExpand\", accessible: \"Who We ServeExpand\"", "selector": "a:nth-of-type(13)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(13)", "a", "nth-of-type", "visible", "Who", "We", "ServeExpand", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 9, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 16 has consistent label and accessible name", "value": "a:nth-of-type(16) - visible: \"Services\", accessible: \"Services\"", "selector": "a:nth-of-type(16)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(16)", "a", "nth-of-type", "visible", "Services", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 10, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 17 has consistent label and accessible name", "value": "a:nth-of-type(17) - visible: \"Professional Services\", accessible: \"Professional Services\"", "selector": "a:nth-of-type(17)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(17)", "a", "nth-of-type", "visible", "Professional", "Services", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 11, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 18 has consistent label and accessible name", "value": "a:nth-of-type(18) - visible: \"Support Services\", accessible: \"Support Services\"", "selector": "a:nth-of-type(18)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(18)", "a", "nth-of-type", "visible", "Support", "Services", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 12, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 19 has consistent label and accessible name", "value": "a:nth-of-type(19) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(19)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(19)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 13, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 20 has consistent label and accessible name", "value": "a:nth-of-type(20) - visible: \"Healthcare Professionals\", accessible: \"Healthcare Professionals\"", "selector": "a:nth-of-type(20)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(20)", "a", "nth-of-type", "visible", "Healthcare", "Professionals", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 14, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 21 has consistent label and accessible name", "value": "a:nth-of-type(21) - visible: \"Physicians\", accessible: \"Physicians\"", "selector": "a:nth-of-type(21)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(21)", "a", "nth-of-type", "visible", "Physicians", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 15, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 22 has consistent label and accessible name", "value": "a:nth-of-type(22) - visible: \"Nurses\", accessible: \"Nurses\"", "selector": "a:nth-of-type(22)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(22)", "a", "nth-of-type", "visible", "Nurses", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 16, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 23 has consistent label and accessible name", "value": "a:nth-of-type(23) - visible: \"Executives\", accessible: \"Executives\"", "selector": "a:nth-of-type(23)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(23)", "a", "nth-of-type", "visible", "Executives", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 17, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 24 has consistent label and accessible name", "value": "a:nth-of-type(24) - visible: \"IT\", accessible: \"IT\"", "selector": "a:nth-of-type(24)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(24)", "a", "nth-of-type", "visible", "IT", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 18, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 25 has consistent label and accessible name", "value": "a:nth-of-type(25) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(25)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(25)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 19, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 26 has consistent label and accessible name", "value": "a:nth-of-type(26) - visible: \"Healthcare Organizations\", accessible: \"Healthcare Organizations\"", "selector": "a:nth-of-type(26)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(26)", "a", "nth-of-type", "visible", "Healthcare", "Organizations", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 20, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 27 has consistent label and accessible name", "value": "a:nth-of-type(27) - visible: \"Ambulatory Surgery Centers\", accessible: \"Ambulatory Surgery Centers\"", "selector": "a:nth-of-type(27)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(27)", "a", "nth-of-type", "visible", "Ambulatory", "Surgery", "Centers", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 21, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 28 has consistent label and accessible name", "value": "a:nth-of-type(28) - visible: \"Behavioral Health\", accessible: \"Behavioral Health\"", "selector": "a:nth-of-type(28)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(28)", "a", "nth-of-type", "visible", "Behavioral", "Health", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 22, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 29 has consistent label and accessible name", "value": "a:nth-of-type(29) - visible: \"Health Systems\", accessible: \"Health Systems\"", "selector": "a:nth-of-type(29)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(29)", "a", "nth-of-type", "visible", "Health", "Systems", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 23, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 30 has consistent label and accessible name", "value": "a:nth-of-type(30) - visible: \"Home Health & Hospice\", accessible: \"Home Health & Hospice\"", "selector": "a:nth-of-type(30)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(30)", "a", "nth-of-type", "visible", "Home", "Health", "Hospice", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 24, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 31 has consistent label and accessible name", "value": "a:nth-of-type(31) - visible: \"Hospitals\", accessible: \"Hospitals\"", "selector": "a:nth-of-type(31)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(31)", "a", "nth-of-type", "visible", "Hospitals", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 25, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 32 has consistent label and accessible name", "value": "a:nth-of-type(32) - visible: \"Physician Groups\", accessible: \"Physician Groups\"", "selector": "a:nth-of-type(32)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(32)", "a", "nth-of-type", "visible", "Physician", "Groups", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 26, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 33 has consistent label and accessible name", "value": "a:nth-of-type(33) - visible: \"Skilled Nursing Facilities\", accessible: \"Skilled Nursing Facilities\"", "selector": "a:nth-of-type(33)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(33)", "a", "nth-of-type", "visible", "Skilled", "Nursing", "Facilities", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 27, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 34 has consistent label and accessible name", "value": "a:nth-of-type(34) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(34)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(34)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 28, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 35 has consistent label and accessible name", "value": "a:nth-of-type(35) - visible: \"SolutionsExpand\", accessible: \"SolutionsExpand\"", "selector": "a:nth-of-type(35)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(35)", "a", "nth-of-type", "visible", "SolutionsExpand", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 29, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.656Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 38 has consistent label and accessible name", "value": "a:nth-of-type(38) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(38)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(38)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 30, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 39 has consistent label and accessible name", "value": "a:nth-of-type(39) - visible: \"Solutions\", accessible: \"Solutions\"", "selector": "a:nth-of-type(39)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(39)", "a", "nth-of-type", "visible", "Solutions", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 31, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 40 has consistent label and accessible name", "value": "a:nth-of-type(40) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(40)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(40)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 32, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 41 has consistent label and accessible name", "value": "a:nth-of-type(41) - visible: \"Physician Scheduling\", accessible: \"Physician Scheduling\"", "selector": "a:nth-of-type(41)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(41)", "a", "nth-of-type", "visible", "Physician", "Scheduling", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 33, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 42 has consistent label and accessible name", "value": "a:nth-of-type(42) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(42)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(42)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 34, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 43 has consistent label and accessible name", "value": "a:nth-of-type(43) - visible: \"Alarm Management & Event Notification\", accessible: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(43)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(43)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 35, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 44 has consistent label and accessible name", "value": "a:nth-of-type(44) - visible: \"Patient Engagement\", accessible: \"Patient Engagement\"", "selector": "a:nth-of-type(44)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(44)", "a", "nth-of-type", "visible", "Patient", "Engagement", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 36, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 45 has consistent label and accessible name", "value": "a:nth-of-type(45) - visible: \"Care<PERSON>ond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(45)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(45)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 37, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 46 has consistent label and accessible name", "value": "a:nth-of-type(46) - visible: \"Integrations\", accessible: \"Integrations\"", "selector": "a:nth-of-type(46)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(46)", "a", "nth-of-type", "visible", "Integrations", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 38, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Interactive element 47 has consistent label and accessible name", "value": "a:nth-of-type(47) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(47)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(47)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 532, "elementsAnalyzed": 339, "checkSpecificData": {"automationRate": 0.9, "checkType": "label-name-consistency-analysis", "accessibleNameValidation": true, "visualLabelComparison": true, "aiSemanticValidation": true, "formAccessibilityAnalysis": true, "evidenceIndex": 39, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.657Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752377358657, "hash": "a3a330b83c1604547582c01ecd587526", "accessCount": 1, "lastAccessed": 1752377358657, "size": 36382, "metadata": {"originalKey": "WCAG-055:WCAG-055", "normalizedKey": "wcag-055_wcag-055", "savedAt": 1752377358658, "version": "1.1", "keyHash": "adc28f0e8427c42a9f5bb8b3ca4aa0e1"}}