'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Shield, Lock, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { HipaaSecurityResultsPage } from '@/components/hipaa-security';
import { HipaaSecurityScanResult } from '@/types/hipaa-security';
import { ComplianceBreadcrumb, createComplianceBreadcrumbs } from '@/components/navigation';

/**
 * HIPAA Security Module Scan Results Page
 * Enhanced version of the security results with improved navigation and integration
 */
export default function HipaaSecurityScanResultPage() {
  const params = useParams();
  const router = useRouter();
  const scanId = params.scanId as string;

  const [scanResult, setScanResult] = useState<HipaaSecurityScanResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!scanId) {
      setError('Invalid scan ID');
      setLoading(false);
      return;
    }

    // Fetch scan result from API
    fetchScanResult(scanId);
  }, [scanId]);

  const fetchScanResult = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      // Fetch from the backend API
      const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3001/api/v1';
      const response = await fetch(`${apiUrl}/hipaa-security/scan/${id}/result`);

      if (!response.ok) {
        throw new Error(`Failed to fetch scan result: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setScanResult(data.data);
      } else {
        throw new Error(data.error || 'Failed to load scan result');
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Error fetching scan result:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    // eslint-disable-next-line no-console
    console.log('Exporting report for scan:', scanId);
  };

  const handleStartNewScan = () => {
    router.push('/hipaa-security-live');
  };

  const handleRecalculateScoring = () => {
    // TODO: Implement recalculate scoring functionality
    // eslint-disable-next-line no-console
    console.log('Recalculating scoring for scan:', scanId);
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard/hipaa');
  };

  if (loading) {
    return (
      <div
        className="container mx-auto p-6 space-y-6"
        style={{ backgroundColor: '#F5F5F5', minHeight: '100vh' }}
      >
        {/* Header with loading state */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBackToDashboard}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to HIPAA Dashboard
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">HIPAA Security Scan Results</h1>
              <p className="text-gray-600">Loading scan details...</p>
            </div>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="space-y-6">
          <Card className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="container mx-auto p-6 space-y-6"
        style={{ backgroundColor: '#F5F5F5', minHeight: '100vh' }}
      >
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBackToDashboard}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to HIPAA Dashboard
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">HIPAA Security Scan Results</h1>
              <p className="text-gray-600">Error loading scan results</p>
            </div>
          </div>
        </div>

        {/* Error display */}
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="text-center">
              <Lock className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Failed to Load Scan Results
              </h3>
              <p className="text-red-600 mb-4">{error}</p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => fetchScanResult(scanId)} variant="outline">
                  Try Again
                </Button>
                <Button onClick={handleBackToDashboard}>Back to Dashboard</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!scanResult) {
    return (
      <div
        className="container mx-auto p-6 space-y-6"
        style={{ backgroundColor: '#F5F5F5', minHeight: '100vh' }}
      >
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBackToDashboard}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to HIPAA Dashboard
          </Button>
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8" style={{ color: '#0055A4' }} />
            <div>
              <h1 className="text-3xl font-bold" style={{ color: '#333333' }}>
                HIPAA Security Scan Results
              </h1>
              <p style={{ color: '#666666' }}>Scan not found</p>
            </div>
          </div>
        </div>

        {/* Not found display */}
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Lock className="h-12 w-12 mx-auto mb-4" style={{ color: '#999999' }} />
              <h3 className="text-lg font-semibold mb-2" style={{ color: '#333333' }}>
                Scan Not Found
              </h3>
              <p className="mb-4" style={{ color: '#666666' }}>
                The requested scan (ID: {scanId}) could not be found.
              </p>
              <Button onClick={handleBackToDashboard}>Back to Dashboard</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div
      className="container mx-auto p-6 space-y-6"
      style={{ backgroundColor: '#F5F5F5', minHeight: '100vh' }}
    >
      {/* Enhanced Header with Navigation */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleBackToDashboard}
          style={{ borderColor: '#0055A4', color: '#0055A4' }}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to HIPAA Dashboard
        </Button>
        <div className="flex items-center gap-3 flex-1">
          <Shield className="h-8 w-8" style={{ color: '#0055A4' }} />
          <div>
            <h1 className="text-3xl font-bold" style={{ color: '#333333' }}>
              HIPAA Security Scan Results
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <p style={{ color: '#666666' }}>
                Scan ID: <span className="font-mono text-sm">{scanId}</span>
              </p>
              <Badge
                variant="outline"
                style={{
                  borderColor: '#663399',
                  color: '#663399',
                  backgroundColor: 'white',
                }}
              >
                Security Analysis
              </Badge>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            asChild
            style={{ borderColor: '#0055A4', color: '#0055A4' }}
          >
            <Link href="/dashboard/hipaa">
              <Lock className="h-4 w-4 mr-2" />
              Dashboard
            </Link>
          </Button>
          <Button
            variant="outline"
            size="sm"
            asChild
            style={{ borderColor: '#0055A4', color: '#0055A4' }}
          >
            <Link href={scanResult.targetUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Site
            </Link>
          </Button>
        </div>
      </div>

      {/* Breadcrumb Navigation */}
      <ComplianceBreadcrumb items={createComplianceBreadcrumbs.hipaaSecurityResult(scanId)} />

      {/* Main Results Component */}
      <HipaaSecurityResultsPage
        scanResult={scanResult}
        onExportReport={handleExportReport}
        onStartNewScan={handleStartNewScan}
        onRecalculateScoring={handleRecalculateScoring}
      />

      {/* Additional Actions */}
      <Card style={{ backgroundColor: 'white', border: '1px solid #E5E7EB' }}>
        <CardHeader>
          <CardTitle style={{ color: '#333333' }}>Additional Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              onClick={handleStartNewScan}
              className="hover:bg-gray-50"
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4',
              }}
            >
              <Lock className="h-4 w-4 mr-2" />
              Start New Security Scan
            </Button>
            <Button
              variant="outline"
              asChild
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4',
              }}
            >
              <Link href="/dashboard/hipaa/privacy-scan">
                <Shield className="h-4 w-4 mr-2" />
                Run Privacy Scan
              </Link>
            </Button>
            <Button
              variant="outline"
              asChild
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4',
              }}
            >
              <Link href="/dashboard/hipaa/security">View All Scans</Link>
            </Button>
            <Button
              variant="outline"
              asChild
              style={{
                backgroundColor: 'white',
                color: '#333333',
                borderColor: '#0055A4',
              }}
            >
              <Link href="/dashboard/guidance">Compliance Guidance</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
