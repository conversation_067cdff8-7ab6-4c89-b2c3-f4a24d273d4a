# WCAG Implementation Part 01: Foundation Setup

## Overview

This document provides step-by-step instructions for setting up the foundational infrastructure for WCAG compliance scanning. This includes database schema, core types, and basic project structure following established HIPAA/GDPR patterns.

## ⚠️ CRITICAL REQUIREMENTS

### 🚫 NO MOCK DATA POLICY
**STRICTLY PROHIBITED**: All implementations must use real website scanning. No mock data, fake responses, or simulated results are permitted.

### ✅ STRICT TYPESCRIPT COMPLIANCE
- **NO `any[]` TYPES**: Forbidden throughout implementation
- **Strict Mode**: All TypeScript must compile in strict mode
- **Complete Type Coverage**: Every function, parameter, and return value must be typed

### 🔒 AUTHENTICATION REQUIREMENTS
- **Keycloak Protection**: All WCAG endpoints must be protected
- **Bug-048 Prevention**: Proper authentication state management
- **User Context**: All scans associated with authenticated users

### 🎯 AUTOMATED vs MANUAL SEPARATION (LESSON FROM GDPR)
- **STRICT SEPARATION**: Automated checks completely separate from manual reviews
- **SINGLE AUTOMATED SCORE**: Only automated checks contribute to main score
- **SEPARATE MANUAL TRACKING**: Manual reviews tracked separately, not mixed into scoring
- **NO DUAL SCORING**: Avoid GDPR's mixed scoring issues

## Prerequisites

- Completed HIPAA and GDPR implementations (reference architecture)
- PostgreSQL database running
- Node.js 18+ and npm 9+
- Docker and docker-compose setup
- Keycloak authentication service configured

## Step 1: Database Schema Implementation

### 1.1 Create WCAG Migration File

Create `migrations/20250701000001_create_wcag_tables.ts`:

```typescript
import { Knex } from 'knex';

/**
 * WCAG Compliance Tables Migration
 * Creates all necessary tables for WCAG compliance scanning
 * Following established HIPAA/GDPR patterns with proper foreign keys
 */

export async function up(knex: Knex): Promise<void> {
  // Main WCAG scans table - following GDPR pattern
  await knex.schema.createTable('wcag_scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE'); // Proper FK like GDPR
    table.string('target_url', 2048).notNullable();
    table.timestamp('scan_timestamp', { useTz: true }).defaultTo(knex.fn.now());
    table.integer('scan_duration'); // milliseconds - following GDPR pattern
    table.decimal('overall_score', 5, 2); // Following GDPR decimal pattern
    table.enu('scan_status', ['pending', 'running', 'completed', 'failed']).defaultTo('pending');
    table.timestamp('completion_timestamp', { useTz: true });
    table.enu('level_achieved', ['A', 'AA', 'AAA', 'FAIL']);
    table.enu('risk_level', ['critical', 'high', 'medium', 'low']); // Following GDPR enum pattern
    table.integer('perceivable_score');
    table.integer('operable_score');
    table.integer('understandable_score');
    table.integer('robust_score');
    table.integer('wcag21_score');
    table.integer('wcag22_score');
    table.integer('wcag30_score');
    table.integer('total_automated_checks').notNullable(); // Only automated checks
    table.integer('passed_automated_checks').notNullable(); // Only automated passes
    table.integer('failed_automated_checks').notNullable(); // Only automated failures
    table.integer('manual_review_items').defaultTo(0); // Separate manual tracking
    table.jsonb('scan_options');
    table.text('error_message');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes - following HIPAA/GDPR patterns
    table.index('user_id');
    table.index('scan_timestamp');
    table.index('scan_status');
    table.index('level_achieved');
    table.index('risk_level');
  });

  // AUTOMATED check results table - ONLY for automated checks
  await knex.schema.createTable('wcag_automated_results', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('rule_id', 20).notNullable(); // e.g., 'WCAG-001'
    table.string('rule_name', 255).notNullable();
    table.enu('category', ['perceivable', 'operable', 'understandable', 'robust']); // Enum for consistency
    table.enu('wcag_version', ['2.1', '2.2', '3.0']); // Enum for consistency
    table.string('success_criterion', 20); // e.g., '1.1.1', '2.4.11'
    table.enu('level', ['A', 'AA', 'AAA']); // Enum for consistency
    table.enu('status', ['passed', 'failed', 'not_applicable']); // NO manual_review status
    table.integer('score').notNullable(); // Contributes to main score
    table.integer('max_score').notNullable();
    table.decimal('weight', 3, 2).notNullable();
    table.jsonb('evidence'); // array of evidence objects
    table.jsonb('recommendations'); // array of recommendation strings
    table.integer('execution_time'); // milliseconds
    table.text('error_message');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('rule_id');
    table.index('status');
    table.index('category');
    table.index('wcag_version');
    table.index('level');
  });

  // Color contrast analysis table
  await knex.schema.createTable('wcag_contrast_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('element_selector', 1000).notNullable();
    table.string('element_type', 100); // text, button, link, etc.
    table.string('foreground_color', 20).notNullable();
    table.string('background_color', 20).notNullable();
    table.decimal('contrast_ratio', 4, 2).notNullable();
    table.boolean('is_large_text').notNullable();
    table.boolean('level_aa_pass').notNullable();
    table.boolean('level_aaa_pass').notNullable();
    table.text('context_description');
    table.text('recommendation');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('level_aa_pass');
    table.index('level_aaa_pass');
  });

  // Focus analysis table
  await knex.schema.createTable('wcag_focus_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('element_selector', 1000).notNullable();
    table.string('element_type', 100);
    table.boolean('is_focusable').notNullable();
    table.integer('tab_index');
    table.boolean('has_visible_focus').notNullable();
    table.decimal('focus_indicator_width', 4, 2);
    table.string('focus_indicator_color', 20);
    table.decimal('focus_contrast_ratio', 4, 2);
    table.boolean('is_obscured').notNullable();
    table.boolean('keyboard_accessible').notNullable();
    table.jsonb('issues'); // array of focus-related issues
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('is_focusable');
    table.index('has_visible_focus');
    table.index('is_obscured');
    table.index('keyboard_accessible');
  });

  // Keyboard analysis table
  await knex.schema.createTable('wcag_keyboard_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('element_selector', 1000).notNullable();
    table.string('element_type', 100);
    table.boolean('is_reachable').notNullable();
    table.boolean('is_operable').notNullable();
    table.jsonb('supported_keys'); // array of supported key combinations
    table.boolean('has_keyboard_trap').notNullable();
    table.integer('focus_order_position');
    table.boolean('is_logical_order').notNullable();
    table.jsonb('issues'); // array of keyboard-related issues
    table.jsonb('recommendations'); // array of recommendations
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('is_reachable');
    table.index('has_keyboard_trap');
    table.index('is_logical_order');
  });

  // Manual review items table - following GDPR pattern for manual reviews
  await knex.schema.createTable('wcag_manual_reviews', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('rule_id', 20).notNullable();
    table.string('element_selector', 1000);
    table.text('description').notNullable();
    table.text('automated_findings');
    table.text('review_required').notNullable();
    table.enu('priority', ['high', 'medium', 'low']).notNullable();
    table.integer('estimated_time'); // minutes
    table.enu('review_status', ['pending', 'in_progress', 'completed', 'skipped']).defaultTo('pending');
    table.text('reviewer_notes');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('reviewed_at', { useTz: true });

    // Indexes
    table.index('scan_id');
    table.index('rule_id');
    table.index('priority');
    table.index('review_status');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('wcag_manual_reviews');
  await knex.schema.dropTableIfExists('wcag_keyboard_analysis');
  await knex.schema.dropTableIfExists('wcag_focus_analysis');
  await knex.schema.dropTableIfExists('wcag_contrast_analysis');
  await knex.schema.dropTableIfExists('wcag_automated_results');
  await knex.schema.dropTableIfExists('wcag_scans');
}
```

### 1.2 Run Migration

```bash
# Run the migration
npm run migrate:latest

# Verify tables were created
npm run db:status
```

## Step 2: TypeScript Type Definitions

### 2.1 Create Core Types File

Create `backend/src/compliance/wcag/types.ts`:

```typescript
/**
 * WCAG Compliance Type Definitions
 * Strict TypeScript interfaces - NO any[] types allowed
 * Following established HIPAA/GDPR patterns with Zod validation
 */

import { z } from 'zod';

// Core WCAG Rule Identifiers - Following GDPR pattern
export type WcagRuleId =
  | 'WCAG-001' | 'WCAG-002' | 'WCAG-003' | 'WCAG-004' | 'WCAG-005'
  | 'WCAG-006' | 'WCAG-007' | 'WCAG-008' | 'WCAG-009' | 'WCAG-010'
  | 'WCAG-011' | 'WCAG-012' | 'WCAG-013' | 'WCAG-014' | 'WCAG-015'
  | 'WCAG-016' | 'WCAG-017' | 'WCAG-018' | 'WCAG-019' | 'WCAG-020'
  | 'WCAG-021';

// Core WCAG Types
export type WcagVersion = '2.1' | '2.2' | '3.0';
export type WcagLevel = 'A' | 'AA' | 'AAA';
export type WcagCategory = 'perceivable' | 'operable' | 'understandable' | 'robust';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
// AUTOMATED ONLY - No manual_review status (separate tracking)
export type AutomatedCheckStatus = 'passed' | 'failed' | 'not_applicable';
export type ManualReviewStatus = 'pending' | 'in_progress' | 'completed' | 'skipped';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

// Scan Configuration
export interface WcagScanConfig {
  targetUrl: string;
  scanOptions?: WcagScanOptions;
  userId: string;
  requestId: string;
}

export interface WcagScanOptions {
  enableContrastAnalysis?: boolean;
  enableKeyboardTesting?: boolean;
  enableFocusAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  wcagVersion?: WcagVersion | 'all';
  level?: WcagLevel;
  maxPages?: number;
  timeout?: number;
}

// Scan Results
export interface WcagScanResult {
  scanId: string;
  targetUrl: string;
  status: ScanStatus;
  overallScore: number;
  levelAchieved: WcagLevel | 'FAIL';
  riskLevel: RiskLevel;
  summary: WcagScanSummary;
  checks: WcagCheckResult[];
  recommendations: WcagRecommendation[];
  metadata: WcagScanMetadata;
}

// SEPARATED: Automated vs Manual tracking
export interface WcagAutomatedSummary {
  totalAutomatedChecks: number;
  passedAutomatedChecks: number;
  failedAutomatedChecks: number;
  automatedScore: number; // SINGLE automated score only
  categoryScores: {
    perceivable: number;
    operable: number;
    understandable: number;
    robust: number;
  };
  versionScores: {
    wcag21: number;
    wcag22: number;
    wcag30: number;
  };
  automationRate: number;
}

// Individual Check Results
export interface WcagCheckResult {
  ruleId: string;
  ruleName: string;
  category: WcagCategory;
  wcagVersion: WcagVersion;
  successCriterion: string;
  level: WcagLevel;
  status: CheckStatus;
  score: number;
  maxScore: number;
  weight: number;
  automated: boolean;
  evidence: WcagEvidence[];
  recommendations: string[];
  executionTime: number;
  errorMessage?: string;
}

export interface WcagEvidence {
  type: 'text' | 'image' | 'code' | 'measurement' | 'interaction';
  description: string;
  value: string;
  selector?: string;
  screenshot?: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

export interface WcagRecommendation {
  ruleId: string;
  priority: 'high' | 'medium' | 'low';
  category: WcagCategory;
  title: string;
  description: string;
  implementation: string;
  resources: string[];
}

// Specialized Analysis Types
export interface ContrastAnalysisResult {
  elementSelector: string;
  elementType: string;
  foregroundColor: string;
  backgroundColor: string;
  contrastRatio: number;
  isLargeText: boolean;
  levelAAPass: boolean;
  levelAAAPass: boolean;
  contextDescription: string;
  recommendation: string;
}

export interface FocusAnalysisResult {
  elementSelector: string;
  elementType: string;
  isFocusable: boolean;
  tabIndex: number;
  hasVisibleFocus: boolean;
  focusIndicatorWidth: number;
  focusIndicatorColor: string;
  focusContrastRatio: number;
  isObscured: boolean;
  keyboardAccessible: boolean;
  issues: string[];
}

export interface KeyboardAnalysisResult {
  elementSelector: string;
  elementType: string;
  isReachable: boolean;
  isOperable: boolean;
  supportedKeys: string[];
  hasKeyboardTrap: boolean;
  focusOrderPosition: number;
  isLogicalOrder: boolean;
  issues: string[];
  recommendations: string[];
}

// Database Models
export interface WcagScanModel {
  id: string;
  user_id: string;
  target_url: string;
  scan_status: ScanStatus;
  scan_timestamp: Date;
  completion_timestamp?: Date;
  overall_score?: number;
  level_achieved?: WcagLevel | 'FAIL';
  risk_level?: RiskLevel;
  perceivable_score?: number;
  operable_score?: number;
  understandable_score?: number;
  robust_score?: number;
  wcag21_score?: number;
  wcag22_score?: number;
  wcag30_score?: number;
  total_checks?: number;
  passed_checks?: number;
  failed_checks?: number;
  manual_review_required?: number;
  scan_options?: WcagScanOptions;
  error_message?: string;
  created_at: Date;
  updated_at: Date;
}

// Metadata and Configuration
export interface WcagScanMetadata {
  scanId: string;
  userId: string;
  requestId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  userAgent: string;
  viewport: {
    width: number;
    height: number;
  };
  environment: string;
  version: string;
}

// API Request/Response Types
export interface WcagScanRequest {
  targetUrl: string;
  scanOptions?: WcagScanOptions;
}

export interface WcagScanResponse {
  success: boolean;
  data: WcagScanResult;
  requestId: string;
  processingTime: number;
}

// Error Types
export interface WcagError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  ruleId?: string;
  context?: string;
}

// Configuration Constants
export interface WcagRuleConfig {
  ruleId: string;
  ruleName: string;
  category: WcagCategory;
  wcagVersion: WcagVersion;
  successCriterion: string;
  level: WcagLevel;
  weight: number;
  automated: boolean;
  description: string;
  checkFunction: string;
}
```

## Step 3: WCAG Constants and Configuration

### 3.1 Create Constants File

Create `backend/src/compliance/wcag/constants.ts`:

```typescript
/**
 * WCAG Constants and Configuration
 * STRICT SEPARATION: Automated vs Manual rules
 * Following HIPAA/GDPR patterns with single automated scoring
 */

import { WcagRuleConfig, WcagCategory, WcagVersion, WcagLevel } from './types';

// AUTOMATED WCAG Rules - ONLY these contribute to main score
// AUTOMATED RULES ONLY - contribute to main score
export const WCAG_AUTOMATED_RULES: WcagRuleConfig[] = [
  // WCAG 2.1 Automated Rules (high automation level only)
  {
    ruleId: 'WCAG-001',
    ruleName: 'Non-text Content',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.1.1',
    level: 'A',
    weight: 0.08,
    automated: true,
    description: 'All non-text content has text alternatives',
    checkFunction: 'NonTextContentCheck'
  },
  {
    ruleId: 'WCAG-002',
    ruleName: 'Captions (Prerecorded)',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.2.2',
    level: 'A',
    weight: 0.06,
    automated: true,
    description: 'Captions provided for prerecorded audio content',
    checkFunction: 'CaptionsCheck'
  },
  {
    ruleId: 'WCAG-003',
    ruleName: 'Info and Relationships',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.3.1',
    level: 'A',
    weight: 0.09,
    automated: true,
    description: 'Information and relationships conveyed through presentation can be programmatically determined',
    checkFunction: 'InfoRelationshipsCheck'
  },
  {
    ruleId: 'WCAG-004',
    ruleName: 'Contrast (Minimum)',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.3',
    level: 'AA',
    weight: 0.10,
    automated: true,
    description: 'Text has sufficient contrast ratio',
    checkFunction: 'ContrastMinimumCheck'
  },
  {
    ruleId: 'WCAG-005',
    ruleName: 'Keyboard',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.1.1',
    level: 'A',
    weight: 0.12,
    automated: true,
    description: 'All functionality available from keyboard',
    checkFunction: 'KeyboardCheck'
  },
  {
    ruleId: 'WCAG-006',
    ruleName: 'Focus Order',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.3',
    level: 'A',
    weight: 0.08,
    automated: true,
    description: 'Focusable components receive focus in logical order',
    checkFunction: 'FocusOrderCheck'
  },
  {
    ruleId: 'WCAG-007',
    ruleName: 'Focus Visible',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.7',
    level: 'AA',
    weight: 0.09,
    automated: true,
    description: 'Keyboard focus indicator is visible',
    checkFunction: 'FocusVisibleCheck'
  },
  {
    ruleId: 'WCAG-008',
    ruleName: 'Error Identification',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.3.1',
    level: 'A',
    weight: 0.07,
    automated: true,
    description: 'Input errors are identified and described to user',
    checkFunction: 'ErrorIdentificationCheck'
  },
  {
    ruleId: 'WCAG-009',
    ruleName: 'Name, Role, Value',
    category: 'robust',
    wcagVersion: '2.1',
    successCriterion: '4.1.2',
    level: 'A',
    weight: 0.08,
    automated: true,
    description: 'UI components have accessible name, role, and value',
    checkFunction: 'NameRoleValueCheck'
  },

  // WCAG 2.2 New Rules (7 rules)
  {
    ruleId: 'WCAG-010',
    ruleName: 'Focus Not Obscured (Minimum)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.11',
    level: 'AA',
    weight: 0.06,
    automated: true,
    description: 'Focused element is not fully hidden by author content',
    checkFunction: 'FocusNotObscuredMinCheck'
  },
  {
    ruleId: 'WCAG-011',
    ruleName: 'Focus Not Obscured (Enhanced)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.12',
    level: 'AAA',
    weight: 0.04,
    automated: true,
    description: 'Focused element is never hidden by author content',
    checkFunction: 'FocusNotObscuredEnhCheck'
  },
  {
    ruleId: 'WCAG-012',
    ruleName: 'Focus Appearance',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.13',
    level: 'AAA',
    weight: 0.04,
    automated: true,
    description: 'Focus indicator meets size and contrast requirements',
    checkFunction: 'FocusAppearanceCheck'
  },
  {
    ruleId: 'WCAG-013',
    ruleName: 'Dragging Movements',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.5.7',
    level: 'AA',
    weight: 0.05,
    automated: true,
    description: 'Dragging movements have single pointer alternative',
    checkFunction: 'DraggingMovementsCheck'
  },
  {
    ruleId: 'WCAG-014',
    ruleName: 'Target Size (Minimum)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.5.8',
    level: 'AA',
    weight: 0.06,
    automated: true,
    description: 'Target size is at least 24×24 CSS pixels',
    checkFunction: 'TargetSizeCheck'
  },
  {
    ruleId: 'WCAG-015',
    ruleName: 'Consistent Help',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.2.6',
    level: 'A',
    weight: 0.04,
    automated: true,
    description: 'Help mechanisms appear in consistent order',
    checkFunction: 'ConsistentHelpCheck'
  },
  {
    ruleId: 'WCAG-016',
    ruleName: 'Redundant Entry',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.3.7',
    level: 'A',
    weight: 0.05,
    automated: true,
    description: 'Information previously entered is auto-populated',
    checkFunction: 'RedundantEntryCheck'
  },

  // WCAG 3.0 Draft Rules (5 rules)
  {
    ruleId: 'WCAG-017',
    ruleName: 'Image Alternatives',
    category: 'perceivable',
    wcagVersion: '3.0',
    successCriterion: '2.1',
    level: 'A',
    weight: 0.04,
    automated: true,
    description: 'Enhanced image alternative requirements',
    checkFunction: 'ImageAlternativesCheck'
  },
  {
    ruleId: 'WCAG-018',
    ruleName: 'Text and Wording',
    category: 'understandable',
    wcagVersion: '3.0',
    successCriterion: '2.2',
    level: 'AA',
    weight: 0.04,
    automated: true,
    description: 'Content uses plain language',
    checkFunction: 'TextWordingCheck'
  },
  {
    ruleId: 'WCAG-019',
    ruleName: 'Keyboard Focus',
    category: 'operable',
    wcagVersion: '3.0',
    successCriterion: '2.4',
    level: 'A',
    weight: 0.05,
    automated: true,
    description: 'Enhanced keyboard focus requirements',
    checkFunction: 'KeyboardFocusCheck'
  },
  {
    ruleId: 'WCAG-020',
    ruleName: 'Motor',
    category: 'operable',
    wcagVersion: '3.0',
    successCriterion: '2.5',
    level: 'AA',
    weight: 0.04,
    automated: true,
    description: 'Motor accessibility requirements',
    checkFunction: 'MotorCheck'
  },
  {
    ruleId: 'WCAG-021',
    ruleName: 'Pronunciation & Meaning',
    category: 'understandable',
    wcagVersion: '3.0',
    successCriterion: '3.1',
    level: 'AAA',
    weight: 0.03,
    automated: true,
    description: 'Unusual words are defined',
    checkFunction: 'PronunciationMeaningCheck'
  }
];

// MANUAL REVIEW RULES - tracked separately, NO scoring contribution
export const WCAG_MANUAL_RULES = [
  {
    ruleId: 'WCAG-MANUAL-001',
    ruleName: 'Content Quality Review',
    category: 'understandable',
    description: 'Manual review of content clarity and comprehension',
    priority: 'medium',
    estimatedTime: 15, // minutes
  },
  {
    ruleId: 'WCAG-MANUAL-002',
    ruleName: 'User Experience Flow',
    category: 'operable',
    description: 'Manual testing of complete user workflows',
    priority: 'high',
    estimatedTime: 30,
  },
  {
    ruleId: 'WCAG-MANUAL-003',
    ruleName: 'Context-Specific Accessibility',
    category: 'perceivable',
    description: 'Manual review of context-dependent accessibility features',
    priority: 'medium',
    estimatedTime: 20,
  }
] as const;

// AUTOMATED SCORING ONLY - Single score system
export const AUTOMATED_CATEGORY_WEIGHTS: Record<WcagCategory, number> = {
  perceivable: 0.25,
  operable: 0.35,
  understandable: 0.25,
  robust: 0.15
};

// AUTOMATED VERSION WEIGHTS - Single score system
export const AUTOMATED_VERSION_WEIGHTS: Record<WcagVersion, number> = {
  '2.1': 0.50,
  '2.2': 0.35,
  '3.0': 0.15
};

// AUTOMATED LEVEL REQUIREMENTS - Single score system
export const AUTOMATED_LEVEL_REQUIREMENTS = {
  A: 0.80,    // 80% of automated Level A rules must pass
  AA: 0.75,   // 75% of automated Level A + AA rules must pass
  AAA: 0.70   // 70% of all automated rules must pass
};

// MANUAL REVIEW PRIORITIES - separate tracking
export const MANUAL_REVIEW_PRIORITIES = {
  high: 1.0,
  medium: 0.7,
  low: 0.4
} as const;

// Risk Level Thresholds
export const RISK_THRESHOLDS = {
  critical: 40,  // 0-40 score
  high: 60,      // 41-60 score
  medium: 80,    // 61-80 score
  low: 100       // 81-100 score
};

// Automation Levels by Rule
export const AUTOMATION_LEVELS: Record<string, number> = {
  'WCAG-001': 0.95, // Non-text Content
  'WCAG-002': 0.80, // Captions
  'WCAG-003': 0.90, // Info and Relationships
  'WCAG-004': 1.00, // Contrast (Minimum)
  'WCAG-005': 0.85, // Keyboard
  'WCAG-006': 0.75, // Focus Order
  'WCAG-007': 1.00, // Focus Visible
  'WCAG-008': 0.90, // Error Identification
  'WCAG-009': 0.90, // Name, Role, Value
  'WCAG-010': 1.00, // Focus Not Obscured (Minimum)
  'WCAG-011': 1.00, // Focus Not Obscured (Enhanced)
  'WCAG-012': 1.00, // Focus Appearance
  'WCAG-013': 0.70, // Dragging Movements
  'WCAG-014': 1.00, // Target Size
  'WCAG-015': 0.80, // Consistent Help
  'WCAG-016': 0.85, // Redundant Entry
  'WCAG-017': 0.95, // Image Alternatives (3.0)
  'WCAG-018': 0.75, // Text and Wording (3.0)
  'WCAG-019': 0.90, // Keyboard Focus (3.0)
  'WCAG-020': 0.80, // Motor (3.0)
  'WCAG-021': 0.60  // Pronunciation & Meaning (3.0)
};

// Default Scan Configuration
export const DEFAULT_SCAN_CONFIG: Required<WcagScanOptions> = {
  enableContrastAnalysis: true,
  enableKeyboardTesting: true,
  enableFocusAnalysis: true,
  enableSemanticValidation: true,
  wcagVersion: 'all',
  level: 'AA',
  maxPages: 5,
  timeout: 30000
};

// Error Codes
export const WCAG_ERROR_CODES = {
  INVALID_URL: 'WCAG_INVALID_URL',
  SCAN_TIMEOUT: 'WCAG_SCAN_TIMEOUT',
  NETWORK_ERROR: 'WCAG_NETWORK_ERROR',
  BROWSER_ERROR: 'WCAG_BROWSER_ERROR',
  VALIDATION_ERROR: 'WCAG_VALIDATION_ERROR',
  DATABASE_ERROR: 'WCAG_DATABASE_ERROR',
  AUTHENTICATION_ERROR: 'WCAG_AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR: 'WCAG_RATE_LIMIT_ERROR'
} as const;

// Success Messages
export const WCAG_SUCCESS_MESSAGES = {
  SCAN_STARTED: 'WCAG scan initiated successfully',
  SCAN_COMPLETED: 'WCAG scan completed successfully',
  RESULTS_RETRIEVED: 'WCAG scan results retrieved successfully',
  EXPORT_GENERATED: 'WCAG report exported successfully'
} as const;
```

## Step 4: Project Structure Setup

### 4.1 Create Directory Structure

```bash
# Create WCAG module directories
mkdir -p backend/src/compliance/wcag/{checks,utils,services,database}
mkdir -p backend/src/compliance/wcag/__tests__/{checks,utils,services}

# Create frontend directories
mkdir -p frontend/components/wcag
mkdir -p frontend/app/dashboard/wcag
mkdir -p frontend/types
mkdir -p frontend/services
```

### 4.2 Create Index File

Create `backend/src/compliance/wcag/index.ts`:

```typescript
/**
 * WCAG Module Main Export
 * Central export point for all WCAG functionality
 */

// Core Types
export * from './types';
export * from './constants';

// Services (will be implemented in subsequent parts)
export { WcagOrchestrator } from './orchestrator';
export { WcagDatabase } from './database/wcag-database';

// Utilities (will be implemented in subsequent parts)
export * from './utils/color-analyzer';
export * from './utils/focus-tracker';
export * from './utils/keyboard-tester';

// Check Functions (will be implemented in subsequent parts)
export * from './checks';

/**
 * WCAG Module Version
 */
export const WCAG_MODULE_VERSION = '1.0.0';

/**
 * Supported WCAG Rules
 */
export const SUPPORTED_WCAG_RULES = [
  'WCAG-001', 'WCAG-002', 'WCAG-003', 'WCAG-004', 'WCAG-005',
  'WCAG-006', 'WCAG-007', 'WCAG-008', 'WCAG-009', 'WCAG-010',
  'WCAG-011', 'WCAG-012', 'WCAG-013', 'WCAG-014', 'WCAG-015',
  'WCAG-016', 'WCAG-017', 'WCAG-018', 'WCAG-019', 'WCAG-020',
  'WCAG-021'
] as const;

/**
 * Module Configuration
 */
export const WCAG_MODULE_CONFIG = {
  version: WCAG_MODULE_VERSION,
  totalRules: 21,
  averageAutomation: 0.87,
  supportedVersions: ['2.1', '2.2', '3.0'],
  supportedLevels: ['A', 'AA', 'AAA']
} as const;
```

## Step 5: Frontend Type Definitions

### 5.1 Create Frontend Types

Create `frontend/types/wcag.ts`:

```typescript
/**
 * Frontend WCAG Type Definitions
 * Client-side types for WCAG functionality
 */

// Re-export backend types for frontend use
export type {
  WcagScanConfig,
  WcagScanOptions,
  WcagScanResult,
  WcagScanSummary,
  WcagCheckResult,
  WcagEvidence,
  WcagRecommendation,
  ContrastAnalysisResult,
  FocusAnalysisResult,
  KeyboardAnalysisResult,
  WcagVersion,
  WcagLevel,
  WcagCategory,
  ScanStatus,
  CheckStatus,
  RiskLevel
} from '../../../backend/src/compliance/wcag/types';

// Frontend-specific types
export interface WcagScanFormData {
  targetUrl: string;
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;
}

export interface WcagDashboardState {
  currentScan?: WcagScanResult;
  recentScans: WcagScanResult[];
  isScanning: boolean;
  scanProgress: number;
  error?: string;
}

export interface WcagComponentProps {
  scanResult?: WcagScanResult;
  onScanStart?: (config: WcagScanConfig) => void;
  onExport?: () => void;
  onRescan?: () => void;
}
```

## Step 6: Validation and Testing

### 6.1 Verify Database Schema

```bash
# Check if tables were created successfully
psql -d comply_checker -c "\dt wcag*"

# Verify indexes
psql -d comply_checker -c "\di wcag*"
```

### 6.2 Verify TypeScript Compilation

```bash
# Compile TypeScript to check for errors
cd backend && npm run build

# Check for any type errors
cd frontend && npm run type-check
```

### 6.3 Create Basic Test

Create `backend/src/compliance/wcag/__tests__/foundation.test.ts`:

```typescript
/**
 * Foundation Tests
 * Verify basic WCAG module setup
 */

import { WCAG_AUTOMATED_RULES, WCAG_MANUAL_RULES, AUTOMATED_CATEGORY_WEIGHTS, AUTOMATED_VERSION_WEIGHTS } from '../constants';
import { WcagRuleConfig } from '../types';

describe('WCAG Foundation', () => {
  test('should have automated rules defined', () => {
    expect(WCAG_AUTOMATED_RULES.length).toBeGreaterThan(0);

    // Verify each automated rule has required properties
    WCAG_AUTOMATED_RULES.forEach((rule: WcagRuleConfig) => {
      expect(rule.ruleId).toBeDefined();
      expect(rule.ruleName).toBeDefined();
      expect(rule.category).toBeDefined();
      expect(rule.wcagVersion).toBeDefined();
      expect(rule.level).toBeDefined();
      expect(rule.weight).toBeGreaterThan(0);
      expect(rule.automated).toBeDefined();
      expect(rule.checkFunction).toBeDefined();
      expect(rule.automated).toBe(true); // Must be automated
    });
  });

  test('should have manual rules defined separately', () => {
    expect(WCAG_MANUAL_RULES.length).toBeGreaterThan(0);

    // Verify manual rules don't have scoring weights
    WCAG_MANUAL_RULES.forEach(rule => {
      expect(rule.weight).toBeUndefined();
      expect(rule.priority).toBeDefined();
      expect(rule.estimatedTime).toBeDefined();
    });
  });

  test('should have proper weight distribution', () => {
    const totalWeight = WCAG_RULES.reduce((sum, rule) => sum + rule.weight, 0);
    expect(totalWeight).toBeCloseTo(1.0, 2);
  });

  test('should have valid category weights', () => {
    const totalCategoryWeight = Object.values(CATEGORY_WEIGHTS).reduce((sum, weight) => sum + weight, 0);
    expect(totalCategoryWeight).toBe(1.0);
  });

  test('should have valid version weights', () => {
    const totalVersionWeight = Object.values(VERSION_WEIGHTS).reduce((sum, weight) => sum + weight, 0);
    expect(totalVersionWeight).toBe(1.0);
  });

  test('should have no any[] types in type definitions', () => {
    // This test ensures TypeScript compilation catches any[] usage
    expect(true).toBe(true); // Will fail compilation if any[] types exist
  });
});
```

## Validation Checklist

- [ ] Database migration runs successfully
- [ ] All 5 WCAG tables created with proper indexes
- [ ] TypeScript types compile without errors
- [ ] No `any[]` types used (strict compliance)
- [ ] All 21 WCAG rules defined with proper weights
- [ ] Directory structure matches HIPAA/GDPR patterns
- [ ] Foundation tests pass
- [ ] Ready for Part 02 implementation

## Next Steps

Continue with **Part 02: Core Services & Utilities** to build the scanning engine foundation and utility functions.

---

*This foundation provides the robust base for implementing a world-class WCAG compliance system with 87% automation and strict TypeScript compliance.*
