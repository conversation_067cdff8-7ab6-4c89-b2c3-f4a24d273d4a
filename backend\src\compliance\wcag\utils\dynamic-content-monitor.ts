/**
 * Enhanced Dynamic Content Monitor for WCAG Scanning
 * Provides comprehensive monitoring of SPAs, AJAX updates, and dynamic content changes
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface DynamicContentChange {
  type: 'childList' | 'attributes' | 'characterData';
  target: string;
  addedNodes: number;
  removedNodes: number;
  attributeName?: string;
  oldValue?: string;
  newValue?: string;
  timestamp: number;
  xpath: string;
}

export interface NetworkRequest {
  url: string;
  method: string;
  resourceType: string;
  status?: number;
  responseSize?: number;
  timestamp: number;
  isXHR: boolean;
  isFetch: boolean;
}

export interface SPANavigation {
  from: string;
  to: string;
  method: 'pushState' | 'replaceState' | 'popstate' | 'hashchange';
  timestamp: number;
  triggeredByUser: boolean;
}

export interface DynamicContentResult {
  domChanges: DynamicContentChange[];
  networkRequests: NetworkRequest[];
  spaNavigations: SPANavigation[];
  hasDynamicContent: boolean;
  isLikelySPA: boolean;
  contentStabilityScore: number; // 0-100, higher = more stable
  recommendations: string[];
  detectedFrameworks: string[];
}

export interface DynamicContentConfig {
  monitoringDuration: number; // milliseconds
  stabilityThreshold: number; // changes per second
  enableNetworkMonitoring: boolean;
  enableSPADetection: boolean;
  enableFrameworkDetection: boolean;
  maxChangesToTrack: number;
}

/**
 * Advanced dynamic content monitoring for modern web applications
 */
export class DynamicContentMonitor {
  private static instance: DynamicContentMonitor;
  private activeMonitors = new Map<
    string,
    {
      page: Page;
      config: DynamicContentConfig;
      startTime: number;
      changes: DynamicContentChange[];
      requests: NetworkRequest[];
      navigations: SPANavigation[];
    }
  >();

  private constructor() {}

  static getInstance(): DynamicContentMonitor {
    if (!DynamicContentMonitor.instance) {
      DynamicContentMonitor.instance = new DynamicContentMonitor();
    }
    return DynamicContentMonitor.instance;
  }

  /**
   * Start monitoring dynamic content changes
   */
  async startMonitoring(
    page: Page,
    scanId: string,
    config: Partial<DynamicContentConfig> = {},
  ): Promise<void> {
    const fullConfig: DynamicContentConfig = {
      monitoringDuration: config.monitoringDuration || 30000, // 30 seconds
      stabilityThreshold: config.stabilityThreshold || 5, // 5 changes per second
      enableNetworkMonitoring: config.enableNetworkMonitoring ?? true,
      enableSPADetection: config.enableSPADetection ?? true,
      enableFrameworkDetection: config.enableFrameworkDetection ?? true,
      maxChangesToTrack: config.maxChangesToTrack || 1000,
    };

    logger.debug(`🔍 Starting dynamic content monitoring for scan: ${scanId}`);

    // Initialize monitoring data
    this.activeMonitors.set(scanId, {
      page,
      config: fullConfig,
      startTime: Date.now(),
      changes: [],
      requests: [],
      navigations: [],
    });

    // Set up DOM mutation observer
    if (fullConfig.enableSPADetection) {
      await this.setupMutationObserver(page, scanId);
    }

    // Set up network monitoring
    if (fullConfig.enableNetworkMonitoring) {
      await this.setupNetworkMonitoring(page, scanId);
    }

    // Set up SPA navigation detection
    if (fullConfig.enableSPADetection) {
      await this.setupSPADetection(page, scanId);
    }

    // Set up framework detection
    if (fullConfig.enableFrameworkDetection) {
      await this.setupFrameworkDetection(page, scanId);
    }
  }

  /**
   * Stop monitoring and return results
   */
  async stopMonitoring(scanId: string): Promise<DynamicContentResult> {
    const monitor = this.activeMonitors.get(scanId);
    if (!monitor) {
      throw new Error(`No active monitor found for scan: ${scanId}`);
    }

    logger.debug(`📊 Stopping dynamic content monitoring for scan: ${scanId}`);

    // Calculate monitoring duration
    const duration = Date.now() - monitor.startTime;

    // Collect final data from page
    const finalData = await this.collectFinalData(monitor.page);

    // Analyze results
    const result = await this.analyzeResults(monitor, finalData, duration);

    // Cleanup
    this.activeMonitors.delete(scanId);

    logger.info(`✅ Dynamic content analysis completed for scan: ${scanId}`, {
      domChanges: result.domChanges.length,
      networkRequests: result.networkRequests.length,
      isLikelySPA: result.isLikelySPA,
      contentStabilityScore: result.contentStabilityScore,
    });

    return result;
  }

  /**
   * Set up DOM mutation observer
   */
  private async setupMutationObserver(page: Page, _scanId: string): Promise<void> {
    await page.evaluateOnNewDocument((_scanId) => {
      // Initialize global storage
      interface WcagDynamicData {
        changes: DynamicContentChange[];
        navigations: SPANavigation[];
        frameworks: string[];
      }

      (window as unknown as { wcagDynamicData: WcagDynamicData }).wcagDynamicData = (
        window as unknown as { wcagDynamicData?: WcagDynamicData }
      ).wcagDynamicData || {
        changes: [],
        navigations: [],
        frameworks: [],
      };

      // Create mutation observer
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          const change: DynamicContentChange = {
            type: mutation.type as 'childList' | 'attributes' | 'characterData',
            target: (
              window as unknown as { getElementXPath: (el: Element) => string }
            ).getElementXPath(mutation.target as Element),
            addedNodes: mutation.addedNodes.length,
            removedNodes: mutation.removedNodes.length,
            attributeName: mutation.attributeName || undefined,
            oldValue: mutation.oldValue || undefined,
            newValue:
              mutation.type === 'attributes' && mutation.target
                ? (mutation.target as Element).getAttribute(mutation.attributeName!) || undefined
                : undefined,
            timestamp: Date.now(),
            xpath: (
              window as unknown as { getElementXPath: (el: Element) => string }
            ).getElementXPath(mutation.target as Element),
          };

          (window as unknown as { wcagDynamicData: WcagDynamicData }).wcagDynamicData.changes.push(
            change,
          );

          // Limit stored changes to prevent memory issues
          const windowData = (window as unknown as { wcagDynamicData: WcagDynamicData })
            .wcagDynamicData;
          if (windowData.changes.length > 1000) {
            windowData.changes = windowData.changes.slice(-500);
          }
        });
      });

      // Start observing
      observer.observe(document.body || document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeOldValue: true,
        characterData: true,
        characterDataOldValue: true,
      });

      // Helper function to get XPath
      (window as unknown as { getElementXPath: (element: Element) => string }).getElementXPath =
        function (element: Element): string {
          if (!element) return '';

          if (element.id) {
            return `//*[@id="${element.id}"]`;
          }

          const parts = [];
          while (element && element.nodeType === Node.ELEMENT_NODE) {
            const tagName = element.nodeName.toLowerCase();
            let index = 1;

            // Count preceding siblings with same tag name
            let sibling = element.previousElementSibling;
            while (sibling) {
              if (sibling.nodeName.toLowerCase() === tagName) {
                index++;
              }
              sibling = sibling.previousElementSibling;
            }

            parts.unshift(`${tagName}[${index}]`);
            element = element.parentElement!;
          }

          return '/' + parts.join('/');
        };
    }, _scanId);
  }

  /**
   * Set up network monitoring
   */
  private async setupNetworkMonitoring(page: Page, scanId: string): Promise<void> {
    const monitor = this.activeMonitors.get(scanId);
    if (!monitor) return;

    // Enable request interception
    await page.setRequestInterception(true);

    page.on('request', (request) => {
      const networkRequest: NetworkRequest = {
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType(),
        timestamp: Date.now(),
        isXHR: request.resourceType() === 'xhr',
        isFetch: request.resourceType() === 'fetch',
      };

      monitor.requests.push(networkRequest);

      // Continue the request
      request.continue();
    });

    page.on('response', (response) => {
      const url = response.url();
      const request = monitor.requests.find((req) => req.url === url && !req.status);

      if (request) {
        request.status = response.status();
        request.responseSize = response.headers()['content-length']
          ? parseInt(response.headers()['content-length'])
          : undefined;
      }
    });
  }

  /**
   * Set up SPA navigation detection
   */
  private async setupSPADetection(page: Page, _scanId: string): Promise<void> {
    await page.evaluateOnNewDocument((_scanId) => {
      interface WcagDynamicData {
        changes: DynamicContentChange[];
        navigations: SPANavigation[];
        frameworks: string[];
      }

      // Monitor history API changes
      const originalPushState = history.pushState;
      const originalReplaceState = history.replaceState;

      history.pushState = function (...args: Parameters<typeof history.pushState>) {
        const navigation: SPANavigation = {
          from: window.location.href,
          to: args[2] ? new URL(args[2], window.location.origin).href : window.location.href,
          method: 'pushState' as const,
          timestamp: Date.now(),
          triggeredByUser: true,
        };

        (
          window as unknown as { wcagDynamicData: WcagDynamicData }
        ).wcagDynamicData.navigations.push(navigation);
        return originalPushState.apply(this, args);
      };

      history.replaceState = function (...args: Parameters<typeof history.replaceState>) {
        const navigation: SPANavigation = {
          from: window.location.href,
          to: args[2] ? new URL(args[2], window.location.origin).href : window.location.href,
          method: 'replaceState' as const,
          timestamp: Date.now(),
          triggeredByUser: true,
        };

        (
          window as unknown as { wcagDynamicData: WcagDynamicData }
        ).wcagDynamicData.navigations.push(navigation);
        return originalReplaceState.apply(this, args);
      };

      // Monitor popstate events
      window.addEventListener('popstate', () => {
        const navigation: SPANavigation = {
          from: document.referrer || 'unknown',
          to: window.location.href,
          method: 'popstate' as const,
          timestamp: Date.now(),
          triggeredByUser: true,
        };

        (
          window as unknown as { wcagDynamicData: WcagDynamicData }
        ).wcagDynamicData.navigations.push(navigation);
      });

      // Monitor hash changes
      window.addEventListener('hashchange', () => {
        const navigation: SPANavigation = {
          from: document.referrer || 'unknown',
          to: window.location.href,
          method: 'hashchange' as const,
          timestamp: Date.now(),
          triggeredByUser: true,
        };

        (
          window as unknown as { wcagDynamicData: WcagDynamicData }
        ).wcagDynamicData.navigations.push(navigation);
      });
    }, _scanId);
  }

  /**
   * Set up framework detection
   */
  private async setupFrameworkDetection(page: Page, _scanId: string): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      interface WcagDynamicData {
        changes: DynamicContentChange[];
        navigations: SPANavigation[];
        frameworks: string[];
      }

      // Detect common frameworks
      const detectFrameworks = (): string[] => {
        const frameworks: string[] = [];

        // React detection
        if (
          (window as unknown as { React?: unknown }).React ||
          document.querySelector('[data-reactroot]') ||
          document.querySelector('[data-react-helmet]')
        ) {
          frameworks.push('React');
        }

        // Vue detection
        if (
          (window as unknown as { Vue?: unknown }).Vue ||
          document.querySelector('[data-v-]') ||
          document.querySelector('.v-application')
        ) {
          frameworks.push('Vue');
        }

        // Angular detection
        if (
          (window as unknown as { ng?: unknown; angular?: unknown }).ng ||
          (window as unknown as { ng?: unknown; angular?: unknown }).angular ||
          document.querySelector('[ng-app]') ||
          document.querySelector('[ng-controller]') ||
          document.querySelector('app-root')
        ) {
          frameworks.push('Angular');
        }

        // Svelte detection
        if (document.querySelector('[class*="svelte-"]')) {
          frameworks.push('Svelte');
        }

        // Next.js detection
        if (
          (window as unknown as { __NEXT_DATA__?: unknown }).__NEXT_DATA__ ||
          document.querySelector('#__next')
        ) {
          frameworks.push('Next.js');
        }

        // Nuxt.js detection
        if (
          (window as unknown as { __NUXT__?: unknown }).__NUXT__ ||
          document.querySelector('#__nuxt')
        ) {
          frameworks.push('Nuxt.js');
        }

        // Gatsby detection
        if (
          (window as unknown as { ___gatsby?: unknown }).___gatsby ||
          document.querySelector('#___gatsby')
        ) {
          frameworks.push('Gatsby');
        }

        return frameworks;
      };

      // Store detected frameworks
      (window as unknown as { wcagDynamicData: WcagDynamicData }).wcagDynamicData.frameworks =
        detectFrameworks();

      // Re-detect after DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          (window as unknown as { wcagDynamicData: WcagDynamicData }).wcagDynamicData.frameworks =
            detectFrameworks();
        });
      }
    });
  }

  /**
   * Collect final data from page
   */
  private async collectFinalData(page: Page): Promise<{
    changes: DynamicContentChange[];
    navigations: SPANavigation[];
    frameworks: string[];
  }> {
    try {
      return await page.evaluate(() => {
        const data = (
          window as unknown as {
            wcagDynamicData?: {
              changes: DynamicContentChange[];
              navigations: SPANavigation[];
              frameworks: string[];
            };
          }
        ).wcagDynamicData || {
          changes: [],
          navigations: [],
          frameworks: [],
        };

        return {
          changes: data.changes || [],
          navigations: data.navigations || [],
          frameworks: data.frameworks || [],
        };
      });
    } catch (error) {
      logger.warn('Failed to collect final dynamic content data:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        changes: [],
        navigations: [],
        frameworks: [],
      };
    }
  }

  /**
   * Analyze monitoring results
   */
  private async analyzeResults(
    monitor: {
      changes: DynamicContentChange[];
      requests: NetworkRequest[];
      navigations: SPANavigation[];
    },
    finalData: {
      changes: DynamicContentChange[];
      navigations: SPANavigation[];
      frameworks: string[];
    },
    duration: number,
  ): Promise<DynamicContentResult> {
    const allChanges = [...monitor.changes, ...finalData.changes];
    const allNavigations = [...monitor.navigations, ...finalData.navigations];
    const allRequests = monitor.requests;

    // Calculate content stability score
    const changesPerSecond = allChanges.length / (duration / 1000);
    const stabilityScore = Math.max(0, 100 - changesPerSecond * 10);

    // Determine if it's likely a SPA
    const isLikelySPA =
      allNavigations.length > 0 ||
      finalData.frameworks.some((fw: string) =>
        ['React', 'Vue', 'Angular', 'Svelte'].includes(fw),
      ) ||
      allRequests.filter((req: NetworkRequest) => req.isXHR || req.isFetch).length > 5;

    // Generate recommendations
    const recommendations = this.generateRecommendations(
      allChanges,
      allNavigations,
      allRequests,
      isLikelySPA,
      stabilityScore,
    );

    return {
      domChanges: allChanges,
      networkRequests: allRequests,
      spaNavigations: allNavigations,
      hasDynamicContent: allChanges.length > 0 || allRequests.length > 0,
      isLikelySPA,
      contentStabilityScore: Math.round(stabilityScore),
      recommendations,
      detectedFrameworks: finalData.frameworks,
    };
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(
    changes: DynamicContentChange[],
    navigations: SPANavigation[],
    requests: NetworkRequest[],
    isLikelySPA: boolean,
    stabilityScore: number,
  ): string[] {
    const recommendations: string[] = [];

    if (isLikelySPA) {
      recommendations.push(
        'Detected SPA - ensure accessibility testing covers dynamic route changes',
      );
      recommendations.push('Verify that screen readers announce content changes appropriately');
    }

    if (stabilityScore < 50) {
      recommendations.push('High content volatility detected - consider longer scan durations');
      recommendations.push('Implement wait strategies for dynamic content to stabilize');
    }

    if (changes.length > 100) {
      recommendations.push(
        'Frequent DOM changes detected - focus on accessibility of dynamic updates',
      );
    }

    if (requests.filter((req) => req.isXHR || req.isFetch).length > 10) {
      recommendations.push('High AJAX activity - ensure loading states are accessible');
      recommendations.push('Verify that dynamically loaded content maintains accessibility');
    }

    if (navigations.length > 0) {
      recommendations.push(
        'Client-side navigation detected - test focus management during route changes',
      );
      recommendations.push('Ensure page titles and landmarks update appropriately');
    }

    return recommendations;
  }

  /**
   * Get monitoring statistics
   */
  getActiveMonitors(): string[] {
    return Array.from(this.activeMonitors.keys());
  }

  /**
   * Force stop all monitoring
   */
  stopAllMonitoring(): void {
    this.activeMonitors.clear();
    logger.info('🔄 All dynamic content monitoring stopped');
  }
}

export default DynamicContentMonitor;
