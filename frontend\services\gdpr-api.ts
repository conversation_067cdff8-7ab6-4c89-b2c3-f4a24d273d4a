import { ApiResponse } from '@/types/gdpr';
import { GdprScanRequest, GdprScanResult, CookieAnalysisResult } from '@/types/gdpr';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export class GdprApiService {
  /**
   * Start GDPR compliance scan - REAL SCANNING ONLY
   */
  static async startScan(scanRequest: GdprScanRequest): Promise<GdprScanResult> {
    try {
      // eslint-disable-next-line no-console
      console.log('🔍 Starting GDPR scan:', scanRequest.targetUrl);

      const token = await this.getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(scanRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Scan failed');
      }

      // eslint-disable-next-line no-console
      console.log('✅ GDPR scan completed:', apiResponse.data.scanId);
      return apiResponse.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ GDPR scan failed:', error);
      throw new Error(
        `GDPR scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get scan result by ID - REAL DATA ONLY
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult> {
    try {
      const token = await this.getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<GdprScanResult> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve scan result');
      }

      return apiResponse.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      throw new Error(
        `Failed to retrieve scan result: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get user's scan history - REAL DATA ONLY
   */
  static async getScanHistory(limit: number = 50, offset: number = 0): Promise<GdprScanResult[]> {
    try {
      const token = await this.getAuthToken();
      console.log(
        '🔑 Auth token status:',
        token ? `Token present (${token.substring(0, 20)}...)` : 'No token',
      );

      const url = `${API_BASE_URL}/api/v1/compliance/gdpr/scans?limit=${limit}&offset=${offset}`;
      // eslint-disable-next-line no-console
      console.log('📡 Fetching scan history from:', url);

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      // eslint-disable-next-line no-console
      console.log('📡 Scan history response status:', response.status);
      // eslint-disable-next-line no-console
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        // eslint-disable-next-line no-console
        console.error('❌ Scan history error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const apiResponse: ApiResponse<{
        scans: GdprScanResult[];
        pagination: unknown /* eslint-disable-line @typescript-eslint/no-explicit-any */;
      }> = await response.json();
      console.log('📊 Scan history API response:', {
        success: apiResponse.success,
        dataType: typeof apiResponse.data,
        scansCount: apiResponse.data?.scans?.length || 0,
        pagination: apiResponse.data?.pagination,
      });

      if (!apiResponse.success) {
        // eslint-disable-next-line no-console
        console.error('❌ API returned success=false:', apiResponse.error);
        throw new Error(apiResponse.error?.message || 'Failed to retrieve scan history');
      }

      // Extract scans array from the response data structure
      const scans = apiResponse.data?.scans || [];
      // eslint-disable-next-line no-console
      console.log('✅ Returning', scans.length, 'GDPR scans');

      return scans;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to retrieve GDPR scan history:', error);
      throw new Error(
        `Failed to retrieve scan history: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get cookie analysis for scan - REAL DATA ONLY
   */
  static async getCookieAnalysis(scanId: string): Promise<CookieAnalysisResult[]> {
    try {
      const token = await this.getAuthToken();
      const response = await fetch(
        `${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/cookies`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const apiResponse: ApiResponse<CookieAnalysisResult[]> = await response.json();

      if (!apiResponse.success) {
        throw new Error(apiResponse.error?.message || 'Failed to retrieve cookie analysis');
      }

      return apiResponse.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to retrieve cookie analysis:', error);
      throw new Error(
        `Failed to retrieve cookie analysis: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update manual review status for a specific check
   */
  static async updateManualReview(
    scanId: string,
    ruleId: string,
    assessment: string,
    notes?: string,
    reviewerName?: string,
  ): Promise<{ success: boolean; updatedScore?: number; message?: string }> {
    try {
      const token = await this.getAuthToken();
      const response = await fetch(
        `${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/manual-review`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            ruleId,
            assessment,
            notes: notes || '',
            reviewerName: reviewerName || 'Legal Reviewer',
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        return {
          success: true,
          updatedScore: data.data?.updatedScore,
          message: data.data?.message || 'Manual review updated successfully',
        };
      } else {
        throw new Error(data.message || 'Failed to update manual review');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to update manual review:', error);
      throw error;
    }
  }

  /**
   * Export scan report - REAL DATA ONLY
   */
  static async exportScanReport(scanId: string, format: 'pdf' | 'json' | 'csv'): Promise<Blob> {
    try {
      const token = await this.getAuthToken();
      const response = await fetch(`${API_BASE_URL}/api/v1/compliance/gdpr/scan/${scanId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ format }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ Failed to export scan report:', error);
      throw new Error(
        `Failed to export scan report: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get authentication token from Keycloak
   */
  private static async getAuthToken(): Promise<string> {
    try {
      // Try to get token from Keycloak instance
      if (typeof window !== 'undefined') {
        const { getKeycloakInstance } = await import('@/lib/keycloak');
        const keycloak = getKeycloakInstance();

        if (keycloak && keycloak.authenticated && keycloak.token) {
          // Update token if it's about to expire
          await keycloak.updateToken(30);
          return keycloak.token;
        }
      }

      // Fallback to localStorage for development
      return localStorage.getItem('authToken') || '';
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to get auth token:', error);
      return localStorage.getItem('authToken') || '';
    }
  }
}
