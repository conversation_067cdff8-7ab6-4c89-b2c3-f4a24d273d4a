/**
 * WCAG Test Runner
 * Comprehensive test execution and coverage reporting
 */

import { execSync } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

interface TestResults {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  coverage: {
    statements: number;
    branches: number;
    functions: number;
    lines: number;
  };
  duration: number;
  errors: string[];
}

interface TestSuite {
  name: string;
  pattern: string;
  description: string;
  required: boolean;
}

export class WcagTestRunner {
  private readonly testSuites: TestSuite[] = [
    {
      name: 'newly-registered-checks',
      pattern: '__tests__/newly-registered-checks.test.ts',
      description: 'Tests for WCAG-024 through WCAG-038',
      required: true,
    },
    {
      name: 'enhanced-features-integration',
      pattern: '__tests__/enhanced-features-integration.test.ts',
      description: 'Enhanced evidence system integration tests',
      required: true,
    },
    {
      name: 'existing-checks',
      pattern: '__tests__/*.test.ts',
      description: 'All existing WCAG check tests',
      required: false,
    },
  ];

  /**
   * Run all WCAG tests with coverage reporting
   */
  async runAllTests(): Promise<TestResults> {
    console.log('🧪 Starting comprehensive WCAG test suite...');

    const startTime = Date.now();
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    const errors: string[] = [];

    try {
      // Run Jest with coverage
      const jestCommand = [
        'npx jest',
        '--testPathPattern="src/compliance/wcag"',
        '--coverage',
        '--coverageDirectory=coverage/wcag',
        '--coverageReporters=text,lcov,html,json-summary',
        '--verbose',
        '--detectOpenHandles',
        '--forceExit',
      ].join(' ');

      console.log(`📋 Executing: ${jestCommand}`);

      const output = execSync(jestCommand, {
        cwd: path.resolve(__dirname, '../../../..'),
        encoding: 'utf8',
        stdio: 'pipe',
      });

      // Parse Jest output for test results
      const testResults = this.parseJestOutput(output);
      totalTests = testResults.total;
      passedTests = testResults.passed;
      failedTests = testResults.failed;

      console.log('✅ All tests completed successfully');
    } catch (error: any) {
      console.error('❌ Test execution failed:', error.message);
      errors.push(error.message);

      // Try to parse partial results from error output
      if (error.stdout) {
        const partialResults = this.parseJestOutput(error.stdout);
        totalTests = partialResults.total;
        passedTests = partialResults.passed;
        failedTests = partialResults.failed;
      }
    }

    // Read coverage report
    const coverage = await this.readCoverageReport();

    const duration = Date.now() - startTime;

    const results: TestResults = {
      totalTests,
      passedTests,
      failedTests,
      coverage,
      duration,
      errors,
    };

    this.printTestSummary(results);
    return results;
  }

  /**
   * Run specific test suite
   */
  async runTestSuite(suiteName: string): Promise<TestResults> {
    const suite = this.testSuites.find((s) => s.name === suiteName);
    if (!suite) {
      throw new Error(`Test suite '${suiteName}' not found`);
    }

    console.log(`🧪 Running test suite: ${suite.description}`);

    const startTime = Date.now();

    try {
      const jestCommand = ['npx jest', `--testPathPattern="${suite.pattern}"`, '--verbose'].join(
        ' ',
      );

      const output = execSync(jestCommand, {
        cwd: path.resolve(__dirname, '../../../..'),
        encoding: 'utf8',
      });

      const testResults = this.parseJestOutput(output);
      const duration = Date.now() - startTime;

      return {
        totalTests: testResults.total,
        passedTests: testResults.passed,
        failedTests: testResults.failed,
        coverage: { statements: 0, branches: 0, functions: 0, lines: 0 },
        duration,
        errors: [],
      };
    } catch (error: any) {
      console.error(`❌ Test suite '${suiteName}' failed:`, error.message);
      throw error;
    }
  }

  /**
   * Validate test coverage meets requirements
   */
  validateCoverage(results: TestResults): boolean {
    const requirements = {
      statements: 80,
      branches: 75,
      functions: 80,
      lines: 80,
    };

    const meetsRequirements =
      results.coverage.statements >= requirements.statements &&
      results.coverage.branches >= requirements.branches &&
      results.coverage.functions >= requirements.functions &&
      results.coverage.lines >= requirements.lines;

    if (!meetsRequirements) {
      console.warn('⚠️ Coverage requirements not met:');
      console.warn(
        `  Statements: ${results.coverage.statements}% (required: ${requirements.statements}%)`,
      );
      console.warn(
        `  Branches: ${results.coverage.branches}% (required: ${requirements.branches}%)`,
      );
      console.warn(
        `  Functions: ${results.coverage.functions}% (required: ${requirements.functions}%)`,
      );
      console.warn(`  Lines: ${results.coverage.lines}% (required: ${requirements.lines}%)`);
    }

    return meetsRequirements;
  }

  /**
   * Parse Jest output to extract test results
   */
  private parseJestOutput(output: string): { total: number; passed: number; failed: number } {
    const lines = output.split('\n');
    let total = 0;
    let passed = 0;
    let failed = 0;

    // Look for Jest summary line
    for (const line of lines) {
      if (line.includes('Tests:')) {
        const match = line.match(/Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/);
        if (match) {
          failed = parseInt(match[1], 10);
          passed = parseInt(match[2], 10);
          total = parseInt(match[3], 10);
          break;
        }

        // Alternative format: "Tests: X passed, Y total"
        const altMatch = line.match(/Tests:\s+(\d+)\s+passed,\s+(\d+)\s+total/);
        if (altMatch) {
          passed = parseInt(altMatch[1], 10);
          total = parseInt(altMatch[2], 10);
          failed = total - passed;
          break;
        }
      }
    }

    return { total, passed, failed };
  }

  /**
   * Read coverage report from Jest output
   */
  private async readCoverageReport(): Promise<TestResults['coverage']> {
    const coveragePath = path.resolve(__dirname, '../../../../coverage/wcag/coverage-summary.json');

    try {
      if (fs.existsSync(coveragePath)) {
        const coverageData = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        const total = coverageData.total;

        return {
          statements: total.statements.pct,
          branches: total.branches.pct,
          functions: total.functions.pct,
          lines: total.lines.pct,
        };
      }
    } catch (error) {
      console.warn('⚠️ Could not read coverage report:', error);
    }

    return { statements: 0, branches: 0, functions: 0, lines: 0 };
  }

  /**
   * Print comprehensive test summary
   */
  private printTestSummary(results: TestResults): void {
    console.log('\n📊 WCAG Test Results Summary');
    console.log('================================');
    console.log(`Total Tests: ${results.totalTests}`);
    console.log(`Passed: ${results.passedTests} ✅`);
    console.log(`Failed: ${results.failedTests} ${results.failedTests > 0 ? '❌' : '✅'}`);
    console.log(`Duration: ${(results.duration / 1000).toFixed(2)}s`);

    console.log('\n📈 Coverage Report');
    console.log('==================');
    console.log(`Statements: ${results.coverage.statements.toFixed(1)}%`);
    console.log(`Branches: ${results.coverage.branches.toFixed(1)}%`);
    console.log(`Functions: ${results.coverage.functions.toFixed(1)}%`);
    console.log(`Lines: ${results.coverage.lines.toFixed(1)}%`);

    if (results.errors.length > 0) {
      console.log('\n❌ Errors');
      console.log('=========');
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    const coverageMet = this.validateCoverage(results);
    const allTestsPassed = results.failedTests === 0;

    console.log('\n🎯 Overall Status');
    console.log('================');
    console.log(`Tests: ${allTestsPassed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Coverage: ${coverageMet ? '✅ MEETS REQUIREMENTS' : '⚠️ BELOW REQUIREMENTS'}`);
    console.log(`Ready for Production: ${allTestsPassed && coverageMet ? '✅ YES' : '❌ NO'}`);
  }
}

// CLI execution
if (require.main === module) {
  const runner = new WcagTestRunner();

  const command = process.argv[2];

  if (command === 'suite' && process.argv[3]) {
    runner
      .runTestSuite(process.argv[3])
      .then((results) => {
        process.exit(results.failedTests > 0 ? 1 : 0);
      })
      .catch((error) => {
        console.error('Test execution failed:', error);
        process.exit(1);
      });
  } else {
    runner
      .runAllTests()
      .then((results) => {
        const success = results.failedTests === 0 && runner.validateCoverage(results);
        process.exit(success ? 0 : 1);
      })
      .catch((error) => {
        console.error('Test execution failed:', error);
        process.exit(1);
      });
  }
}
