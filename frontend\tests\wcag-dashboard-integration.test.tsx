/**
 * WCAG Dashboard Integration Tests
 * Tests for Part 9 - Dashboard Integration & Navigation
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
  useParams: jest.fn(),
}));

// Mock components
jest.mock('@/components/wcag/WcagScanForm', () => ({
  WcagScanForm: () => <div data-testid="wcag-scan-form">WCAG Scan Form</div>,
}));

jest.mock('@/components/wcag/WcagScanProgress', () => ({
  WcagScanProgress: () => <div data-testid="wcag-scan-progress">WCAG Scan Progress</div>,
}));

jest.mock('@/components/wcag/WcagScanOverview', () => ({
  WcagScanOverview: () => <div data-testid="wcag-scan-overview">WCAG Scan Overview</div>,
}));

// Import components to test
import WcagDashboardPage from '@/app/dashboard/wcag/page';
import WcagScanPage from '@/app/dashboard/wcag/scan/page';
import WcagHistoryPage from '@/app/dashboard/wcag/history/page';
import WcagReportsPage from '@/app/dashboard/wcag/reports/page';
import WcagSettingsPage from '@/app/dashboard/wcag/settings/page';
import WcagScanDetailsPage from '@/app/dashboard/wcag/scan/[scanId]/page';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

describe('WCAG Dashboard Integration', () => {
  const mockPush = jest.fn();
  const mockRouter = {
    push: mockPush,
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    replace: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  describe('WCAG Dashboard Page', () => {
    it('renders dashboard with navigation and quick actions', async () => {
      render(<WcagDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('WCAG Compliance Dashboard')).toBeInTheDocument();
      });

      // Check for quick action buttons
      expect(screen.getByText('Start New Scan')).toBeInTheDocument();
      expect(screen.getByText('View History')).toBeInTheDocument();
      expect(screen.getByText('Generate Reports')).toBeInTheDocument();
      expect(screen.getByText('Settings')).toBeInTheDocument();
    });

    it('navigates to scan page when start scan is clicked', async () => {
      render(<WcagDashboardPage />);

      await waitFor(() => {
        const startScanButton = screen.getByText('Start New Scan');
        fireEvent.click(startScanButton);
      });

      expect(mockPush).toHaveBeenCalledWith('/dashboard/wcag/scan');
    });

    it('navigates to history page when view history is clicked', async () => {
      render(<WcagDashboardPage />);

      await waitFor(() => {
        const historyButton = screen.getByText('View History');
        fireEvent.click(historyButton);
      });

      expect(mockPush).toHaveBeenCalledWith('/dashboard/wcag/history');
    });
  });

  describe('WCAG Scan Page', () => {
    it('renders scan form and progress components', async () => {
      render(<WcagScanPage />);

      await waitFor(() => {
        expect(screen.getByText('WCAG Compliance Scan')).toBeInTheDocument();
        expect(screen.getByTestId('wcag-scan-form')).toBeInTheDocument();
      });
    });

    it('shows scan guidelines and queue status', async () => {
      render(<WcagScanPage />);

      await waitFor(() => {
        expect(screen.getByText('Scan Guidelines')).toBeInTheDocument();
        expect(screen.getByText('Queue Status')).toBeInTheDocument();
      });
    });
  });

  describe('WCAG History Page', () => {
    it('renders history page with filters and search', async () => {
      render(<WcagHistoryPage />);

      await waitFor(() => {
        expect(screen.getByText('Scan History')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Search by URL...')).toBeInTheDocument();
      });
    });

    it('shows pagination controls when needed', async () => {
      render(<WcagHistoryPage />);

      await waitFor(() => {
        // Check for pagination elements
        expect(screen.getByText('per page')).toBeInTheDocument();
      });
    });
  });

  describe('WCAG Reports Page', () => {
    it('renders reports page with export options', async () => {
      render(<WcagReportsPage />);

      await waitFor(() => {
        expect(screen.getByText('WCAG Reports')).toBeInTheDocument();
        expect(screen.getByText('Export Settings')).toBeInTheDocument();
      });
    });

    it('shows export format options', async () => {
      render(<WcagReportsPage />);

      await waitFor(() => {
        expect(screen.getByText('Export Format')).toBeInTheDocument();
        expect(screen.getByText('Include in Export')).toBeInTheDocument();
      });
    });
  });

  describe('WCAG Settings Page', () => {
    it('renders settings page with configuration options', async () => {
      render(<WcagSettingsPage />);

      await waitFor(() => {
        expect(screen.getByText('WCAG Settings')).toBeInTheDocument();
        expect(screen.getByText('Default Scan Settings')).toBeInTheDocument();
      });
    });

    it('shows save and reset buttons', async () => {
      render(<WcagSettingsPage />);

      await waitFor(() => {
        expect(screen.getByText('Save Changes')).toBeInTheDocument();
        expect(screen.getByText('Reset to Defaults')).toBeInTheDocument();
      });
    });
  });

  describe('WCAG Scan Details Page', () => {
    beforeEach(() => {
      // Mock useParams for scan details page
      const mockUseParams = require('next/navigation').useParams;
      mockUseParams.mockReturnValue({ scanId: 'test-scan-123' });
    });

    it('renders scan details with export options', async () => {
      render(<WcagScanDetailsPage />);

      await waitFor(() => {
        expect(screen.getByText('Scan Details')).toBeInTheDocument();
        expect(screen.getByText('Export PDF')).toBeInTheDocument();
        expect(screen.getByText('Export JSON')).toBeInTheDocument();
        expect(screen.getByText('Export CSV')).toBeInTheDocument();
      });
    });
  });

  describe('WCAG Breadcrumb Navigation', () => {
    it('renders breadcrumb navigation correctly', () => {
      const mockUsePathname = require('next/navigation').usePathname;
      mockUsePathname.mockReturnValue('/dashboard/wcag/scan');

      render(<WcagBreadcrumb />);

      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Compliance')).toBeInTheDocument();
      expect(screen.getByText('WCAG')).toBeInTheDocument();
      expect(screen.getByText('Start Scan')).toBeInTheDocument();
    });

    it('shows correct breadcrumb for history page', () => {
      const mockUsePathname = require('next/navigation').usePathname;
      mockUsePathname.mockReturnValue('/dashboard/wcag/history');

      render(<WcagBreadcrumb />);

      expect(screen.getByText('Scan History')).toBeInTheDocument();
    });

    it('shows correct breadcrumb for scan details page', () => {
      const mockUsePathname = require('next/navigation').usePathname;
      mockUsePathname.mockReturnValue('/dashboard/wcag/scan/test-scan-123');

      render(<WcagBreadcrumb />);

      expect(screen.getByText('Scan History')).toBeInTheDocument();
      expect(screen.getByText('Scan test-sca...')).toBeInTheDocument();
    });
  });

  describe('Navigation Integration', () => {
    it('all pages include breadcrumb navigation', async () => {
      const pages = [
        WcagDashboardPage,
        WcagScanPage,
        WcagHistoryPage,
        WcagReportsPage,
        WcagSettingsPage,
      ];

      for (const PageComponent of pages) {
        const { unmount } = render(<PageComponent />);

        await waitFor(() => {
          // Each page should have breadcrumb navigation
          expect(screen.getByText('Dashboard')).toBeInTheDocument();
        });

        unmount();
      }
    });

    it('layout provides consistent navigation structure', () => {
      // Test that layout component provides consistent navigation
      // This would be tested with the actual layout component
      expect(true).toBe(true); // Placeholder for layout tests
    });
  });

  describe('Context Integration', () => {
    it('all pages are wrapped with WcagProvider', async () => {
      // Test that all pages have access to WCAG context
      const pages = [
        WcagDashboardPage,
        WcagScanPage,
        WcagHistoryPage,
        WcagReportsPage,
        WcagSettingsPage,
        WcagScanDetailsPage,
      ];

      for (const PageComponent of pages) {
        const { unmount } = render(<PageComponent />);

        // Pages should render without context errors
        await waitFor(() => {
          expect(document.body).toBeInTheDocument();
        });

        unmount();
      }
    });
  });

  describe('Responsive Design', () => {
    it('dashboard adapts to mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<WcagDashboardPage />);

      await waitFor(() => {
        expect(screen.getByText('WCAG Compliance Dashboard')).toBeInTheDocument();
      });

      // Check that mobile-specific classes are applied
      // This would require more specific testing of responsive behavior
    });
  });

  describe('Error Handling', () => {
    it('handles missing scan data gracefully', async () => {
      const mockUseParams = require('next/navigation').useParams;
      mockUseParams.mockReturnValue({ scanId: 'non-existent-scan' });

      render(<WcagScanDetailsPage />);

      await waitFor(() => {
        expect(screen.getByText('Scan Not Found')).toBeInTheDocument();
      });
    });
  });
});
