/**
 * AccessibilityChecker.org Style Evidence Display
 * Matches the visual style and functionality of AccessibilityChecker.org
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ChevronDown,
  ChevronRight,
  Code,
  ExternalLink,
  Info,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Clock,
  Target,
  Zap,
  Copy,
  Eye,
} from 'lucide-react';
import {
  WcagEvidenceEnhanced,
  WcagFixExample,
  WcagElementCounts,
  WcagPerformanceMetrics,
} from '@/types/wcag';

interface AccessibilityCheckerStyleEvidenceProps {
  evidence: WcagEvidenceEnhanced[];
  showFixExamples?: boolean;
  showElementCounts?: boolean;
  showPerformanceMetrics?: boolean;
}

const SeverityIcon = ({ severity }: { severity?: string }) => {
  switch (severity) {
    case 'critical':
      return <XCircle className="h-5 w-5 text-red-600" />;
    case 'error':
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    case 'info':
      return <Info className="h-5 w-5 text-blue-500" />;
    default:
      return <CheckCircle className="h-5 w-5 text-green-500" />;
  }
};

const ElementCountBadge = ({ count, severity }: { count: number; severity?: string }) => {
  const badgeClass = severity === 'error' || severity === 'critical' 
    ? 'bg-red-100 text-red-800 border-red-200'
    : severity === 'warning'
    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
    : 'bg-blue-100 text-blue-800 border-blue-200';

  return (
    <Badge className={`${badgeClass} font-medium`}>
      {count} element{count !== 1 ? 's' : ''}
    </Badge>
  );
};

const CodeBlock = ({ code, language = 'html' }: { code: string; language?: string }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative">
      <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
        <code>{code}</code>
      </pre>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleCopy}
        className="absolute top-2 right-2 h-8 w-8 p-0 bg-gray-800 hover:bg-gray-700 text-gray-300"
      >
        {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
      </Button>
    </div>
  );
};

const FixExampleDisplay = ({ fixExample }: { fixExample: WcagFixExample }) => {
  const [activeTab, setActiveTab] = useState('before');

  return (
    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
      <div className="flex items-center gap-2 mb-3">
        <Zap className="h-5 w-5 text-green-600" />
        <h4 className="font-semibold text-green-800">How to Fix</h4>
      </div>
      
      <p className="text-sm text-green-700 mb-4">{fixExample.description}</p>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="before" className="text-red-600">❌ Before</TabsTrigger>
          <TabsTrigger value="after" className="text-green-600">✅ After</TabsTrigger>
        </TabsList>
        
        <TabsContent value="before" className="mt-3">
          <CodeBlock code={fixExample.before} />
        </TabsContent>
        
        <TabsContent value="after" className="mt-3">
          <CodeBlock code={fixExample.after} />
        </TabsContent>
      </Tabs>

      {fixExample.codeExample && (
        <Collapsible className="mt-4">
          <CollapsibleTrigger asChild>
            <Button variant="outline" size="sm" className="w-full">
              <Code className="h-4 w-4 mr-2" />
              View Complete Example
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-2">
            <CodeBlock code={fixExample.codeExample} />
          </CollapsibleContent>
        </Collapsible>
      )}

      {fixExample.resources && fixExample.resources.length > 0 && (
        <div className="mt-4">
          <h5 className="text-sm font-medium text-green-800 mb-2">Learn More:</h5>
          <div className="space-y-1">
            {fixExample.resources.map((resource, index) => (
              <a
                key={index}
                href={resource}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
              >
                <ExternalLink className="h-3 w-3" />
                {resource.replace('https://', '').split('/')[0]}
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const EvidenceItem = ({ 
  evidence, 
  showFixExamples, 
  showElementCounts 
}: {
  evidence: WcagEvidenceEnhanced;
  showFixExamples: boolean;
  showElementCounts: boolean;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="border border-gray-200 rounded-lg mb-3">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <SeverityIcon severity={evidence.severity} />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2 flex-wrap">
                <h4 className="font-medium text-gray-900">{evidence.description}</h4>
                {evidence.elementCount && evidence.elementCount > 0 && (
                  <ElementCountBadge count={evidence.elementCount} severity={evidence.severity} />
                )}
              </div>
              
              {evidence.type === 'code' ? (
                <div className="mb-2">
                  <CodeBlock code={evidence.value} />
                </div>
              ) : (
                <p className="text-gray-700 mb-2">{evidence.value}</p>
              )}
              
              {evidence.selector && (
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xs text-gray-500">Selector:</span>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded border">
                    {evidence.selector}
                  </code>
                </div>
              )}
            </div>
          </div>
          
          {(evidence.fixExample || (evidence.affectedSelectors && evidence.affectedSelectors.length > 0)) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-blue-600 hover:text-blue-800"
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  Hide
                </>
              ) : (
                <>
                  <ChevronRight className="h-4 w-4 mr-1" />
                  Fix
                </>
              )}
            </Button>
          )}
        </div>

        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            {/* Affected Selectors */}
            {showElementCounts && evidence.affectedSelectors && evidence.affectedSelectors.length > 0 && (
              <div className="mb-4">
                <h5 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-500" />
                  Affected Elements ({evidence.affectedSelectors.length}):
                </h5>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {evidence.affectedSelectors.slice(0, 10).map((selector, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 border rounded">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <code className="text-xs text-gray-700 flex-1">
                        {selector}
                      </code>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-blue-600">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  {evidence.affectedSelectors.length > 10 && (
                    <p className="text-xs text-gray-500 italic text-center">
                      ... and {evidence.affectedSelectors.length - 10} more elements
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Fix Example */}
            {showFixExamples && evidence.fixExample && (
              <FixExampleDisplay fixExample={evidence.fixExample} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export const AccessibilityCheckerStyleEvidence: React.FC<AccessibilityCheckerStyleEvidenceProps> = ({
  evidence,
  showFixExamples = true,
  showElementCounts = true,
}) => {
  if (!evidence || evidence.length === 0) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>No evidence available for this check.</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-3">
      {evidence.map((item, index) => (
        <EvidenceItem
          key={index}
          evidence={item}
          showFixExamples={showFixExamples}
          showElementCounts={showElementCounts}
        />
      ))}
    </div>
  );
};

export default AccessibilityCheckerStyleEvidence;
