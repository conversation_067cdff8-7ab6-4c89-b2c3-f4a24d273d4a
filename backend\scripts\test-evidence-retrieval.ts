/**
 * Test Evidence Retrieval Script
 * Tests if evidence is being properly retrieved from the database
 */

import { WcagDatabase } from '../src/compliance/wcag/database/wcag-database';
import logger from '../src/utils/logger';

async function testEvidenceRetrieval() {
  try {
    logger.info('🧪 Testing evidence retrieval from database...');

    const wcagDatabase = new WcagDatabase();

    // Get all scans to find one with evidence
    const scansResult = await wcagDatabase.getUserScans('test-user-id', {
      page: 1,
      limit: 10,
      sortBy: 'scanTimestamp',
      sortOrder: 'desc',
    });

    if (scansResult.scans.length === 0) {
      logger.info('📋 No scans found in database');
      return;
    }

    logger.info(`📋 Found ${scansResult.scans.length} scans`);

    // Test the first scan
    const firstScan = scansResult.scans[0];
    logger.info(`🔍 Testing scan: ${firstScan.scanId}`);

    const scanDetails = await wcagDatabase.getScanById(firstScan.scanId, 'test-user-id');

    if (!scanDetails) {
      logger.error('❌ Could not retrieve scan details');
      return;
    }

    logger.info(`✅ Retrieved scan details for: ${scanDetails.scanId}`);
    logger.info(`📊 Total checks: ${scanDetails.checks.length}`);

    // Check evidence for each check
    let checksWithEvidence = 0;
    let totalEvidenceItems = 0;

    for (const check of scanDetails.checks) {
      if (check.evidence && check.evidence.length > 0) {
        checksWithEvidence++;
        totalEvidenceItems += check.evidence.length;
        
        logger.info(`🔍 Check ${check.ruleId} (${check.ruleName}): ${check.evidence.length} evidence items`);
        
        // Log first evidence item details
        if (check.evidence[0]) {
          const evidence = check.evidence[0];
          logger.info(`   📝 Evidence: ${evidence.type} - ${evidence.description}`);
          logger.info(`   📄 Value: ${evidence.value.substring(0, 100)}${evidence.value.length > 100 ? '...' : ''}`);
          if (evidence.selector) {
            logger.info(`   🎯 Selector: ${evidence.selector}`);
          }
        }
      }
    }

    logger.info(`📈 Summary:`);
    logger.info(`   - Total checks: ${scanDetails.checks.length}`);
    logger.info(`   - Checks with evidence: ${checksWithEvidence}`);
    logger.info(`   - Total evidence items: ${totalEvidenceItems}`);
    logger.info(`   - Average evidence per check: ${(totalEvidenceItems / scanDetails.checks.length).toFixed(2)}`);

    if (checksWithEvidence === 0) {
      logger.warn('⚠️ No checks have evidence! This indicates the evidence retrieval issue.');
    } else {
      logger.info('✅ Evidence retrieval is working correctly!');
    }

  } catch (error) {
    logger.error('❌ Error testing evidence retrieval:', error);
  }
}

// Run the test
testEvidenceRetrieval()
  .then(() => {
    logger.info('🏁 Evidence retrieval test completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('💥 Evidence retrieval test failed:', error);
    process.exit(1);
  });
