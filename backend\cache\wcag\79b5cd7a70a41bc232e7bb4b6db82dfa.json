{"data": {"ruleId": "WCAG-061", "ruleName": "Abbreviations", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.6.1", "level": "AAA", "status": "partial", "score": 30, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "text", "description": "Abbreviations without expanded forms", "value": "Found 16 abbreviations that need expanded forms for AAA compliance", "elementCount": 1, "affectedSelectors": ["Found", "abbreviations", "that", "need", "expanded", "forms", "for", "AAA", "compliance"], "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}}, {"type": "text", "description": "Abbreviation without expansion: \"er\"", "value": "Context: \"utcomes.\n\n\n\n\n\n\n\n\n\n\n\n\n20% increase\n\n\n\nin ER capacity from faster transfers\n\n\n\nSee H\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "utcomes", "increase", "in", "ER", "capacity", "from", "faster", "transfers", "See", "H"]}, {"type": "text", "description": "Abbreviation without expansion: \"ems\"", "value": "Context: \"Single Platform\n\n\n\n\nPre-HospitalConnect EMS to hospital teams to accelerate time to\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "Single", "Platform", "Pre-HospitalConnect", "EMS", "to", "hospital", "teams", "accelerate", "time"]}, {"type": "text", "description": "Abbreviation without expansion: \"physicians\"", "value": "Context: \"t With Your Care Teams in Mind\n\n\n\n\n\n\n\n\n\nPHYSICIANS\n\n\n\nEmpower Collaboration \n\n\n\nRead More\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "t", "With", "Your", "Care", "Teams", "in", "Mind", "PHYSICIANS", "Empower", "Collaboration", "Read", "More"]}, {"type": "text", "description": "Abbreviation without expansion: \"teams\"", "value": "Context: \"Collaboration \n\n\n\nRead More\n\n\n\n\n\n\n\n\n\nIT TEAMS\n\n\n\nConsolidate Your Technology  \n\n\n\nRea\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "Collaboration", "Read", "More", "IT", "TEAMS", "Consolidate", "Your", "Technology", "<PERSON><PERSON>"]}, {"type": "text", "description": "Abbreviation without expansion: \"nurse\"", "value": "Context: \"Your Technology  \n\n\n\nRead More\n\n\n\n\n\n\n\n\n\nNURSE TEAMS\n\n\n\nMinimize Administrative Tasks\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "Your", "Technology", "Read", "More", "NURSE", "TEAMS", "Minimize", "Administrative", "Tasks"]}, {"type": "text", "description": "Abbreviation without expansion: \"executives\"", "value": "Context: \"nistrative Tasks \n\n\n\nRead More\n\n\n\n\n\n\n\n\n\nEXECUTIVES\n\n\n\nIncrease Efficiency\n\n\n\nRead More\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "nistrative", "Tasks", "Read", "More", "EXECUTIVES", "Increase", "Efficiency"]}, {"type": "text", "description": "Abbreviation without expansion: \"rave\"", "value": "Context: \"Unified Healthcare Communication\n\n\n\n\n\n\nRAVE Reviews by\n\n\n\nReal Customers\n\n\n\n\n\n\n“Tig\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "Unified", "Healthcare", "Communication", "RAVE", "Reviews", "by", "Real", "Customers", "Tig"]}, {"type": "text", "description": "Abbreviation without expansion: \"klas\"", "value": "Context: \"uge improvement for us.”\n\n\n\nDirector\n\n\n\nKLAS Research, October 2024\n\n\n\n\n\n“TigerConne\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "uge", "improvement", "for", "us", "Director", "KLAS", "Research", "October", "TigerConne"]}, {"type": "text", "description": "Abbreviation without expansion: \"ehr\"", "value": "Context: \"face that we hope to integrate with our EHR so that we can send messages out of our\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "face", "that", "we", "hope", "to", "integrate", "with", "our", "EHR", "so", "can", "send", "messages", "out", "of"]}, {"type": "text", "description": "Abbreviation without expansion: \"cio\"", "value": "Context: \"e integrated rather than bolted on.”\n\n\n\nCIO\n\n\n\nKLAS Research, October 2024\n\n\n\n\n\n“I\"", "selector": "#wrapper", "severity": "warning", "metadata": {"scanDuration": 12187, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-061", "ruleName": "Abbreviations", "timestamp": "2025-07-13T03:29:34.298Z"}}, "elementCount": 1, "affectedSelectors": ["#wrapper", "Context", "e", "integrated", "rather", "than", "bolted", "on", "CIO", "KLAS", "Research", "October", "I"]}], "recommendations": ["Use <abbr> elements with title attributes for abbreviations", "Provide inline expansions for abbreviations on first use", "Create a glossary page for frequently used abbreviations", "Consider the reading level and technical knowledge of your audience", "Ensure abbreviations are consistently expanded throughout the site"], "executionTime": 11887, "originalScore": 50, "adjustedScore": 50, "thresholdApplied": 70, "scoringDetails": "Original: 50% → Threshold: 70% → Penalty tier: 45%+ (0.6x) → PARTIAL", "penaltyTier": 0.6, "confidenceAdjustment": 1, "enhancedStatus": "partial"}, "timestamp": 1752377374298, "hash": "9a133ca9013e4f4036caf7455261e52c", "accessCount": 1, "lastAccessed": 1752377374298, "size": 7573, "metadata": {"originalKey": "WCAG-061:053b13d2:add92319", "normalizedKey": "wcag-061_053b13d2_add92319", "savedAt": 1752377374299, "version": "1.1", "keyHash": "608ff1c3d3cab2e61950f0ef37d32334"}}