/**
 * Test Enhanced Evidence Generation
 */

import { EnhancedEvidenceGenerator, ElementIssue } from '../src/compliance/wcag/utils/enhanced-evidence-generator';

async function testEnhancedEvidenceGeneration() {
  console.log('🧪 Testing Enhanced Evidence Generation...\n');

  // Test 1: Missing Alt Text Issues
  console.log('Test 1: Missing Alt Text Issues');
  const altTextIssues: ElementIssue[] = [
    {
      selector: 'img.logo',
      element: '<img src="logo.png" class="logo">',
      issues: ['Missing alt attribute'],
      severity: 'error',
      fixSuggestion: 'Add descriptive alt text that conveys the purpose of the logo',
    },
    {
      selector: 'img.banner',
      element: '<img src="banner.jpg" class="banner">',
      issues: ['Missing alt attribute'],
      severity: 'error',
      fixSuggestion: 'Add descriptive alt text that describes the banner content',
    },
    {
      selector: 'img.icon',
      element: '<img src="icon.svg" class="icon">',
      issues: ['Missing alt attribute'],
      severity: 'error',
      fixSuggestion: 'Add alt text describing the icon purpose',
    },
  ];

  const altTextEvidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
    'WCAG-001',
    'Non-text Content',
    altTextIssues
  );

  console.log(`✅ Generated ${altTextEvidence.length} evidence items`);
  console.log(`📊 Main evidence: ${altTextEvidence[0].description}`);
  console.log(`🔢 Element count: ${altTextEvidence[0].elementCount}`);
  console.log(`🎯 Affected selectors: ${altTextEvidence[0].affectedSelectors?.length || 0}`);
  console.log(`🔧 Has fix example: ${altTextEvidence[0].fixExample ? 'Yes' : 'No'}\n`);

  // Test 2: Mixed Issue Types
  console.log('Test 2: Mixed Issue Types');
  const mixedIssues: ElementIssue[] = [
    {
      selector: 'img.photo',
      element: '<img src="photo.jpg">',
      issues: ['Missing alt attribute'],
      severity: 'error',
      fixSuggestion: 'Add alt text',
    },
    {
      selector: 'button.submit',
      element: '<button onclick="submit()">Submit</button>',
      issues: ['Missing keyboard handler'],
      severity: 'error',
      fixSuggestion: 'Add keyboard support',
    },
    {
      selector: 'div.text',
      element: '<div style="color: #ccc; background: #fff;">Low contrast text</div>',
      issues: ['Insufficient color contrast'],
      severity: 'error',
      fixSuggestion: 'Increase color contrast',
    },
  ];

  const mixedEvidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
    'WCAG-005',
    'Keyboard',
    mixedIssues
  );

  console.log(`✅ Generated ${mixedEvidence.length} evidence items for mixed issues`);
  
  // Count evidence by type
  const evidenceByType = mixedEvidence.reduce((acc, evidence) => {
    const type = evidence.description.toLowerCase();
    if (type.includes('alternative text')) acc.altText++;
    else if (type.includes('keyboard')) acc.keyboard++;
    else if (type.includes('contrast')) acc.contrast++;
    else acc.other++;
    return acc;
  }, { altText: 0, keyboard: 0, contrast: 0, other: 0 });

  console.log(`📊 Evidence breakdown:`, evidenceByType);
  console.log();

  // Test 3: No Issues (Passing)
  console.log('Test 3: No Issues (Passing)');
  const passingEvidence = EnhancedEvidenceGenerator.generateEnhancedEvidence(
    'WCAG-001',
    'Non-text Content',
    []
  );

  console.log(`✅ Generated ${passingEvidence.length} evidence item for passing check`);
  console.log(`📊 Evidence: ${passingEvidence[0].description}`);
  console.log(`🎯 Severity: ${passingEvidence[0].severity}\n`);

  // Test 4: Element Analysis
  console.log('Test 4: Element Analysis');
  
  const testElements = [
    {
      tagName: 'IMG',
      alt: undefined,
      outerHTML: '<img src="test.jpg">',
    },
    {
      tagName: 'IMG',
      alt: '',
      outerHTML: '<img src="decorative.jpg" alt="">',
    },
    {
      tagName: 'IMG',
      alt: 'x',
      outerHTML: '<img src="short.jpg" alt="x">',
    },
  ];

  testElements.forEach((element, index) => {
    const issue = EnhancedEvidenceGenerator.analyzeElementIssues(
      element,
      `img.test-${index}`,
      'WCAG-001',
      'non-text-content'
    );

    console.log(`Element ${index + 1}: ${issue ? `${issue.issues.join(', ')} (${issue.severity})` : 'No issues'}`);
  });

  console.log('\n🎉 Enhanced Evidence Generation Tests Completed!');
  
  // Test 5: Fix Example Generation
  console.log('\nTest 5: Fix Example Details');
  const evidenceWithFix = altTextEvidence.find(e => e.fixExample);
  if (evidenceWithFix?.fixExample) {
    console.log('🔧 Fix Example:');
    console.log(`   Description: ${evidenceWithFix.fixExample.description}`);
    console.log(`   Before: ${evidenceWithFix.fixExample.before}`);
    console.log(`   After: ${evidenceWithFix.fixExample.after}`);
    console.log(`   Resources: ${evidenceWithFix.fixExample.resources?.length || 0} links`);
  }

  return {
    altTextEvidence,
    mixedEvidence,
    passingEvidence,
    testResults: {
      altTextIssuesCount: altTextIssues.length,
      altTextEvidenceCount: altTextEvidence.length,
      mixedIssuesCount: mixedIssues.length,
      mixedEvidenceCount: mixedEvidence.length,
      hasFixExamples: altTextEvidence.some(e => e.fixExample),
      hasElementCounts: altTextEvidence.some(e => e.elementCount && e.elementCount > 0),
      hasAffectedSelectors: altTextEvidence.some(e => e.affectedSelectors && e.affectedSelectors.length > 0),
    }
  };
}

// Run the test
if (require.main === module) {
  testEnhancedEvidenceGeneration()
    .then((results) => {
      console.log('\n📊 Test Summary:');
      console.log(JSON.stringify(results.testResults, null, 2));
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export { testEnhancedEvidenceGeneration };
