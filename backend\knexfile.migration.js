/**
 * Knex configuration for migrations only
 * Bypasses strict environment validation for migration operations
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const config = {
  development: {
    client: 'pg',
    connection: {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      user: process.env.POSTGRES_USER || 'complyuser',
      password: process.env.POSTGRES_PASSWORD || 'complypassword',
      database: process.env.POSTGRES_DB || 'complychecker_dev',
    },
    migrations: {
      directory: path.join(__dirname, '../migrations'),
      tableName: 'knex_migrations',
      extension: 'ts',
    },
    seeds: {
      directory: path.join(__dirname, '../seeds'),
      extension: 'ts',
    },
  },

  staging: {
    client: 'pg',
    connection: {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      user: process.env.POSTGRES_USER || 'complyuser',
      password: process.env.POSTGRES_PASSWORD || 'complypassword',
      database: process.env.POSTGRES_DB || 'complychecker_dev',
    },
    migrations: {
      directory: path.join(__dirname, '../migrations'),
      tableName: 'knex_migrations',
      extension: 'ts',
    },
    seeds: {
      directory: path.join(__dirname, '../seeds/staging'),
      extension: 'ts',
    },
  },

  production: {
    client: 'pg',
    connection: {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      database: process.env.POSTGRES_DB,
    },
    migrations: {
      directory: path.join(__dirname, '../migrations'),
      tableName: 'knex_migrations',
      extension: 'ts',
    },
  },
};

module.exports = config;
