/**
 * WCAG Database Service
 * Database operations for WCAG compliance data
 * Following HIPAA/GDPR patterns with real database operations using Knex ORM
 */

import db from '../../../lib/db';
import { v4 as uuidv4 } from 'uuid';
// import { Knex } from 'knex';
import {
  WcagScan<PERSON><PERSON>ult,
  WcagScanConfig,
  WcagRuleResult,
  WcagCheckResult,
  ScanStatus,
  RiskLevel,
  WcagEvidence,
  WcagScanOptions,
} from '../types';
import logger from '../../../utils/logger';

export interface ScanListOptions {
  page: number;
  limit: number;
  status?: ScanStatus;
  startDate?: Date;
  endDate?: Date;
  sortBy: 'scanTimestamp' | 'overallScore' | 'targetUrl';
  sortOrder: 'asc' | 'desc';
}

export interface ScanListResult {
  scans: Array<{
    scanId: string;
    targetUrl: string;
    status: ScanStatus;
    overallScore?: number;
    levelAchieved?: 'A' | 'AA' | 'AAA' | 'FAIL';
    riskLevel?: RiskLevel;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number; // Count only, no scoring
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface WcagScanEntity {
  id: string;
  user_id: string;
  target_url: string;
  scan_timestamp: Date;
  scan_duration?: number;
  overall_score?: number;
  scan_status: ScanStatus;
  completion_timestamp?: Date;
  total_automated_checks: number;
  passed_automated_checks: number;
  failed_automated_checks: number;
  manual_review_items?: number;
  risk_level?: RiskLevel;
  level_achieved?: 'A' | 'AA' | 'AAA' | 'FAIL';
  perceivable_score?: number;
  operable_score?: number;
  understandable_score?: number;
  robust_score?: number;
  wcag21_score?: number;
  wcag22_score?: number;
  wcag30_score?: number;
  scan_options?: WcagScanOptions;
  error_message?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface WcagAutomatedResultEntity {
  id: string;
  scan_id: string;
  rule_id: string;
  rule_name: string;
  category: string;
  wcag_version: string;
  conformance_level: string;
  automation_level: number;
  status: 'passed' | 'failed' | 'not_applicable';
  score: number;
  max_score: number;
  execution_time: number;
  evidence?:
    | string
    | Array<{
        type: string;
        description: string;
        value: string;
        selector?: string;
        severity?: string;
        message?: string;
        element?: string;
        details?: string;
      }>; // Primary evidence storage (JSON string or array)

  recommendations?: string[];
  created_at: Date;
  updated_at: Date;

  // Enhanced evidence fields (added by migration)
  total_element_count?: number;
  failed_element_count?: number;
  affected_selectors?: string; // JSON string
  fix_examples?: string; // JSON string
  evidence_metadata?: string; // JSON string
  scan_duration_ms?: number;
  elements_analyzed?: number;
  check_metadata?: string; // JSON string
}

export class WcagDatabase {
  /**
   * Safe JSON stringify helper to prevent database JSON errors
   * Following the proven GDPR pattern
   */
  private static stringifyJsonSafely(data: unknown): string {
    if (data === null || data === undefined) {
      return JSON.stringify([]);
    }
    try {
      // Ensure the data is properly serializable
      const serialized = JSON.stringify(data);
      // Validate it can be parsed back
      JSON.parse(serialized);
      return serialized;
    } catch (error) {
      logger.warn('Failed to stringify JSON safely:', { data, error });
      return JSON.stringify([]);
    }
  }

  /**
   * Clean data for JSONB columns without manual JSON escaping
   * Knex/PostgreSQL will handle the JSON serialization automatically
   */
  private static cleanDataForJsonb(data: unknown): unknown {
    if (data === null || data === undefined) {
      return data;
    }

    if (typeof data === 'string') {
      // Only remove control characters, don't escape quotes for JSONB
      return (
        data
          // eslint-disable-next-line no-control-regex
          .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
          .replace(/\n/g, ' ') // Replace newlines with spaces
          .replace(/\r/g, ' ') // Replace carriage returns with spaces
          .replace(/\t/g, ' ') // Replace tabs with spaces
          .replace(/\s+/g, ' ') // Normalize multiple spaces
          .trim()
      );
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.cleanDataForJsonb(item));
    }

    if (data && typeof data === 'object') {
      const cleaned: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        cleaned[key] = this.cleanDataForJsonb(value);
      }
      return cleaned;
    }

    return data;
  }

  /**
   * Safe JSONB data helper for PostgreSQL JSONB columns
   * Returns the actual data object/array for Knex to handle serialization
   */
  private static prepareJsonbData(data: unknown): unknown {
    if (data === null || data === undefined) {
      return [];
    }

    try {
      // If it's already an array or object, clean and return it
      if (Array.isArray(data)) {
        return data.map((item) => this.cleanDataForJsonb(item));
      }

      if (typeof data === 'object') {
        return this.cleanDataForJsonb(data);
      }

      // If it's a string, try to parse it first
      if (typeof data === 'string') {
        try {
          const parsed = JSON.parse(data);
          return Array.isArray(parsed)
            ? parsed.map((item) => this.cleanDataForJsonb(item))
            : this.cleanDataForJsonb(parsed);
        } catch {
          // If parsing fails, treat as a single string item
          return [this.cleanDataForJsonb(data)];
        }
      }

      // For other types, wrap in array
      return [this.cleanDataForJsonb(data)];
    } catch (error) {
      logger.warn('Failed to prepare JSONB data safely:', { data, error });
      return [];
    }
  }

  /**
   * Clean data to remove problematic characters that could break JSON
   */
  private static cleanDataForJson(data: unknown): unknown {
    if (typeof data === 'string') {
      // Remove control characters and normalize the string
      return (
        data
          // eslint-disable-next-line no-control-regex
          .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
          .replace(/\\/g, '\\\\') // Escape backslashes
          .replace(/"/g, '\\"') // Escape quotes
          .replace(/\n/g, '\\n') // Escape newlines
          .replace(/\r/g, '\\r') // Escape carriage returns
          .replace(/\t/g, '\\t') // Escape tabs
          .trim()
      );
    }

    if (Array.isArray(data)) {
      return data.map((item) => WcagDatabase.cleanDataForJson(item));
    }

    if (data && typeof data === 'object') {
      const cleaned: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        cleaned[key] = WcagDatabase.cleanDataForJson(value);
      }
      return cleaned;
    }

    return data;
  }

  /**
   * Create a new WCAG scan record
   */
  static async createScan(config: WcagScanConfig): Promise<string> {
    try {
      logger.info('📝 Creating WCAG scan record...');

      const insertData: Partial<WcagScanEntity> = {
        user_id: config.userId,
        target_url: config.targetUrl,
        scan_timestamp: new Date(),
        total_automated_checks: 0,
        passed_automated_checks: 0,
        failed_automated_checks: 0,
        manual_review_items: 0,
        scan_status: 'pending',
        scan_options: (config.scanOptions as WcagScanOptions) || {},
      };

      // Use provided scanId if available, otherwise let database generate one
      if (config.requestId) {
        insertData.id = config.requestId;
      }

      const [scanId] = await db('wcag_scans').insert(insertData).returning('id');
      const finalScanId = typeof scanId === 'object' ? scanId.id : scanId;

      logger.info(`✅ WCAG scan record created with ID: ${finalScanId}`);
      return finalScanId;
    } catch (error) {
      logger.error('❌ Error creating WCAG scan record', { error });
      throw new Error(
        `Failed to create WCAG scan record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get user's WCAG scans with pagination and filtering
   */
  async getUserScans(userId: string, options: ScanListOptions): Promise<ScanListResult> {
    try {
      logger.info(`📊 Fetching WCAG scans for user: ${userId}`, { options });

      let query = db('wcag_scans').where('user_id', userId).select('*');

      // Apply filters
      if (options.status) {
        query = query.where('scan_status', options.status);
      }

      if (options.startDate) {
        query = query.where('scan_timestamp', '>=', options.startDate);
      }

      if (options.endDate) {
        query = query.where('scan_timestamp', '<=', options.endDate);
      }

      // Get total count for pagination (before applying sorting and pagination)
      let countQuery = db('wcag_scans').where('user_id', userId);

      // Apply the same filters to count query
      if (options.status) {
        countQuery = countQuery.where('scan_status', options.status);
      }

      if (options.startDate) {
        countQuery = countQuery.where('scan_timestamp', '>=', options.startDate);
      }

      if (options.endDate) {
        countQuery = countQuery.where('scan_timestamp', '<=', options.endDate);
      }

      const [{ count }] = await countQuery.count('* as count');
      const total = parseInt(count as string, 10);

      // Apply sorting to the main query
      const sortColumn =
        options.sortBy === 'scanTimestamp'
          ? 'scan_timestamp'
          : options.sortBy === 'overallScore'
            ? 'overall_score'
            : 'target_url';
      query = query.orderBy(sortColumn, options.sortOrder);

      // Apply pagination
      const offset = (options.page - 1) * options.limit;
      const scans = await query.limit(options.limit).offset(offset);

      // Transform to expected format
      const transformedScans = scans.map((scan: WcagScanEntity) => ({
        scanId: scan.id,
        targetUrl: scan.target_url,
        status: scan.scan_status,
        overallScore: scan.overall_score ? Number(scan.overall_score) : undefined,
        levelAchieved: this.calculateLevelAchieved(scan.overall_score),
        riskLevel: scan.risk_level,
        scanTimestamp: scan.scan_timestamp.toISOString(),
        completionTimestamp: scan.completion_timestamp?.toISOString(),
        totalAutomatedChecks: scan.total_automated_checks,
        passedAutomatedChecks: scan.passed_automated_checks,
        failedAutomatedChecks: scan.failed_automated_checks,
        manualReviewItems: 0, // Will be calculated from manual_reviews table
      }));

      return {
        scans: transformedScans,
        pagination: {
          page: options.page,
          limit: options.limit,
          total,
          totalPages: Math.ceil(total / options.limit),
        },
      };
    } catch (error) {
      logger.error('❌ Error fetching WCAG scans', { error });
      throw new Error(
        `Failed to fetch WCAG scans: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get scan by ID for specific user
   */
  async getScanById(scanId: string, userId: string): Promise<WcagScanResult | null> {
    try {
      logger.info(`🔍 Fetching WCAG scan: ${scanId} for user: ${userId}`);

      const scan = await db('wcag_scans').where({ id: scanId, user_id: userId }).first();

      if (!scan) {
        return null;
      }

      // Get automated results
      const automatedResults = await db('wcag_automated_results')
        .where('scan_id', scanId)
        .select('*');

      // Get manual review items
      const manualReviewItems = await this.getManualReviewItems(scanId);
      logger.info(`🔍 Debug - Manual review items fetched: ${manualReviewItems.length}`, {
        manualReviewItems,
      });

      // Transform to WcagScanResult format
      const ruleResults: WcagRuleResult[] = automatedResults.map(
        (result: WcagAutomatedResultEntity) => {
          // Debug logging to see what's in the database
          logger.info(`🔍 Processing result for ${result.rule_id}:`, {
            hasEvidence: !!result.evidence,
            evidenceType: typeof result.evidence,
            evidenceLength: Array.isArray(result.evidence) ? result.evidence.length : 0,
            totalElementCount: result.total_element_count,
            affectedSelectors: result.affected_selectors,
            fixExamples: result.fix_examples,
          });

          // Extract evidence from evidence column (primary) or details field (fallback)
          let evidence: WcagEvidence[] = [];

          // First try to get evidence from the evidence column
          if (result.evidence) {
            try {
              const evidenceData = typeof result.evidence === 'string' ? JSON.parse(result.evidence) : result.evidence;
              if (Array.isArray(evidenceData)) {
                evidence = evidenceData.map((evidenceItem, index) => {
                  const baseEvidence: WcagEvidence = {
                    type: evidenceItem.type as 'text' | 'image' | 'code' | 'measurement' | 'interaction' | 'info' | 'warning' | 'error',
                    description: evidenceItem.description,
                    value: evidenceItem.value,
                    selector: evidenceItem.selector,
                    severity: evidenceItem.severity || (result.status === 'failed' ? 'error' : 'info'),
                    message: evidenceItem.message,
                    element: evidenceItem.element,
                    details: evidenceItem.details,
                  };

                  // Add enhanced fields from database if available
                  const enhancedEvidence = baseEvidence as WcagEvidence & {
                    elementCount?: number;
                    affectedSelectors?: string[];
                    fixExample?: any;
                    metadata?: any;
                  };

                  // Add enhanced data from database columns
                  if (result.total_element_count && index === 0) {
                    enhancedEvidence.elementCount = result.total_element_count;
                  }

                  if (result.affected_selectors) {
                    try {
                      enhancedEvidence.affectedSelectors = JSON.parse(result.affected_selectors);
                    } catch (e) {
                      // Ignore JSON parse errors
                    }
                  }

                  if (result.fix_examples) {
                    try {
                      const fixExamples = JSON.parse(result.fix_examples);
                      if (fixExamples && fixExamples.length > 0) {
                        enhancedEvidence.fixExample = fixExamples[0]; // Use first fix example
                      }
                    } catch (e) {
                      // Ignore JSON parse errors
                    }
                  }

                  if (result.evidence_metadata) {
                    try {
                      enhancedEvidence.metadata = JSON.parse(result.evidence_metadata);
                    } catch (e) {
                      // Ignore JSON parse errors
                    }
                  }

                  return enhancedEvidence;
                });
              }
            } catch (e) {
              logger.warn(`Failed to parse evidence column for ${result.rule_id}:`, { error: e });
            }
          }



          // If no evidence exists, create fallback evidence from database fields
          if (evidence.length === 0) {
            logger.info(`⚠️ No evidence found for ${result.rule_id}, creating fallback evidence`);

            const fallbackEvidence: WcagEvidence[] = [];

            // Create evidence from status and description
            if (result.status === 'failed') {
              fallbackEvidence.push({
                type: 'error',
                description: result.rule_name || result.rule_id || 'WCAG Check Failed',
                value: `This check failed with score ${result.score}/${result.max_score}`,
                severity: 'error',
                message: 'Check failed - no detailed evidence available',
              });

              // Add element count evidence if available
              if (result.total_element_count && result.total_element_count > 0) {
                fallbackEvidence.push({
                  type: 'measurement',
                  description: 'Elements analyzed',
                  value: `${result.total_element_count} elements found`,
                  severity: 'info',
                });
              }

              // Add failed element count if available
              if (result.failed_element_count && result.failed_element_count > 0) {
                fallbackEvidence.push({
                  type: 'error',
                  description: 'Failed elements',
                  value: `${result.failed_element_count} elements failed this check`,
                  severity: 'error',
                });
              }

              // Add affected selectors if available
              if (result.affected_selectors) {
                try {
                  const selectors = JSON.parse(result.affected_selectors);
                  if (Array.isArray(selectors) && selectors.length > 0) {
                    fallbackEvidence.push({
                      type: 'code',
                      description: 'Affected CSS selectors',
                      value: selectors.slice(0, 5).join(', ') + (selectors.length > 5 ? '...' : ''),
                      severity: 'warning',
                      selector: selectors[0],
                    });
                  }
                } catch (e) {
                  // Ignore JSON parse errors
                }
              }
            } else if (result.status === 'passed') {
              fallbackEvidence.push({
                type: 'info',
                description: result.rule_name || result.rule_id || 'WCAG Check Passed',
                value: 'This check passed successfully',
                severity: 'info',
                message: 'Check passed - all requirements met',
              });
            }

            evidence = fallbackEvidence;
          }

          return {
            ruleId: result.rule_id,
            ruleName: result.rule_name,
            category: result.category as 'perceivable' | 'operable' | 'understandable' | 'robust',
            wcagVersion: result.wcag_version as '2.1' | '2.2' | '3.0',
            successCriterion: '', // Not available in entity, will be populated from rule config
            level: result.conformance_level as 'A' | 'AA' | 'AAA',
            status: result.status,
            score: result.score,
            maxScore: result.max_score,
            weight: 1.0, // Default weight
            automated: true, // These are automated results
            evidence: evidence,
            recommendations: result.recommendations || [],
            executionTime: result.execution_time,
            errorMessage: undefined, // Not available in entity
            manualReviewItems: [], // Not stored in entity, would need separate table
          };
        }
      );

      // Normalize scores to 0-100 range
      const normalizeScore = (score: number | null | undefined): number => {
        if (!score) return 0;
        return Math.min(Math.max(Math.round(score), 0), 100);
      };

      // Build scan result
      const scanResult: WcagScanResult = {
        scanId: scan.id,
        targetUrl: scan.target_url,
        status: scan.scan_status,
        overallScore: normalizeScore(scan.overall_score),
        levelAchieved: scan.level_achieved || 'FAIL',
        riskLevel: scan.risk_level || 'medium',
        summary: {
          totalAutomatedChecks: scan.total_automated_checks || 0,
          passedAutomatedChecks: scan.passed_automated_checks || 0,
          failedAutomatedChecks: scan.failed_automated_checks || 0,
          automatedScore: normalizeScore(scan.overall_score),
          categoryScores: {
            perceivable: normalizeScore(scan.perceivable_score),
            operable: normalizeScore(scan.operable_score),
            understandable: normalizeScore(scan.understandable_score),
            robust: normalizeScore(scan.robust_score),
          },
          versionScores: {
            wcag21: normalizeScore(scan.wcag21_score),
            wcag22: normalizeScore(scan.wcag22_score),
            wcag30: normalizeScore(scan.wcag30_score),
          },
          automationRate: 0.87,
          manualReviewItems: manualReviewItems.length,
          // Additional properties for database compatibility
          overallScore: normalizeScore(scan.overall_score),
          totalRules: scan.total_automated_checks || 0,
          passedRules: scan.passed_automated_checks || 0,
          failedRules: scan.failed_automated_checks || 0,
          riskLevel: scan.risk_level || 'medium',
          scanDuration: scan.scan_duration || 0,
        },
        checks: ruleResults,
        recommendations: [],
        metadata: {
          scanId: scan.id,
          userId: scan.user_id,
          requestId: scan.id,
          startTime: scan.scan_timestamp,
          endTime: scan.completion_timestamp || undefined,
          duration: scan.scan_duration || 0,
          userAgent: 'Comply Checker Bot',
          viewport: { width: 1920, height: 1080 },
          environment: process.env.NODE_ENV || 'development',
          version: '1.0.0',
        },
        ruleResults,
      };

      // Add frontend compatibility properties using type assertion
      const extendedScanResult = scanResult as WcagScanResult & {
        url: string;
        scanTimestamp: string;
        completionTimestamp?: string;
        wcagVersion: string;
        complianceLevel: string;
        scanOptions: WcagScanConfig;
        manualReviewData?: Array<{
          id: string;
          ruleId: string;
          selector: string;
          description: string;
          automatedFindings: string;
          reviewRequired: string;
          priority: 'high' | 'medium' | 'low';
          estimatedTime: number;
          reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
          reviewerNotes?: string;
          reviewAssessment?: string;
          reviewedAt?: Date;
          reviewedBy?: string;
        }>;
      };

      extendedScanResult.url = scan.target_url;
      extendedScanResult.scanTimestamp = scan.scan_timestamp
        ? scan.scan_timestamp.toISOString()
        : new Date().toISOString();
      extendedScanResult.completionTimestamp = scan.completion_timestamp
        ? scan.completion_timestamp.toISOString()
        : undefined;
      extendedScanResult.wcagVersion = '2.1';
      extendedScanResult.complianceLevel = scan.level_achieved || 'FAIL';
      extendedScanResult.manualReviewData = manualReviewItems;
      extendedScanResult.scanOptions = scan.scan_options
        ? {
            targetUrl: scan.target_url,
            userId: scan.user_id,
            requestId: scan.id,
            scanOptions: {
              enableContrastAnalysis: scan.scan_options.enableContrastAnalysis || true,
              enableKeyboardTesting: scan.scan_options.enableKeyboardTesting || true,
              enableFocusAnalysis: scan.scan_options.enableFocusAnalysis || true,
              enableSemanticValidation: scan.scan_options.enableSemanticValidation || true,
              wcagVersion: scan.scan_options.wcagVersion || '2.1',
              level: scan.scan_options.level || 'AA',
              maxPages: scan.scan_options.maxPages || 5,
            },
          }
        : {
            targetUrl: scan.target_url,
            userId: scan.user_id,
            requestId: scan.id,
            scanOptions: {
              enableContrastAnalysis: true,
              enableKeyboardTesting: true,
              enableFocusAnalysis: true,
              enableSemanticValidation: true,
              wcagVersion: '2.1' as const,
              level: 'AA' as const,
              maxPages: 5,
            },
          };

      return extendedScanResult;
    } catch (error) {
      logger.error('❌ Error fetching WCAG scan', { error });
      throw new Error(
        `Failed to fetch WCAG scan: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Delete scan by ID for specific user
   */
  async deleteScan(scanId: string, userId: string): Promise<boolean> {
    try {
      logger.info(`🗑️ Deleting WCAG scan: ${scanId} for user: ${userId}`);

      const result = await db('wcag_scans').where({ id: scanId, user_id: userId }).del();

      return result > 0;
    } catch (error) {
      logger.error('❌ Error deleting WCAG scan', { error });
      throw new Error(
        `Failed to delete WCAG scan: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Health check for database connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      logger.info('🏥 Checking WCAG database health');

      // Test basic database connectivity
      await db.raw('SELECT 1');

      // Check if WCAG tables exist
      const tables = ['wcag_scans', 'wcag_automated_results'];
      for (const table of tables) {
        const exists = await db.schema.hasTable(table);
        if (!exists) {
          logger.error(`❌ Table ${table} does not exist`);
          return false;
        }
      }

      logger.info('✅ WCAG database health check passed');
      return true;
    } catch (error) {
      logger.error('❌ WCAG database health check failed', { error });
      return false;
    }
  }

  /**
   * Save scan result to database
   */
  async saveScanResult(scanResult: WcagScanResult): Promise<void> {
    try {
      logger.info(`💾 Saving WCAG scan result: ${scanResult.scanId}`);

      // Update main scan record
      await db('wcag_scans')
        .where('id', scanResult.scanId)
        .update({
          overall_score: scanResult.summary.overallScore || scanResult.summary.automatedScore,
          scan_status: 'completed',
          completion_timestamp: new Date(),
          total_automated_checks:
            scanResult.summary.totalRules || scanResult.summary.totalAutomatedChecks,
          passed_automated_checks:
            scanResult.summary.passedRules || scanResult.summary.passedAutomatedChecks,
          failed_automated_checks:
            scanResult.summary.failedRules || scanResult.summary.failedAutomatedChecks,
          risk_level: scanResult.summary.riskLevel || scanResult.riskLevel,
          scan_duration: scanResult.summary.scanDuration || scanResult.metadata?.duration,
          level_achieved: scanResult.levelAchieved,
          perceivable_score: scanResult.summary.categoryScores?.perceivable,
          operable_score: scanResult.summary.categoryScores?.operable,
          understandable_score: scanResult.summary.categoryScores?.understandable,
          robust_score: scanResult.summary.categoryScores?.robust,
          wcag21_score: scanResult.summary.versionScores?.wcag21,
          wcag22_score: scanResult.summary.versionScores?.wcag22,
          wcag30_score: scanResult.summary.versionScores?.wcag30,
          manual_review_items: scanResult.summary.manualReviewItems,
        });

      // Save automated results
      if (scanResult.ruleResults && scanResult.ruleResults.length > 0) {
        const automatedResults = scanResult.ruleResults.map((result) => {
          // Ensure evidence and recommendations are properly serialized for JSONB
          let evidence: WcagEvidence[] = [];
          let recommendations: string[] = [];

          try {
            // Handle evidence - ensure it's a proper array of objects
            if (Array.isArray(result.evidence)) {
              evidence = result.evidence.map((item) => {
                if (typeof item === 'string') {
                  return {
                    type: 'text' as const,
                    description: WcagDatabase.cleanDataForJsonb(item) as string,
                    value: WcagDatabase.cleanDataForJsonb(item) as string,
                  };
                }
                // Ensure all evidence objects have required fields and clean all string values
                const cleanedSeverity = item.severity
                  ? (WcagDatabase.cleanDataForJsonb(item.severity) as string)
                  : undefined;

                // Validate severity value
                const validSeverity =
                  cleanedSeverity &&
                  ['info', 'warning', 'error', 'critical'].includes(cleanedSeverity)
                    ? (cleanedSeverity as 'info' | 'warning' | 'error' | 'critical')
                    : undefined;

                return {
                  type: (item.type || 'text') as WcagEvidence['type'],
                  description: WcagDatabase.cleanDataForJsonb(item.description || '') as string,
                  value: WcagDatabase.cleanDataForJsonb(item.value || '') as string,
                  selector: item.selector
                    ? (WcagDatabase.cleanDataForJsonb(item.selector) as string)
                    : undefined,
                  screenshot: item.screenshot
                    ? (WcagDatabase.cleanDataForJsonb(item.screenshot) as string)
                    : undefined,
                  severity: validSeverity,
                  message: item.message
                    ? (WcagDatabase.cleanDataForJsonb(item.message) as string)
                    : undefined,
                  element: item.element
                    ? (WcagDatabase.cleanDataForJsonb(item.element) as string)
                    : undefined,
                  details: item.details
                    ? (WcagDatabase.cleanDataForJsonb(item.details) as string)
                    : undefined,
                };
              });
            } else if (result.evidence) {
              evidence = [
                {
                  type: 'text',
                  description: 'Evidence',
                  value: WcagDatabase.cleanDataForJsonb(result.evidence) as string,
                },
              ];
            }

            // Handle recommendations - ensure it's a proper array of strings
            if (Array.isArray(result.recommendations)) {
              recommendations = result.recommendations.map(
                (item) => WcagDatabase.cleanDataForJsonb(item) as string,
              );
            } else if (result.recommendations) {
              recommendations = [WcagDatabase.cleanDataForJsonb(result.recommendations) as string];
            }
          } catch (serializationError: unknown) {
            logger.error(`Error serializing evidence/recommendations for rule ${result.ruleId}:`, {
              error:
                serializationError instanceof Error ? serializationError.message : 'Unknown error',
            });
            evidence = [
              {
                type: 'error',
                description: 'Serialization error',
                value: 'Could not serialize evidence',
              },
            ];
            recommendations = ['Manual review required due to data serialization error'];
          }

          // Extract enhanced evidence data for database population
          const enhancedData = WcagDatabase.extractEnhancedEvidenceData(result, evidence);

          return {
            id: uuidv4(),
            scan_id: scanResult.scanId,
            rule_id: result.ruleId,
            rule_name: result.ruleName,
            category: result.category,
            wcag_version: result.wcagVersion,
            success_criterion: result.successCriterion || '',
            level: result.level,
            status: result.status,
            score: result.score,
            max_score: result.maxScore,
            weight: result.weight || 1.0,
            evidence: JSON.stringify(WcagDatabase.prepareJsonbData(evidence)), // JSONB requires JSON string for Knex
            recommendations: JSON.stringify(WcagDatabase.prepareJsonbData(recommendations)), // JSONB requires JSON string for Knex
            execution_time: result.executionTime || 0,
            error_message: result.errorMessage || null,
            created_at: new Date(),

            // ✅ ENHANCED EVIDENCE FIELDS - Now properly populated
            total_element_count: enhancedData.totalElementCount,
            failed_element_count: enhancedData.failedElementCount,
            affected_selectors: enhancedData.affectedSelectors
              ? JSON.stringify(enhancedData.affectedSelectors)
              : null,
            fix_examples: enhancedData.fixExamples
              ? JSON.stringify(enhancedData.fixExamples)
              : null,
            evidence_metadata: enhancedData.evidenceMetadata
              ? JSON.stringify(enhancedData.evidenceMetadata)
              : null,
            scan_duration_ms: enhancedData.scanDurationMs,
            elements_analyzed: enhancedData.elementsAnalyzed,
            check_metadata: enhancedData.checkMetadata
              ? JSON.stringify(enhancedData.checkMetadata)
              : null,
          };
        });

        await db('wcag_automated_results').insert(automatedResults);
      }

      logger.info(`✅ WCAG scan result saved: ${scanResult.scanId}`);
    } catch (error) {
      logger.error('❌ Error saving WCAG scan result', { error });
      throw new Error(
        `Failed to save WCAG scan result: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update scan status
   */
  async updateScanStatus(scanId: string, status: ScanStatus, errorMessage?: string): Promise<void> {
    try {
      logger.info(`📝 Updating WCAG scan status: ${scanId} -> ${status}`);

      const updateData: Partial<WcagScanEntity> = {
        scan_status: status,
      };

      if (status === 'failed' && errorMessage) {
        (updateData as Record<string, unknown>).metadata = db.raw("COALESCE(metadata, '{}') || ?", [
          JSON.stringify({ errorMessage }),
        ]);
      }

      if (status === 'completed') {
        updateData.completion_timestamp = new Date();
      }

      await db('wcag_scans').where('id', scanId).update(updateData);

      logger.info(`✅ WCAG scan status updated: ${scanId} -> ${status}`);
    } catch (error) {
      logger.error('❌ Error updating WCAG scan status', { error });
      throw new Error(
        `Failed to update WCAG scan status: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update manual review status for a specific WCAG check
   */
  async updateManualReview(
    scanId: string,
    ruleId: string,
    selector: string,
    reviewData: {
      assessment: string;
      notes: string;
      reviewerName: string;
      reviewedAt: string;
      reviewedBy: string;
      // ✅ ENHANCED FEATURES: Additional fields for enhanced integration
      confidence?: number;
      severity?: string;
      impact?: string;
      effort?: string;
      enhancedEvidence?: WcagEvidence[];
    },
  ): Promise<boolean> {
    try {
      logger.info(
        `📝 Updating WCAG manual review for scan ${scanId}, rule ${ruleId}, selector ${selector}`,
        { reviewData },
      );

      // Check if the manual review record exists
      const existingRecord = await db('wcag_manual_reviews')
        .where({ scan_id: scanId, rule_id: ruleId, element_selector: selector })
        .first();

      if (!existingRecord) {
        logger.warn(
          `⚠️ No WCAG manual review record found for scan ${scanId}, rule ${ruleId}, selector ${selector}`,
        );

        // List all available manual review records for this scan for debugging
        const allRecords = await db('wcag_manual_reviews')
          .where({ scan_id: scanId })
          .select('rule_id', 'element_selector', 'description');

        logger.info(`🔍 Available manual review rules for scan ${scanId}:`, {
          records: allRecords,
          count: allRecords.length,
        });
        return false;
      }

      // Update the manual review record
      // First check if the columns exist, if not add them
      try {
        await db.schema.alterTable('wcag_manual_reviews', (table) => {
          table.string('review_assessment').nullable();
          table.string('reviewed_by').nullable();
          table.timestamp('updated_at').nullable();
        });
      } catch (error) {
        // Columns might already exist, ignore error
        logger.info('Manual review columns may already exist, continuing...');
      }

      const updateData: Record<string, unknown> = {
        review_status: 'completed',
        reviewer_notes: reviewData.notes,
        reviewed_at: new Date(reviewData.reviewedAt),
      };

      // Only add these fields if the columns exist
      try {
        updateData.review_assessment = reviewData.assessment;
        updateData.reviewed_by = reviewData.reviewedBy;
        updateData.updated_at = new Date();

        // ✅ ENHANCED FEATURES: Add enhanced fields if provided
        if (reviewData.confidence !== undefined) {
          updateData.confidence = reviewData.confidence;
        }
        if (reviewData.severity) {
          updateData.severity = reviewData.severity;
        }
        if (reviewData.impact) {
          updateData.impact = reviewData.impact;
        }
        if (reviewData.effort) {
          updateData.effort = reviewData.effort;
        }
        if (reviewData.enhancedEvidence) {
          updateData.enhanced_evidence = JSON.stringify(reviewData.enhancedEvidence);
        }
      } catch (error) {
        logger.warn('Some manual review columns not available, using basic update');
      }

      const updateResult = await db('wcag_manual_reviews')
        .where({ scan_id: scanId, rule_id: ruleId, element_selector: selector })
        .update(updateData);

      logger.info(`💾 WCAG manual review update result: ${updateResult} rows affected`);

      if (updateResult === 0) {
        logger.warn(
          `⚠️ No WCAG manual review records updated for scan ${scanId}, rule ${ruleId}, selector ${selector}`,
        );
        return false;
      }

      logger.info(
        `✅ WCAG manual review updated successfully for scan ${scanId}, rule ${ruleId}, selector ${selector}`,
      );
      return true;
    } catch (error) {
      logger.error('❌ Error updating WCAG manual review:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        scanId,
        ruleId,
      });
      throw error;
    }
  }

  /**
   * Get manual review items for a scan
   */
  async getManualReviewItems(scanId: string): Promise<
    Array<{
      id: string;
      ruleId: string;
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
      reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
      reviewerNotes?: string;
      reviewAssessment?: string;
      reviewedAt?: Date;
      reviewedBy?: string;
    }>
  > {
    try {
      logger.info(`📋 Fetching manual review items for scan ${scanId}`);

      const manualReviews = await db('wcag_manual_reviews')
        .where('scan_id', scanId)
        .select('*')
        .orderBy('created_at', 'asc');

      return manualReviews.map((review) => ({
        id: review.id,
        ruleId: review.rule_id,
        selector: review.element_selector,
        description: review.description,
        automatedFindings: review.automated_findings,
        reviewRequired: review.review_required,
        priority: review.priority,
        estimatedTime: review.estimated_time,
        reviewStatus: review.review_status,
        reviewerNotes: review.reviewer_notes,
        reviewAssessment: review.review_assessment || null,
        reviewedAt: review.reviewed_at || null,
        reviewedBy: review.reviewed_by || null,
      }));
    } catch (error) {
      logger.error('❌ Error fetching manual review items', { error });
      throw new Error(
        `Failed to fetch manual review items: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Store manual review items for a scan
   */
  async storeManualReviewItems(
    scanId: string,
    manualReviewItems: Array<{
      ruleId: string;
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
    }>,
  ): Promise<void> {
    try {
      if (manualReviewItems.length === 0) {
        logger.info(`📝 No manual review items to store for scan ${scanId}`);
        return;
      }

      logger.info(`📝 Storing ${manualReviewItems.length} manual review items for scan ${scanId}`);

      const manualReviewRecords = manualReviewItems.map((item) => ({
        id: uuidv4(),
        scan_id: scanId,
        rule_id: item.ruleId,
        element_selector: item.selector,
        description: item.description,
        automated_findings: item.automatedFindings,
        review_required: item.reviewRequired,
        priority: item.priority,
        estimated_time: item.estimatedTime,
        review_status: 'pending',
        created_at: new Date(),
      }));

      await db('wcag_manual_reviews').insert(manualReviewRecords);

      logger.info(`✅ Stored ${manualReviewItems.length} manual review items for scan ${scanId}`);
    } catch (error) {
      logger.error(`❌ Failed to store manual review items for scan ${scanId}:`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        scanId,
        itemCount: manualReviewItems.length,
      });
      throw error;
    }
  }

  /**
   * Get scan progress (placeholder for orchestrator compatibility)
   */
  async getScanProgress(scanId: string): Promise<{
    scanId: string;
    status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
    currentRule?: string;
    completedRules: string[];
    totalRules: number;
    progress: number;
    estimatedTimeRemaining?: number;
    startTime: Date;
    lastUpdate: Date;
  } | null> {
    // This would typically be stored in a separate progress table or cache
    // For now, return basic scan information
    try {
      // Get scan without user restriction for progress checking
      const scan = await db('wcag_scans').where('id', scanId).first();
      if (!scan) {
        return null;
      }

      return {
        scanId,
        status: scan.status as 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled',
        currentRule: undefined,
        completedRules: [],
        totalRules: 0,
        progress: scan.status === 'completed' ? 100 : 0,
        estimatedTimeRemaining: undefined,
        startTime: new Date(scan.created_at),
        lastUpdate: new Date(scan.updated_at),
      };
    } catch (error) {
      logger.error(`❌ Error getting scan progress`, { error });
      return null;
    }
  }

  /**
   * Store scan progress (placeholder for orchestrator compatibility)
   */
  async storeScanProgress(progress: {
    scanId: string;
    status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
    currentRule?: string;
    completedRules: string[];
    totalRules: number;
    progress: number;
    estimatedTimeRemaining?: number;
    startTime: Date;
    lastUpdate: Date;
  }): Promise<void> {
    // This would typically store progress in a separate table or cache
    // For now, just update the scan status
    try {
      if (progress.scanId && progress.status) {
        // Map progress status to ScanStatus
        const scanStatus: ScanStatus =
          progress.status === 'initializing' ? 'pending' : progress.status;
        await this.updateScanStatus(progress.scanId, scanStatus);
      }
    } catch (error) {
      logger.error(`❌ Error storing scan progress`, { error });
    }
  }

  /**
   * Update scan progress (placeholder for orchestrator compatibility)
   */
  async updateScanProgress(progress: {
    scanId: string;
    status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
    currentRule?: string;
    completedRules: string[];
    totalRules: number;
    progress: number;
    estimatedTimeRemaining?: number;
    startTime: Date;
    lastUpdate: Date;
  }): Promise<void> {
    // This would typically update progress in a separate table or cache
    // For now, just update the scan status
    try {
      if (progress.scanId && progress.status) {
        // Map progress status to ScanStatus
        const scanStatus: ScanStatus =
          progress.status === 'initializing' ? 'pending' : progress.status;
        await this.updateScanStatus(progress.scanId, scanStatus);
      }
    } catch (error) {
      logger.error(`❌ Error updating scan progress`, { error });
    }
  }

  /**
   * Extract enhanced evidence data from check results for database population
   * This method processes enhanced check results and extracts data for the enhanced evidence columns
   */
  private static extractEnhancedEvidenceData(
    result: WcagCheckResult,
    evidence: WcagEvidence[],
  ): {
    totalElementCount: number | null;
    failedElementCount: number | null;
    affectedSelectors: string[] | null;
    fixExamples: Array<{ before: string; after: string; description: string; codeExample?: string; resources?: string[] }> | null;
    evidenceMetadata: Record<string, unknown> | null;
    scanDurationMs: number | null;
    elementsAnalyzed: number | null;
    checkMetadata: Record<string, unknown> | null;
  } {
    try {
      // Type-safe casting to enhanced types
      const enhancedResult = result as WcagCheckResult & {
        elementCounts?: { total: number; failed: number; passed: number };
        performance?: { scanDuration: number; elementsAnalyzed: number };
        checkMetadata?: { version: string; algorithm: string; confidence: number; additionalData?: Record<string, unknown> };
      };

      const enhancedEvidence = evidence as Array<WcagEvidence & {
        elementCount?: number;
        affectedSelectors?: string[];
        fixExample?: { before: string; after: string; description: string; codeExample?: string; resources?: string[] };
        metadata?: Record<string, unknown>;
      }>;

      // Calculate element counts
      let totalElementCount = enhancedResult.elementCounts?.total || null;
      let failedElementCount = enhancedResult.elementCounts?.failed || null;

      // If not available from result, calculate from evidence
      if (totalElementCount === null || failedElementCount === null) {
        const evidenceElementCounts = enhancedEvidence.reduce(
          (acc, ev) => {
            const count = ev.elementCount || 0;
            acc.total += count;
            if (ev.severity === 'error' || ev.type === 'error') {
              acc.failed += count;
            }
            return acc;
          },
          { total: 0, failed: 0 }
        );

        totalElementCount = totalElementCount || evidenceElementCounts.total || null;
        failedElementCount = failedElementCount || evidenceElementCounts.failed || null;
      }

      // Extract affected selectors from evidence
      const affectedSelectors = enhancedEvidence
        .flatMap(ev => ev.affectedSelectors || (ev.selector ? [ev.selector] : []))
        .filter((selector, index, arr) => arr.indexOf(selector) === index) // Remove duplicates
        .slice(0, 50); // Limit to 50 selectors for performance

      // Extract fix examples from evidence
      const fixExamples = enhancedEvidence
        .map(ev => ev.fixExample)
        .filter((example): example is NonNullable<typeof example> => Boolean(example))
        .slice(0, 10); // Limit to 10 fix examples

      // Compile evidence metadata
      const evidenceMetadata = {
        totalEvidenceItems: evidence.length,
        evidenceTypes: Array.from(new Set(evidence.map(ev => ev.type))),
        severityDistribution: evidence.reduce((acc, ev) => {
          const severity = ev.severity || 'info';
          acc[severity] = (acc[severity] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        hasScreenshots: evidence.some(ev => ev.screenshot),
        hasSelectors: evidence.some(ev => ev.selector),
        timestamp: new Date().toISOString(),
      };

      // Extract performance data
      const scanDurationMs = enhancedResult.performance?.scanDuration || result.executionTime || null;
      const elementsAnalyzed = enhancedResult.performance?.elementsAnalyzed || totalElementCount || null;

      // Extract check metadata
      const checkMetadata = enhancedResult.checkMetadata ? {
        version: enhancedResult.checkMetadata.version,
        algorithm: enhancedResult.checkMetadata.algorithm,
        confidence: enhancedResult.checkMetadata.confidence,
        additionalData: enhancedResult.checkMetadata.additionalData || {},
        ruleId: result.ruleId,
        category: result.category,
        level: result.level,
        automationRate: this.getAutomationRate(result.ruleId),
        timestamp: new Date().toISOString(),
      } : null;

      return {
        totalElementCount: (totalElementCount && totalElementCount > 0) ? totalElementCount : null,
        failedElementCount: (failedElementCount && failedElementCount > 0) ? failedElementCount : null,
        affectedSelectors: affectedSelectors.length > 0 ? affectedSelectors : null,
        fixExamples: fixExamples.length > 0 ? fixExamples : null,
        evidenceMetadata: evidenceMetadata,
        scanDurationMs,
        elementsAnalyzed: (elementsAnalyzed && elementsAnalyzed > 0) ? elementsAnalyzed : null,
        checkMetadata,
      };
    } catch (error) {
      logger.warn(`⚠️ Error extracting enhanced evidence data for rule ${result.ruleId}:`, {
        error: error instanceof Error ? error.message : String(error),
      });

      // Return safe defaults on error
      return {
        totalElementCount: null,
        failedElementCount: null,
        affectedSelectors: null,
        fixExamples: null,
        evidenceMetadata: {
          totalEvidenceItems: evidence.length,
          extractionError: true,
          timestamp: new Date().toISOString(),
        },
        scanDurationMs: result.executionTime || null,
        elementsAnalyzed: null,
        checkMetadata: null,
      };
    }
  }

  /**
   * Get automation rate for a specific rule ID
   */
  private static getAutomationRate(ruleId: string): number {
    // Import automation levels from the checks index
    const automationLevels: Record<string, number> = {
      'WCAG-001': 0.95, 'WCAG-002': 0.8, 'WCAG-003': 0.9, 'WCAG-004': 1.0,
      'WCAG-005': 0.85, 'WCAG-006': 0.75, 'WCAG-007': 1.0, 'WCAG-008': 0.9,
      'WCAG-009': 0.9, 'WCAG-010': 1.0, 'WCAG-011': 1.0, 'WCAG-012': 1.0,
      'WCAG-013': 0.7, 'WCAG-014': 1.0, 'WCAG-015': 0.8, 'WCAG-016': 0.85,
      'WCAG-017': 0.95, 'WCAG-018': 0.75, 'WCAG-019': 0.9, 'WCAG-020': 0.8,
      'WCAG-021': 0.6, 'WCAG-022': 0.5, 'WCAG-023': 0.4, 'WCAG-024': 1.0,
      'WCAG-025': 0.95, 'WCAG-026': 0.9, 'WCAG-027': 0.85, 'WCAG-028': 0.9,
      'WCAG-029': 1.0, 'WCAG-030': 0.8, 'WCAG-031': 0.75, 'WCAG-032': 0.7,
      'WCAG-033': 0.65, 'WCAG-034': 0.6, 'WCAG-035': 0.7, 'WCAG-036': 0.8,
      'WCAG-037': 0.75, 'WCAG-038': 0.65, 'WCAG-039': 0.7, 'WCAG-040': 0.8,
      'WCAG-041': 0.75, 'WCAG-042': 0.85, 'WCAG-043': 0.75, 'WCAG-044': 0.85,
      'WCAG-045': 0.8, 'WCAG-046': 0.75, 'WCAG-047': 0.9, 'WCAG-048': 0.85,
      'WCAG-049': 0.8, 'WCAG-050': 0.8, 'WCAG-051': 0.85, 'WCAG-052': 0.7,
      'WCAG-053': 0.75, 'WCAG-054': 0.8, 'WCAG-055': 0.85, 'WCAG-056': 0.75,
      'WCAG-057': 0.8, 'WCAG-058': 0.7, 'WCAG-059': 0.65, 'WCAG-060': 0.6,
      'WCAG-061': 0.75, 'WCAG-062': 0.55, 'WCAG-063': 0.5, 'WCAG-064': 0.7,
      'WCAG-065': 0.8, 'WCAG-066': 0.85,
    };
    return automationLevels[ruleId] || 0.5;
  }

  /**
   * Helper method to calculate level achieved based on score
   */
  private calculateLevelAchieved(score?: number): 'A' | 'AA' | 'AAA' | 'FAIL' {
    if (!score) return 'FAIL';
    if (score >= 95) return 'AAA';
    if (score >= 80) return 'AA';
    if (score >= 60) return 'A';
    return 'FAIL';
  }
}
