/**
 * Test file for Enhanced Color Contrast Integration
 * Tests the integration of get-contrast and colorjs.io libraries
 */

import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import logger from '../../../utils/logger';

/**
 * Test the enhanced color analyzer functionality
 */
async function testEnhancedColorAnalyzer() {
  console.log('🧪 Testing Enhanced Color Analyzer Integration...');

  const analyzer = EnhancedColorAnalyzer.getInstance();

  // Check library availability
  const libraryStatus = analyzer.hasEnhancedLibraries();
  console.log('📚 Library Status:', libraryStatus);

  // Test color conversion (this should work regardless of library availability)
  try {
    // Create a mock text and background analysis for testing
    const mockTextAnalysis = {
      text: 'Test text',
      effectiveColor: { r: 0, g: 0, b: 0, luminance: 0 }, // Black text
      isLarge: false,
      fontSize: 16,
      fontWeight: 'normal',
    };

    const mockBackgroundAnalysis = {
      effectiveColor: { r: 255, g: 255, b: 255, luminance: 1 }, // White background
      type: 'solid' as const,
      confidence: 1.0,
      cssValue: '#ffffff',
      computedValue: 'rgb(255, 255, 255)',
    };

    // Test the enhanced contrast calculation
    const contrastResult = (analyzer as any).calculateEnhancedContrast(
      mockTextAnalysis,
      mockBackgroundAnalysis,
    );

    console.log('✅ Enhanced Contrast Test Results:');
    console.log('   Ratio:', contrastResult.ratio);
    console.log('   Passes AA:', contrastResult.passes.aa);
    console.log('   Passes AAA:', contrastResult.passes.aaa);
    console.log('   Confidence:', contrastResult.confidence);
    console.log('   Issues:', contrastResult.issues);

    // Expected: Black on white should have ~21:1 ratio and pass all tests
    if (contrastResult.ratio >= 20 && contrastResult.passes.aa && contrastResult.passes.aaa) {
      console.log('✅ Test PASSED: High contrast detected correctly');
    } else {
      console.log('❌ Test FAILED: Expected high contrast ratio');
    }
  } catch (error) {
    console.error('❌ Test FAILED with error:', error);
  }
}

/**
 * Test color conversion functionality
 */
function testColorConversion() {
  console.log('\n🎨 Testing Color Conversion...');

  const analyzer = EnhancedColorAnalyzer.getInstance();

  try {
    // Test the colorToHex method
    const testColors = [
      { r: 255, g: 0, b: 0 }, // Red
      { r: 0, g: 255, b: 0 }, // Green
      { r: 0, g: 0, b: 255 }, // Blue
      { r: 0, g: 0, b: 0 }, // Black
      { r: 255, g: 255, b: 255 }, // White
    ];

    const expectedHex = ['#ff0000', '#00ff00', '#0000ff', '#000000', '#ffffff'];

    testColors.forEach((color, index) => {
      const hex = (analyzer as any).colorToHex(color);
      const expected = expectedHex[index];

      if (hex === expected) {
        console.log(`✅ Color conversion test ${index + 1}: ${JSON.stringify(color)} -> ${hex}`);
      } else {
        console.log(`❌ Color conversion test ${index + 1}: Expected ${expected}, got ${hex}`);
      }
    });
  } catch (error) {
    console.error('❌ Color conversion test failed:', error);
  }
}

/**
 * Test advanced color space detection
 */
function testAdvancedColorSpaceDetection() {
  console.log('\n🌈 Testing Advanced Color Space Detection...');

  const analyzer = EnhancedColorAnalyzer.getInstance();

  try {
    const testCases = [
      { color1: '#ff0000', color2: '#00ff00', expected: false }, // Standard hex
      { color1: 'color(display-p3 1 0 0)', color2: '#00ff00', expected: true }, // P3 color
      { color1: 'oklch(0.7 0.15 180)', color2: '#ffffff', expected: true }, // OKLCH
      { color1: 'rgb(255, 0, 0)', color2: 'rgb(0, 255, 0)', expected: false }, // Standard RGB
    ];

    testCases.forEach((testCase, index) => {
      const requiresAdvanced = (analyzer as any).requiresAdvancedAnalysis(
        testCase.color1,
        testCase.color2,
      );

      if (requiresAdvanced === testCase.expected) {
        console.log(
          `✅ Advanced detection test ${index + 1}: ${testCase.color1} + ${testCase.color2} -> ${requiresAdvanced}`,
        );
      } else {
        console.log(
          `❌ Advanced detection test ${index + 1}: Expected ${testCase.expected}, got ${requiresAdvanced}`,
        );
      }
    });
  } catch (error) {
    console.error('❌ Advanced color space detection test failed:', error);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Enhanced Color Analyzer Tests\n');

  await testEnhancedColorAnalyzer();
  testColorConversion();
  testAdvancedColorSpaceDetection();

  console.log('\n✅ All tests completed!');
  console.log(
    '\n📝 Note: If third-party libraries are not installed, the system will use fallback calculations.',
  );
  console.log('   To install enhanced libraries, run: npm install get-contrast colorjs.io');
}

// Export for use in other test files
export {
  testEnhancedColorAnalyzer,
  testColorConversion,
  testAdvancedColorSpaceDetection,
  runAllTests,
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
