const { Client } = require('pg');

async function debugSpecificScan() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'complyuser',
    password: 'complypassword',
    database: 'complychecker_dev'
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database');

    const scanId = 'db252a46-cb51-486d-8b41-f8f5e08cc037';
    console.log(`\n🔍 Analyzing scan: ${scanId}`);

    // Check if scan exists
    const scanResult = await client.query(`
      SELECT id, target_url, scan_status, created_at, overall_score
      FROM wcag_scans 
      WHERE id = $1
    `, [scanId]);

    if (scanResult.rows.length === 0) {
      console.log('❌ Scan not found');
      return;
    }

    const scan = scanResult.rows[0];
    console.log(`   URL: ${scan.target_url}`);
    console.log(`   Status: ${scan.scan_status}`);
    console.log(`   Overall Score: ${scan.overall_score}`);

    // Get all results for this scan
    const allResults = await client.query(`
      SELECT rule_id, rule_name, status, score, 
             LENGTH(evidence::text) as evidence_length,
             total_element_count, failed_element_count, 
             affected_selectors,
             created_at
      FROM wcag_automated_results 
      WHERE scan_id = $1
      ORDER BY rule_id
    `, [scanId]);

    console.log(`\n📊 Total results: ${allResults.rows.length}`);

    // Check specifically for WCAG-009
    const wcag009 = allResults.rows.find(r => r.rule_id === 'WCAG-009');
    
    if (!wcag009) {
      console.log('❌ WCAG-009 result not found in database');
      return;
    }

    console.log(`\n🔍 WCAG-009 Database Record:`);
    console.log(`   Status: ${wcag009.status}`);
    console.log(`   Score: ${wcag009.score}`);
    console.log(`   Evidence Length: ${wcag009.evidence_length} characters`);
    console.log(`   Total Elements: ${wcag009.total_element_count}`);
    console.log(`   Failed Elements: ${wcag009.failed_element_count}`);
    console.log(`   Created: ${wcag009.created_at}`);

    // Get the actual evidence
    const evidenceResult = await client.query(`
      SELECT evidence, affected_selectors
      FROM wcag_automated_results 
      WHERE scan_id = $1 AND rule_id = 'WCAG-009'
    `, [scanId]);

    if (evidenceResult.rows.length > 0) {
      let evidence = [];
      let selectors = [];

      // Handle evidence - could be JSON string or object
      const rawEvidence = evidenceResult.rows[0].evidence;
      if (typeof rawEvidence === 'string') {
        try {
          evidence = JSON.parse(rawEvidence);
        } catch (e) {
          console.log(`   ❌ Evidence parsing error: ${e.message}`);
        }
      } else if (Array.isArray(rawEvidence)) {
        evidence = rawEvidence;
      } else {
        console.log(`   ❌ Evidence is unexpected type: ${typeof rawEvidence}`);
        console.log(`   Raw evidence: ${JSON.stringify(rawEvidence).substring(0, 200)}...`);
      }

      // Handle selectors - could be JSON string or array
      const rawSelectors = evidenceResult.rows[0].affected_selectors;
      if (typeof rawSelectors === 'string') {
        try {
          selectors = JSON.parse(rawSelectors);
        } catch (e) {
          console.log(`   ❌ Selectors parsing error: ${e.message}`);
        }
      } else if (Array.isArray(rawSelectors)) {
        selectors = rawSelectors;
      }

      console.log(`\n📋 Evidence Analysis:`);
      console.log(`   Evidence items: ${evidence.length}`);
      console.log(`   Affected selectors: ${selectors.length}`);

      // Analyze evidence types and severities
      const evidenceStats = evidence.reduce((stats, item) => {
        stats.types[item.type] = (stats.types[item.type] || 0) + 1;
        stats.severities[item.severity] = (stats.severities[item.severity] || 0) + 1;
        return stats;
      }, { types: {}, severities: {} });

      console.log(`   Evidence types:`, evidenceStats.types);
      console.log(`   Evidence severities:`, evidenceStats.severities);

      // Show first few evidence items
      console.log(`\n📝 Sample Evidence Items:`);
      evidence.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. Type: ${item.type}, Severity: ${item.severity}`);
        console.log(`      Description: ${item.description}`);
        console.log(`      Value: ${item.value ? item.value.substring(0, 100) + '...' : 'N/A'}`);
        console.log(`      Selector: ${item.selector || 'N/A'}`);
        console.log('');
      });

      // Show affected selectors
      console.log(`\n🎯 Affected Selectors (first 10):`);
      selectors.slice(0, 10).forEach((selector, index) => {
        console.log(`   ${index + 1}. ${selector}`);
      });
    }

    // Check failed vs passed results
    const failedResults = allResults.rows.filter(r => r.status === 'failed');
    const passedResults = allResults.rows.filter(r => r.status === 'passed');

    console.log(`\n📈 Scan Summary:`);
    console.log(`   Failed checks: ${failedResults.length}`);
    console.log(`   Passed checks: ${passedResults.length}`);
    console.log(`   Total checks: ${allResults.rows.length}`);

    console.log(`\n❌ Failed Checks:`);
    failedResults.forEach(result => {
      console.log(`   ${result.rule_id}: ${result.rule_name} (Score: ${result.score})`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.end();
  }
}

debugSpecificScan();
