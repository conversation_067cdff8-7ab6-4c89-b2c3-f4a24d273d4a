# WCAG Implementation Part 05: High & Medium Automation Checks

## Overview

This document implements the 7 WCAG rules with 60-80% automation. These checks provide good automated coverage with separate manual review item tracking for complex scenarios.

## ⚠️ CRITICAL REQUIREMENTS

### 🚫 NO ANY[] TYPES
**STRICTLY PROHIBITED**: All check implementations must use strict TypeScript typing.

### ✅ DEPENDENCIES
- **Parts 01-04 Complete**: Foundation, utilities, and higher automation checks
- **60-80% Automation**: Good automation with separate manual review item tracking
- **Strict Separation**: Automated results completely separate from manual review items

## Prerequisites

- Parts 01-04 completed successfully
- Automated check template with manual tracking available
- NLP and readability analysis libraries installed

## High & Medium Automation Rules (7 Rules - 60-80% Automation)

1. **Rule 2: Captions (Prerecorded)** - 80% automated (caption file analysis)
2. **Rule 6: Focus Order** - 75% automated (visual layout correlation)
3. **Rule 13: Dragging Movements** - 70% automated (alternative detection)
4. **Rule 15: Consistent Help** - 80% automated (multi-page pattern analysis)
5. **Rule 18: Text and Wording (3.0)** - 75% automated (readability + NLP)
6. **Rule 20: Motor (3.0)** - 80% automated (gesture analysis + alternatives)
7. **Rule 21: Pronunciation & Meaning (3.0)** - 60% automated (pattern detection)

## Step 1: Install Additional Dependencies

### 1.1 Add NLP and Analysis Libraries

```bash
cd backend

# Add readability and language analysis
npm install --save flesch-kincaid readability-js syllable
npm install --save natural compromise sentiment
npm install --save-dev @types/natural
```

## Step 2: Captions Check (Rule 2)

### 2.1 Implement Captions Check

Create `backend/src/compliance/wcag/checks/captions.ts`:

```typescript
/**
 * WCAG Rule 2: Captions (Prerecorded) - 1.2.2
 * 80% Automated - Manual review for caption accuracy and timing
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export interface CaptionTrack {
  src: string;
  kind: string;
  srclang: string;
  label: string;
  isDefault: boolean;
}

export interface VideoAnalysis {
  selector: string;
  src: string;
  hasTracks: boolean;
  tracks: CaptionTrack[];
  hasControls: boolean;
  isAutoplay: boolean;
  duration?: number;
}

export class CaptionsCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform captions check - 80% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-002',
      'Captions (Prerecorded)',
      'perceivable',
      0.06,
      'A',
      0.80, // 80% automation rate
      config,
      this.executeCaptionsCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive captions analysis
   */
  private async executeCaptionsCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];
    
    // Analyze video and audio elements
    const mediaAnalysis = await this.analyzeMediaElements(page);
    
    let totalElements = 0;
    let passedElements = 0;
    let manualReviewCount = 0;
    
    // Process video elements
    for (const video of mediaAnalysis.videos) {
      totalElements++;
      
      const captionAnalysis = await this.analyzeCaptions(page, video);
      
      if (captionAnalysis.status === 'passed') {
        passedElements++;
        
        evidence.push({
          type: 'text',
          description: 'Video has proper caption tracks',
          value: `${video.tracks.length} caption tracks found`,
          selector: video.selector,
          severity: 'info'
        });
      } else if (captionAnalysis.status === 'failed') {
        issues.push(`Video missing captions: ${video.selector}`);
        
        evidence.push({
          type: 'text',
          description: 'Video lacks proper captions',
          value: captionAnalysis.reason,
          selector: video.selector,
          severity: 'error'
        });
        
        recommendations.push(`${video.selector}: ${captionAnalysis.recommendation}`);
      } else if (captionAnalysis.status === 'manual_review') {
        manualReviewCount++;
        
        manualReviewItems.push({
          selector: video.selector,
          description: 'Caption quality and accuracy verification',
          automatedFindings: captionAnalysis.reason,
          reviewRequired: 'Verify caption accuracy, timing, speaker identification, and sound effects',
          priority: 'high',
          estimatedTime: 15
        });
        
        evidence.push({
          type: 'text',
          description: 'Video captions require manual review',
          value: captionAnalysis.reason,
          selector: video.selector,
          severity: 'warning'
        });
      }
    }
    
    // Process audio elements
    for (const audio of mediaAnalysis.audioElements) {
      totalElements++;
      
      if (audio.hasTranscript) {
        passedElements++;
        
        evidence.push({
          type: 'text',
          description: 'Audio has transcript or captions',
          value: 'Transcript or caption support detected',
          selector: audio.selector,
          severity: 'info'
        });
      } else {
        manualReviewCount++;
        
        manualReviewItems.push({
          selector: audio.selector,
          description: 'Audio transcript verification',
          automatedFindings: 'Audio element without detectable transcript',
          reviewRequired: 'Verify if audio needs transcript and if one is provided elsewhere on page',
          priority: 'medium',
          estimatedTime: 10
        });
      }
    }
    
    // Calculate automated score
    const automatedElements = totalElements - manualReviewCount;
    const automatedScore = automatedElements > 0 ? Math.round((passedElements / automatedElements) * 100) : 100;
    
    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Media captions analysis summary',
      value: `${passedElements}/${automatedElements} media elements pass automated checks, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error'
    });
    
    if (automatedScore < 100) {
      recommendations.unshift('Provide captions for all prerecorded video content');
      recommendations.push('Ensure captions include speaker identification and sound effects');
      recommendations.push('Verify caption timing and accuracy');
    }
    
    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.80
    };
  }

  /**
   * Analyze all media elements on the page
   */
  private async analyzeMediaElements(page: Page): Promise<{
    videos: VideoAnalysis[];
    audioElements: Array<{ selector: string; hasTranscript: boolean }>;
  }> {
    return await page.evaluate(() => {
      const videos: VideoAnalysis[] = [];
      const audioElements: Array<{ selector: string; hasTranscript: boolean }> = [];
      
      // Analyze video elements
      const videoElements = document.querySelectorAll('video');
      videoElements.forEach((video, index) => {
        const computedStyle = window.getComputedStyle(video);
        const isVisible = computedStyle.display !== 'none' &&
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';
        
        if (isVisible) {
          const tracks: CaptionTrack[] = [];
          const trackElements = video.querySelectorAll('track');
          
          trackElements.forEach(track => {
            tracks.push({
              src: track.getAttribute('src') || '',
              kind: track.getAttribute('kind') || '',
              srclang: track.getAttribute('srclang') || '',
              label: track.getAttribute('label') || '',
              isDefault: track.hasAttribute('default')
            });
          });
          
          videos.push({
            selector: this.generateSelector(video, 'video', index),
            src: video.src || video.currentSrc || '',
            hasTracks: tracks.length > 0,
            tracks,
            hasControls: video.hasAttribute('controls'),
            isAutoplay: video.hasAttribute('autoplay'),
            duration: video.duration
          });
        }
      });
      
      // Analyze audio elements
      const audioElementsNodes = document.querySelectorAll('audio');
      audioElementsNodes.forEach((audio, index) => {
        const computedStyle = window.getComputedStyle(audio);
        const isVisible = computedStyle.display !== 'none' &&
                         computedStyle.visibility !== 'hidden';
        
        if (isVisible) {
          // Check for transcript links or text nearby
          const hasTranscript = this.checkForTranscript(audio);
          
          audioElements.push({
            selector: this.generateSelector(audio, 'audio', index),
            hasTranscript
          });
        }
      });
      
      // Check for embedded videos (YouTube, Vimeo, etc.)
      const iframes = document.querySelectorAll('iframe[src*="youtube"], iframe[src*="vimeo"], iframe[src*="video"]');
      iframes.forEach((iframe, index) => {
        const computedStyle = window.getComputedStyle(iframe);
        const isVisible = computedStyle.display !== 'none' &&
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';
        
        if (isVisible) {
          videos.push({
            selector: this.generateSelector(iframe, 'iframe', index),
            src: iframe.getAttribute('src') || '',
            hasTracks: false, // Cannot detect tracks in embedded videos
            tracks: [],
            hasControls: true, // Assume embedded videos have controls
            isAutoplay: iframe.src.includes('autoplay=1'),
            duration: undefined
          });
        }
      });
      
      return { videos, audioElements };
    });
  }

  /**
   * Analyze caption quality and completeness
   */
  private async analyzeCaptions(page: Page, video: VideoAnalysis): Promise<{
    status: 'passed' | 'failed' | 'manual_review';
    reason: string;
    recommendation?: string;
  }> {
    // Check if video has any caption tracks
    if (!video.hasTracks || video.tracks.length === 0) {
      // Check if it's an embedded video (different handling)
      if (video.selector.includes('iframe')) {
        return {
          status: 'manual_review',
          reason: 'Embedded video - cannot detect captions automatically'
        };
      }
      
      return {
        status: 'failed',
        reason: 'No caption tracks found',
        recommendation: 'Add <track> elements with captions'
      };
    }
    
    // Check for proper caption tracks (kind="captions" or kind="subtitles")
    const captionTracks = video.tracks.filter(track => 
      track.kind === 'captions' || track.kind === 'subtitles'
    );
    
    if (captionTracks.length === 0) {
      return {
        status: 'failed',
        reason: 'Track elements found but none are captions or subtitles',
        recommendation: 'Set track kind="captions" for caption tracks'
      };
    }
    
    // Check for language specification
    const tracksWithLang = captionTracks.filter(track => track.srclang);
    if (tracksWithLang.length === 0) {
      return {
        status: 'failed',
        reason: 'Caption tracks missing language specification',
        recommendation: 'Add srclang attribute to caption tracks'
      };
    }
    
    // Check if caption files are accessible
    const accessibleTracks = await this.validateCaptionFiles(page, captionTracks);
    
    if (accessibleTracks === 0) {
      return {
        status: 'failed',
        reason: 'Caption track files are not accessible',
        recommendation: 'Ensure caption files exist and are accessible'
      };
    }
    
    // If basic checks pass, require manual review for quality
    return {
      status: 'manual_review',
      reason: `${captionTracks.length} caption tracks found - quality verification needed`
    };
  }

  /**
   * Validate that caption files are accessible
   */
  private async validateCaptionFiles(page: Page, tracks: CaptionTrack[]): Promise<number> {
    let accessibleCount = 0;
    
    for (const track of tracks) {
      if (track.src) {
        try {
          // Try to fetch the caption file
          const response = await page.evaluate(async (src) => {
            try {
              const response = await fetch(src);
              return response.ok;
            } catch {
              return false;
            }
          }, track.src);
          
          if (response) {
            accessibleCount++;
          }
        } catch (error) {
          console.warn(`Could not validate caption file: ${track.src}`);
        }
      }
    }
    
    return accessibleCount;
  }

  /**
   * Check for transcript links or text near audio element
   */
  private checkForTranscript(audio: HTMLAudioElement): boolean {
    // Check for transcript links in nearby elements
    const parent = audio.parentElement;
    if (!parent) return false;
    
    const nearbyText = parent.textContent?.toLowerCase() || '';
    const transcriptKeywords = ['transcript', 'text version', 'full text', 'audio description'];
    
    const hasTranscriptKeyword = transcriptKeywords.some(keyword => 
      nearbyText.includes(keyword)
    );
    
    if (hasTranscriptKeyword) return true;
    
    // Check for links with transcript-related text
    const nearbyLinks = parent.querySelectorAll('a');
    for (const link of nearbyLinks) {
      const linkText = link.textContent?.toLowerCase() || '';
      if (transcriptKeywords.some(keyword => linkText.includes(keyword))) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Generate unique selector for element
   */
  private generateSelector(element: Element, tagName: string, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.toString().split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${tagName}.${classes.join('.')}`;
      }
    }
    
    return `${tagName}:nth-of-type(${index + 1})`;
  }
}
```

## Step 3: Focus Order Check (Rule 6)

### 3.1 Implement Focus Order Check

Create `backend/src/compliance/wcag/checks/focus-order.ts`:

```typescript
/**
 * WCAG Rule 6: Focus Order - 2.4.3
 * 75% Automated - Manual review for complex layout logical flow
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { FocusTracker } from '../utils/focus-tracker';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { WcagEvidence } from '../types';

export class FocusOrderCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform focus order check - 75% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-006',
      'Focus Order',
      'operable',
      0.08,
      'A',
      0.75, // 75% automation rate
      config,
      this.executeFocusOrderCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive focus order analysis
   */
  private async executeFocusOrderCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];
    
    // Analyze focus order
    const focusOrder = await FocusTracker.analyzeFocusOrder(page);
    
    // Get all focusable elements for layout analysis
    const focusableElements = await FocusTracker.getFocusableElements(page);
    const selectors = focusableElements.map(el => el.selector);
    
    // Analyze layout for visual order comparison
    const layoutAnalysis = await LayoutAnalyzer.analyzeLayout(page, selectors);
    
    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;
    
    // Check basic focus order logic
    totalChecks++;
    if (focusOrder.isLogical) {
      passedChecks++;
      
      evidence.push({
        type: 'text',
        description: 'Focus order follows logical sequence',
        value: `${focusOrder.elements.length} focusable elements in logical order`,
        severity: 'info'
      });
    } else {
      issues.push('Focus order does not follow logical sequence');
      
      evidence.push({
        type: 'text',
        description: 'Focus order has logical issues',
        value: `Issues: ${focusOrder.issues.join(', ')}`,
        severity: 'error'
      });
      
      focusOrder.recommendations.forEach(rec => recommendations.push(rec));
    }
    
    // Check for positive tabindex values
    totalChecks++;
    const positiveTabindexElements = await this.findPositiveTabindexElements(page);
    
    if (positiveTabindexElements.length === 0) {
      passedChecks++;
      
      evidence.push({
        type: 'text',
        description: 'No positive tabindex values found',
        value: 'Focus order relies on DOM order (recommended)',
        severity: 'info'
      });
    } else {
      issues.push(`${positiveTabindexElements.length} elements use positive tabindex values`);
      
      evidence.push({
        type: 'text',
        description: 'Positive tabindex values detected',
        value: `Elements: ${positiveTabindexElements.join(', ')}`,
        severity: 'warning'
      });
      
      recommendations.push('Avoid positive tabindex values - use DOM order instead');
    }
    
    // Check visual vs DOM order correlation
    totalChecks++;
    if (layoutAnalysis.isLogicalOrder) {
      passedChecks++;
      
      evidence.push({
        type: 'text',
        description: 'Visual order matches DOM order',
        value: 'Reading order is logical',
        severity: 'info'
      });
    } else {
      // This requires manual review for complex layouts
      manualReviewCount++;
      
      manualReviewItems.push({
        selector: 'page',
        description: 'Complex layout focus order verification',
        automatedFindings: 'Visual order differs from DOM order',
        reviewRequired: 'Verify that focus order makes sense for the visual layout and user workflow',
        priority: 'high',
        estimatedTime: 10
      });
      
      evidence.push({
        type: 'text',
        description: 'Visual order differs from DOM order - manual review needed',
        value: 'Complex layout detected',
        severity: 'warning'
      });
    }
    
    // Check for skip links
    totalChecks++;
    const skipLinks = await this.findSkipLinks(page);
    
    if (skipLinks.length > 0) {
      passedChecks++;
      
      evidence.push({
        type: 'text',
        description: 'Skip links found',
        value: `${skipLinks.length} skip links detected`,
        severity: 'info'
      });
    } else {
      // Skip links are recommended but not always required
      manualReviewItems.push({
        selector: 'page',
        description: 'Skip link necessity verification',
        automatedFindings: 'No skip links detected',
        reviewRequired: 'Verify if skip links are needed for this page layout',
        priority: 'medium',
        estimatedTime: 5
      });
    }
    
    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore = automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;
    
    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Focus order analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error'
    });
    
    if (automatedScore < 100) {
      recommendations.unshift('Ensure focus order follows logical reading sequence');
      recommendations.push('Avoid using positive tabindex values');
      recommendations.push('Consider adding skip links for complex layouts');
    }
    
    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.75
    };
  }

  /**
   * Find elements with positive tabindex values
   */
  private async findPositiveTabindexElements(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const elements = document.querySelectorAll('[tabindex]');
      const positiveTabindexElements: string[] = [];
      
      elements.forEach((element, index) => {
        const tabindex = parseInt(element.getAttribute('tabindex') || '0');
        if (tabindex > 0) {
          positiveTabindexElements.push(this.generateSelector(element, index));
        }
      });
      
      return positiveTabindexElements;
    });
  }

  /**
   * Find skip links on the page
   */
  private async findSkipLinks(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const links = document.querySelectorAll('a[href^="#"]');
      const skipLinks: string[] = [];
      
      links.forEach((link, index) => {
        const linkText = link.textContent?.toLowerCase() || '';
        const skipKeywords = ['skip', 'jump', 'go to main', 'go to content', 'bypass'];
        
        if (skipKeywords.some(keyword => linkText.includes(keyword))) {
          skipLinks.push(this.generateSelector(link, index));
        }
      });
      
      return skipLinks;
    });
  }

  /**
   * Generate unique selector for element
   */
  private generateSelector(element: Element, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.toString().split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }
    
    return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
  }
}
```

## Step 4: Text and Wording Check (Rule 18)

### 4.1 Implement Text and Wording Check

Create `backend/src/compliance/wcag/checks/text-wording.ts`:

```typescript
/**
 * WCAG Rule 18: Text and Wording - 2.2 (WCAG 3.0)
 * 75% Automated - Manual review for context-appropriate language
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

// Import readability libraries
const flesch = require('flesch-kincaid');
const syllable = require('syllable');

export interface ReadabilityAnalysis {
  fleschKincaidGrade: number;
  fleschReadingEase: number;
  averageWordsPerSentence: number;
  averageSyllablesPerWord: number;
  complexWords: string[];
  jargonTerms: string[];
  readingLevel: 'elementary' | 'middle' | 'high' | 'college' | 'graduate';
}

export class TextWordingCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform text and wording check - 75% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-018',
      'Text and Wording',
      'understandable',
      0.04,
      'AA',
      0.75, // 75% automation rate
      config,
      this.executeTextWordingCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive text and wording analysis
   */
  private async executeTextWordingCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Extract and analyze text content
    const textContent = await this.extractTextContent(page);

    if (textContent.totalWords < 50) {
      // Not enough text to analyze meaningfully
      evidence.push({
        type: 'text',
        description: 'Insufficient text content for analysis',
        value: `Only ${textContent.totalWords} words found`,
        severity: 'info'
      });

      return {
        automatedScore: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
        automationRate: 0.75
      };
    }

    // Perform readability analysis
    const readabilityAnalysis = this.analyzeReadability(textContent.mainText);

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    // Check reading level
    totalChecks++;
    if (readabilityAnalysis.fleschKincaidGrade <= 9) {
      passedChecks++;

      evidence.push({
        type: 'measurement',
        description: 'Text reading level is appropriate',
        value: `Grade level: ${readabilityAnalysis.fleschKincaidGrade.toFixed(1)} (${readabilityAnalysis.readingLevel})`,
        severity: 'info'
      });
    } else if (readabilityAnalysis.fleschKincaidGrade <= 12) {
      // High school level - may need manual review
      manualReviewCount++;

      manualReviewItems.push({
        selector: 'page',
        description: 'High reading level content verification',
        automatedFindings: `Grade level: ${readabilityAnalysis.fleschKincaidGrade.toFixed(1)}`,
        reviewRequired: 'Verify if high reading level is appropriate for target audience',
        priority: 'medium',
        estimatedTime: 10
      });
    } else {
      issues.push(`Text reading level too high: grade ${readabilityAnalysis.fleschKincaidGrade.toFixed(1)}`);

      evidence.push({
        type: 'measurement',
        description: 'Text reading level is too high',
        value: `Grade level: ${readabilityAnalysis.fleschKincaidGrade.toFixed(1)} (${readabilityAnalysis.readingLevel})`,
        severity: 'error'
      });

      recommendations.push('Simplify language to improve readability');
    }

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore = automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.75
    };
  }

  /**
   * Extract text content from page
   */
  private async extractTextContent(page: Page): Promise<{
    mainText: string;
    totalWords: number;
    headings: string[];
    paragraphs: string[];
  }> {
    return await page.evaluate(() => {
      // Get main content text, excluding navigation, headers, footers
      const excludeSelectors = [
        'nav', 'header', 'footer', 'aside',
        '.navigation', '.nav', '.menu', '.sidebar',
        '.header', '.footer', '.advertisement', '.ad'
      ];

      let mainText = '';
      const headings: string[] = [];
      const paragraphs: string[] = [];

      // Get headings
      const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headingElements.forEach(heading => {
        const text = heading.textContent?.trim();
        if (text) {
          headings.push(text);
          mainText += text + ' ';
        }
      });

      // Get paragraph text
      const paragraphElements = document.querySelectorAll('p, li, td, th, div');
      paragraphElements.forEach(element => {
        // Skip if element is inside excluded sections
        const isExcluded = excludeSelectors.some(selector =>
          element.closest(selector) !== null
        );

        if (!isExcluded) {
          const text = element.textContent?.trim();
          if (text && text.length > 10) {
            paragraphs.push(text);
            mainText += text + ' ';
          }
        }
      });

      const words = mainText.trim().split(/\s+/).filter(word => word.length > 0);

      return {
        mainText: mainText.trim(),
        totalWords: words.length,
        headings,
        paragraphs
      };
    });
  }

  /**
   * Analyze text readability using multiple metrics
   */
  private analyzeReadability(text: string): ReadabilityAnalysis {
    if (!text || text.length < 100) {
      return {
        fleschKincaidGrade: 0,
        fleschReadingEase: 100,
        averageWordsPerSentence: 0,
        averageSyllablesPerWord: 0,
        complexWords: [],
        jargonTerms: [],
        readingLevel: 'elementary'
      };
    }

    // Calculate Flesch-Kincaid metrics
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const totalSyllables = words.reduce((sum, word) => sum + syllable(word), 0);

    const averageWordsPerSentence = words.length / sentences.length;
    const averageSyllablesPerWord = totalSyllables / words.length;

    const fleschKincaidGrade = 0.39 * averageWordsPerSentence + 11.8 * averageSyllablesPerWord - 15.59;
    const fleschReadingEase = 206.835 - 1.015 * averageWordsPerSentence - 84.6 * averageSyllablesPerWord;

    // Identify complex words (3+ syllables)
    const complexWords = words.filter(word => syllable(word) >= 3);

    // Identify potential jargon terms
    const jargonTerms = this.identifyJargonTerms(words);

    // Determine reading level
    let readingLevel: 'elementary' | 'middle' | 'high' | 'college' | 'graduate';
    if (fleschKincaidGrade <= 5) readingLevel = 'elementary';
    else if (fleschKincaidGrade <= 8) readingLevel = 'middle';
    else if (fleschKincaidGrade <= 12) readingLevel = 'high';
    else if (fleschKincaidGrade <= 16) readingLevel = 'college';
    else readingLevel = 'graduate';

    return {
      fleschKincaidGrade: Math.max(0, fleschKincaidGrade),
      fleschReadingEase,
      averageWordsPerSentence,
      averageSyllablesPerWord,
      complexWords: [...new Set(complexWords)], // Remove duplicates
      jargonTerms: [...new Set(jargonTerms)], // Remove duplicates
      readingLevel
    };
  }

  /**
   * Identify potential jargon and technical terms
   */
  private identifyJargonTerms(words: string[]): string[] {
    const jargonPatterns = [
      // Technical suffixes
      /\w+(tion|sion|ment|ness|ity|ism|ology|ography)$/i,
      // Medical/scientific terms
      /\w*(medical|clinical|therapeutic|diagnostic|pharmaceutical|biochemical|physiological)\w*/i,
      // Business jargon
      /\w*(leverage|synergy|paradigm|optimization|methodology|implementation|infrastructure)\w*/i,
      // Legal terms
      /\w*(pursuant|heretofore|whereas|aforementioned|notwithstanding)\w*/i,
      // Technical terms
      /\w*(algorithm|configuration|architecture|framework|protocol|interface)\w*/i
    ];

    const jargonTerms: string[] = [];

    words.forEach(word => {
      const cleanWord = word.replace(/[^\w]/g, '').toLowerCase();

      if (cleanWord.length > 6) { // Only check longer words
        const isJargon = jargonPatterns.some(pattern => pattern.test(cleanWord));
        if (isJargon) {
          jargonTerms.push(cleanWord);
        }
      }
    });

    return jargonTerms;
  }
}
```

## Step 5: Check Registry Update

### 5.1 Update Check Registry

Update `backend/src/compliance/wcag/checks/index.ts`:

```typescript
/**
 * WCAG Checks Registry - Updated with High/Medium Automation
 * Central registry for all WCAG check implementations
 */

// Fully Automated Checks (100% automation)
export { ContrastMinimumCheck } from './contrast-minimum';
export { FocusVisibleCheck } from './focus-visible';
export { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
export { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
export { FocusAppearanceCheck } from './focus-appearance';
export { TargetSizeCheck } from './target-size';

// Very High Automation Checks (85-95% automation)
export { NonTextContentCheck } from './non-text-content';
export { InfoRelationshipsCheck } from './info-relationships';
export { KeyboardCheck } from './keyboard';

// High & Medium Automation Checks (60-80% automation)
export { CaptionsCheck } from './captions';
export { FocusOrderCheck } from './focus-order';
export { TextWordingCheck } from './text-wording';

// Check registry mapping
export const WCAG_CHECK_REGISTRY = {
  // Fully Automated (100%)
  'WCAG-004': ContrastMinimumCheck,
  'WCAG-007': FocusVisibleCheck,
  'WCAG-010': FocusNotObscuredMinimumCheck,
  'WCAG-011': FocusNotObscuredEnhancedCheck,
  'WCAG-012': FocusAppearanceCheck,
  'WCAG-014': TargetSizeCheck,

  // Very High Automation (85-95%)
  'WCAG-001': NonTextContentCheck,
  'WCAG-003': InfoRelationshipsCheck,
  'WCAG-005': KeyboardCheck,

  // High & Medium Automation (60-80%)
  'WCAG-002': CaptionsCheck,
  'WCAG-006': FocusOrderCheck,
  'WCAG-018': TextWordingCheck
} as const;

/**
 * Get check implementation by rule ID
 */
export function getCheckImplementation(ruleId: string) {
  return WCAG_CHECK_REGISTRY[ruleId as keyof typeof WCAG_CHECK_REGISTRY];
}

/**
 * Get automation level for rule
 */
export function getAutomationLevel(ruleId: string): number {
  const automationLevels: Record<string, number> = {
    // Fully Automated
    'WCAG-004': 1.00, 'WCAG-007': 1.00, 'WCAG-010': 1.00,
    'WCAG-011': 1.00, 'WCAG-012': 1.00, 'WCAG-014': 1.00,

    // Very High Automation
    'WCAG-001': 0.95, 'WCAG-003': 0.90, 'WCAG-005': 0.85,

    // High & Medium Automation
    'WCAG-002': 0.80, 'WCAG-006': 0.75, 'WCAG-018': 0.75
  };

  return automationLevels[ruleId] || 0.50;
}

/**
 * Get all implemented rule IDs
 */
export function getImplementedRuleIds(): string[] {
  return Object.keys(WCAG_CHECK_REGISTRY);
}
```

## Validation Checklist

- [ ] High & medium automation checks implemented
- [ ] Captions check with 80% automation (caption file analysis)
- [ ] Focus order check with 75% automation (layout correlation)
- [ ] Text and wording check with 75% automation (readability analysis)
- [ ] All checks use strict TypeScript (no `any[]` types)
- [ ] Manual review items properly structured with priorities
- [ ] Check registry updated with all implementations
- [ ] Ready for API and orchestrator implementation

## Next Steps

Continue with **Part 06: API Routes & Authentication** to implement the RESTful API endpoints with Keycloak protection.

---

*These high and medium automation checks complete the core WCAG rule implementations, providing comprehensive coverage with structured manual review processes for complex scenarios.*
