/**
 * WCAG Scan Results Overview Component
 * Displays high-level scan results and metrics
 */

'use client';

import React from 'react';
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  BarChart3,
  FileText,
  ClipboardCheck,
} from 'lucide-react';
import { WcagScanResult, RiskLevel } from '../../types/wcag';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert } from '../ui/alert';
import { Button } from '../ui/button';
import RiskMessaging from './RiskMessaging';

interface WcagScanOverviewProps {
  scanResult: WcagScanResult;
  onExport?: () => void; // Changed to simple callback to open export dialog
  onManualReview?: () => void; // Callback to navigate to manual review
  loading?: boolean;
}

const WcagScanOverview: React.FC<WcagScanOverviewProps> = ({
  scanResult,
  onExport,
  onManualReview,
  loading,
}) => {
  /**
   * Get risk level color
   */
  const getRiskColor = (
    riskLevel: RiskLevel,
  ): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (riskLevel) {
      case 'low':
        return 'secondary';
      case 'medium':
        return 'outline';
      case 'high':
        return 'destructive';
      case 'critical':
        return 'destructive';
      default:
        return 'default';
    }
  };

  /**
   * Format duration
   */
  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  /**
   * Calculate automation percentage
   */
  const automationPercentage = Math.round(scanResult.summary.automationRate * 100);

  // Calculate total failed elements across all checks
  const totalFailedElements = scanResult.checks?.reduce((total, check) => {
    return total + (check.evidence?.length || 0);
  }, 0) || 0;

  // Calculate compliance status
  const isCompliant = scanResult.overallScore >= 80;
  const complianceStatus = isCompliant ? 'COMPLIANT' : 'NOT COMPLIANT';

  return (
    <div className="space-y-6">
      {/* AccessibilityChecker.org-style Header */}
      <div className="text-center space-y-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Audit results for {new URL(scanResult.targetUrl).hostname}:
        </h1>

        {/* Download Audit Button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
            disabled={loading}
            className="text-sm"
          >
            📥 Download audit
          </Button>
        </div>

        {/* Scan Progress Info */}
        <p className="text-sm text-gray-600 dark:text-gray-400">
          ⭕ You've scanned 1 page so far. Scan your entire domain to uncover all critical accessibility issues.{' '}
          <a href="#" className="text-blue-600 hover:underline">See Pricing</a>
        </p>
      </div>

      {/* Enhanced Risk Messaging - AccessibilityChecker.org Style */}
      <RiskMessaging
        overallScore={scanResult.overallScore}
        failedChecks={scanResult.summary.failedAutomatedChecks}
        criticalIssues={scanResult.checks?.filter(c => c.status === 'failed' && c.level === 'A').length || 0}
        levelAchieved={scanResult.levelAchieved}
        riskLevel={scanResult.riskLevel}
        targetUrl={scanResult.targetUrl}
      />

      {/* Large Fix Button - AccessibilityChecker.org Style */}
      {totalFailedElements > 0 && (
        <div className="text-center">
          <Button
            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
            size="lg"
            onClick={onManualReview}
          >
            🔧 Fix {totalFailedElements} issue{totalFailedElements !== 1 ? 's' : ''}
          </Button>
        </div>
      )}

      {/* AccessibilityChecker.org-style Audit Score and Criteria */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Audit Score Circle */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Audit Score:</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center">
            <div className="relative w-32 h-32">
              {/* Circular Progress */}
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  stroke="#e5e7eb"
                  strokeWidth="8"
                  fill="none"
                />
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  stroke={isCompliant ? "#10b981" : "#ef4444"}
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray={`${(scanResult.overallScore / 100) * 314} 314`}
                  strokeLinecap="round"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className={`text-2xl font-bold ${isCompliant ? 'text-green-600' : 'text-red-600'}`}>
                  {scanResult.overallScore}%
                </span>
              </div>
            </div>
          </CardContent>
          <CardContent className="text-center pt-0">
            <p className="text-sm text-gray-600">
              Websites with a score lower than 95 are at risk of accessibility lawsuits.
            </p>
          </CardContent>
        </Card>

        {/* WCAG 2.2 Criteria */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              WCAG 2.2 Criteria:
              <Button variant="ghost" size="sm" className="text-blue-600 p-0 h-auto">
                ❓ What is the WCAG?
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                Critical Issues
              </span>
              <span className="font-semibold text-red-600">
                {scanResult.checks?.filter(c => c.status === 'failed' && c.level === 'A').length || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Passed Audits
              </span>
              <span className="font-semibold text-green-600">
                {scanResult.summary.passedAutomatedChecks}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="flex items-center gap-2">
                <ClipboardCheck className="h-4 w-4 text-blue-500" />
                Required Manual Audits
              </span>
              <span className="font-semibold text-blue-600">
                {scanResult.summary.manualReviewItems || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-gray-500" />
                Not Applicable
              </span>
              <span className="font-semibold text-gray-600">
                {scanResult.checks?.filter(c => c.status === 'not_applicable').length || 0}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AccessibilityChecker.org-style Test Categories */}
      <div className="space-y-6">
        {/* Critical Issues Tab */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="bg-red-100 border-b border-red-200">
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2 text-red-800">
                <XCircle className="h-5 w-5" />
                Critical Issues ({scanResult.checks?.filter(c => c.status === 'failed' && c.level === 'A').length || 0})
              </span>
              <Button variant="ghost" size="sm" className="text-red-600">
                ▼
              </Button>
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Interaction and Navigation Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Interaction and Navigation Tests ℹ️</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {scanResult.checks?.filter(c => c.category === 'operable').slice(0, 3).map((check, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    check.status === 'failed' ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'
                  }`}>
                    {check.status === 'failed' ? '⚠️' : '✓'}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{check.title}</p>
                    <p className="text-xs text-gray-600">
                      {check.evidence?.length || 0} element{(check.evidence?.length || 0) !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="text-blue-600">
                  ▼
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Screen Reader and Assistive Technology Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Screen Reader and Assistive Technology Tests ℹ️</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {scanResult.checks?.filter(c => c.category === 'perceivable').slice(0, 4).map((check, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    check.status === 'failed' ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'
                  }`}>
                    {check.status === 'failed' ? '⚠️' : '✓'}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{check.title}</p>
                    <p className="text-xs text-gray-600">
                      {check.evidence?.length || 0} element{(check.evidence?.length || 0) !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="text-blue-600">
                  ▼
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Visual and Structural Accessibility Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Visual and Structural Accessibility Tests ℹ️</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {scanResult.checks?.filter(c => c.category === 'understandable' || c.category === 'robust').slice(0, 3).map((check, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    check.status === 'failed' ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'
                  }`}>
                    {check.status === 'failed' ? '⚠️' : '✓'}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{check.title}</p>
                    <p className="text-xs text-gray-600">
                      {check.evidence?.length || 0} element{(check.evidence?.length || 0) !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="text-blue-600">
                  ▼
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* AccessibilityChecker.org-style Footer */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold">Accessibility guidelines and legislation we test by:</h3>

            <div className="flex justify-center items-center gap-8 flex-wrap">
              <div className="flex items-center gap-2">
                <span className="text-2xl">🌐</span>
                <div className="text-left">
                  <p className="font-semibold">WCAG 2.2</p>
                  <p className="text-sm text-gray-600">ADA</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-2xl">📋</span>
                <div className="text-left">
                  <p className="font-semibold">Section 508</p>
                  <p className="text-sm text-gray-600">SCOPE</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-2xl">🇪🇺</span>
                <div className="text-left">
                  <p className="font-semibold">EN 301 549</p>
                  <p className="text-sm text-gray-600">EU 301 549</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-center gap-4 pt-4">
              <Button
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2"
                onClick={onManualReview}
              >
                🔧 Start fixing
              </Button>

              <Button
                variant="outline"
                className="px-6 py-2"
                onClick={onExport}
                disabled={loading}
              >
                📊 Export Report
              </Button>
            </div>

            {/* Scan Metadata */}
            <div className="pt-4 border-t border-gray-200 text-sm text-gray-600 space-y-1">
              <p><strong>URL:</strong> {scanResult.targetUrl}</p>
              <p><strong>Scan Date:</strong> {scanResult.scanTimestamp ? new Date(scanResult.scanTimestamp).toLocaleDateString() : 'N/A'}</p>
              <p><strong>Duration:</strong> {scanResult.scanDuration ? formatDuration(scanResult.scanDuration) : 'N/A'}</p>
              <p><strong>Total Checks:</strong> {scanResult.summary.totalAutomatedChecks}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WcagScanOverview;
