import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';

interface FindingDetailsDisplayProps {
  standard: string;
  details: Record<string, unknown>;
}

export default function FindingDetailsDisplay({ standard, details }: FindingDetailsDisplayProps) {
  if (!details) {
    return <div className="text-sm text-muted-foreground">No additional details available</div>;
  }

  const renderDetailValue = (key: string, value: unknown): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground">N/A</span>;
    }

    if (typeof value === 'boolean') {
      return (
        <div className="flex items-center gap-1">
          {value ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <XCircle className="h-4 w-4 text-red-600" />
          )}
          <span>{value ? 'Yes' : 'No'}</span>
        </div>
      );
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-muted-foreground">None</span>;
      }
      return (
        <div className="space-y-1">
          {value.map((item, index) => (
            <div key={index} className="text-sm">
              {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}
            </div>
          ))}
        </div>
      );
    }

    if (typeof value === 'object') {
      return (
        <div className="space-y-2">
          {Object.entries(value).map(([subKey, subValue]) => (
            <div key={subKey} className="text-sm">
              <span className="font-medium">{subKey}:</span> {renderDetailValue(subKey, subValue)}
            </div>
          ))}
        </div>
      );
    }

    return <span>{String(value)}</span>;
  };

  const getStandardIcon = (standard: string) => {
    switch (standard.toLowerCase()) {
      case 'hipaa':
        return <AlertTriangle className="h-4 w-4 text-blue-600" />;
      case 'ada':
        return <Info className="h-4 w-4 text-purple-600" />;
      case 'wcag':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStandardColor = (standard: string) => {
    switch (standard.toLowerCase()) {
      case 'hipaa':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ada':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'wcag':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card className="mt-3">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          {getStandardIcon(standard)}
          <span>{standard.toUpperCase()} Finding Details</span>
          <Badge variant="outline" className={getStandardColor(standard)}>
            {standard}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {Object.entries(details).map(([key, value]) => (
            <div key={key} className="border-l-2 border-gray-200 pl-3">
              <div className="text-sm font-medium text-gray-700 capitalize">
                {key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}
              </div>
              <div className="mt-1 text-sm text-gray-600">{renderDetailValue(key, value)}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
