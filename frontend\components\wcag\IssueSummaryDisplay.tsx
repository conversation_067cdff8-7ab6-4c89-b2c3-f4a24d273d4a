/**
 * Issue Summary Display Component
 * Shows grouped accessibility issues with expandable details
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  XCircle,
  Info,
  Target,
  Code,
  Zap,
} from 'lucide-react';
import { WcagCheckEnhanced } from '@/types/wcag';
import { AccessibilityCheckerStyleEvidence } from './AccessibilityCheckerStyleEvidence';

interface IssueSummaryDisplayProps {
  checks: WcagCheckEnhanced[];
  showFixExamples?: boolean;
}

interface GroupedIssue {
  type: string;
  severity: 'critical' | 'error' | 'warning' | 'info';
  count: number;
  affectedChecks: WcagCheckEnhanced[];
  description: string;
}

const SeverityIcon = ({ severity }: { severity: string }) => {
  switch (severity) {
    case 'critical':
      return <XCircle className="h-5 w-5 text-red-600" />;
    case 'error':
      return <AlertTriangle className="h-5 w-5 text-red-500" />;
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    default:
      return <Info className="h-5 w-5 text-blue-500" />;
  }
};

const SeverityBadge = ({ severity, count }: { severity: string; count: number }) => {
  const badgeClass = severity === 'critical' || severity === 'error'
    ? 'bg-red-100 text-red-800 border-red-200'
    : severity === 'warning'
    ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
    : 'bg-blue-100 text-blue-800 border-blue-200';

  return (
    <Badge className={`${badgeClass} font-medium`}>
      {count} issue{count !== 1 ? 's' : ''}
    </Badge>
  );
};

const IssueTypeCard = ({ 
  issue, 
  showFixExamples 
}: { 
  issue: GroupedIssue; 
  showFixExamples: boolean;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="mb-4 border-l-4 border-l-red-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <SeverityIcon severity={issue.severity} />
            <div>
              <h3 className="font-semibold text-gray-900">{issue.description}</h3>
              <div className="flex items-center gap-2 mt-1">
                <SeverityBadge severity={issue.severity} count={issue.count} />
                <Badge variant="outline" className="text-xs">
                  {issue.affectedChecks.length} check{issue.affectedChecks.length !== 1 ? 's' : ''}
                </Badge>
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                Hide Details
              </>
            ) : (
              <>
                <ChevronRight className="h-4 w-4 mr-1" />
                Show Details
              </>
            )}
          </Button>
        </div>
      </CardHeader>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="pt-0 border-t bg-gray-50">
            <div className="space-y-4">
              {issue.affectedChecks.map((check, index) => (
                <div key={index} className="bg-white border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Target className="h-4 w-4 text-blue-500" />
                    <h4 className="font-medium text-gray-900">{check.ruleName}</h4>
                    <Badge variant="outline" className="text-xs">
                      {check.ruleId}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {check.level}
                    </Badge>
                  </div>
                  
                  {check.evidence && check.evidence.length > 0 && (
                    <AccessibilityCheckerStyleEvidence
                      evidence={check.evidence}
                      showFixExamples={showFixExamples}
                      showElementCounts={true}
                    />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export const IssueSummaryDisplay: React.FC<IssueSummaryDisplayProps> = ({
  checks,
  showFixExamples = true,
}) => {
  // Group issues by type and severity
  const groupedIssues = React.useMemo(() => {
    const issueMap = new Map<string, GroupedIssue>();

    const failedChecks = checks.filter(check => check.status === 'failed');

    for (const check of failedChecks) {
      if (!check.evidence || check.evidence.length === 0) continue;

      for (const evidence of check.evidence) {
        const issueType = categorizeIssue(check.ruleName, evidence.description);
        const severity = evidence.severity || 'error';
        const key = `${issueType}_${severity}`;

        if (!issueMap.has(key)) {
          issueMap.set(key, {
            type: issueType,
            severity: severity as 'critical' | 'error' | 'warning' | 'info',
            count: 0,
            affectedChecks: [],
            description: getIssueDescription(issueType),
          });
        }

        const issue = issueMap.get(key)!;
        issue.count += evidence.elementCount || 1;
        
        // Add check if not already included
        if (!issue.affectedChecks.find(c => c.ruleId === check.ruleId)) {
          issue.affectedChecks.push(check);
        }
      }
    }

    return Array.from(issueMap.values()).sort((a, b) => {
      // Sort by severity first, then by count
      const severityOrder = { critical: 0, error: 1, warning: 2, info: 3 };
      const severityDiff = severityOrder[a.severity] - severityOrder[b.severity];
      if (severityDiff !== 0) return severityDiff;
      return b.count - a.count;
    });
  }, [checks]);

  if (groupedIssues.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="flex flex-col items-center gap-2">
            <Info className="h-8 w-8 text-green-500" />
            <h3 className="font-medium text-gray-900">No Issues Found</h3>
            <p className="text-sm text-gray-600">All accessibility checks passed successfully.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalIssues = groupedIssues.reduce((sum, issue) => sum + issue.count, 0);
  const criticalIssues = groupedIssues.filter(issue => issue.severity === 'critical').reduce((sum, issue) => sum + issue.count, 0);
  const errorIssues = groupedIssues.filter(issue => issue.severity === 'error').reduce((sum, issue) => sum + issue.count, 0);

  return (
    <div className="space-y-4">
      {/* Summary Header */}
      <Card className="bg-red-50 border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            Accessibility Issues Found
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium">Total Issues:</span>
              <Badge variant="destructive">{totalIssues}</Badge>
            </div>
            {criticalIssues > 0 && (
              <div className="flex items-center gap-2">
                <span className="font-medium">Critical:</span>
                <Badge className="bg-red-600 text-white">{criticalIssues}</Badge>
              </div>
            )}
            {errorIssues > 0 && (
              <div className="flex items-center gap-2">
                <span className="font-medium">Errors:</span>
                <Badge className="bg-red-500 text-white">{errorIssues}</Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Grouped Issues */}
      <div className="space-y-3">
        {groupedIssues.map((issue, index) => (
          <IssueTypeCard
            key={index}
            issue={issue}
            showFixExamples={showFixExamples}
          />
        ))}
      </div>
    </div>
  );
};

// Helper functions
function categorizeIssue(ruleName: string, description: string): string {
  const text = `${ruleName} ${description}`.toLowerCase();
  
  if (text.includes('alt') || text.includes('alternative')) return 'missing_alt_text';
  if (text.includes('contrast')) return 'low_contrast';
  if (text.includes('keyboard') || text.includes('focus')) return 'keyboard_accessibility';
  if (text.includes('aria') || text.includes('label')) return 'aria_labeling';
  if (text.includes('heading') || text.includes('structure')) return 'content_structure';
  if (text.includes('caption') || text.includes('media')) return 'media_accessibility';
  
  return 'general_accessibility';
}

function getIssueDescription(issueType: string): string {
  const descriptions: Record<string, string> = {
    missing_alt_text: 'Images Missing Alternative Text',
    low_contrast: 'Insufficient Color Contrast',
    keyboard_accessibility: 'Keyboard Accessibility Issues',
    aria_labeling: 'Missing ARIA Labels',
    content_structure: 'Content Structure Issues',
    media_accessibility: 'Media Accessibility Issues',
    general_accessibility: 'General Accessibility Issues',
  };
  
  return descriptions[issueType] || 'Accessibility Issues';
}

export default IssueSummaryDisplay;
