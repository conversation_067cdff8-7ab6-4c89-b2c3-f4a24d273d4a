/**
 * Test Unified DOM Integration
 * Validates that the unified DOM extractor is properly integrated with all checks
 */

import puppeteer from 'puppeteer';
import { UnifiedDOMExtractor } from './utils/unified-dom-extractor';
import { DOMIntegrationHelper } from './utils/dom-integration-helper';
import { CheckTemplate } from './utils/check-template';
import logger from '../../utils/logger';

interface IntegrationTestResult {
  testName: string;
  passed: boolean;
  details: string;
  error?: string;
  metrics?: {
    extractionTime: number;
    elementsFound: number;
    cacheHit: boolean;
  };
}

class UnifiedIntegrationTester {
  private results: IntegrationTestResult[] = [];

  /**
   * Test unified DOM extractor functionality
   */
  async testUnifiedDOMExtractor(): Promise<IntegrationTestResult> {
    const testName = 'Unified DOM Extractor';
    
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      
      // Create a test page with various elements
      await page.setContent(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <title>Test Page</title>
          <meta name="description" content="Test page for WCAG scanning">
        </head>
        <body>
          <header>
            <h1>Main Heading</h1>
            <nav>
              <a href="#section1">Section 1</a>
              <a href="#section2">Section 2</a>
            </nav>
          </header>
          <main>
            <section id="section1">
              <h2>Section 1</h2>
              <p>This is a test paragraph.</p>
              <img src="test.jpg" alt="Test image">
              <form>
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
                <button type="submit">Submit</button>
              </form>
            </section>
            <section id="section2">
              <h2>Section 2</h2>
              <video controls>
                <source src="test.mp4" type="video/mp4">
                <track kind="captions" src="captions.vtt" srclang="en" label="English">
              </video>
            </section>
          </main>
          <footer>
            <p>&copy; 2024 Test Site</p>
          </footer>
        </body>
        </html>
      `);

      const startTime = Date.now();
      const domExtractor = UnifiedDOMExtractor.getInstance();
      const pageStructure = await domExtractor.extractPageStructure(page, 'http://test.example.com');
      const extractionTime = Date.now() - startTime;

      await browser.close();

      // Validate structure
      const isValid = pageStructure && 
                     pageStructure.elements &&
                     pageStructure.elements.headings.length >= 2 &&
                     pageStructure.elements.images.length >= 1 &&
                     pageStructure.elements.forms.length >= 1 &&
                     pageStructure.elements.links.length >= 2 &&
                     pageStructure.elements.multimedia.length >= 1;

      const elementsFound = Object.values(pageStructure.performance.elementCounts).reduce((sum, count) => sum + count, 0);

      return {
        testName,
        passed: isValid,
        details: isValid 
          ? `✅ DOM extraction successful: ${elementsFound} elements extracted in ${extractionTime}ms`
          : '❌ DOM extraction returned invalid structure',
        metrics: {
          extractionTime,
          elementsFound,
          cacheHit: false,
        },
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ DOM extraction failed with error',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Test DOM integration helper functionality
   */
  async testDOMIntegrationHelper(): Promise<IntegrationTestResult> {
    const testName = 'DOM Integration Helper';
    
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      
      await page.setContent(`
        <html>
          <body>
            <h1>Test Heading</h1>
            <img src="test.jpg" alt="Test image">
            <form><input type="text" id="test"></form>
            <a href="#test">Test Link</a>
          </body>
        </html>
      `);

      const domHelper = DOMIntegrationHelper.getInstance();
      
      // Test with page structure (should use unified extractor)
      const domExtractor = UnifiedDOMExtractor.getInstance();
      const pageStructure = await domExtractor.extractPageStructure(page, 'http://test.example.com');
      
      const config = {
        targetUrl: 'http://test.example.com',
        timeout: 30000,
        scanId: 'test-scan',
        pageStructure,
      };

      const headings = await domHelper.getHeadings(config, page);
      const images = await domHelper.getImages(config, page);
      const forms = await domHelper.getForms(config, page);
      const links = await domHelper.getLinks(config, page);

      await browser.close();

      const isValid = headings.length >= 1 && 
                     images.length >= 1 && 
                     forms.length >= 1 && 
                     links.length >= 1;

      return {
        testName,
        passed: isValid,
        details: isValid 
          ? `✅ Integration helper working: ${headings.length} headings, ${images.length} images, ${forms.length} forms, ${links.length} links`
          : '❌ Integration helper returned insufficient elements',
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ Integration helper failed with error',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Test check template integration
   */
  async testCheckTemplateIntegration(): Promise<IntegrationTestResult> {
    const testName = 'Check Template Integration';
    
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      
      await page.setContent(`
        <html>
          <body>
            <h1>Test Page</h1>
            <p>Test content</p>
          </body>
        </html>
      `);

      const checkTemplate = new CheckTemplate();
      
      // Mock check function that uses page structure
      const mockCheckFunction = async (page: any, config: any) => {
        if (!config.pageStructure) {
          throw new Error('Page structure not provided');
        }
        
        return {
          score: 85,
          maxScore: 100,
          evidence: [{
            type: 'text',
            description: 'Test evidence',
            value: 'Test passed',
            severity: 'info' as const,
          }],
          recommendations: ['Test recommendation'],
        };
      };

      const config = {
        targetUrl: 'http://test.example.com',
        timeout: 30000,
        scanId: 'test-scan',
        page,
      };

      const result = await checkTemplate.executeCheck(
        'TEST-001',
        'Test Check',
        1.0,
        'AA',
        config,
        mockCheckFunction,
        true,
        false
      );

      await browser.close();

      const isValid = result && 
                     result.status === 'passed' && // Should pass with 75% threshold
                     result.score === 85 && // Should show actual score when passed
                     result.thresholdApplied === 75;

      return {
        testName,
        passed: isValid,
        details: isValid 
          ? `✅ Check template integration working: score ${result.score}, status ${result.status}, threshold ${result.thresholdApplied}%`
          : '❌ Check template integration failed validation',
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ Check template integration failed with error',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Test cache performance
   */
  async testCachePerformance(): Promise<IntegrationTestResult> {
    const testName = 'Cache Performance';
    
    try {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      
      await page.setContent('<html><body><h1>Cache Test</h1></body></html>');

      const domExtractor = UnifiedDOMExtractor.getInstance();
      
      // First extraction (should cache)
      const startTime1 = Date.now();
      await domExtractor.extractPageStructure(page, 'http://cache-test.example.com');
      const firstExtractionTime = Date.now() - startTime1;
      
      // Second extraction (should use cache)
      const startTime2 = Date.now();
      await domExtractor.extractPageStructure(page, 'http://cache-test.example.com');
      const secondExtractionTime = Date.now() - startTime2;

      await browser.close();

      const cacheWorking = secondExtractionTime < firstExtractionTime * 0.5; // Should be significantly faster

      return {
        testName,
        passed: cacheWorking,
        details: cacheWorking 
          ? `✅ Cache working: first ${firstExtractionTime}ms, second ${secondExtractionTime}ms`
          : `❌ Cache not working: first ${firstExtractionTime}ms, second ${secondExtractionTime}ms`,
        metrics: {
          extractionTime: secondExtractionTime,
          elementsFound: 0,
          cacheHit: cacheWorking,
        },
      };
    } catch (error) {
      return {
        testName,
        passed: false,
        details: '❌ Cache performance test failed with error',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Run all integration tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Unified DOM Integration Tests...\n');
    
    // Run all tests
    this.results.push(await this.testUnifiedDOMExtractor());
    this.results.push(await this.testDOMIntegrationHelper());
    this.results.push(await this.testCheckTemplateIntegration());
    this.results.push(await this.testCachePerformance());
    
    // Print results
    this.printResults();
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('\n📊 Unified DOM Integration Test Results:');
    console.log('=' .repeat(70));
    
    let passedCount = 0;
    
    for (const result of this.results) {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} | ${result.testName}`);
      console.log(`     ${result.details}`);
      
      if (result.error) {
        console.log(`     Error: ${result.error}`);
      }
      
      if (result.metrics) {
        console.log(`     Metrics: ${result.metrics.extractionTime}ms, ${result.metrics.elementsFound} elements, cache: ${result.metrics.cacheHit ? 'HIT' : 'MISS'}`);
      }
      
      if (result.passed) passedCount++;
      console.log('');
    }
    
    console.log('=' .repeat(70));
    console.log(`📈 Overall Results: ${passedCount}/${this.results.length} tests passed`);
    
    if (passedCount === this.results.length) {
      console.log('🎉 All unified DOM integration tests passed!');
      console.log('✅ Unified DOM extractor is properly integrated with all checks');
      console.log('✅ Old parsing system has been successfully replaced');
    } else {
      console.log('⚠️  Some integration tests failed - check implementation');
    }
  }
}

// Export for use in other files
export { UnifiedIntegrationTester };

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new UnifiedIntegrationTester();
  tester.runAllTests().catch(console.error);
}
