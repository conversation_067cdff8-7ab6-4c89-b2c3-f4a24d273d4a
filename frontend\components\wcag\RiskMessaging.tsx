/**
 * AccessibilityChecker.org-style Risk Messaging Component
 * Displays compliance status and risk warnings
 */

'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Shield, 
  Scale, 
  Globe,
  FileText,
  ExternalLink
} from 'lucide-react';

interface RiskMessagingProps {
  overallScore: number;
  failedChecks: number;
  criticalIssues: number;
  levelAchieved: 'A' | 'AA' | 'AAA' | 'FAIL';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  targetUrl: string;
}

export const RiskMessaging: React.FC<RiskMessagingProps> = ({
  overallScore,
  failedChecks,
  criticalIssues,
  levelAchieved,
  riskLevel,
  targetUrl,
}) => {
  const isCompliant = overallScore >= 80;
  const domain = new URL(targetUrl).hostname;

  const getRiskMessage = () => {
    if (isCompliant) {
      return {
        title: 'COMPLIANT',
        message: 'Your website meets accessibility standards and is protected from most accessibility-related legal risks.',
        icon: <CheckCircle className="h-6 w-6 text-green-600" />,
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        textColor: 'text-green-800',
      };
    }

    if (riskLevel === 'critical' || criticalIssues > 10) {
      return {
        title: 'HIGH RISK',
        message: 'Your site has critical accessibility issues and is at high risk of accessibility lawsuits.',
        icon: <XCircle className="h-6 w-6 text-red-600" />,
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        textColor: 'text-red-800',
      };
    }

    if (riskLevel === 'high' || criticalIssues > 5) {
      return {
        title: 'MODERATE RISK',
        message: 'Your site may be at risk of accessibility lawsuits due to compliance issues.',
        icon: <AlertTriangle className="h-6 w-6 text-orange-600" />,
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        textColor: 'text-orange-800',
      };
    }

    return {
      title: 'LOW RISK',
      message: 'Your site has minor accessibility issues that should be addressed for full compliance.',
      icon: <AlertTriangle className="h-6 w-6 text-yellow-600" />,
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-800',
    };
  };

  const risk = getRiskMessage();

  return (
    <div className="space-y-4">
      {/* Main Risk Status Card */}
      <Card className={`${risk.bgColor} ${risk.borderColor} border-2`}>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            {/* Status Icon and Title */}
            <div className="flex items-center justify-center gap-3">
              {risk.icon}
              <h2 className={`text-2xl font-bold ${risk.textColor}`}>
                {risk.title}
              </h2>
            </div>

            {/* Risk Message */}
            <p className={`text-lg ${risk.textColor} max-w-2xl mx-auto`}>
              {risk.message}
            </p>

            {/* Compliance Level Badge */}
            <div className="flex justify-center">
              <Badge 
                variant={levelAchieved === 'FAIL' ? 'destructive' : 'secondary'}
                className="text-sm px-3 py-1"
              >
                WCAG {levelAchieved} Level {levelAchieved !== 'FAIL' ? 'Achieved' : 'Not Met'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Legal Risk Information */}
      {!isCompliant && (
        <Alert className="border-amber-200 bg-amber-50">
          <Scale className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            <div className="space-y-2">
              <p className="font-semibold">Legal Risk Information:</p>
              <ul className="text-sm space-y-1 ml-4">
                <li>• ADA lawsuits have increased by 320% in recent years</li>
                <li>• Average settlement costs range from $10,000 to $50,000</li>
                <li>• WCAG 2.1 AA compliance is the legal standard in most jurisdictions</li>
                <li>• Proactive compliance is more cost-effective than reactive fixes</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Compliance Standards Reference */}
      <Card className="bg-gray-50 border-gray-200">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Shield className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-800">Compliance Standards</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-blue-500" />
              <div>
                <p className="font-medium">WCAG 2.2</p>
                <p className="text-gray-600">Web Content Accessibility Guidelines</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-green-500" />
              <div>
                <p className="font-medium">Section 508</p>
                <p className="text-gray-600">US Federal Accessibility Standard</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4 text-purple-500" />
              <div>
                <p className="font-medium">EN 301 549</p>
                <p className="text-gray-600">European Accessibility Standard</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Success Message for Compliant Sites */}
      {isCompliant && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <div className="space-y-2">
              <p className="font-semibold">Congratulations! {domain} is accessibility compliant.</p>
              <p className="text-sm">
                Your website meets WCAG 2.2 standards and provides an inclusive experience for all users.
                Continue monitoring for new content and updates to maintain compliance.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default RiskMessaging;
