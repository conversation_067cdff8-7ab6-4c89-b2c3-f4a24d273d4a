/**
 * Enhanced Evidence Database Population Test
 * Verifies that enhanced evidence fields are properly populated in the database
 */

import { WcagDatabase } from '../wcag-database';
import { WcagCheckResult, WcagEvidence } from '../../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../../types-enhanced';
import db from '../../../../lib/db';

describe('Enhanced Evidence Database Population', () => {
  let wcagDatabase: WcagDatabase;
  let testScanId: string;

  beforeAll(async () => {
    wcagDatabase = new WcagDatabase();
    
    // Create a test scan
    testScanId = await wcagDatabase.createScan({
      userId: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
      targetUrl: 'https://test-enhanced-evidence.example.com',
      options: {
        includeLevel: ['A', 'AA'],
        includeCategories: ['perceivable', 'operable'],
        timeout: 30000,
      },
    });
  });

  afterAll(async () => {
    // Clean up test data
    if (testScanId) {
      await db('wcag_automated_results').where('scan_id', testScanId).del();
      await db('wcag_scans').where('id', testScanId).del();
    }
  });

  test('should populate enhanced evidence fields for enhanced check results', async () => {
    // Create mock enhanced check result
    const enhancedEvidence: WcagEvidenceEnhanced[] = [
      {
        type: 'error',
        description: 'Missing alt text on image',
        value: '<img src="test.jpg" />',
        selector: 'img[src="test.jpg"]',
        severity: 'error',
        elementCount: 3,
        affectedSelectors: ['img[src="test.jpg"]', 'img.hero-image', 'img.gallery-item'],
        fixExample: {
          before: '<img src="test.jpg" />',
          after: '<img src="test.jpg" alt="Test image description" />',
          description: 'Add descriptive alt text to the image',
          codeExample: 'alt="Descriptive text about the image content"',
          resources: ['https://www.w3.org/WAI/tutorials/images/'],
        },
        metadata: {
          scanDuration: 1500,
          elementsAnalyzed: 10,
          checkSpecificData: {
            imageType: 'content',
            hasAriaLabel: false,
          },
        },
      },
      {
        type: 'warning',
        description: 'Low contrast ratio detected',
        value: 'Contrast ratio: 3.2:1',
        selector: '.text-content',
        severity: 'warning',
        elementCount: 5,
        affectedSelectors: ['.text-content', '.secondary-text', '.footer-text'],
        fixExample: {
          before: 'color: #888888; background: #ffffff;',
          after: 'color: #333333; background: #ffffff;',
          description: 'Increase text color contrast to meet WCAG AA standards',
          codeExample: 'color: #333333; /* Contrast ratio: 4.5:1 */',
          resources: ['https://webaim.org/resources/contrastchecker/'],
        },
      },
    ];

    const enhancedCheckResult: WcagCheckResultEnhanced = {
      ruleId: 'WCAG-001',
      ruleName: 'Non-text Content',
      category: 'perceivable',
      wcagVersion: '2.2',
      successCriterion: '1.1.1',
      level: 'A',
      status: 'failed',
      score: 0,
      maxScore: 100,
      weight: 0.1,
      evidence: enhancedEvidence,
      recommendations: [
        'Add alt text to all images',
        'Improve color contrast for better readability',
      ],
      executionTime: 1500,
      elementCounts: {
        total: 8,
        failed: 3,
        passed: 5,
      },
      performance: {
        scanDuration: 1500,
        elementsAnalyzed: 8,
        cacheHitRate: 0.75,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'enhanced-image-analysis',
        confidence: 0.95,
        additionalData: {
          imageAnalysisEnabled: true,
          contrastAnalysisEnabled: true,
        },
      },
    };

    // Create mock scan result with enhanced check
    const scanResult = {
      scanId: testScanId,
      targetUrl: 'https://test-enhanced-evidence.example.com',
      scanTimestamp: new Date(),
      scanDuration: 5000,
      overallScore: 75,
      levelAchieved: 'A' as const,
      riskLevel: 'medium' as const,
      checks: [enhancedCheckResult],
      summary: {
        totalAutomatedChecks: 1,
        passedAutomatedChecks: 0,
        failedAutomatedChecks: 1,
        manualReviewItems: 0,
        categoryScores: {
          perceivable: 75,
          operable: 80,
          understandable: 85,
          robust: 90,
        },
      },
      metadata: {
        scanDuration: 5000,
        checksExecuted: 1,
        automationRate: 1.0,
        performanceMetrics: {
          totalMemoryUsage: 150,
          peakMemoryUsage: 200,
          averageResponseTime: 1500,
        },
      },
    };

    // Save the scan result
    await wcagDatabase.saveScanResult(scanResult);

    // Verify enhanced evidence fields were populated
    const savedResults = await db('wcag_automated_results')
      .where('scan_id', testScanId)
      .first();

    expect(savedResults).toBeDefined();
    expect(savedResults.total_element_count).toBe(8);
    expect(savedResults.failed_element_count).toBe(3);
    expect(savedResults.scan_duration_ms).toBe(1500);
    expect(savedResults.elements_analyzed).toBe(8);

    // Verify affected_selectors is properly stored
    const affectedSelectors = JSON.parse(savedResults.affected_selectors);
    expect(affectedSelectors).toContain('img[src="test.jpg"]');
    expect(affectedSelectors).toContain('.text-content');

    // Verify fix_examples is properly stored
    const fixExamples = JSON.parse(savedResults.fix_examples);
    expect(fixExamples).toHaveLength(2);
    expect(fixExamples[0].before).toBe('<img src="test.jpg" />');
    expect(fixExamples[0].after).toBe('<img src="test.jpg" alt="Test image description" />');

    // Verify evidence_metadata is properly stored
    const evidenceMetadata = JSON.parse(savedResults.evidence_metadata);
    expect(evidenceMetadata.totalEvidenceItems).toBe(2);
    expect(evidenceMetadata.evidenceTypes).toContain('error');
    expect(evidenceMetadata.evidenceTypes).toContain('warning');

    // Verify check_metadata is properly stored
    const checkMetadata = JSON.parse(savedResults.check_metadata);
    expect(checkMetadata.version).toBe('1.0.0');
    expect(checkMetadata.algorithm).toBe('enhanced-image-analysis');
    expect(checkMetadata.confidence).toBe(0.95);
    expect(checkMetadata.ruleId).toBe('WCAG-001');
  });

  test('should handle basic check results without enhanced fields gracefully', async () => {
    // Create basic check result without enhanced fields
    const basicCheckResult: WcagCheckResult = {
      ruleId: 'WCAG-002',
      ruleName: 'Basic Check',
      category: 'operable',
      wcagVersion: '2.2',
      successCriterion: '2.1.1',
      level: 'A',
      status: 'passed',
      score: 100,
      maxScore: 100,
      weight: 0.1,
      evidence: [
        {
          type: 'info',
          description: 'All elements are keyboard accessible',
          value: 'No issues found',
        },
      ],
      recommendations: ['Continue following accessibility best practices'],
      executionTime: 500,
    };

    const basicScanResult = {
      scanId: testScanId,
      targetUrl: 'https://test-basic-evidence.example.com',
      scanTimestamp: new Date(),
      scanDuration: 2000,
      overallScore: 100,
      levelAchieved: 'A' as const,
      riskLevel: 'low' as const,
      checks: [basicCheckResult],
      summary: {
        totalAutomatedChecks: 1,
        passedAutomatedChecks: 1,
        failedAutomatedChecks: 0,
        manualReviewItems: 0,
        categoryScores: {
          perceivable: 100,
          operable: 100,
          understandable: 100,
          robust: 100,
        },
      },
      metadata: {
        scanDuration: 2000,
        checksExecuted: 1,
        automationRate: 1.0,
        performanceMetrics: {
          totalMemoryUsage: 100,
          peakMemoryUsage: 120,
          averageResponseTime: 500,
        },
      },
    };

    // Save the basic scan result
    await wcagDatabase.saveScanResult(basicScanResult);

    // Verify that enhanced fields are null for basic results
    const savedResults = await db('wcag_automated_results')
      .where('scan_id', testScanId)
      .where('rule_id', 'WCAG-002')
      .first();

    expect(savedResults).toBeDefined();
    expect(savedResults.total_element_count).toBeNull();
    expect(savedResults.failed_element_count).toBeNull();
    expect(savedResults.affected_selectors).toBeNull();
    expect(savedResults.fix_examples).toBeNull();
    expect(savedResults.scan_duration_ms).toBe(500); // Should still capture execution time
    expect(savedResults.elements_analyzed).toBeNull();

    // Evidence metadata should still be created with basic info
    const evidenceMetadata = JSON.parse(savedResults.evidence_metadata);
    expect(evidenceMetadata.totalEvidenceItems).toBe(1);
    expect(evidenceMetadata.evidenceTypes).toContain('info');
  });
});
