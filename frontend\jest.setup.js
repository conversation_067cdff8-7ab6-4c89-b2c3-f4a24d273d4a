/**
 * Jest Setup File
 * Global test configuration and mocks
 */

import '@testing-library/jest-dom';
import 'jest-axe/extend-expect';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
      pathname: '/dashboard/hipaa',
      query: {},
      asPath: '/dashboard/hipaa',
      route: '/dashboard/hipaa',
    };
  },
  usePathname() {
    return '/dashboard/hipaa';
  },
  useSearchParams() {
    return new URLSearchParams();
  },
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, ...props }) => {
    return React.createElement('a', { href, ...props }, children);
  };
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock fetch globally
global.fetch = jest.fn();

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
        args[0].includes('Warning: An invalid form control') ||
        args[0].includes('Warning: componentWillReceiveProps'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };

  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: React.createFactory') ||
        args[0].includes('Warning: componentWillMount'))
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Global test utilities
global.testUtils = {
  // Test helper functions
  createMockResponse: (data, success = true) => ({
    success,
    data,
    error: success ? null : 'Mock error',
  }),

  // Mock privacy scan result
  mockPrivacyScan: {
    targetUrl: 'https://example.com',
    timestamp: '2025-06-24T10:30:00Z',
    overallScore: 82,
    overallPassed: true,
    summary: {
      totalChecks: 24,
      passedChecks: 20,
      failedChecks: 4,
      criticalIssues: 1,
      highIssues: 2,
      mediumIssues: 1,
      lowIssues: 0,
      overallScore: 82,
      complianceLevel: 'mostly_compliant',
      riskLevel: 'medium',
      analysisLevelsUsed: [1, 2, 3],
    },
    checks: [],
    recommendations: [],
    metadata: {
      scanDuration: 45000,
      pagesAnalyzed: 5,
      privacyPolicyFound: true,
      privacyPolicyUrl: 'https://example.com/privacy',
      lastUpdated: '2025-06-24T10:30:00Z',
      scanVersion: '1.0.0',
    },
  },

  // Mock security scan result
  mockSecurityScan: {
    scanId: 'sec-001',
    targetUrl: 'https://example.com',
    scanTimestamp: new Date('2025-06-24T11:00:00Z'),
    overallScore: 88,
    riskLevel: 'low',
    passedTests: [],
    failedTests: [],
    vulnerabilities: [],
    categoryResults: [],
    scanDuration: 120000,
    toolsUsed: ['Nuclei'],
    pagesScanned: ['https://example.com'],
    scanConfig: {
      targetUrl: 'https://example.com',
      timeout: 300000,
      maxPages: 15,
      scanDepth: 2,
      enableVulnerabilityScanning: true,
      enableSSLAnalysis: true,
      enableContentAnalysis: true,
    },
  },

  // Helper to create mock fetch responses
  createMockFetchResponse: (data, ok = true, status = 200) => {
    return Promise.resolve({
      ok,
      status,
      json: () => Promise.resolve(data),
      text: () => Promise.resolve(JSON.stringify(data)),
    });
  },

  // Helper to wait for async operations
  waitFor: (ms = 0) => new Promise((resolve) => setTimeout(resolve, ms)),

  // Helper to trigger window resize
  triggerResize: (width = 1024, height = 768) => {
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: width,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: height,
    });
    window.dispatchEvent(new Event('resize'));
  },

  // Helper to mock localStorage
  mockLocalStorage: () => {
    const store = {};
    return {
      getItem: jest.fn((key) => store[key] || null),
      setItem: jest.fn((key, value) => {
        store[key] = value.toString();
      }),
      removeItem: jest.fn((key) => {
        delete store[key];
      }),
      clear: jest.fn(() => {
        Object.keys(store).forEach((key) => delete store[key]);
      }),
    };
  },
};

// Setup localStorage mock
Object.defineProperty(window, 'localStorage', {
  value: global.testUtils.mockLocalStorage(),
});

// Setup sessionStorage mock
Object.defineProperty(window, 'sessionStorage', {
  value: global.testUtils.mockLocalStorage(),
});

// Mock CSS modules
jest.mock('*.module.css', () => ({}));
jest.mock('*.module.scss', () => ({}));

// Mock static file imports
jest.mock('*.svg', () => 'svg');
jest.mock('*.png', () => 'png');
jest.mock('*.jpg', () => 'jpg');
jest.mock('*.jpeg', () => 'jpeg');
jest.mock('*.gif', () => 'gif');

// Increase timeout for async tests
jest.setTimeout(10000);

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Cleanup after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset fetch mock
  if (global.fetch && global.fetch.mockClear) {
    global.fetch.mockClear();
  }

  // Clear localStorage and sessionStorage
  window.localStorage.clear();
  window.sessionStorage.clear();

  // Reset window size
  global.testUtils.triggerResize(1024, 768);
});
