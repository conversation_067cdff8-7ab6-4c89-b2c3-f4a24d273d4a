/**
 * AccessibilityChecker.org-style Fix Issues Button
 * Prominent action button for fixing accessibility issues
 */

'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Wrench, ArrowRight, CheckCircle } from 'lucide-react';

interface FixIssuesButtonProps {
  issueCount: number;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'success';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

export const FixIssuesButton: React.FC<FixIssuesButtonProps> = ({
  issueCount,
  onClick,
  variant = 'primary',
  size = 'lg',
  disabled = false,
  loading = false,
  className = '',
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-green-600 hover:bg-green-700 text-white border-green-600';
      case 'secondary':
        return 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600';
      case 'success':
        return 'bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600';
      default:
        return 'bg-green-600 hover:bg-green-700 text-white border-green-600';
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-4 py-2 text-sm';
      case 'md':
        return 'px-6 py-3 text-base';
      case 'lg':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-8 py-4 text-lg';
    }
  };

  const getIcon = () => {
    if (loading) return '⏳';
    if (variant === 'success') return <CheckCircle className="h-5 w-5" />;
    return <Wrench className="h-5 w-5" />;
  };

  const getButtonText = () => {
    if (loading) return 'Processing...';
    if (variant === 'success') return 'Issues Fixed!';
    if (issueCount === 0) return 'No Issues Found';
    return `Fix ${issueCount} issue${issueCount !== 1 ? 's' : ''}`;
  };

  return (
    <div className={`flex flex-col items-center gap-3 ${className}`}>
      {/* AccessibilityChecker.org-style Large Button */}
      <Button
        onClick={onClick}
        disabled={disabled || loading || issueCount === 0}
        className={`
          ${getVariantStyles()}
          ${getSizeStyles()}
          font-semibold rounded-lg shadow-lg hover:shadow-xl
          transition-all duration-200 transform hover:scale-105
          flex items-center gap-3 min-w-[200px] justify-center
          ${disabled || issueCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {getIcon()}
        <span>{getButtonText()}</span>
        {!loading && issueCount > 0 && <ArrowRight className="h-5 w-5" />}
      </Button>

      {/* Issue Count Badge */}
      {issueCount > 0 && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            {issueCount} critical issue{issueCount !== 1 ? 's' : ''} found
          </Badge>
        </div>
      )}

      {/* Success Message */}
      {issueCount === 0 && variant === 'success' && (
        <div className="flex items-center gap-2 text-sm text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span>All accessibility issues resolved!</span>
        </div>
      )}

      {/* Help Text */}
      {issueCount > 0 && (
        <p className="text-xs text-gray-500 text-center max-w-md">
          Click to start the guided fix process. We'll help you resolve each issue step by step.
        </p>
      )}
    </div>
  );
};

// Compact version for use in cards and smaller spaces
export const CompactFixButton: React.FC<Omit<FixIssuesButtonProps, 'size'>> = (props) => {
  return <FixIssuesButton {...props} size="sm" />;
};

// Hero version for main dashboard
export const HeroFixButton: React.FC<FixIssuesButtonProps> = (props) => {
  return (
    <div className="text-center py-8">
      <FixIssuesButton {...props} size="lg" />
      {props.issueCount > 0 && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg max-w-md mx-auto">
          <p className="text-sm text-yellow-800">
            <strong>⚠️ Important:</strong> Your site may be at risk of accessibility lawsuits. 
            Fix these issues to ensure compliance with WCAG 2.2 guidelines.
          </p>
        </div>
      )}
    </div>
  );
};

export default FixIssuesButton;
