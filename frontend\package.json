{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "keycloak-js": "^26.2.0", "lucide-react": "^0.300.0", "next": "^14.2.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@playwright/test": "^1.53.2", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.29", "jest-axe": "^10.0.0", "postcss": "^8.4.0", "react-router-dom": "^7.6.3", "typescript": "^5.3.0"}}