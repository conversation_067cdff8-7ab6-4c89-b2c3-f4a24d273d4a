/**
 * Enhanced Features Integration Test
 * Tests the complete enhanced evidence system end-to-end
 */

import { EvidenceProcessor } from '../utils/evidence-processor';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagFixExample } from '../types-enhanced';

describe('Enhanced Features Integration', () => {
  describe('Evidence Processor', () => {
    const mockEvidence: WcagEvidence[] = [
      {
        type: 'code',
        description: 'Missing lang attribute',
        value: '<html>',
        selector: 'html',
        severity: 'error',
      },
      {
        type: 'text',
        description: 'Content outside landmarks',
        value: 'Found 3 elements outside landmarks',
        severity: 'warning',
      },
    ];

    it('should process evidence with enhanced features', () => {
      const processed = EvidenceProcessor.processEvidence(mockEvidence);

      expect(processed).toHaveLength(2);
      expect(processed[0]).toMatchObject(mockEvidence[0]);
      expect(processed[1]).toMatchObject(mockEvidence[1]);
    });

    it('should convert enhanced evidence back to legacy format', () => {
      const enhanced: WcagEvidenceEnhanced[] = [
        {
          ...mockEvidence[0],
          elementCount: 1,
          affectedSelectors: ['html'],
          fixExample: {
            before: '<html>',
            after: '<html lang="en">',
            description: 'Add lang attribute',
            resources: ['https://example.com'],
          },
        },
      ];

      const legacy = EvidenceProcessor.toLegacyFormat(enhanced);

      expect(legacy).toHaveLength(1);
      expect(legacy[0]).toMatchObject(mockEvidence[0]);
      expect(legacy[0]).not.toHaveProperty('elementCount');
      expect(legacy[0]).not.toHaveProperty('fixExample');
    });

    it('should generate fix examples for specific rule IDs', () => {
      const enhanced: WcagEvidenceEnhanced[] = [
        {
          ...mockEvidence[0],
          severity: 'error',
        },
      ];

      const fixExamples = EvidenceProcessor.generateFixExamples(enhanced, 'WCAG-024');

      expect(fixExamples).toHaveLength(1);
      expect(fixExamples[0].description).toContain('Add lang attribute');
      expect(fixExamples[0].before).toBe('<html>');
      expect(fixExamples[0].after).toBe('<html lang="en">');
      expect(fixExamples[0].resources).toHaveLength(2);
    });

    it('should enhance evidence with metadata', () => {
      const enhanced = EvidenceProcessor.enhanceWithMetadata(
        mockEvidence as WcagEvidenceEnhanced[],
        1500,
        10,
      );

      expect(enhanced).toHaveLength(2);
      expect(enhanced[0].elementCount).toBe(1);
      expect(enhanced[0].metadata?.scanDuration).toBe(1500);
      expect(enhanced[0].metadata?.elementsAnalyzed).toBe(10);
      expect(enhanced[0].metadata?.checkSpecificData?.enhancedProcessing).toBe(true);
    });

    it('should validate enhanced evidence structure', () => {
      const validEvidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Valid evidence',
          value: '<div>content</div>',
          elementCount: 1,
          affectedSelectors: ['div'],
          fixExample: {
            before: '<div>',
            after: '<div role="main">',
            description: 'Add landmark role',
          },
        },
      ];

      const validation = EvidenceProcessor.validateEnhancedEvidence(validEvidence);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
      expect(validation.warnings).toHaveLength(0);
    });

    it('should detect validation errors in enhanced evidence', () => {
      const invalidEvidence: WcagEvidenceEnhanced[] = [
        {
          type: '',
          description: '',
          value: '',
          elementCount: -1,
          affectedSelectors: 'invalid' as any,
          fixExample: {
            before: '',
            after: '',
            description: '',
          },
        },
      ];

      const validation = EvidenceProcessor.validateEnhancedEvidence(invalidEvidence);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain('Missing required fields');
      expect(validation.errors[1]).toContain('elementCount cannot be negative');
      expect(validation.errors[2]).toContain('affectedSelectors must be an array');
    });

    it('should calculate performance metrics', () => {
      const startTime = Date.now() - 1000; // 1 second ago
      const enhanced: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test evidence',
          value: 'test',
          elementCount: 1,
          fixExample: {
            before: 'before',
            after: 'after',
            description: 'fix',
          },
        },
        {
          type: 'text',
          description: 'Regular evidence',
          value: 'test',
        },
      ];

      const metrics = EvidenceProcessor.calculatePerformanceMetrics(enhanced, startTime);

      expect(metrics.totalProcessingTime).toBeGreaterThan(0);
      expect(metrics.averageProcessingTime).toBeGreaterThan(0);
      expect(metrics.enhancedItemsCount).toBe(1);
      expect(metrics.enhancementRate).toBe(0.5);
    });
  });

  describe('Fix Example Templates', () => {
    it('should provide templates for all registered checks', () => {
      const ruleIds = ['WCAG-024', 'WCAG-025', 'WCAG-026'];

      ruleIds.forEach((ruleId) => {
        const fixExamples = EvidenceProcessor.generateFixExamples(
          [
            {
              type: 'code',
              description: 'Test issue',
              value: 'test',
              severity: 'error',
            },
          ],
          ruleId as any,
        );

        if (ruleId === 'WCAG-024' || ruleId === 'WCAG-025' || ruleId === 'WCAG-026') {
          expect(fixExamples).toHaveLength(1);
          expect(fixExamples[0].description).toBeTruthy();
          expect(fixExamples[0].before).toBeTruthy();
          expect(fixExamples[0].after).toBeTruthy();
          expect(fixExamples[0].resources).toHaveLength(2);
        }
      });
    });
  });

  describe('Type Safety Validation', () => {
    it('should maintain strict TypeScript typing', () => {
      const evidence: WcagEvidenceEnhanced = {
        type: 'code',
        description: 'Type test',
        value: 'test value',
        elementCount: 1,
        affectedSelectors: ['div', 'span'],
        fixExample: {
          before: 'before code',
          after: 'after code',
          description: 'Fix description',
          codeExample: 'full example',
          resources: ['https://example.com'],
        },
        metadata: {
          scanDuration: 1000,
          elementsAnalyzed: 5,
          checkSpecificData: {
            testData: 'value',
            numericData: 42,
            booleanData: true,
          },
          performanceMetrics: {
            domParseTime: 100,
            selectorQueryTime: 200,
            evaluationTime: 300,
          },
        },
      };

      // TypeScript compilation validates the structure
      expect(evidence.type).toBe('code');
      expect(evidence.elementCount).toBe(1);
      expect(evidence.affectedSelectors).toHaveLength(2);
      expect(evidence.fixExample?.resources).toHaveLength(1);
      expect(evidence.metadata?.checkSpecificData?.testData).toBe('value');
    });

    it('should handle optional enhanced fields gracefully', () => {
      const basicEvidence: WcagEvidenceEnhanced = {
        type: 'text',
        description: 'Basic evidence',
        value: 'basic value',
        // All enhanced fields are optional
      };

      const processed = EvidenceProcessor.processEvidence([basicEvidence]);

      expect(processed).toHaveLength(1);
      expect(processed[0].type).toBe('text');
      expect(processed[0].elementCount).toBeUndefined();
      expect(processed[0].fixExample).toBeUndefined();
    });
  });

  describe('Backward Compatibility', () => {
    it('should process legacy evidence without breaking', () => {
      const legacyEvidence: WcagEvidence[] = [
        {
          type: 'code',
          description: 'Legacy evidence',
          value: '<div>legacy</div>',
          selector: 'div',
        },
      ];

      const processed = EvidenceProcessor.processEvidence(legacyEvidence);

      expect(processed).toHaveLength(1);
      expect(processed[0].type).toBe('code');
      expect(processed[0].description).toBe('Legacy evidence');
      expect(processed[0].value).toBe('<div>legacy</div>');
      expect(processed[0].selector).toBe('div');
    });

    it('should convert enhanced evidence to legacy format without data loss', () => {
      const enhanced: WcagEvidenceEnhanced[] = [
        {
          type: 'measurement',
          description: 'Enhanced evidence',
          value: 'enhanced value',
          selector: 'span',
          screenshot: 'screenshot.png',
          severity: 'warning',
          message: 'test message',
          element: 'span element',
          details: 'additional details',
          // Enhanced fields that should be stripped
          elementCount: 5,
          affectedSelectors: ['span', 'div'],
          fixExample: {
            before: 'before',
            after: 'after',
            description: 'fix',
          },
        },
      ];

      const legacy = EvidenceProcessor.toLegacyFormat(enhanced);

      expect(legacy).toHaveLength(1);
      expect(legacy[0]).toEqual({
        type: 'measurement',
        description: 'Enhanced evidence',
        value: 'enhanced value',
        selector: 'span',
        screenshot: 'screenshot.png',
        severity: 'warning',
        message: 'test message',
        element: 'span element',
        details: 'additional details',
      });
    });
  });
});
