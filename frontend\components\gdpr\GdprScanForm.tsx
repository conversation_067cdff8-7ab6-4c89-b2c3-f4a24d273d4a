'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { GdprApiService } from '@/services/gdpr-api';
import { GdprScanRequest, GdprScanResult } from '@/types/gdpr';
import { Shield, AlertTriangle, CheckCircle, Loader2 } from 'lucide-react';

interface GdprScanFormProps {
  onScanComplete: (result: GdprScanResult) => void;
}

export function GdprScanForm({ onScanComplete }: GdprScanFormProps) {
  const [formData, setFormData] = useState<GdprScanRequest>({
    targetUrl: '',
    scanOptions: {
      enableCookieAnalysis: true,
      enableTrackerDetection: true,
      enableConsentTesting: true,
      maxPages: 10,
      timeout: 300000,
    },
  });

  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // URL validation
    if (!formData.targetUrl) {
      errors.targetUrl = 'Website URL is required';
    } else {
      try {
        new URL(formData.targetUrl);
      } catch {
        errors.targetUrl = 'Please enter a valid URL (including http:// or https://)';
      }
    }

    // Timeout validation
    if (
      formData.scanOptions?.timeout &&
      (formData.scanOptions.timeout < 60000 || formData.scanOptions.timeout > 3600000)
    ) {
      errors.timeout = 'Timeout must be between 1 minute and 1 hour';
    }

    // Max pages validation
    if (
      formData.scanOptions?.maxPages &&
      (formData.scanOptions.maxPages < 1 || formData.scanOptions.maxPages > 50)
    ) {
      errors.maxPages = 'Max pages must be between 1 and 50';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsScanning(true);
    setError(null);
    setScanProgress(0);

    try {
      // Better progress simulation based on actual GDPR checks (21 total)
      // Exclude manual review checks from progress calculation
      const totalAutomatedChecks = 18; // 21 total - 3 manual review checks
      let currentCheck = 0;

      const progressInterval = setInterval(() => {
        currentCheck++;
        const progressPercent = Math.min(90, (currentCheck / totalAutomatedChecks) * 90);
        setScanProgress(progressPercent);

        if (currentCheck >= totalAutomatedChecks) {
          clearInterval(progressInterval);
        }
      }, 12000); // ~4 minutes total for 18 checks

      // eslint-disable-next-line no-console
      console.log('🔍 Starting GDPR scan with real backend integration');

      // REAL SCAN - NO MOCK DATA
      const result = await GdprApiService.startScan(formData);

      clearInterval(progressInterval);
      setScanProgress(100);

      // eslint-disable-next-line no-console
      console.log('✅ GDPR scan completed successfully');
      onScanComplete(result);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('❌ GDPR scan failed:', error);
      setError(error instanceof Error ? error.message : 'Scan failed');
    } finally {
      setIsScanning(false);
      setScanProgress(0);
    }
  };

  const updateScanOption = (
    key: keyof NonNullable<GdprScanRequest['scanOptions']>,
    value: boolean | number,
  ) => {
    setFormData((prev) => ({
      ...prev,
      scanOptions: {
        ...prev.scanOptions,
        [key]: value,
      },
    }));
  };

  if (isScanning) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            GDPR Compliance Scan in Progress
          </CardTitle>
          <CardDescription>Analyzing {formData.targetUrl} for GDPR compliance...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Scanning Progress</span>
              <span>{Math.round(scanProgress)}%</span>
            </div>
            <Progress value={scanProgress} className="w-full" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>21 GDPR Rules</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Real Website Analysis</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Cookie & Tracker Detection</span>
            </div>
          </div>

          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Performing comprehensive GDPR compliance analysis. This may take a few minutes.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Start GDPR Compliance Scan
        </CardTitle>
        <CardDescription>
          Analyze a website for GDPR compliance across all 21 requirements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Input */}
          <div className="space-y-2">
            <Label htmlFor="targetUrl">Website URL *</Label>
            <Input
              id="targetUrl"
              type="url"
              placeholder="https://example.com"
              value={formData.targetUrl}
              onChange={(e) => setFormData((prev) => ({ ...prev, targetUrl: e.target.value }))}
              className={validationErrors.targetUrl ? 'border-red-500' : ''}
            />
            {validationErrors.targetUrl && (
              <p className="text-sm text-red-500">{validationErrors.targetUrl}</p>
            )}
          </div>

          {/* Scan Options */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Scan Configuration</Label>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="cookieAnalysis">Cookie Analysis</Label>
                  <p className="text-sm text-muted-foreground">
                    Analyze cookie consent and classification
                  </p>
                </div>
                <Switch
                  id="cookieAnalysis"
                  checked={formData.scanOptions?.enableCookieAnalysis ?? true}
                  onCheckedChange={(checked) => updateScanOption('enableCookieAnalysis', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="trackerDetection">Tracker Detection</Label>
                  <p className="text-sm text-muted-foreground">
                    Detect third-party tracking scripts
                  </p>
                </div>
                <Switch
                  id="trackerDetection"
                  checked={formData.scanOptions?.enableTrackerDetection ?? true}
                  onCheckedChange={(checked) => updateScanOption('enableTrackerDetection', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="consentTesting">Consent Testing</Label>
                  <p className="text-sm text-muted-foreground">Test consent banner functionality</p>
                </div>
                <Switch
                  id="consentTesting"
                  checked={formData.scanOptions?.enableConsentTesting ?? true}
                  onCheckedChange={(checked) => updateScanOption('enableConsentTesting', checked)}
                />
              </div>
            </div>

            {/* Advanced Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="maxPages">Max Pages to Scan</Label>
                <Input
                  id="maxPages"
                  type="number"
                  min="1"
                  max="50"
                  value={formData.scanOptions?.maxPages ?? 10}
                  onChange={(e) => updateScanOption('maxPages', parseInt(e.target.value))}
                  className={validationErrors.maxPages ? 'border-red-500' : ''}
                />
                {validationErrors.maxPages && (
                  <p className="text-sm text-red-500">{validationErrors.maxPages}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout">Timeout (seconds)</Label>
                <Input
                  id="timeout"
                  type="number"
                  min="60"
                  max="3600"
                  value={(formData.scanOptions?.timeout ?? 300000) / 1000}
                  onChange={(e) => updateScanOption('timeout', parseInt(e.target.value) * 1000)}
                  className={validationErrors.timeout ? 'border-red-500' : ''}
                />
                {validationErrors.timeout && (
                  <p className="text-sm text-red-500">{validationErrors.timeout}</p>
                )}
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Button type="submit" className="w-full" size="lg">
            <Shield className="h-4 w-4 mr-2" />
            Start GDPR Compliance Scan
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
