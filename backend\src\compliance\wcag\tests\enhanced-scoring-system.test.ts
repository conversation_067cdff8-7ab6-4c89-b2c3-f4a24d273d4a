/**
 * Enhanced Scoring System Test Suite
 * Tests the graduated penalty system, confidence weighting, and category-specific thresholds
 */

import { CheckTemplate, CheckResult } from '../utils/check-template';
import { 
  SCORING_CONFIG, 
  PENALTY_TIERS, 
  CATEGORY_THRESHOLDS, 
  LEVEL_THRESHOLDS,
  CONFIDENCE_ADJUSTMENTS 
} from '../constants';
import { WcagCategory, WcagLevel } from '../types';

describe('Enhanced Scoring System', () => {
  let checkTemplate: CheckTemplate;

  beforeEach(() => {
    checkTemplate = new CheckTemplate();
  });

  describe('Graduated Penalty System', () => {
    test('should apply correct penalty tiers', async () => {
      const testCases = [
        { score: 90, maxScore: 100, expectedMultiplier: 1.0, expectedStatus: 'passed' },
        { score: 75, maxScore: 100, expectedMultiplier: 1.0, expectedStatus: 'passed' },
        { score: 70, maxScore: 100, expectedMultiplier: 0.8, expectedStatus: 'partial' },
        { score: 60, maxScore: 100, expectedMultiplier: 0.8, expectedStatus: 'partial' },
        { score: 50, maxScore: 100, expectedMultiplier: 0.6, expectedStatus: 'partial' },
        { score: 40, maxScore: 100, expectedMultiplier: 0.4, expectedStatus: 'partial' },
        { score: 20, maxScore: 100, expectedMultiplier: 0.0, expectedStatus: 'failed' },
      ];

      for (const testCase of testCases) {
        const result: CheckResult = {
          score: testCase.score,
          maxScore: testCase.maxScore,
          evidence: [],
          issues: [],
          recommendations: []
        };

        // Use reflection to access private method
        const scoringResult = (checkTemplate as any).calculateWcagCompliance(result);

        expect(scoringResult.status).toBe(testCase.expectedStatus);
        expect(scoringResult.penaltyTier?.multiplier).toBe(testCase.expectedMultiplier);
        
        const expectedFinalScore = Math.round(testCase.score * testCase.expectedMultiplier);
        expect(scoringResult.score).toBe(expectedFinalScore);
      }
    });

    test('should maintain backward compatibility with legacy mode', async () => {
      const result: CheckResult = {
        score: 70,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: []
      };

      // Test with graduated penalties disabled (legacy mode)
      const legacyConfig = {
        passThreshold: 75,
        strictMode: false,
        enableGradualScoring: true,
        enableGraduatedPenalties: false
      };

      const scoringResult = (checkTemplate as any).calculateWcagCompliance(
        result, 
        undefined, 
        undefined, 
        undefined, 
        legacyConfig
      );

      // Should get 0 in legacy mode (below 75% threshold)
      expect(scoringResult.score).toBe(0);
      expect(scoringResult.status).toBe('failed');
    });
  });

  describe('Confidence Weighting', () => {
    test('should apply confidence adjustments correctly', async () => {
      const testCases = [
        { confidence: 0.95, expectedAdjustment: 1.0 },
        { confidence: 0.85, expectedAdjustment: 0.95 },
        { confidence: 0.75, expectedAdjustment: 0.9 },
        { confidence: 0.65, expectedAdjustment: 0.85 },
        { confidence: 0.55, expectedAdjustment: 0.8 },
      ];

      for (const testCase of testCases) {
        const result: CheckResult = {
          score: 80,
          maxScore: 100,
          evidence: [],
          issues: [],
          recommendations: [],
          confidence: testCase.confidence
        };

        const scoringResult = (checkTemplate as any).calculateWcagCompliance(result);

        expect(scoringResult.confidenceAdjustment).toBe(testCase.expectedAdjustment);
        
        const expectedAdjustedScore = Math.round(80 * testCase.expectedAdjustment);
        expect(scoringResult.adjustedScore).toBe(expectedAdjustedScore);
      }
    });

    test('should handle missing confidence gracefully', async () => {
      const result: CheckResult = {
        score: 80,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: []
        // No confidence field
      };

      const scoringResult = (checkTemplate as any).calculateWcagCompliance(result);

      expect(scoringResult.confidenceAdjustment).toBe(1.0);
      expect(scoringResult.adjustedScore).toBe(80);
    });
  });

  describe('Category-Specific Thresholds', () => {
    test('should apply category-specific thresholds', async () => {
      const result: CheckResult = {
        score: 75,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: []
      };

      // Test operable category (80% threshold)
      const operableResult = (checkTemplate as any).calculateWcagCompliance(
        result, 
        'operable' as WcagCategory
      );
      expect(operableResult.threshold).toBe(80);
      expect(operableResult.status).toBe('partial'); // 75% < 80% threshold

      // Test understandable category (70% threshold)
      const understandableResult = (checkTemplate as any).calculateWcagCompliance(
        result, 
        'understandable' as WcagCategory
      );
      expect(understandableResult.threshold).toBe(70);
      expect(understandableResult.status).toBe('passed'); // 75% > 70% threshold
    });
  });

  describe('Level-Specific Thresholds', () => {
    test('should apply level-specific thresholds', async () => {
      const result: CheckResult = {
        score: 75,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: []
      };

      // Test AAA level (85% threshold)
      const aaaResult = (checkTemplate as any).calculateWcagCompliance(
        result, 
        undefined,
        'AAA' as WcagLevel
      );
      expect(aaaResult.threshold).toBe(85);
      expect(aaaResult.status).toBe('partial'); // 75% < 85% threshold

      // Test A level (70% threshold)
      const aResult = (checkTemplate as any).calculateWcagCompliance(
        result, 
        undefined,
        'A' as WcagLevel
      );
      expect(aResult.threshold).toBe(70);
      expect(aResult.status).toBe('passed'); // 75% > 70% threshold
    });
  });

  describe('Combined Scoring Features', () => {
    test('should handle complex scoring scenario', async () => {
      const result: CheckResult = {
        score: 70,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: [],
        confidence: 0.8 // 95% confidence adjustment
      };

      const scoringResult = (checkTemplate as any).calculateWcagCompliance(
        result,
        'operable' as WcagCategory, // 80% threshold
        'AA' as WcagLevel, // 75% threshold (max with category = 80%)
        0.8 // confidence
      );

      // Expected flow:
      // 1. Original score: 70/100 = 70%
      // 2. Confidence adjustment: 70 * 0.95 = 66.5 → 67
      // 3. Threshold: max(80%, 75%) = 80%
      // 4. 67% < 80%, so gets partial credit
      // 5. Penalty tier for 67%: 0.8 multiplier (60-74% range)
      // 6. Final score: 67 * 0.8 = 53.6 → 54

      expect(scoringResult.originalScore).toBe(70);
      expect(scoringResult.adjustedScore).toBe(67); // 70 * 0.95 rounded
      expect(scoringResult.threshold).toBe(80);
      expect(scoringResult.status).toBe('partial');
      expect(scoringResult.penaltyTier?.multiplier).toBe(0.8);
      expect(scoringResult.score).toBe(54); // 67 * 0.8 rounded
    });
  });

  describe('Scoring Details Generation', () => {
    test('should generate comprehensive scoring details', async () => {
      const result: CheckResult = {
        score: 70,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: [],
        confidence: 0.8
      };

      const scoringResult = (checkTemplate as any).calculateWcagCompliance(
        result,
        'operable' as WcagCategory,
        undefined,
        0.8
      );

      expect(scoringResult.details).toContain('70.0%'); // Original percentage
      expect(scoringResult.details).toContain('66.5%'); // Adjusted percentage
      expect(scoringResult.details).toContain('confidence: 80%'); // Confidence info
      expect(scoringResult.details).toContain('threshold: 80%'); // Threshold info
      expect(scoringResult.details).toContain('80% credit'); // Penalty tier info
      expect(scoringResult.details).toContain('PARTIAL'); // Status
    });
  });

  describe('Configuration Validation', () => {
    test('should validate scoring configuration constants', () => {
      // Validate penalty tiers are in descending order
      for (let i = 0; i < PENALTY_TIERS.length - 1; i++) {
        expect(PENALTY_TIERS[i].min).toBeGreaterThan(PENALTY_TIERS[i + 1].min);
      }

      // Validate confidence adjustments are in descending order
      for (let i = 0; i < CONFIDENCE_ADJUSTMENTS.length - 1; i++) {
        expect(CONFIDENCE_ADJUSTMENTS[i].min).toBeGreaterThan(CONFIDENCE_ADJUSTMENTS[i + 1].min);
      }

      // Validate threshold values are reasonable
      Object.values(CATEGORY_THRESHOLDS).forEach(threshold => {
        expect(threshold).toBeGreaterThanOrEqual(50);
        expect(threshold).toBeLessThanOrEqual(100);
      });

      Object.values(LEVEL_THRESHOLDS).forEach(threshold => {
        expect(threshold).toBeGreaterThanOrEqual(50);
        expect(threshold).toBeLessThanOrEqual(100);
      });
    });
  });

  describe('Performance Impact', () => {
    test('should not significantly impact performance', async () => {
      const result: CheckResult = {
        score: 75,
        maxScore: 100,
        evidence: [],
        issues: [],
        recommendations: [],
        confidence: 0.85
      };

      const iterations = 1000;
      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        (checkTemplate as any).calculateWcagCompliance(
          result,
          'perceivable' as WcagCategory,
          'AA' as WcagLevel,
          0.85
        );
      }

      const endTime = Date.now();
      const avgTime = (endTime - startTime) / iterations;

      // Should complete in less than 1ms per calculation on average
      expect(avgTime).toBeLessThan(1);
    });
  });
});
