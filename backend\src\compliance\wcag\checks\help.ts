/**
 * WCAG-065: Help Check (3.3.5 Level AAA)
 * 80% Automated - Detects availability of contextual help
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export interface HelpConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableContentQualityAnalysis?: boolean;
}

export class HelpCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();

  async performCheck(config: HelpConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: HelpConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableContentQualityAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-065',
      'Help',
      'robust',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeHelpCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with help availability analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-065',
        ruleName: 'Help',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'help-availability-analysis',
          contextualHelpDetection: true,
          helpAccessibilityValidation: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 25,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeHelpCheck(
    page: Page,
    config: HelpConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer with session management
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
      config.scanId || 'help-check'
    );

    // Analyze help content using enhanced analyzer
    const helpContentAnalysis = await this.analyzeHelpContentEnhanced(formAccessibilityReport);

    // Analyze context-sensitive help validation
    const contextSensitiveHelpAnalysis = await this.analyzeContextSensitiveHelp(page);

    // Analyze assistance mechanism testing
    const assistanceMechanismAnalysis = await this.analyzeAssistanceMechanisms(page);

    // Combine analysis results
    const allAnalyses = [
      helpContentAnalysis,
      contextSensitiveHelpAnalysis,
      assistanceMechanismAnalysis,
    ];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze help content using FormAccessibilityAnalyzer
   */
  private async analyzeHelpContentEnhanced(formAccessibilityReport: {
    forms: Array<{
      selector: string;
      fields: Array<{
        selector: string;
        validation: {
          isRequired: boolean;
          hasValidation: boolean;
        };
        accessibility: {
          hasDescription: boolean;
        };
      }>;
    }>;
  }) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form, formIndex: number) => {
      totalChecks++;

      // Check if complex forms have help available
      const isComplexForm =
        form.fields.length > 5 ||
        form.fields.some((field) => field.validation.hasValidation || field.validation.isRequired);

      if (isComplexForm) {
        // Check for form-level help - assume no help for now since FormAnalysis doesn't track this
        const hasFormHelp = false;

        if (hasFormHelp) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Complex form ${formIndex + 1} has help available`,
            value: `${form.selector} - form has help or instructions`,
            selector: form.selector,
            severity: 'info',
          });
        } else {
          issues.push(`Complex form ${formIndex + 1} lacks help content`);
          evidence.push({
            type: 'code',
            description: `Complex form ${formIndex + 1} requires help content`,
            value: `${form.selector} - complex form without help (${form.fields.length} fields)`,
            selector: form.selector,
            severity: 'warning',
          });
          recommendations.push(`Add help content to complex form ${formIndex + 1}`);
        }
      } else {
        // Simple forms pass by default
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Simple form ${formIndex + 1} does not require help`,
          value: `${form.selector} - simple form (${form.fields.length} fields)`,
          selector: form.selector,
          severity: 'info',
        });
      }

      // Check individual field help for complex fields
      form.fields.forEach((field, fieldIndex: number) => {
        if (field.validation.hasValidation || field.validation.isRequired) {
          totalChecks++;

          if (field.accessibility.hasDescription) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Complex field ${fieldIndex + 1} has help available`,
              value: `${field.selector} - field has description`,
              selector: field.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Complex field ${fieldIndex + 1} lacks help content`);
            evidence.push({
              type: 'code',
              description: `Complex field ${fieldIndex + 1} needs help content`,
              value: `${field.selector} - required/validated field without help`,
              selector: field.selector,
              severity: 'warning',
            });
            recommendations.push(`Add help content to complex field ${fieldIndex + 1}`);
          }
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze context-sensitive help validation
   */
  private async analyzeContextSensitiveHelp(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for context-sensitive help elements
    const contextHelpElements = await page.$$eval(
      '.help, .help-text, .tooltip, .popover, [aria-describedby], [title], .help-icon, .help-button',
      (elements) => {
        return elements.map((element, index) => {
          const htmlElement = element as HTMLElement;
          const isVisible = htmlElement.offsetParent !== null;
          const hasKeyboardAccess =
            htmlElement.tabIndex >= 0 || ['BUTTON', 'A', 'INPUT'].includes(element.tagName);
          const hasAriaLabel =
            element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby');
          const helpText =
            element.textContent?.trim() ||
            element.getAttribute('title') ||
            element.getAttribute('aria-label') ||
            '';

          const associatedField =
            element.getAttribute('aria-describedby') ||
            element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          return {
            index,
            selector: `help-element-${index}`,
            isVisible,
            hasKeyboardAccess,
            hasAriaLabel,
            helpText,
            hasAssociatedField: !!associatedField,
            isContextual:
              !!associatedField || !!element.closest('.form-group, .field, .input-group'),
          };
        });
      },
    );

    const totalChecks = contextHelpElements.length;
    let passedChecks = 0;

    contextHelpElements.forEach((helpElement, index) => {
      let helpPassed = true;

      // Check if help is accessible
      if (!helpElement.hasKeyboardAccess) {
        helpPassed = false;
        issues.push(`Help element ${index + 1} lacks keyboard access`);
        evidence.push({
          type: 'code',
          description: `Help element ${index + 1} needs keyboard accessibility`,
          value: `hasKeyboardAccess: ${helpElement.hasKeyboardAccess}`,
          severity: 'error',
        });
        recommendations.push(`Make help element ${index + 1} keyboard accessible`);
      }

      // Check if help has meaningful content
      if (helpElement.helpText.length < 10) {
        helpPassed = false;
        issues.push(`Help element ${index + 1} has insufficient content`);
        evidence.push({
          type: 'code',
          description: `Help element ${index + 1} needs more detailed content`,
          value: `helpText: "${helpElement.helpText}" (${helpElement.helpText.length} chars)`,
          severity: 'warning',
        });
        recommendations.push(`Add more detailed help content to element ${index + 1}`);
      }

      // Check if help is contextual
      if (!helpElement.isContextual) {
        issues.push(`Help element ${index + 1} is not contextual`);
        evidence.push({
          type: 'code',
          description: `Help element ${index + 1} should be contextual`,
          value: `isContextual: ${helpElement.isContextual}`,
          severity: 'warning',
        });
        recommendations.push(`Make help element ${index + 1} contextual to specific form fields`);
      }

      if (helpPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Help element ${index + 1} is accessible and contextual`,
          value: `"${helpElement.helpText}" - accessible and contextual`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze assistance mechanism testing
   */
  private async analyzeAssistanceMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for various assistance mechanisms
    const assistanceMechanisms = await page.$$eval('*', (elements) => {
      const mechanisms = {
        helpLinks: 0,
        tooltips: 0,
        helpButtons: 0,
        instructions: 0,
        examples: 0,
        glossaries: 0,
        tutorials: 0,
      };

      elements.forEach((element) => {
        const text = element.textContent?.toLowerCase() || '';
        const href = element.getAttribute('href')?.toLowerCase() || '';
        const className = element.className?.toLowerCase() || '';
        const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';

        // Count different types of assistance mechanisms
        if (href.includes('help') || text.includes('help') || ariaLabel.includes('help')) {
          mechanisms.helpLinks++;
        }
        if (className.includes('tooltip') || element.hasAttribute('title')) {
          mechanisms.tooltips++;
        }
        if (className.includes('help') && element.tagName === 'BUTTON') {
          mechanisms.helpButtons++;
        }
        if (text.includes('instruction') || text.includes('how to') || text.includes('example')) {
          mechanisms.instructions++;
        }
        if (text.includes('example:') || text.includes('e.g.') || text.includes('for example')) {
          mechanisms.examples++;
        }
        if (text.includes('glossary') || text.includes('definition') || text.includes('term')) {
          mechanisms.glossaries++;
        }
        if (text.includes('tutorial') || text.includes('guide') || text.includes('walkthrough')) {
          mechanisms.tutorials++;
        }
      });

      return mechanisms;
    });

    const totalMechanismTypes = Object.keys(assistanceMechanisms).length;
    const availableMechanisms = Object.values(assistanceMechanisms).filter(
      (count) => count > 0,
    ).length;

    const totalChecks = 1;
    let passedChecks = 0;

    // Check if sufficient assistance mechanisms are available
    if (availableMechanisms >= 3) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Sufficient assistance mechanisms available',
        value: `${availableMechanisms}/${totalMechanismTypes} types of assistance mechanisms found`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient assistance mechanisms available');
      evidence.push({
        type: 'code',
        description: 'More assistance mechanisms needed',
        value: `Only ${availableMechanisms}/${totalMechanismTypes} types found: ${JSON.stringify(assistanceMechanisms)}`,
        severity: 'warning',
      });
      recommendations.push(
        'Add more assistance mechanisms (help links, tooltips, instructions, examples, etc.)',
      );
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }
}
