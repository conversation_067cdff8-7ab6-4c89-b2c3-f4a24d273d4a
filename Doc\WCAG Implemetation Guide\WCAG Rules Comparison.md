% WCAG Versions Comparison Report
% Your Name · Date

# Table of Contents
1. [Summary Matrix](#summary-matrix)  
2. [WCAG 2.1 Details](#wcag-21-details)  
3. [WCAG 2.2 Details](#wcag-22-details)  
4. [WCAG 3.0 Details](#wcag-30-details)  

---

# Summary Matrix {#summary-matrix}
<div style="overflow-x:auto; white-space:nowrap;">

| **Aspect**               | **WCAG 2.1**                   | **WCAG 2.2**                                   | **WCAG 3.0 (Draft)**                      |
|--------------------------|--------------------------------|------------------------------------------------|-------------------------------------------|
| Release Date             | June 5, 2018                   | December 14, 2023                              | Working Draft (ongoing, started 2021)     |
| Total Criteria           | 78 (A/AA/AAA)                  | 87 (adds 9 new)                                | ~100+ Outcomes planned                    |
| Structure                | Principles → Guidelines → SC   | Same as 2.1 + 9 new SC                         | Reorganized by user needs & outcomes      |
| New Focus Areas          | Mobile, Low Vision             | Focus, Dragging, Target Size, Consistent Help, Redundant Entry, Accessible Auth | Plain Language, Motor Accessibility, Personalization, Contextual Help |
| Example New Criteria     | 1.4.10 Reflow; 2.5.1 Pointer Gestures | 2.4.11/12 Focus Not Obscured; 2.5.8 Target Size; 3.3.7–3.3.9 Redundant Entry & Auth | 2.2 Text & Wording; 2.5 Motor; 3.1 Pronunciation & Meaning |
| Automation Potential     | Partial (contrast, alt, labels) | Partial + outline/thickness/size tests         | Emphasis on measurable outcomes           |
| Manual Review Needed     | Content meaning, logical order, captions | Drag UI, consistency, auth workflows           | Language clarity, context judgment        |
| Maturity & Stability     | Final, widely adopted          | Final, adoption underway                       | Draft – evolving                          |
| Official Spec            | [WCAG 2.1](https://www.w3.org/TR/WCAG21/) | [WCAG 2.2](https://www.w3.org/TR/WCAG22/)      | [WCAG 3.0 Silver Draft](https://www.w3.org/TR/wcag-silver/) |

</div>

---

# WCAG 2.1 Details {#wcag-21-details}

<!-- Repeat this block for each Success Criterion in WCAG 2.1 -->

## 1.1.1 Non‑text Content (WCAG 2.1)

**What to Check**  
- Every meaningful image, icon, chart, etc., has a descriptive `alt` attribute or equivalent.  
- Decorative images are marked empty (`alt=""`) or hidden from assistive technology (`aria-hidden="true"`).  
- Complex visuals (charts, diagrams) have longer text descriptions or linked transcripts.

**Check Type:**  
- **Automated**: detect missing/empty `alt` attributes  
- **Manual**: verify that provided text accurately conveys the intended meaning  

---

## 1.2.2 Captions (Prerecorded) (WCAG 2.1)

**What to Check**  
- All prerecorded audio in videos has accurately synchronized captions.  
- Captions include speaker IDs and non‑speech sounds.  
- Caption tracks are properly tagged and toggleable.

**Check Type:**  
- **Automated**: detect presence of `<track kind="captions">`  
- **Manual**: confirm timing, accuracy, and completeness  

---

<!-- …continue listing all WCAG 2.1 SC… -->

---

# WCAG 2.2 Details {#wcag-22-details}

<!-- Repeat this block for each new or existing Success Criterion in WCAG 2.2 -->

## 2.4.11 Focus Not Obscured (Minimum) (WCAG 2.2)

**What to Check**  
- Focused element is not hidden by fixed headers/footers or overlays.  
- Page scrolls or adjusts to keep focus visible.

**Check Type:**  
- **Automated**: detect `scroll-padding`/`scroll-margin` CSS  
- **Manual**: keyboard navigation walkthrough  

---

## 2.5.8 Target Size (Minimum) (WCAG 2.2)

**What to Check**  
- Touch/click targets ≥ 24×24 CSS px, or spaced > 16 CSS px apart.  

**Check Type:**  
- **Automated**: measure element bounding boxes  
- **Manual**: spot-check on real devices  

---

<!-- …continue listing all WCAG 2.2 SC… -->

---

# WCAG 3.0 Details {#wcag-30-details}

> _Note: 3.0 is a Working Draft. Outcomes are grouped by user needs rather than numbered SC._

## 2.1 Image Alternatives (WCAG 3.0)

**What to Check**  
- All meaningful images, controls, and charts have concise text alternatives.

**Check Type:**  
- **Automated**: detect missing `alt`/text  
- **Manual**: evaluate for accuracy  

---

## 2.2 Text & Wording (WCAG 3.0)

**What to Check**  
- Content uses plain language; avoid jargon.  
- Reading level appropriate or alternate simplified version provided.

**Check Type:**  
- **Manual**: language review/readability tools  

---

<!-- …continue listing all WCAG 3.0 outcomes… -->

---
