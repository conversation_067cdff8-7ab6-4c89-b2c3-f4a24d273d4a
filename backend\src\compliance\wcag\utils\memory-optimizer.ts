/**
 * Memory Optimizer for VPS Environments
 * Advanced memory management with garbage collection optimization and memory leak prevention
 */

import * as v8 from 'v8';
import * as process from 'process';
import logger from '../../../utils/logger';

export interface MemoryStats {
  heapUsed: number; // MB
  heapTotal: number; // MB
  external: number; // MB
  rss: number; // MB
  arrayBuffers: number; // MB
  heapUsedPercent: number;
  gcStats: {
    lastGC: number;
    gcCount: number;
    gcDuration: number;
    gcType: string;
  };
}

export interface MemoryThresholds {
  warning: number; // MB
  critical: number; // MB
  cleanup: number; // MB
  forceGC: number; // MB
}

export interface MemoryOptimizationConfig {
  enableAutoGC: boolean;
  enableMemoryLeakDetection: boolean;
  enableHeapSnapshots: boolean;
  gcInterval: number; // ms
  memoryCheckInterval: number; // ms
  maxHeapSize: number; // MB
  cleanupThreshold: number; // MB
}

export interface MemoryLeak {
  type: string;
  size: number; // MB
  growth: number; // MB/minute
  detected: number; // timestamp
  severity: 'low' | 'medium' | 'high';
}

/**
 * Advanced memory optimizer for VPS environments
 */
export class MemoryOptimizer {
  private static instance: MemoryOptimizer;
  private memoryHistory: MemoryStats[] = [];
  private gcHistory: Array<{
    timestamp: number;
    duration: number;
    type: string;
    [key: string]: unknown;
  }> = [];
  private memoryLeaks: MemoryLeak[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private gcInterval: NodeJS.Timeout | null = null;
  private config: MemoryOptimizationConfig;
  private thresholds: MemoryThresholds;
  private lastCleanup: number = 0;
  private performanceObserver: unknown = null;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.thresholds = this.calculateThresholds();
    this.setupGCMonitoring();
    this.startMemoryMonitoring();
  }

  static getInstance(): MemoryOptimizer {
    if (!MemoryOptimizer.instance) {
      MemoryOptimizer.instance = new MemoryOptimizer();
    }
    return MemoryOptimizer.instance;
  }

  /**
   * Get current memory statistics
   */
  getCurrentMemoryStats(): MemoryStats {
    const memUsage = process.memoryUsage();
    const heapStats = v8.getHeapStatistics();

    const stats: MemoryStats = {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
      rss: Math.round(memUsage.rss / 1024 / 1024),
      arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024),
      heapUsedPercent: Math.round((memUsage.heapUsed / heapStats.heap_size_limit) * 100),
      gcStats: this.getLatestGCStats(),
    };

    // Store in history (keep last 120 entries = 10 minutes at 5-second intervals)
    this.memoryHistory.push(stats);
    if (this.memoryHistory.length > 120) {
      this.memoryHistory.shift();
    }

    return stats;
  }

  /**
   * Force garbage collection if available
   */
  forceGarbageCollection(): boolean {
    try {
      if (global.gc) {
        const beforeGC = process.memoryUsage().heapUsed;
        global.gc();
        const afterGC = process.memoryUsage().heapUsed;
        const freed = Math.round((beforeGC - afterGC) / 1024 / 1024);

        logger.info('Manual garbage collection completed', {
          freedMB: freed,
          heapUsedMB: Math.round(afterGC / 1024 / 1024),
        });

        return true;
      } else {
        logger.warn('Garbage collection not available (run with --expose-gc flag)');
        return false;
      }
    } catch (error) {
      logger.error('Error during manual garbage collection', { error });
      return false;
    }
  }

  /**
   * Optimize memory usage
   */
  async optimizeMemory(): Promise<{
    beforeMB: number;
    afterMB: number;
    freedMB: number;
    optimizations: string[];
  }> {
    const beforeStats = this.getCurrentMemoryStats();
    const optimizations: string[] = [];

    try {
      // 1. Clear internal caches
      this.clearInternalCaches();
      optimizations.push('Cleared internal caches');

      // 2. Force garbage collection
      if (this.forceGarbageCollection()) {
        optimizations.push('Forced garbage collection');
      }

      // 3. Clear old memory history
      if (this.memoryHistory.length > 60) {
        this.memoryHistory = this.memoryHistory.slice(-60);
        optimizations.push('Trimmed memory history');
      }

      // 4. Clear old GC history
      if (this.gcHistory.length > 100) {
        this.gcHistory = this.gcHistory.slice(-100);
        optimizations.push('Trimmed GC history');
      }

      // 5. Clear detected memory leaks older than 1 hour
      const oneHourAgo = Date.now() - 3600000;
      const oldLeakCount = this.memoryLeaks.length;
      this.memoryLeaks = this.memoryLeaks.filter((leak) => leak.detected > oneHourAgo);
      if (this.memoryLeaks.length < oldLeakCount) {
        optimizations.push(
          `Cleared ${oldLeakCount - this.memoryLeaks.length} old memory leak records`,
        );
      }

      const afterStats = this.getCurrentMemoryStats();
      const freedMB = beforeStats.heapUsed - afterStats.heapUsed;

      logger.info('Memory optimization completed', {
        beforeMB: beforeStats.heapUsed,
        afterMB: afterStats.heapUsed,
        freedMB,
        optimizations,
      });

      this.lastCleanup = Date.now();

      return {
        beforeMB: beforeStats.heapUsed,
        afterMB: afterStats.heapUsed,
        freedMB,
        optimizations,
      };
    } catch (error) {
      logger.error('Error during memory optimization', { error });
      throw error;
    }
  }

  /**
   * Detect memory leaks
   */
  detectMemoryLeaks(): MemoryLeak[] {
    if (this.memoryHistory.length < 10) {
      return []; // Need at least 10 data points
    }

    const detectedLeaks: MemoryLeak[] = [];
    const recentHistory = this.memoryHistory.slice(-10); // Last 10 measurements

    // Check for consistent memory growth
    const memoryGrowth = this.calculateMemoryGrowth(recentHistory);

    if (memoryGrowth.heapGrowthRate > 5) {
      // Growing more than 5MB/minute
      detectedLeaks.push({
        type: 'heap',
        size: recentHistory[recentHistory.length - 1].heapUsed,
        growth: memoryGrowth.heapGrowthRate,
        detected: Date.now(),
        severity:
          memoryGrowth.heapGrowthRate > 20
            ? 'high'
            : memoryGrowth.heapGrowthRate > 10
              ? 'medium'
              : 'low',
      });
    }

    if (memoryGrowth.externalGrowthRate > 2) {
      // External memory growing
      detectedLeaks.push({
        type: 'external',
        size: recentHistory[recentHistory.length - 1].external,
        growth: memoryGrowth.externalGrowthRate,
        detected: Date.now(),
        severity:
          memoryGrowth.externalGrowthRate > 10
            ? 'high'
            : memoryGrowth.externalGrowthRate > 5
              ? 'medium'
              : 'low',
      });
    }

    // Add to memory leaks list
    detectedLeaks.forEach((leak) => {
      this.memoryLeaks.push(leak);
      logger.warn('Memory leak detected', {
        type: leak.type,
        sizeMB: leak.size,
        growthRate: leak.growth,
        severity: leak.severity,
      });
    });

    return detectedLeaks;
  }

  /**
   * Get memory optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const currentStats = this.getCurrentMemoryStats();
    const recommendations: string[] = [];

    // Check current memory usage
    if (currentStats.heapUsedPercent > 80) {
      recommendations.push(
        'High memory usage detected - consider increasing heap size or optimizing memory usage',
      );
    }

    if (currentStats.heapUsedPercent > 90) {
      recommendations.push('Critical memory usage - immediate optimization required');
    }

    // Check for memory leaks
    const recentLeaks = this.memoryLeaks.filter(
      (leak) => Date.now() - leak.detected < 600000, // Last 10 minutes
    );

    if (recentLeaks.length > 0) {
      recommendations.push(`${recentLeaks.length} memory leak(s) detected - investigate and fix`);
    }

    // Check GC frequency
    const recentGCs = this.gcHistory.filter(
      (gc) => Date.now() - gc.timestamp < 300000, // Last 5 minutes
    );

    if (recentGCs.length > 10) {
      recommendations.push(
        'Frequent garbage collection detected - consider optimizing object allocation',
      );
    }

    // Check time since last cleanup
    if (Date.now() - this.lastCleanup > 1800000) {
      // 30 minutes
      recommendations.push(
        'Consider running memory optimization - last cleanup was over 30 minutes ago',
      );
    }

    return recommendations;
  }

  /**
   * Get memory health score (0-100)
   */
  getMemoryHealthScore(): number {
    const currentStats = this.getCurrentMemoryStats();
    let score = 100;

    // Deduct points for high memory usage
    if (currentStats.heapUsedPercent > 90) {
      score -= 40;
    } else if (currentStats.heapUsedPercent > 80) {
      score -= 25;
    } else if (currentStats.heapUsedPercent > 70) {
      score -= 15;
    }

    // Deduct points for memory leaks
    const recentLeaks = this.memoryLeaks.filter((leak) => Date.now() - leak.detected < 600000);
    score -= recentLeaks.length * 10;

    // Deduct points for frequent GC
    const recentGCs = this.gcHistory.filter((gc) => Date.now() - gc.timestamp < 300000);
    if (recentGCs.length > 15) {
      score -= 20;
    } else if (recentGCs.length > 10) {
      score -= 10;
    }

    return Math.max(0, score);
  }

  /**
   * Configure memory optimization settings
   */
  configure(config: Partial<MemoryOptimizationConfig>): void {
    this.config = { ...this.config, ...config };
    this.thresholds = this.calculateThresholds();

    // Restart monitoring with new config
    this.stopMonitoring();
    this.startMemoryMonitoring();

    logger.info('Memory optimizer configuration updated', { config: this.config });
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): MemoryOptimizationConfig {
    return {
      enableAutoGC: process.env.WCAG_ENABLE_AUTO_GC !== 'false',
      enableMemoryLeakDetection: true,
      enableHeapSnapshots: process.env.NODE_ENV === 'development',
      gcInterval: parseInt(process.env.WCAG_GC_INTERVAL || '300000'), // 5 minutes
      memoryCheckInterval: parseInt(process.env.WCAG_MEMORY_CHECK_INTERVAL || '30000'), // 30 seconds
      maxHeapSize: parseInt(process.env.WCAG_MAX_HEAP_SIZE || '2048'), // 2GB
      cleanupThreshold: parseInt(process.env.WCAG_CLEANUP_THRESHOLD || '1536'), // 1.5GB
    };
  }

  /**
   * Calculate memory thresholds based on system capacity
   */
  private calculateThresholds(): MemoryThresholds {
    const maxHeap = this.config.maxHeapSize;

    return {
      warning: Math.round(maxHeap * 0.7), // 70%
      critical: Math.round(maxHeap * 0.85), // 85%
      cleanup: Math.round(maxHeap * 0.75), // 75%
      forceGC: Math.round(maxHeap * 0.8), // 80%
    };
  }

  /**
   * Setup garbage collection monitoring
   */
  private setupGCMonitoring(): void {
    try {
      // Use performance observer if available
      if (typeof PerformanceObserver !== 'undefined') {
        this.performanceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'gc') {
              this.gcHistory.push({
                type: entry.name,
                duration: entry.duration,
                timestamp: Date.now(),
              });

              // Keep only last 100 GC events
              if (this.gcHistory.length > 100) {
                this.gcHistory.shift();
              }
            }
          });
        });

        (this.performanceObserver as PerformanceObserver).observe({ entryTypes: ['gc'] });
      }
    } catch (error) {
      logger.warn('GC monitoring not available', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Get latest GC statistics
   */
  private getLatestGCStats(): {
    lastGC: number;
    gcCount: number;
    gcDuration: number;
    gcType: string;
  } {
    if (this.gcHistory.length === 0) {
      return {
        lastGC: 0,
        gcCount: 0,
        gcDuration: 0,
        gcType: 'unknown',
      };
    }

    const recentGCs = this.gcHistory.filter(
      (gc) => Date.now() - gc.timestamp < 300000, // Last 5 minutes
    );

    const latest = this.gcHistory[this.gcHistory.length - 1];

    return {
      lastGC: latest.timestamp,
      gcCount: recentGCs.length,
      gcDuration:
        recentGCs.reduce((sum, gc) => sum + (gc.duration as number), 0) / recentGCs.length || 0,
      gcType: (latest.type as string) || 'unknown',
    };
  }

  /**
   * Calculate memory growth rate
   */
  private calculateMemoryGrowth(history: MemoryStats[]): {
    heapGrowthRate: number;
    externalGrowthRate: number;
  } {
    if (history.length < 2) {
      return { heapGrowthRate: 0, externalGrowthRate: 0 };
    }

    const first = history[0];
    const last = history[history.length - 1];
    const timeSpanMinutes = (history.length - 1) * 0.5; // 30-second intervals

    const heapGrowth = last.heapUsed - first.heapUsed;
    const externalGrowth = last.external - first.external;

    return {
      heapGrowthRate: heapGrowth / timeSpanMinutes,
      externalGrowthRate: externalGrowth / timeSpanMinutes,
    };
  }

  /**
   * Clear internal caches
   */
  private clearInternalCaches(): void {
    // Clear require cache for non-core modules (be careful with this)
    // This is commented out as it can be dangerous in production
    // Object.keys(require.cache).forEach(key => {
    //   if (!key.includes('node_modules')) {
    //     delete require.cache[key];
    //   }
    // });
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      try {
        const stats = this.getCurrentMemoryStats();

        // Check thresholds
        if (stats.heapUsed > this.thresholds.critical) {
          logger.error('Critical memory usage detected', {
            heapUsedMB: stats.heapUsed,
            thresholdMB: this.thresholds.critical,
          });

          // Force optimization
          this.optimizeMemory().catch((error) => {
            logger.error('Emergency memory optimization failed', { error });
          });
        } else if (stats.heapUsed > this.thresholds.warning) {
          logger.warn('High memory usage detected', {
            heapUsedMB: stats.heapUsed,
            thresholdMB: this.thresholds.warning,
          });
        }

        // Auto GC if enabled and threshold reached
        if (this.config.enableAutoGC && stats.heapUsed > this.thresholds.forceGC) {
          this.forceGarbageCollection();
        }

        // Detect memory leaks
        if (this.config.enableMemoryLeakDetection) {
          this.detectMemoryLeaks();
        }
      } catch (error) {
        logger.error('Error during memory monitoring', { error });
      }
    }, this.config.memoryCheckInterval);

    // Setup auto GC interval
    if (this.config.enableAutoGC) {
      this.gcInterval = setInterval(() => {
        const stats = this.getCurrentMemoryStats();
        if (stats.heapUsed > this.thresholds.cleanup) {
          this.forceGarbageCollection();
        }
      }, this.config.gcInterval);
    }
  }

  /**
   * Stop memory monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.gcInterval) {
      clearInterval(this.gcInterval);
      this.gcInterval = null;
    }

    if (this.performanceObserver) {
      (this.performanceObserver as PerformanceObserver).disconnect();
      this.performanceObserver = null;
    }
  }

  /**
   * Get comprehensive memory report
   */
  getMemoryReport(): {
    currentStats: MemoryStats;
    thresholds: MemoryThresholds;
    healthScore: number;
    recommendations: string[];
    memoryLeaks: MemoryLeak[];
    config: MemoryOptimizationConfig;
  } {
    return {
      currentStats: this.getCurrentMemoryStats(),
      thresholds: this.thresholds,
      healthScore: this.getMemoryHealthScore(),
      recommendations: this.getOptimizationRecommendations(),
      memoryLeaks: this.memoryLeaks.filter((leak) => Date.now() - leak.detected < 3600000), // Last hour
      config: this.config,
    };
  }
}

export default MemoryOptimizer;
