{"data": {"ruleId": "WCAG-055", "ruleName": "Label in Name", "category": "operable", "wcagVersion": "2.2", "successCriterion": "0.5.5", "level": "A", "status": "passed", "score": 99, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "text", "description": "Interactive element 1 has consistent label and accessible name", "value": "a:nth-of-type(1) - visible: \"Skip to content\", accessible: \"Skip to content\"", "selector": "a:nth-of-type(1)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(1)", "a", "nth-of-type", "visible", "<PERSON><PERSON>", "to", "content", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 2 has consistent label and accessible name", "value": "a:nth-of-type(2) - visible: \"Contact Sales: (800) 572-0470\", accessible: \"Contact Sales: (800) 572-0470\"", "selector": "a:nth-of-type(2)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(2)", "a", "nth-of-type", "visible", "Contact", "Sales", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 3 has consistent label and accessible name", "value": "a:nth-of-type(3) - visible: \"Contact Support\", accessible: \"Contact Support\"", "selector": "a:nth-of-type(3)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(3)", "a", "nth-of-type", "visible", "Contact", "Support", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 4 has consistent label and accessible name", "value": "a:nth-of-type(4) - visible: \"LoginExpand\", accessible: \"LoginExpand\"", "selector": "a:nth-of-type(4)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(4)", "a", "nth-of-type", "visible", "LoginExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 6 has consistent label and accessible name", "value": "a:nth-of-type(6) - visible: \"TigerConnect\", accessible: \"TigerConnect\"", "selector": "a:nth-of-type(6)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(6)", "a", "nth-of-type", "visible", "TigerConnect", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 7 has consistent label and accessible name", "value": "a:nth-of-type(7) - visible: \"Physician Scheduling\", accessible: \"Physician Scheduling\"", "selector": "a:nth-of-type(7)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(7)", "a", "nth-of-type", "visible", "Physician", "Scheduling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 8 has consistent label and accessible name", "value": "a:nth-of-type(8) - visible: \"TigerConnect Community\", accessible: \"TigerConnect Community\"", "selector": "a:nth-of-type(8)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(8)", "a", "nth-of-type", "visible", "TigerConnect", "Community", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 10 has consistent label and accessible name", "value": "button:nth-of-type(10) - visible: \"Search Button\", accessible: \"Search Button\"", "selector": "button:nth-of-type(10)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(10)", "button", "nth-of-type", "visible", "Search", "<PERSON><PERSON>", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 12 has consistent label and accessible name", "value": "a:nth-of-type(12) - visible: \"Home\", accessible: \"Home\"", "selector": "a:nth-of-type(12)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(12)", "a", "nth-of-type", "visible", "Home", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 13 has consistent label and accessible name", "value": "a:nth-of-type(13) - visible: \"Who We ServeExpand\", accessible: \"Who We ServeExpand\"", "selector": "a:nth-of-type(13)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(13)", "a", "nth-of-type", "visible", "Who", "We", "ServeExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 16 has consistent label and accessible name", "value": "a:nth-of-type(16) - visible: \"Services\", accessible: \"Services\"", "selector": "a:nth-of-type(16)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(16)", "a", "nth-of-type", "visible", "Services", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 17 has consistent label and accessible name", "value": "a:nth-of-type(17) - visible: \"Professional Services\", accessible: \"Professional Services\"", "selector": "a:nth-of-type(17)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(17)", "a", "nth-of-type", "visible", "Professional", "Services", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 18 has consistent label and accessible name", "value": "a:nth-of-type(18) - visible: \"Support Services\", accessible: \"Support Services\"", "selector": "a:nth-of-type(18)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(18)", "a", "nth-of-type", "visible", "Support", "Services", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 19 has consistent label and accessible name", "value": "a:nth-of-type(19) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(19)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(19)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 20 has consistent label and accessible name", "value": "a:nth-of-type(20) - visible: \"Healthcare Professionals\", accessible: \"Healthcare Professionals\"", "selector": "a:nth-of-type(20)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(20)", "a", "nth-of-type", "visible", "Healthcare", "Professionals", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 21 has consistent label and accessible name", "value": "a:nth-of-type(21) - visible: \"Physicians\", accessible: \"Physicians\"", "selector": "a:nth-of-type(21)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(21)", "a", "nth-of-type", "visible", "Physicians", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 22 has consistent label and accessible name", "value": "a:nth-of-type(22) - visible: \"Nurses\", accessible: \"Nurses\"", "selector": "a:nth-of-type(22)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(22)", "a", "nth-of-type", "visible", "Nurses", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 23 has consistent label and accessible name", "value": "a:nth-of-type(23) - visible: \"Executives\", accessible: \"Executives\"", "selector": "a:nth-of-type(23)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(23)", "a", "nth-of-type", "visible", "Executives", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.635Z"}}}, {"type": "text", "description": "Interactive element 24 has consistent label and accessible name", "value": "a:nth-of-type(24) - visible: \"IT\", accessible: \"IT\"", "selector": "a:nth-of-type(24)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(24)", "a", "nth-of-type", "visible", "IT", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 25 has consistent label and accessible name", "value": "a:nth-of-type(25) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(25)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(25)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 26 has consistent label and accessible name", "value": "a:nth-of-type(26) - visible: \"Healthcare Organizations\", accessible: \"Healthcare Organizations\"", "selector": "a:nth-of-type(26)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(26)", "a", "nth-of-type", "visible", "Healthcare", "Organizations", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 27 has consistent label and accessible name", "value": "a:nth-of-type(27) - visible: \"Ambulatory Surgery Centers\", accessible: \"Ambulatory Surgery Centers\"", "selector": "a:nth-of-type(27)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(27)", "a", "nth-of-type", "visible", "Ambulatory", "Surgery", "Centers", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 28 has consistent label and accessible name", "value": "a:nth-of-type(28) - visible: \"Behavioral Health\", accessible: \"Behavioral Health\"", "selector": "a:nth-of-type(28)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(28)", "a", "nth-of-type", "visible", "Behavioral", "Health", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 29 has consistent label and accessible name", "value": "a:nth-of-type(29) - visible: \"Health Systems\", accessible: \"Health Systems\"", "selector": "a:nth-of-type(29)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(29)", "a", "nth-of-type", "visible", "Health", "Systems", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 30 has consistent label and accessible name", "value": "a:nth-of-type(30) - visible: \"Home Health & Hospice\", accessible: \"Home Health & Hospice\"", "selector": "a:nth-of-type(30)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(30)", "a", "nth-of-type", "visible", "Home", "Health", "Hospice", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 31 has consistent label and accessible name", "value": "a:nth-of-type(31) - visible: \"Hospitals\", accessible: \"Hospitals\"", "selector": "a:nth-of-type(31)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(31)", "a", "nth-of-type", "visible", "Hospitals", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 32 has consistent label and accessible name", "value": "a:nth-of-type(32) - visible: \"Physician Groups\", accessible: \"Physician Groups\"", "selector": "a:nth-of-type(32)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(32)", "a", "nth-of-type", "visible", "Physician", "Groups", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 33 has consistent label and accessible name", "value": "a:nth-of-type(33) - visible: \"Skilled Nursing Facilities\", accessible: \"Skilled Nursing Facilities\"", "selector": "a:nth-of-type(33)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(33)", "a", "nth-of-type", "visible", "Skilled", "Nursing", "Facilities", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 34 has consistent label and accessible name", "value": "a:nth-of-type(34) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(34)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(34)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 35 has consistent label and accessible name", "value": "a:nth-of-type(35) - visible: \"SolutionsExpand\", accessible: \"SolutionsExpand\"", "selector": "a:nth-of-type(35)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(35)", "a", "nth-of-type", "visible", "SolutionsExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 38 has consistent label and accessible name", "value": "a:nth-of-type(38) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(38)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(38)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 39 has consistent label and accessible name", "value": "a:nth-of-type(39) - visible: \"Solutions\", accessible: \"Solutions\"", "selector": "a:nth-of-type(39)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(39)", "a", "nth-of-type", "visible", "Solutions", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 40 has consistent label and accessible name", "value": "a:nth-of-type(40) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(40)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(40)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 41 has consistent label and accessible name", "value": "a:nth-of-type(41) - visible: \"Physician Scheduling\", accessible: \"Physician Scheduling\"", "selector": "a:nth-of-type(41)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(41)", "a", "nth-of-type", "visible", "Physician", "Scheduling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 33, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 42 has consistent label and accessible name", "value": "a:nth-of-type(42) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(42)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(42)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 34, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 43 has consistent label and accessible name", "value": "a:nth-of-type(43) - visible: \"Alarm Management & Event Notification\", accessible: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(43)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(43)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 35, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 44 has consistent label and accessible name", "value": "a:nth-of-type(44) - visible: \"Patient Engagement\", accessible: \"Patient Engagement\"", "selector": "a:nth-of-type(44)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(44)", "a", "nth-of-type", "visible", "Patient", "Engagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 36, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 45 has consistent label and accessible name", "value": "a:nth-of-type(45) - visible: \"Care<PERSON>ond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(45)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(45)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 37, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 46 has consistent label and accessible name", "value": "a:nth-of-type(46) - visible: \"Integrations\", accessible: \"Integrations\"", "selector": "a:nth-of-type(46)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(46)", "a", "nth-of-type", "visible", "Integrations", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 38, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 47 has consistent label and accessible name", "value": "a:nth-of-type(47) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(47)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(47)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 39, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 48 has consistent label and accessible name", "value": "a:nth-of-type(48) - visible: \"Workflows\", accessible: \"Workflows\"", "selector": "a:nth-of-type(48)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(48)", "a", "nth-of-type", "visible", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 40, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 49 has consistent label and accessible name", "value": "a:nth-of-type(49) - visible: \"Critical Response Workflows\", accessible: \"Critical Response Workflows\"", "selector": "a:nth-of-type(49)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(49)", "a", "nth-of-type", "visible", "Critical", "Response", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 41, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 50 has consistent label and accessible name", "value": "a:nth-of-type(50) - visible: \"Emergency Department Workflows\", accessible: \"Emergency Department Workflows\"", "selector": "a:nth-of-type(50)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(50)", "a", "nth-of-type", "visible", "Emergency", "Department", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 42, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 51 has consistent label and accessible name", "value": "a:nth-of-type(51) - visible: \"Inpatient Workflows\", accessible: \"Inpatient Workflows\"", "selector": "a:nth-of-type(51)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(51)", "a", "nth-of-type", "visible", "Inpatient", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 43, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 52 has consistent label and accessible name", "value": "a:nth-of-type(52) - visible: \"Operating Room Workflows\", accessible: \"Operating Room Workflows\"", "selector": "a:nth-of-type(52)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(52)", "a", "nth-of-type", "visible", "Operating", "Room", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 44, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 53 has consistent label and accessible name", "value": "a:nth-of-type(53) - visible: \"Post Acute/Ambulatory Workflows\", accessible: \"Post Acute/Ambulatory Workflows\"", "selector": "a:nth-of-type(53)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(53)", "a", "nth-of-type", "visible", "Post", "Acute", "Ambulatory", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 45, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 54 has consistent label and accessible name", "value": "a:nth-of-type(54) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(54)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(54)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 46, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 55 has consistent label and accessible name", "value": "a:nth-of-type(55) - visible: \"ResourcesExpand\", accessible: \"ResourcesExpand\"", "selector": "a:nth-of-type(55)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(55)", "a", "nth-of-type", "visible", "ResourcesExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 47, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 58 has consistent label and accessible name", "value": "a:nth-of-type(58) - visible: \"Support\", accessible: \"Support\"", "selector": "a:nth-of-type(58)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(58)", "a", "nth-of-type", "visible", "Support", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 48, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 59 has consistent label and accessible name", "value": "a:nth-of-type(59) - visible: \"Professional Services\", accessible: \"Professional Services\"", "selector": "a:nth-of-type(59)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(59)", "a", "nth-of-type", "visible", "Professional", "Services", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 49, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 60 has consistent label and accessible name", "value": "a:nth-of-type(60) - visible: \"Support Services\", accessible: \"Support Services\"", "selector": "a:nth-of-type(60)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(60)", "a", "nth-of-type", "visible", "Support", "Services", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 50, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 61 has consistent label and accessible name", "value": "a:nth-of-type(61) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(61)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(61)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 51, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 62 has consistent label and accessible name", "value": "a:nth-of-type(62) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(62)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(62)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 52, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 63 has consistent label and accessible name", "value": "a:nth-of-type(63) - visible: \"App Download\", accessible: \"App Download\"", "selector": "a:nth-of-type(63)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(63)", "a", "nth-of-type", "visible", "App", "Download", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 53, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 64 has consistent label and accessible name", "value": "a:nth-of-type(64) - visible: \"Articles\", accessible: \"Articles\"", "selector": "a:nth-of-type(64)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(64)", "a", "nth-of-type", "visible", "Articles", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 54, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 65 has consistent label and accessible name", "value": "a:nth-of-type(65) - visible: \"Blog\", accessible: \"Blog\"", "selector": "a:nth-of-type(65)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(65)", "a", "nth-of-type", "visible", "Blog", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 55, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 66 has consistent label and accessible name", "value": "a:nth-of-type(66) - visible: \"Case Studies\", accessible: \"Case Studies\"", "selector": "a:nth-of-type(66)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(66)", "a", "nth-of-type", "visible", "Case", "Studies", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 56, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 67 has consistent label and accessible name", "value": "a:nth-of-type(67) - visible: \"Checklists\", accessible: \"Checklists\"", "selector": "a:nth-of-type(67)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(67)", "a", "nth-of-type", "visible", "Checklists", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 57, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 68 has consistent label and accessible name", "value": "a:nth-of-type(68) - visible: \"Datasheets\", accessible: \"Datasheets\"", "selector": "a:nth-of-type(68)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(68)", "a", "nth-of-type", "visible", "Datasheets", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 58, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 69 has consistent label and accessible name", "value": "a:nth-of-type(69) - visible: \"Demo Tours\", accessible: \"Demo Tours\"", "selector": "a:nth-of-type(69)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(69)", "a", "nth-of-type", "visible", "Demo", "Tours", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 59, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 70 has consistent label and accessible name", "value": "a:nth-of-type(70) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(70)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(70)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 60, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 71 has consistent label and accessible name", "value": "a:nth-of-type(71) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(71)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(71)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 61, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 72 has consistent label and accessible name", "value": "a:nth-of-type(72) - visible: \"eBooks\", accessible: \"eBooks\"", "selector": "a:nth-of-type(72)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(72)", "a", "nth-of-type", "visible", "eBooks", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 62, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 73 has consistent label and accessible name", "value": "a:nth-of-type(73) - visible: \"Events\", accessible: \"Events\"", "selector": "a:nth-of-type(73)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(73)", "a", "nth-of-type", "visible", "Events", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 63, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 74 has consistent label and accessible name", "value": "a:nth-of-type(74) - visible: \"Guides\", accessible: \"Guides\"", "selector": "a:nth-of-type(74)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(74)", "a", "nth-of-type", "visible", "Guides", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 64, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 75 has consistent label and accessible name", "value": "a:nth-of-type(75) - visible: \"Infographics\", accessible: \"Infographics\"", "selector": "a:nth-of-type(75)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(75)", "a", "nth-of-type", "visible", "Infographics", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 65, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 76 has consistent label and accessible name", "value": "a:nth-of-type(76) - visible: \"Podcasts\", accessible: \"Podcasts\"", "selector": "a:nth-of-type(76)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(76)", "a", "nth-of-type", "visible", "Podcasts", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 66, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 77 has consistent label and accessible name", "value": "a:nth-of-type(77) - visible: \"Reports\", accessible: \"Reports\"", "selector": "a:nth-of-type(77)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(77)", "a", "nth-of-type", "visible", "Reports", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 67, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 78 has consistent label and accessible name", "value": "a:nth-of-type(78) - visible: \"Videos\", accessible: \"Videos\"", "selector": "a:nth-of-type(78)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(78)", "a", "nth-of-type", "visible", "Videos", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 68, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 79 has consistent label and accessible name", "value": "a:nth-of-type(79) - visible: \"Webinars\", accessible: \"Webinars\"", "selector": "a:nth-of-type(79)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(79)", "a", "nth-of-type", "visible", "Webinars", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 69, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 80 has consistent label and accessible name", "value": "a:nth-of-type(80) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(80)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(80)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 70, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 81 has consistent label and accessible name", "value": "a:nth-of-type(81) - visible: \"Why TigerConnect?Expand\", accessible: \"Why TigerConnect?Expand\"", "selector": "a:nth-of-type(81)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(81)", "a", "nth-of-type", "visible", "Why", "TigerConnect", "Expand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 71, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 84 has consistent label and accessible name", "value": "a:nth-of-type(84) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(84)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(84)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 72, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 85 has consistent label and accessible name", "value": "a:nth-of-type(85) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(85)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(85)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 73, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 86 has consistent label and accessible name", "value": "a:nth-of-type(86) - visible: \"Case Studies\", accessible: \"Case Studies\"", "selector": "a:nth-of-type(86)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(86)", "a", "nth-of-type", "visible", "Case", "Studies", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 74, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 87 has consistent label and accessible name", "value": "a:nth-of-type(87) - visible: \"Videos\", accessible: \"Videos\"", "selector": "a:nth-of-type(87)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(87)", "a", "nth-of-type", "visible", "Videos", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 75, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.636Z"}}}, {"type": "text", "description": "Interactive element 88 has consistent label and accessible name", "value": "a:nth-of-type(88) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(88)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(88)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 76, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 89 has consistent label and accessible name", "value": "a:nth-of-type(89) - visible: \"Why TigerConnect?\", accessible: \"Why TigerConnect?\"", "selector": "a:nth-of-type(89)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(89)", "a", "nth-of-type", "visible", "Why", "TigerConnect", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 77, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 90 has consistent label and accessible name", "value": "a:nth-of-type(90) - visible: \"Company\", accessible: \"Company\"", "selector": "a:nth-of-type(90)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(90)", "a", "nth-of-type", "visible", "Company", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 78, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 91 has consistent label and accessible name", "value": "a:nth-of-type(91) - visible: \"Leadership Team\", accessible: \"Leadership Team\"", "selector": "a:nth-of-type(91)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(91)", "a", "nth-of-type", "visible", "Leadership", "Team", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 79, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 92 has consistent label and accessible name", "value": "a:nth-of-type(92) - visible: \"Careers\", accessible: \"Careers\"", "selector": "a:nth-of-type(92)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(92)", "a", "nth-of-type", "visible", "Careers", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 80, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 93 has consistent label and accessible name", "value": "a:nth-of-type(93) - visible: \"Media Coverage\", accessible: \"Media Coverage\"", "selector": "a:nth-of-type(93)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(93)", "a", "nth-of-type", "visible", "Media", "Coverage", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 81, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 94 has consistent label and accessible name", "value": "a:nth-of-type(94) - visible: \"Newsroom\", accessible: \"Newsroom\"", "selector": "a:nth-of-type(94)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(94)", "a", "nth-of-type", "visible", "Newsroom", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 82, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 95 has consistent label and accessible name", "value": "a:nth-of-type(95) - visible: \"Partners\", accessible: \"Partners\"", "selector": "a:nth-of-type(95)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(95)", "a", "nth-of-type", "visible", "Partners", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 83, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 96 has consistent label and accessible name", "value": "a:nth-of-type(96) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(96)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(96)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 84, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 97 has consistent label and accessible name", "value": "a:nth-of-type(97) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(97)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(97)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 85, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "code", "description": "Interactive element 99 needs consistent naming", "value": "button:nth-of-type(99) - visible: \"Toggle Menu\", accessible: \"Open menu\"", "selector": "button:nth-of-type(99)", "severity": "error", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(99)", "button", "nth-of-type", "visible", "Toggle", "<PERSON><PERSON>", "accessible", "Open", "menu"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 86, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 100 has consistent label and accessible name", "value": "a:nth-of-type(100) - visible: \"Watch Video\", accessible: \"Watch Video\"", "selector": "a:nth-of-type(100)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(100)", "a", "nth-of-type", "visible", "Watch", "Video", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 87, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 101 has consistent label and accessible name", "value": "a:nth-of-type(101) - visible: \"Take a Tour\", accessible: \"Take a Tour\"", "selector": "a:nth-of-type(101)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(101)", "a", "nth-of-type", "visible", "Take", "Tour", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 88, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 104 has consistent label and accessible name", "value": "a:nth-of-type(104) - visible: \"PatientEngagement\", accessible: \"PatientEngagement\"", "selector": "a:nth-of-type(104)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(104)", "a", "nth-of-type", "visible", "PatientEngagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 89, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 106 has consistent label and accessible name", "value": "a:nth-of-type(106) - visible: \"Care<PERSON>ond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(106)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(106)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 90, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 107 has consistent label and accessible name", "value": "a:nth-of-type(107) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(107)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(107)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 91, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 108 has consistent label and accessible name", "value": "a:nth-of-type(108) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(108)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(108)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 92, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 110 has consistent label and accessible name", "value": "a:nth-of-type(110) - visible: \"PhysicianScheduling\", accessible: \"PhysicianScheduling\"", "selector": "a:nth-of-type(110)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(110)", "a", "nth-of-type", "visible", "Physician<PERSON><PERSON>uling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 93, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 112 has consistent label and accessible name", "value": "a:nth-of-type(112) - visible: \"Alarm Management & Event Notification\", accessible: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(112)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(112)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 94, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 114 has consistent label and accessible name", "value": "a:nth-of-type(114) - visible: \"PatientEngagement\", accessible: \"PatientEngagement\"", "selector": "a:nth-of-type(114)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(114)", "a", "nth-of-type", "visible", "PatientEngagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 95, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 116 has consistent label and accessible name", "value": "a:nth-of-type(116) - visible: \"Care<PERSON>ond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(116)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(116)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 96, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 117 has consistent label and accessible name", "value": "a:nth-of-type(117) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(117)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(117)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 97, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 118 has consistent label and accessible name", "value": "a:nth-of-type(118) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(118)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(118)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 98, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 120 has consistent label and accessible name", "value": "a:nth-of-type(120) - visible: \"PhysicianScheduling\", accessible: \"PhysicianScheduling\"", "selector": "a:nth-of-type(120)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(120)", "a", "nth-of-type", "visible", "Physician<PERSON><PERSON>uling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 99, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 122 has consistent label and accessible name", "value": "a:nth-of-type(122) - visible: \"Alarm Management & Event Notification\", accessible: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(122)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(122)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 100, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 124 has consistent label and accessible name", "value": "a:nth-of-type(124) - visible: \"PatientEngagement\", accessible: \"PatientEngagement\"", "selector": "a:nth-of-type(124)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(124)", "a", "nth-of-type", "visible", "PatientEngagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 101, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 126 has consistent label and accessible name", "value": "a:nth-of-type(126) - visible: \"CareCond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(126)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(126)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 102, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 127 has consistent label and accessible name", "value": "a:nth-of-type(127) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(127)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(127)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 103, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 128 has consistent label and accessible name", "value": "a:nth-of-type(128) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(128)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(128)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 104, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 130 has consistent label and accessible name", "value": "a:nth-of-type(130) - visible: \"PhysicianScheduling\", accessible: \"PhysicianScheduling\"", "selector": "a:nth-of-type(130)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(130)", "a", "nth-of-type", "visible", "Physician<PERSON><PERSON>uling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 105, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 132 has consistent label and accessible name", "value": "a:nth-of-type(132) - visible: \"Alarm Management & Event Notification\", accessible: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(132)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(132)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 106, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 134 has consistent label and accessible name", "value": "a:nth-of-type(134) - visible: \"PatientEngagement\", accessible: \"PatientEngagement\"", "selector": "a:nth-of-type(134)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(134)", "a", "nth-of-type", "visible", "PatientEngagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 107, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 136 has consistent label and accessible name", "value": "a:nth-of-type(136) - visible: \"Care<PERSON>ond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(136)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(136)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 108, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 137 has consistent label and accessible name", "value": "a:nth-of-type(137) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(137)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(137)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 109, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 138 has consistent label and accessible name", "value": "a:nth-of-type(138) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(138)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(138)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 110, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 146 has consistent label and accessible name", "value": "a:nth-of-type(146) - visible: \"Critical Response\", accessible: \"Critical Response\"", "selector": "a:nth-of-type(146)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(146)", "a", "nth-of-type", "visible", "Critical", "Response", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 111, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 147 has consistent label and accessible name", "value": "a:nth-of-type(147) - visible: \"Emergency Department\", accessible: \"Emergency Department\"", "selector": "a:nth-of-type(147)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(147)", "a", "nth-of-type", "visible", "Emergency", "Department", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 112, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 148 has consistent label and accessible name", "value": "a:nth-of-type(148) - visible: \"Inpatient Care\", accessible: \"Inpatient Care\"", "selector": "a:nth-of-type(148)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(148)", "a", "nth-of-type", "visible", "Inpatient", "Care", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 113, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 149 has consistent label and accessible name", "value": "a:nth-of-type(149) - visible: \"Operating Room\", accessible: \"Operating Room\"", "selector": "a:nth-of-type(149)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(149)", "a", "nth-of-type", "visible", "Operating", "Room", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 114, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 150 has consistent label and accessible name", "value": "a:nth-of-type(150) - visible: \"Post Acute/Ambulatory\", accessible: \"Post Acute/Ambulatory\"", "selector": "a:nth-of-type(150)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(150)", "a", "nth-of-type", "visible", "Post", "Acute", "Ambulatory", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 115, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 151 has consistent label and accessible name", "value": "a:nth-of-type(151) - visible: \"Learn More\", accessible: \"Learn More\"", "selector": "a:nth-of-type(151)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(151)", "a", "nth-of-type", "visible", "Learn", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 116, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 152 has consistent label and accessible name", "value": "a:nth-of-type(152) - visible: \"Play\", accessible: \"Play\"", "selector": "a:nth-of-type(152)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(152)", "a", "nth-of-type", "visible", "Play", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 117, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 155 has consistent label and accessible name", "value": "a:nth-of-type(155) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(155)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(155)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 118, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 156 has consistent label and accessible name", "value": "a:nth-of-type(156) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(156)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(156)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 119, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 157 has consistent label and accessible name", "value": "a:nth-of-type(157) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(157)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(157)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 120, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 158 has consistent label and accessible name", "value": "a:nth-of-type(158) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(158)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(158)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 121, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 159 has consistent label and accessible name", "value": "a:nth-of-type(159) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(159)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(159)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 122, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 160 has consistent label and accessible name", "value": "a:nth-of-type(160) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(160)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(160)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 123, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 161 has consistent label and accessible name", "value": "a:nth-of-type(161) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(161)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(161)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 124, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 162 has consistent label and accessible name", "value": "a:nth-of-type(162) - visible: \"See How\", accessible: \"See How\"", "selector": "a:nth-of-type(162)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(162)", "a", "nth-of-type", "visible", "See", "How", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 125, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 163 has consistent label and accessible name", "value": "a:nth-of-type(163) - visible: \"Pre-HospitalConnect EMS to hospital teams to accelerate time to treatment.\", accessible: \"Pre-HospitalConnect EMS to hospital teams to accelerate time to treatment.\"", "selector": "a:nth-of-type(163)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(163)", "a", "nth-of-type", "visible", "Pre-HospitalConnect", "EMS", "to", "hospital", "teams", "accelerate", "time", "treatment", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 126, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 164 has consistent label and accessible name", "value": "a:nth-of-type(164) - visible: \"Physician <PERSON>hedulingSimplify provider scheduling with an intuitive, rules-based solution.\", accessible: \"Physician SchedulingSimplify provider scheduling with an intuitive, rules-based solution.\"", "selector": "a:nth-of-type(164)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(164)", "a", "nth-of-type", "visible", "Physician", "SchedulingSimplify", "provider", "scheduling", "with", "an", "intuitive", "rules-based", "solution", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 127, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 165 has consistent label and accessible name", "value": "a:nth-of-type(165) - visible: \"Clinical CollaborationCoordinate care seamlessly across the care continuum.\", accessible: \"Clinical CollaborationCoordinate care seamlessly across the care continuum.\"", "selector": "a:nth-of-type(165)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(165)", "a", "nth-of-type", "visible", "Clinical", "CollaborationCoordinate", "care", "seamlessly", "across", "the", "continuum", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 128, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 166 has consistent label and accessible name", "value": "a:nth-of-type(166) - visible: \"Alarm Management & Event NotificationStay on top of patient needs with instant, intelligently routed clinical system alerts.\", accessible: \"Alarm Management & Event NotificationStay on top of patient needs with instant, intelligently routed clinical system alerts.\"", "selector": "a:nth-of-type(166)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(166)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "NotificationStay", "on", "top", "of", "patient", "needs", "with", "instant", "intelligently", "routed", "clinical", "system", "alerts", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 129, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 167 has consistent label and accessible name", "value": "a:nth-of-type(167) - visible: \"Patient EngagementEffortlessly engage with patients and families before, during, and after care.\", accessible: \"Patient EngagementEffortlessly engage with patients and families before, during, and after care.\"", "selector": "a:nth-of-type(167)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(167)", "a", "nth-of-type", "visible", "Patient", "EngagementEffortlessly", "engage", "with", "patients", "and", "families", "before", "during", "after", "care", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 130, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 168 has consistent label and accessible name", "value": "a:nth-of-type(168) - visible: \"Read More\", accessible: \"Read More\"", "selector": "a:nth-of-type(168)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(168)", "a", "nth-of-type", "visible", "Read", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 131, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 169 has consistent label and accessible name", "value": "a:nth-of-type(169) - visible: \"Read More\", accessible: \"Read More\"", "selector": "a:nth-of-type(169)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(169)", "a", "nth-of-type", "visible", "Read", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 132, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 170 has consistent label and accessible name", "value": "a:nth-of-type(170) - visible: \"Read More\", accessible: \"Read More\"", "selector": "a:nth-of-type(170)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(170)", "a", "nth-of-type", "visible", "Read", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 133, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 171 has consistent label and accessible name", "value": "a:nth-of-type(171) - visible: \"Read More\", accessible: \"Read More\"", "selector": "a:nth-of-type(171)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(171)", "a", "nth-of-type", "visible", "Read", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 134, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 178 has consistent label and accessible name", "value": "a:nth-of-type(178) - visible: \"2024 Best in KLAS Winnerfor Clinical Communications in Ambulatory/Post-Acute Care​Read Verified Reviews >\", accessible: \"2024 Best in KLAS Winnerfor Clinical Communications in Ambulatory/Post-Acute Care​Read Verified Reviews >\"", "selector": "a:nth-of-type(178)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(178)", "a", "nth-of-type", "visible", "Best", "in", "KLAS", "Winnerfor", "Clinical", "Communications", "Ambulatory", "Post-Acute", "Care", "Read", "Verified", "Reviews", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 135, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 179 has consistent label and accessible name", "value": "a:nth-of-type(179) - visible: \"#1 Leader in Winter 2025 G2 Rankingsfor HIPAA-Compliant Messaging, Clinical Communication & Collaboration, and Medical Staff Scheduling​Read Verified Reviews >\", accessible: \"#1 Leader in Winter 2025 G2 Rankingsfor HIPAA-Compliant Messaging, Clinical Communication & Collaboration, and Medical Staff Scheduling​Read Verified Reviews >\"", "selector": "a:nth-of-type(179)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(179)", "a", "nth-of-type", "visible", "Leader", "in", "Winter", "G2", "Rankingsfor", "HIPAA-Compliant", "Messaging", "Clinical", "Communication", "Collaboration", "and", "Medical", "Staff", "Scheduling", "Read", "Verified", "Reviews", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 136, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 180 has consistent label and accessible name", "value": "a:nth-of-type(180) - visible: \"Named as a Leader and Placed Highest for Ability to Executein the 2024 Gartner® Magic Quadrant™ for Clinical Communication and CollaborationRead the Report >\", accessible: \"Named as a Leader and Placed Highest for Ability to Executein the 2024 Gartner® Magic Quadrant™ for Clinical Communication and CollaborationRead the Report >\"", "selector": "a:nth-of-type(180)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(180)", "a", "nth-of-type", "visible", "Named", "as", "Leader", "and", "Placed", "Highest", "for", "Ability", "to", "Executein", "the", "<PERSON><PERSON><PERSON>", "Magic", "Quadrant", "Clinical", "Communication", "CollaborationRead", "Report", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 137, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 181 has consistent label and accessible name", "value": "a:nth-of-type(181) - visible: \"eBooks\", accessible: \"eBooks\"", "selector": "a:nth-of-type(181)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(181)", "a", "nth-of-type", "visible", "eBooks", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 138, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 182 has consistent label and accessible name", "value": "a:nth-of-type(182) - visible: \"Webinars\", accessible: \"Webinars\"", "selector": "a:nth-of-type(182)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(182)", "a", "nth-of-type", "visible", "Webinars", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 139, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 183 has consistent label and accessible name", "value": "a:nth-of-type(183) - visible: \"Infographics\", accessible: \"Infographics\"", "selector": "a:nth-of-type(183)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(183)", "a", "nth-of-type", "visible", "Infographics", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 140, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 184 has consistent label and accessible name", "value": "a:nth-of-type(184) - visible: \"Case Studies\", accessible: \"Case Studies\"", "selector": "a:nth-of-type(184)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(184)", "a", "nth-of-type", "visible", "Case", "Studies", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 141, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 185 has consistent label and accessible name", "value": "a:nth-of-type(185) - visible: \"Product Tours\", accessible: \"Product Tours\"", "selector": "a:nth-of-type(185)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(185)", "a", "nth-of-type", "visible", "Product", "Tours", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 142, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 186 has consistent label and accessible name", "value": "a:nth-of-type(186) - visible: \"Blogs\", accessible: \"Blogs\"", "selector": "a:nth-of-type(186)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(186)", "a", "nth-of-type", "visible", "Blogs", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 143, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 187 has consistent label and accessible name", "value": "a:nth-of-type(187) - visible: \"Explore All Resources\", accessible: \"Explore All Resources\"", "selector": "a:nth-of-type(187)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(187)", "a", "nth-of-type", "visible", "Explore", "All", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 144, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 189 has consistent label and accessible name", "value": "a:nth-of-type(189) - visible: \"Read the Full Report\", accessible: \"Read the Full Report\"", "selector": "a:nth-of-type(189)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(189)", "a", "nth-of-type", "visible", "Read", "the", "Full", "Report", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 145, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 191 has consistent label and accessible name", "value": "a:nth-of-type(191) - visible: \"(800) 572-0470\", accessible: \"(800) 572-0470\"", "selector": "a:nth-of-type(191)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(191)", "a", "nth-of-type", "visible", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 146, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 192 has consistent label and accessible name", "value": "a:nth-of-type(192) - visible: \"Email\", accessible: \"Email\"", "selector": "a:nth-of-type(192)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(192)", "a", "nth-of-type", "visible", "Email", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 147, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 193 has consistent label and accessible name", "value": "a:nth-of-type(193) - visible: \"Email\", accessible: \"Email\"", "selector": "a:nth-of-type(193)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(193)", "a", "nth-of-type", "visible", "Email", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 148, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 194 has consistent label and accessible name", "value": "a:nth-of-type(194) - visible: \"What We Do\", accessible: \"What We Do\"", "selector": "a:nth-of-type(194)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(194)", "a", "nth-of-type", "visible", "What", "We", "Do", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 149, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 195 has consistent label and accessible name", "value": "a:nth-of-type(195) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(195)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(195)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 150, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 196 has consistent label and accessible name", "value": "a:nth-of-type(196) - visible: \"Alarm Management and Event Notification\", accessible: \"Alarm Management and Event Notification\"", "selector": "a:nth-of-type(196)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(196)", "a", "nth-of-type", "visible", "Alarm", "Management", "and", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 151, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 197 has consistent label and accessible name", "value": "a:nth-of-type(197) - visible: \"Patient Engagement\", accessible: \"Patient Engagement\"", "selector": "a:nth-of-type(197)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(197)", "a", "nth-of-type", "visible", "Patient", "Engagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 152, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 198 has consistent label and accessible name", "value": "a:nth-of-type(198) - visible: \"Physician Scheduling\", accessible: \"Physician Scheduling\"", "selector": "a:nth-of-type(198)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(198)", "a", "nth-of-type", "visible", "Physician", "Scheduling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 153, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 199 has consistent label and accessible name", "value": "a:nth-of-type(199) - visible: \"CareConduit\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(199)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(199)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 154, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 200 has consistent label and accessible name", "value": "a:nth-of-type(200) - visible: \"Workflows\", accessible: \"Workflows\"", "selector": "a:nth-of-type(200)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(200)", "a", "nth-of-type", "visible", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 155, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.637Z"}}}, {"type": "text", "description": "Interactive element 201 has consistent label and accessible name", "value": "a:nth-of-type(201) - visible: \"Who We Serve\", accessible: \"Who We Serve\"", "selector": "a:nth-of-type(201)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(201)", "a", "nth-of-type", "visible", "Who", "We", "Serve", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 156, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 202 has consistent label and accessible name", "value": "a:nth-of-type(202) - visible: \"Healthcare Organizations\", accessible: \"Healthcare Organizations\"", "selector": "a:nth-of-type(202)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(202)", "a", "nth-of-type", "visible", "Healthcare", "Organizations", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 157, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 203 has consistent label and accessible name", "value": "a:nth-of-type(203) - visible: \"Healthcare Professionals\", accessible: \"Healthcare Professionals\"", "selector": "a:nth-of-type(203)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(203)", "a", "nth-of-type", "visible", "Healthcare", "Professionals", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 158, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 204 has consistent label and accessible name", "value": "a:nth-of-type(204) - visible: \"Physicians\", accessible: \"Physicians\"", "selector": "a:nth-of-type(204)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(204)", "a", "nth-of-type", "visible", "Physicians", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 159, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 205 has consistent label and accessible name", "value": "a:nth-of-type(205) - visible: \"Nurses\", accessible: \"Nurses\"", "selector": "a:nth-of-type(205)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(205)", "a", "nth-of-type", "visible", "Nurses", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 160, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 206 has consistent label and accessible name", "value": "a:nth-of-type(206) - visible: \"Executives\", accessible: \"Executives\"", "selector": "a:nth-of-type(206)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(206)", "a", "nth-of-type", "visible", "Executives", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 161, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 207 has consistent label and accessible name", "value": "a:nth-of-type(207) - visible: \"IT\", accessible: \"IT\"", "selector": "a:nth-of-type(207)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(207)", "a", "nth-of-type", "visible", "IT", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 162, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 208 has consistent label and accessible name", "value": "a:nth-of-type(208) - visible: \"About Us\", accessible: \"About Us\"", "selector": "a:nth-of-type(208)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(208)", "a", "nth-of-type", "visible", "About", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 163, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 209 has consistent label and accessible name", "value": "a:nth-of-type(209) - visible: \"Why TigerConnect?\", accessible: \"Why TigerConnect?\"", "selector": "a:nth-of-type(209)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(209)", "a", "nth-of-type", "visible", "Why", "TigerConnect", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 164, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 210 has consistent label and accessible name", "value": "a:nth-of-type(210) - visible: \"Careers\", accessible: \"Careers\"", "selector": "a:nth-of-type(210)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(210)", "a", "nth-of-type", "visible", "Careers", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 165, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 211 has consistent label and accessible name", "value": "a:nth-of-type(211) - visible: \"Leadership\", accessible: \"Leadership\"", "selector": "a:nth-of-type(211)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(211)", "a", "nth-of-type", "visible", "Leadership", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 166, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 212 has consistent label and accessible name", "value": "a:nth-of-type(212) - visible: \"Media Coverage\", accessible: \"Media Coverage\"", "selector": "a:nth-of-type(212)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(212)", "a", "nth-of-type", "visible", "Media", "Coverage", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 167, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 213 has consistent label and accessible name", "value": "a:nth-of-type(213) - visible: \"Newsroom\", accessible: \"Newsroom\"", "selector": "a:nth-of-type(213)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(213)", "a", "nth-of-type", "visible", "Newsroom", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 168, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 214 has consistent label and accessible name", "value": "a:nth-of-type(214) - visible: \"Partners\", accessible: \"Partners\"", "selector": "a:nth-of-type(214)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(214)", "a", "nth-of-type", "visible", "Partners", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 169, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 215 has consistent label and accessible name", "value": "a:nth-of-type(215) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(215)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(215)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 170, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 216 has consistent label and accessible name", "value": "a:nth-of-type(216) - visible: \"App Download\", accessible: \"App Download\"", "selector": "a:nth-of-type(216)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(216)", "a", "nth-of-type", "visible", "App", "Download", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 171, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 217 has consistent label and accessible name", "value": "a:nth-of-type(217) - visible: \"Blog Articles\", accessible: \"Blog Articles\"", "selector": "a:nth-of-type(217)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(217)", "a", "nth-of-type", "visible", "Blog", "Articles", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 172, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 218 has consistent label and accessible name", "value": "a:nth-of-type(218) - visible: \"Case Studies\", accessible: \"Case Studies\"", "selector": "a:nth-of-type(218)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(218)", "a", "nth-of-type", "visible", "Case", "Studies", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 173, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 219 has consistent label and accessible name", "value": "a:nth-of-type(219) - visible: \"Demo Tours\", accessible: \"Demo Tours\"", "selector": "a:nth-of-type(219)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(219)", "a", "nth-of-type", "visible", "Demo", "Tours", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 174, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 220 has consistent label and accessible name", "value": "a:nth-of-type(220) - visible: \"Ebooks\", accessible: \"Ebooks\"", "selector": "a:nth-of-type(220)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(220)", "a", "nth-of-type", "visible", "Ebooks", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 175, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 221 has consistent label and accessible name", "value": "a:nth-of-type(221) - visible: \"Webinars\", accessible: \"Webinars\"", "selector": "a:nth-of-type(221)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(221)", "a", "nth-of-type", "visible", "Webinars", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 176, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 222 has consistent label and accessible name", "value": "a:nth-of-type(222) - visible: \"Sitemap\", accessible: \"Sitemap\"", "selector": "a:nth-of-type(222)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(222)", "a", "nth-of-type", "visible", "Sitemap", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 177, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 223 has consistent label and accessible name", "value": "a:nth-of-type(223) - visible: \"Accessibility\", accessible: \"Accessibility\"", "selector": "a:nth-of-type(223)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(223)", "a", "nth-of-type", "visible", "Accessibility", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 178, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 224 has consistent label and accessible name", "value": "a:nth-of-type(224) - visible: \"Legal\", accessible: \"Legal\"", "selector": "a:nth-of-type(224)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(224)", "a", "nth-of-type", "visible", "Legal", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 179, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 225 has consistent label and accessible name", "value": "a:nth-of-type(225) - visible: \"Privacy\", accessible: \"Privacy\"", "selector": "a:nth-of-type(225)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(225)", "a", "nth-of-type", "visible", "Privacy", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 180, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 235 has consistent label and accessible name", "value": "a:nth-of-type(235) - visible: \"Save My Seat\", accessible: \"Save My Seat\"", "selector": "a:nth-of-type(235)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(235)", "a", "nth-of-type", "visible", "Save", "My", "<PERSON><PERSON>", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 181, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 236 has consistent label and accessible name", "value": "a:nth-of-type(236) - visible: \"Learn More\", accessible: \"Learn More\"", "selector": "a:nth-of-type(236)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(236)", "a", "nth-of-type", "visible", "Learn", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 182, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 237 has consistent label and accessible name", "value": "a:nth-of-type(237) - visible: \"Save My Seat\", accessible: \"Save My Seat\"", "selector": "a:nth-of-type(237)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(237)", "a", "nth-of-type", "visible", "Save", "My", "<PERSON><PERSON>", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 183, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 238 has consistent label and accessible name", "value": "a:nth-of-type(238) - visible: \"Save My Seat\", accessible: \"Save My Seat\"", "selector": "a:nth-of-type(238)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(238)", "a", "nth-of-type", "visible", "Save", "My", "<PERSON><PERSON>", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 184, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 239 has consistent label and accessible name", "value": "a:nth-of-type(239) - visible: \"Learn More\", accessible: \"Learn More\"", "selector": "a:nth-of-type(239)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(239)", "a", "nth-of-type", "visible", "Learn", "More", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 185, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 242 has consistent label and accessible name", "value": "a:nth-of-type(242) - visible: \"Home\", accessible: \"Home\"", "selector": "a:nth-of-type(242)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(242)", "a", "nth-of-type", "visible", "Home", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 186, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 243 has consistent label and accessible name", "value": "a:nth-of-type(243) - visible: \"Solutions\", accessible: \"Solutions\"", "selector": "a:nth-of-type(243)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(243)", "a", "nth-of-type", "visible", "Solutions", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 187, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 244 has consistent label and accessible name", "value": "button:nth-of-type(244) - visible: \"Toggle child menuExpand\", accessible: \"Toggle child menuExpand\"", "selector": "button:nth-of-type(244)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(244)", "button", "nth-of-type", "visible", "Toggle", "child", "menuExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 188, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 245 has consistent label and accessible name", "value": "a:nth-of-type(245) - visible: \"Pre-Hospital\", accessible: \"Pre-Hospital\"", "selector": "a:nth-of-type(245)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(245)", "a", "nth-of-type", "visible", "Pre-Hospital", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 189, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 246 has consistent label and accessible name", "value": "a:nth-of-type(246) - visible: \"Physician Scheduling\", accessible: \"Physician Scheduling\"", "selector": "a:nth-of-type(246)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(246)", "a", "nth-of-type", "visible", "Physician", "Scheduling", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 190, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 247 has consistent label and accessible name", "value": "a:nth-of-type(247) - visible: \"Clinical Collaboration\", accessible: \"Clinical Collaboration\"", "selector": "a:nth-of-type(247)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(247)", "a", "nth-of-type", "visible", "Clinical", "Collaboration", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 191, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 248 has consistent label and accessible name", "value": "a:nth-of-type(248) - visible: \"Alarm Management & Event Notification\", accessible: \"Alarm Management & Event Notification\"", "selector": "a:nth-of-type(248)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(248)", "a", "nth-of-type", "visible", "Alarm", "Management", "Event", "Notification", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 192, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 249 has consistent label and accessible name", "value": "a:nth-of-type(249) - visible: \"Patient Engagement\", accessible: \"Patient Engagement\"", "selector": "a:nth-of-type(249)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(249)", "a", "nth-of-type", "visible", "Patient", "Engagement", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 193, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 250 has consistent label and accessible name", "value": "a:nth-of-type(250) - visible: \"Care<PERSON>ond<PERSON>\", accessible: \"CareConduit\"", "selector": "a:nth-of-type(250)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(250)", "a", "nth-of-type", "visible", "CareConduit", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 194, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 251 has consistent label and accessible name", "value": "a:nth-of-type(251) - visible: \"Workflows\", accessible: \"Workflows\"", "selector": "a:nth-of-type(251)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(251)", "a", "nth-of-type", "visible", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 195, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 252 has consistent label and accessible name", "value": "button:nth-of-type(252) - visible: \"Toggle child menuExpand\", accessible: \"Toggle child menuExpand\"", "selector": "button:nth-of-type(252)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(252)", "button", "nth-of-type", "visible", "Toggle", "child", "menuExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 196, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 253 has consistent label and accessible name", "value": "a:nth-of-type(253) - visible: \"Critical Response\", accessible: \"Critical Response\"", "selector": "a:nth-of-type(253)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(253)", "a", "nth-of-type", "visible", "Critical", "Response", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 197, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 254 has consistent label and accessible name", "value": "a:nth-of-type(254) - visible: \"Emergency Department Workflows\", accessible: \"Emergency Department Workflows\"", "selector": "a:nth-of-type(254)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(254)", "a", "nth-of-type", "visible", "Emergency", "Department", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 198, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 255 has consistent label and accessible name", "value": "a:nth-of-type(255) - visible: \"Inpatient Workflows\", accessible: \"Inpatient Workflows\"", "selector": "a:nth-of-type(255)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(255)", "a", "nth-of-type", "visible", "Inpatient", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 199, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 256 has consistent label and accessible name", "value": "a:nth-of-type(256) - visible: \"Operating Room Workflows\", accessible: \"Operating Room Workflows\"", "selector": "a:nth-of-type(256)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(256)", "a", "nth-of-type", "visible", "Operating", "Room", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 200, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 257 has consistent label and accessible name", "value": "a:nth-of-type(257) - visible: \"Post Acute & Ambulatory Workflows\", accessible: \"Post Acute & Ambulatory Workflows\"", "selector": "a:nth-of-type(257)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(257)", "a", "nth-of-type", "visible", "Post", "Acute", "Ambulatory", "Workflows", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 201, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 258 has consistent label and accessible name", "value": "a:nth-of-type(258) - visible: \"Organizations\", accessible: \"Organizations\"", "selector": "a:nth-of-type(258)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(258)", "a", "nth-of-type", "visible", "Organizations", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 202, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 259 has consistent label and accessible name", "value": "button:nth-of-type(259) - visible: \"Toggle child menuExpand\", accessible: \"Toggle child menuExpand\"", "selector": "button:nth-of-type(259)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(259)", "button", "nth-of-type", "visible", "Toggle", "child", "menuExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 203, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 260 has consistent label and accessible name", "value": "a:nth-of-type(260) - visible: \"Ambulatory Surgery Centers\", accessible: \"Ambulatory Surgery Centers\"", "selector": "a:nth-of-type(260)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(260)", "a", "nth-of-type", "visible", "Ambulatory", "Surgery", "Centers", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 204, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 261 has consistent label and accessible name", "value": "a:nth-of-type(261) - visible: \"Behavioral Health\", accessible: \"Behavioral Health\"", "selector": "a:nth-of-type(261)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(261)", "a", "nth-of-type", "visible", "Behavioral", "Health", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 205, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 262 has consistent label and accessible name", "value": "a:nth-of-type(262) - visible: \"Health Systems\", accessible: \"Health Systems\"", "selector": "a:nth-of-type(262)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(262)", "a", "nth-of-type", "visible", "Health", "Systems", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 206, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 263 has consistent label and accessible name", "value": "a:nth-of-type(263) - visible: \"Home Health & Hospice\", accessible: \"Home Health & Hospice\"", "selector": "a:nth-of-type(263)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(263)", "a", "nth-of-type", "visible", "Home", "Health", "Hospice", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 207, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 264 has consistent label and accessible name", "value": "a:nth-of-type(264) - visible: \"Hospitals\", accessible: \"Hospitals\"", "selector": "a:nth-of-type(264)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(264)", "a", "nth-of-type", "visible", "Hospitals", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 208, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 265 has consistent label and accessible name", "value": "a:nth-of-type(265) - visible: \"Physician Groups\", accessible: \"Physician Groups\"", "selector": "a:nth-of-type(265)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(265)", "a", "nth-of-type", "visible", "Physician", "Groups", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 209, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 266 has consistent label and accessible name", "value": "a:nth-of-type(266) - visible: \"Skilled Nursing Facilities\", accessible: \"Skilled Nursing Facilities\"", "selector": "a:nth-of-type(266)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(266)", "a", "nth-of-type", "visible", "Skilled", "Nursing", "Facilities", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 210, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 267 has consistent label and accessible name", "value": "a:nth-of-type(267) - visible: \"Professionals\", accessible: \"Professionals\"", "selector": "a:nth-of-type(267)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(267)", "a", "nth-of-type", "visible", "Professionals", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 211, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 268 has consistent label and accessible name", "value": "button:nth-of-type(268) - visible: \"Toggle child menuExpand\", accessible: \"Toggle child menuExpand\"", "selector": "button:nth-of-type(268)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(268)", "button", "nth-of-type", "visible", "Toggle", "child", "menuExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 212, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 269 has consistent label and accessible name", "value": "a:nth-of-type(269) - visible: \"Physicians\", accessible: \"Physicians\"", "selector": "a:nth-of-type(269)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(269)", "a", "nth-of-type", "visible", "Physicians", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 213, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 270 has consistent label and accessible name", "value": "a:nth-of-type(270) - visible: \"Nurses\", accessible: \"Nurses\"", "selector": "a:nth-of-type(270)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(270)", "a", "nth-of-type", "visible", "Nurses", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 214, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 271 has consistent label and accessible name", "value": "a:nth-of-type(271) - visible: \"Executives\", accessible: \"Executives\"", "selector": "a:nth-of-type(271)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(271)", "a", "nth-of-type", "visible", "Executives", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 215, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 272 has consistent label and accessible name", "value": "a:nth-of-type(272) - visible: \"IT\", accessible: \"IT\"", "selector": "a:nth-of-type(272)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(272)", "a", "nth-of-type", "visible", "IT", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 216, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 273 has consistent label and accessible name", "value": "a:nth-of-type(273) - visible: \"Resources\", accessible: \"Resources\"", "selector": "a:nth-of-type(273)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(273)", "a", "nth-of-type", "visible", "Resources", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 217, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 274 has consistent label and accessible name", "value": "button:nth-of-type(274) - visible: \"Toggle child menuExpand\", accessible: \"Toggle child menuExpand\"", "selector": "button:nth-of-type(274)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(274)", "button", "nth-of-type", "visible", "Toggle", "child", "menuExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 218, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 275 has consistent label and accessible name", "value": "a:nth-of-type(275) - visible: \"Articles\", accessible: \"Articles\"", "selector": "a:nth-of-type(275)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(275)", "a", "nth-of-type", "visible", "Articles", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 219, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 276 has consistent label and accessible name", "value": "a:nth-of-type(276) - visible: \"Blog\", accessible: \"Blog\"", "selector": "a:nth-of-type(276)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(276)", "a", "nth-of-type", "visible", "Blog", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 220, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 277 has consistent label and accessible name", "value": "a:nth-of-type(277) - visible: \"Case Studies\", accessible: \"Case Studies\"", "selector": "a:nth-of-type(277)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(277)", "a", "nth-of-type", "visible", "Case", "Studies", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 221, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 278 has consistent label and accessible name", "value": "a:nth-of-type(278) - visible: \"Checklists\", accessible: \"Checklists\"", "selector": "a:nth-of-type(278)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(278)", "a", "nth-of-type", "visible", "Checklists", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 222, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 279 has consistent label and accessible name", "value": "a:nth-of-type(279) - visible: \"Datasheets\", accessible: \"Datasheets\"", "selector": "a:nth-of-type(279)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(279)", "a", "nth-of-type", "visible", "Datasheets", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 223, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 280 has consistent label and accessible name", "value": "a:nth-of-type(280) - visible: \"eBooks\", accessible: \"eBooks\"", "selector": "a:nth-of-type(280)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(280)", "a", "nth-of-type", "visible", "eBooks", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 224, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 281 has consistent label and accessible name", "value": "a:nth-of-type(281) - visible: \"Events\", accessible: \"Events\"", "selector": "a:nth-of-type(281)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(281)", "a", "nth-of-type", "visible", "Events", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 225, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 282 has consistent label and accessible name", "value": "a:nth-of-type(282) - visible: \"Guides\", accessible: \"Guides\"", "selector": "a:nth-of-type(282)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(282)", "a", "nth-of-type", "visible", "Guides", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 226, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 283 has consistent label and accessible name", "value": "a:nth-of-type(283) - visible: \"Infographics\", accessible: \"Infographics\"", "selector": "a:nth-of-type(283)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(283)", "a", "nth-of-type", "visible", "Infographics", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 227, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 284 has consistent label and accessible name", "value": "a:nth-of-type(284) - visible: \"Why TigerConnect\", accessible: \"Why TigerConnect\"", "selector": "a:nth-of-type(284)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(284)", "a", "nth-of-type", "visible", "Why", "TigerConnect", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 228, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 285 has consistent label and accessible name", "value": "button:nth-of-type(285) - visible: \"Toggle child menuExpand\", accessible: \"Toggle child menuExpand\"", "selector": "button:nth-of-type(285)", "severity": "info", "elementCount": 1, "affectedSelectors": ["button:nth-of-type(285)", "button", "nth-of-type", "visible", "Toggle", "child", "menuExpand", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 229, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 286 has consistent label and accessible name", "value": "a:nth-of-type(286) - visible: \"Careers\", accessible: \"Careers\"", "selector": "a:nth-of-type(286)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(286)", "a", "nth-of-type", "visible", "Careers", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 230, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 287 has consistent label and accessible name", "value": "a:nth-of-type(287) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(287)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(287)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 231, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 288 has consistent label and accessible name", "value": "a:nth-of-type(288) - visible: \"Leadership\", accessible: \"Leadership\"", "selector": "a:nth-of-type(288)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(288)", "a", "nth-of-type", "visible", "Leadership", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 232, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 289 has consistent label and accessible name", "value": "a:nth-of-type(289) - visible: \"Media Coverage\", accessible: \"Media Coverage\"", "selector": "a:nth-of-type(289)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(289)", "a", "nth-of-type", "visible", "Media", "Coverage", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 233, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 290 has consistent label and accessible name", "value": "a:nth-of-type(290) - visible: \"Partners\", accessible: \"Partners\"", "selector": "a:nth-of-type(290)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(290)", "a", "nth-of-type", "visible", "Partners", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 234, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 291 has consistent label and accessible name", "value": "a:nth-of-type(291) - visible: \"Contact Us\", accessible: \"Contact Us\"", "selector": "a:nth-of-type(291)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(291)", "a", "nth-of-type", "visible", "Contact", "Us", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 235, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 292 has consistent label and accessible name", "value": "a:nth-of-type(292) - visible: \"Download Our App\", accessible: \"Download Our App\"", "selector": "a:nth-of-type(292)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(292)", "a", "nth-of-type", "visible", "Download", "Our", "App", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 236, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Interactive element 293 has consistent label and accessible name", "value": "a:nth-of-type(293) - visible: \"Get a Demo\", accessible: \"Get a Demo\"", "selector": "a:nth-of-type(293)", "severity": "info", "elementCount": 1, "affectedSelectors": ["a:nth-of-type(293)", "a", "nth-of-type", "visible", "Get", "Demo", "accessible"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 237, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 1 is compatible with speech recognition", "value": "aria-label: \"Secondary Navigation\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Secondary", "Navigation", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 238, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 2 is compatible with speech recognition", "value": "aria-label: \"Child menu\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Child", "menu", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 239, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 3 is compatible with speech recognition", "value": "aria-label: \"Search Icon Link\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Search", "Icon", "Link", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 240, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 4 is compatible with speech recognition", "value": "aria-label: \"Search\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Search", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 241, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 5 is compatible with speech recognition", "value": "aria-label: \"Search\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Search", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 242, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 6 is compatible with speech recognition", "value": "aria-label: \"Primary Navigation\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Primary", "Navigation", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 243, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 7 is compatible with speech recognition", "value": "aria-label: \"Child menu of Who We Serve\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Child", "menu", "of", "Who", "We", "Serve", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 244, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 8 is compatible with speech recognition", "value": "aria-label: \"Child menu of Solutions\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Child", "menu", "of", "Solutions", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 245, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 9 is compatible with speech recognition", "value": "aria-label: \"Child menu of Resources\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Child", "menu", "of", "Resources", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 246, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "code", "description": "Element 10 needs speech recognition optimization", "value": "aria-label: \"Child menu of Why TigerConnect?\" - symbols: true, numbers: false, abbreviations: false", "severity": "warning", "elementCount": 1, "affectedSelectors": ["aria-label", "Child", "menu", "of", "Why", "TigerConnect", "symbols", "true", "numbers", "false", "abbreviations"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 247, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 11 is compatible with speech recognition", "value": "aria-label: \"Open menu\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Open", "menu", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 248, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 12 is compatible with speech recognition", "value": "aria-label: \"Previous slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Previous", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 249, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.638Z"}}}, {"type": "text", "description": "Element 13 is compatible with speech recognition", "value": "aria-label: \"Go to first slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "first", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 250, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 14 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 251, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 15 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 252, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 16 is compatible with speech recognition", "value": "aria-label: \"CareConduit\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "CareConduit", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 253, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 17 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 254, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 18 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 255, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 19 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 256, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 20 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 257, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 21 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 258, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 22 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 259, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 23 is compatible with speech recognition", "value": "aria-label: \"CareConduit\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "CareConduit", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 260, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 24 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 261, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 25 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 262, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 26 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 263, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 27 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 264, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 28 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 265, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 29 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 266, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 30 is compatible with speech recognition", "value": "aria-label: \"CareConduit\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "CareConduit", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 267, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 31 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 268, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 32 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 269, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 33 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 270, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 34 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 271, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 35 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 272, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 36 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 273, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 37 is compatible with speech recognition", "value": "aria-label: \"CareConduit\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "CareConduit", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 274, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 38 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 275, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 39 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 276, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 40 is compatible with speech recognition", "value": "aria-label: \"Select a slide to show\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Select", "a", "slide", "to", "show", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 277, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 41 is compatible with speech recognition", "value": "aria-label: \"Go to slide 1\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 278, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 42 is compatible with speech recognition", "value": "aria-label: \"Go to slide 2\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 279, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 43 is compatible with speech recognition", "value": "aria-label: \"Go to slide 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 280, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 44 is compatible with speech recognition", "value": "aria-label: \"Go to slide 4\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 281, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 45 is compatible with speech recognition", "value": "aria-label: \"Go to slide 5\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 282, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 46 is compatible with speech recognition", "value": "aria-label: \"Go to slide 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 283, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 47 is compatible with speech recognition", "value": "aria-label: \"Photo Gallery Carousel\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Photo", "Gallery", "Carousel", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 284, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 48 is compatible with speech recognition", "value": "aria-label: \"Go to last slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "last", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 285, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 49 is compatible with speech recognition", "value": "aria-label: \"Next slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Next", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 286, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 50 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 287, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 51 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 288, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 52 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 289, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 53 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 290, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 54 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 291, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 55 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 292, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 56 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 293, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 57 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 294, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 58 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 295, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 59 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 296, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 60 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 297, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 61 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 298, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 62 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 299, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 63 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 300, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 64 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 301, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 65 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 302, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 66 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 303, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 67 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 304, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 68 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 305, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 69 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 306, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 70 is compatible with speech recognition", "value": "aria-label: \"5 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 307, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 71 is compatible with speech recognition", "value": "aria-label: \"6 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 308, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 72 is compatible with speech recognition", "value": "aria-label: \"1 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 309, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 73 is compatible with speech recognition", "value": "aria-label: \"2 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 310, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 74 is compatible with speech recognition", "value": "aria-label: \"3 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 311, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 75 is compatible with speech recognition", "value": "aria-label: \"4 of 6\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 312, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 76 is compatible with speech recognition", "value": "aria-label: \"\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 313, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 77 is compatible with speech recognition", "value": "aria-label: \"\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 314, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 78 is compatible with speech recognition", "value": "aria-label: \"\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 315, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 79 is compatible with speech recognition", "value": "aria-label: \"\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 316, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 80 is compatible with speech recognition", "value": "aria-label: \"\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 317, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 81 is compatible with speech recognition", "value": "aria-label: \"Previous slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Previous", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 318, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 82 is compatible with speech recognition", "value": "aria-label: \"Next slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Next", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 319, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 83 is compatible with speech recognition", "value": "aria-label: \"1 of 4\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 320, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 84 is compatible with speech recognition", "value": "aria-label: \"2 of 4\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 321, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 85 is compatible with speech recognition", "value": "aria-label: \"3 of 4\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 322, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 86 is compatible with speech recognition", "value": "aria-label: \"4 of 4\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 323, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 87 is compatible with speech recognition", "value": "aria-label: \"Select a slide to show\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Select", "a", "slide", "to", "show", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 324, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 88 is compatible with speech recognition", "value": "aria-label: \"Go to slide 1\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 325, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 89 is compatible with speech recognition", "value": "aria-label: \"Go to slide 2\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 326, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 90 is compatible with speech recognition", "value": "aria-label: \"Go to slide 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 327, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 91 is compatible with speech recognition", "value": "aria-label: \"Go to slide 4\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Go", "to", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 328, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 92 is compatible with speech recognition", "value": "aria-label: \"Previous slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Previous", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 329, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 93 is compatible with speech recognition", "value": "aria-label: \"Next slide\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Next", "slide", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 330, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 94 is compatible with speech recognition", "value": "aria-label: \"3 of 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 331, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 95 is compatible with speech recognition", "value": "aria-label: \"1 of 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 332, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 96 is compatible with speech recognition", "value": "aria-label: \"2 of 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 333, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 97 is compatible with speech recognition", "value": "aria-label: \"3 of 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 334, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 98 is compatible with speech recognition", "value": "aria-label: \"1 of 3\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "of", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 335, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 99 is compatible with speech recognition", "value": "aria-label: \"Close\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Close", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 336, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 100 is compatible with speech recognition", "value": "aria-label: \"Close menu\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Close", "menu", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 337, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}, {"type": "text", "description": "Element 101 is compatible with speech recognition", "value": "aria-label: \"Primary Mobile Navigation\" - no speech recognition issues", "severity": "info", "elementCount": 1, "affectedSelectors": ["aria-label", "Primary", "Mobile", "Navigation", "no", "speech", "recognition", "issues"], "metadata": {"scanDuration": 590, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 338, "ruleId": "WCAG-055", "ruleName": "Label in Name", "timestamp": "2025-07-13T03:29:18.639Z"}}}], "recommendations": ["Ensure accessible name for element 99 includes the visible text", "Optimize element 10 for speech recognition by avoiding symbols, spelling out numbers, and expanding abbreviations"], "executionTime": 532, "originalScore": 99, "adjustedScore": 99, "thresholdApplied": 80, "scoringDetails": "Original: 99% → Threshold: 80% → Penalty tier: 75%+ (1x) → PASSED", "penaltyTier": 1, "confidenceAdjustment": 1, "enhancedStatus": "passed"}, "timestamp": 1752377358639, "hash": "958e315e5106f6a0e5a739a37d77ce17", "accessCount": 1, "lastAccessed": 1752377358639, "size": 194057, "metadata": {"originalKey": "WCAG-055:053b13d2:add92319", "normalizedKey": "wcag-055_053b13d2_add92319", "savedAt": 1752377358643, "version": "1.1", "keyHash": "0180ff70c52099652bc4d4f4d1756f5a"}}