const { Client } = require('pg');

async function debugWCAG009() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'complyuser',
    password: 'complypassword',
    database: 'complychecker_dev'
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database');

    // Get the most recent scan
    const recentScanResult = await client.query(`
      SELECT id, target_url, scan_status, created_at 
      FROM wcag_scans 
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (recentScanResult.rows.length === 0) {
      console.log('❌ No scans found');
      return;
    }

    const recentScan = recentScanResult.rows[0];
    console.log(`\n🔍 Most recent scan: ${recentScan.id}`);
    console.log(`   URL: ${recentScan.target_url}`);
    console.log(`   Status: ${recentScan.scan_status}`);

    // Get WCAG-009 specific result
    const wcag009Result = await client.query(`
      SELECT rule_id, rule_name, status, score, evidence,
             total_element_count, failed_element_count, affected_selectors,
             fix_examples, evidence_metadata
      FROM wcag_automated_results
      WHERE scan_id = $1 AND rule_id = 'WCAG-009'
    `, [recentScan.id]);

    if (wcag009Result.rows.length === 0) {
      console.log('❌ No WCAG-009 result found');
      return;
    }

    const result = wcag009Result.rows[0];
    console.log(`\n📊 WCAG-009 Result:`);
    console.log(`   Status: ${result.status}`);
    console.log(`   Score: ${result.score}`);
    console.log(`   Total Elements: ${result.total_element_count}`);
    console.log(`   Failed Elements: ${result.failed_element_count}`);

    // Check evidence
    console.log(`\n🔍 Evidence Analysis:`);
    if (result.evidence) {
      const evidence = JSON.parse(result.evidence);
      console.log(`   Evidence items: ${evidence.length}`);
      
      evidence.forEach((item, index) => {
        console.log(`\n   Evidence ${index + 1}:`);
        console.log(`     Type: ${item.type}`);
        console.log(`     Description: ${item.description}`);
        console.log(`     Severity: ${item.severity}`);
        console.log(`     Selector: ${item.selector || 'N/A'}`);
        console.log(`     Value: ${item.value ? item.value.substring(0, 100) + '...' : 'N/A'}`);
      });
    } else {
      console.log('   ❌ No evidence found');
    }

    // Check affected selectors
    console.log(`\n🎯 Affected Selectors:`);
    if (result.affected_selectors) {
      const selectors = JSON.parse(result.affected_selectors);
      console.log(`   Count: ${selectors.length}`);
      selectors.slice(0, 10).forEach((selector, index) => {
        console.log(`   ${index + 1}. ${selector}`);
      });
      if (selectors.length > 10) {
        console.log(`   ... and ${selectors.length - 10} more`);
      }
    } else {
      console.log('   ❌ No affected selectors found');
    }

    // Check evidence metadata
    console.log(`\n📋 Evidence Metadata:`);
    if (result.evidence_metadata) {
      const metadata = JSON.parse(result.evidence_metadata);
      console.log(`   Metadata keys: ${Object.keys(metadata)}`);
      console.log(`   Total evidence items: ${metadata.totalEvidenceItems || 'N/A'}`);
    } else {
      console.log('   ❌ No evidence metadata found');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.end();
  }
}

debugWCAG009();
