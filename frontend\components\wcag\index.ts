/**
 * WCAG Components Export
 * Central export point for all WCAG components
 */

export { default as WcagScanForm } from './WcagScanForm';
export { default as WcagScanOverview } from './WcagScanOverview';
export { default as WcagScanProgress } from './WcagScanProgress';
export { default as WcagExportDialog } from './WcagExportDialog';
export { default as EnhancedEvidenceDisplay } from './EnhancedEvidenceDisplay';
export { default as EnhancedScanResults } from './EnhancedScanResults';
export { default as EnhancedFailureEvidenceDisplay } from './EnhancedFailureEvidenceDisplay';

// Re-export types for convenience
export type {
  WcagScanFormData,
  WcagScanResult,
  WcagComponentProps,
  ScanProgressInfo,
  QueueStatusInfo,
  WcagUIState,
  ScoreChartData,
  TrendChartData,
  ComplianceBreakdownData,
  // Enhanced types
  WcagEvidenceEnhanced,
  WcagScanResultEnhanced,
  WcagCheckEnhanced,
  WcagFixExample,
  WcagElementCounts,
  WcagPerformanceMetrics,
  WcagEnhancedSummary,
  EnhancedEvidenceDisplayProps,
} from '../../types/wcag';
