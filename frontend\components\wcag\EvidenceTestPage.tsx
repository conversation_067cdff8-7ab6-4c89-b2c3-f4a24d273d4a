'use client';

import React from 'react';
import { EnhancedFailureEvidenceDisplay } from './EnhancedFailureEvidenceDisplay';
import { WcagCheckEnhanced } from '@/types/wcag';

const mockChecks: WcagCheckEnhanced[] = [
  {
    ruleId: 'WCAG-001',
    ruleName: 'Image Alternative Text',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.1.1',
    level: 'A',
    status: 'failed',
    score: 0,
    maxScore: 100,
    weight: 1.0,
    automated: true,
    evidence: [
      {
        type: 'error',
        description: 'Missing alt text on images',
        value: '5 images found without alt text',
        severity: 'error',
        message: 'Images must have alternative text for screen readers',
        selector: 'img[src="example.jpg"]',
        elementCount: 5,
      },
      {
        type: 'code',
        description: 'Example of problematic code',
        value: '<img src="example.jpg">',
        severity: 'error',
        message: 'This image is missing the alt attribute',
        selector: 'img[src="example.jpg"]',
      },
      {
        type: 'info',
        description: 'Recommended fix',
        value: '<img src="example.jpg" alt="Description of the image">',
        severity: 'info',
        message: 'Add descriptive alt text to make images accessible',
      }
    ],
    recommendations: ['Add alt text to all images'],
    executionTime: 150,
    errorMessage: undefined,
    manualReviewItems: [],
  },
  {
    ruleId: 'WCAG-019',
    ruleName: 'Keyboard Focus',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.7',
    level: 'AA',
    status: 'failed',
    score: 25,
    maxScore: 100,
    weight: 1.0,
    automated: true,
    evidence: [
      {
        type: 'error',
        description: 'Elements not keyboard accessible',
        value: '3 interactive elements cannot be reached via keyboard',
        severity: 'error',
        message: 'All interactive elements must be keyboard accessible',
        elementCount: 3,
      },
      {
        type: 'measurement',
        description: 'Focus indicators missing',
        value: '8 elements lack visible focus indicators',
        severity: 'warning',
        message: 'Focus indicators help keyboard users navigate',
        elementCount: 8,
      }
    ],
    recommendations: ['Ensure all interactive elements are keyboard accessible'],
    executionTime: 200,
    errorMessage: undefined,
    manualReviewItems: [],
  }
];

export const EvidenceTestPage: React.FC = () => {
  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Evidence Display Test</h1>
      <p className="text-gray-600 mb-6">
        This page tests the evidence display component with mock data to verify it works correctly.
      </p>
      
      <EnhancedFailureEvidenceDisplay
        checks={mockChecks}
        showFixExamples={true}
        showElementCounts={true}
        showPerformanceMetrics={false}
        groupByCategory={true}
      />
    </div>
  );
};

export default EvidenceTestPage;
