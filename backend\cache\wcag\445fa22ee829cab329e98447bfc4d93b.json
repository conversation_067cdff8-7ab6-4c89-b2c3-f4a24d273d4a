{"data": [{"type": "text", "description": "Tab navigation is functional", "value": "Focus moved to: a", "severity": "info", "elementCount": 1, "affectedSelectors": ["Focus", "moved", "to", "a"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 2, "checkSpecificData": {"totalInteractiveElements": 2, "keyboardAccessibleElements": 2, "automationRate": 0.85, "checkType": "keyboard-accessibility", "interactionPatterns": true, "focusManagement": true, "evidenceIndex": 0, "ruleId": "WCAG-005", "ruleName": "Keyboard", "timestamp": "2025-07-13T03:28:11.622Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "text", "description": "Reverse tab navigation is functional", "value": "Shift+Tab successfully moves focus backwards", "severity": "info", "elementCount": 1, "affectedSelectors": ["Shift", "Tab", "successfully", "moves", "focus", "backwards"], "metadata": {"scanDuration": 0, "elementsAnalyzed": 2, "checkSpecificData": {"totalInteractiveElements": 2, "keyboardAccessibleElements": 2, "automationRate": 0.85, "checkType": "keyboard-accessibility", "interactionPatterns": true, "focusManagement": true, "evidenceIndex": 1, "ruleId": "WCAG-005", "ruleName": "Keyboard", "timestamp": "2025-07-13T03:28:11.622Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752377291622, "hash": "5ef84df14b89bb69f96af90f1635bbde", "accessCount": 1, "lastAccessed": 1752377291622, "size": 1456, "metadata": {"originalKey": "WCAG-005:WCAG-005", "normalizedKey": "wcag-005_wcag-005", "savedAt": 1752377291622, "version": "1.1", "keyHash": "e2cf2604ec14ee0244272e937a2eda35"}}