/**
 * Evidence Processor Tests
 * Tests for enhanced evidence processing utilities
 */

import { EvidenceProcessor } from '../evidence-processor';
import { WcagEvidence } from '../../types';
import { WcagEvidenceEnhanced, WcagFixExample } from '../../types-enhanced';

describe('EvidenceProcessor', () => {
  const mockEvidence: WcagEvidence[] = [
    {
      type: 'code',
      description: 'Missing alt attribute',
      value: '<img src="test.jpg">',
      selector: 'img:nth-child(1)',
      severity: 'error',
    },
    {
      type: 'info',
      description: 'Good heading structure',
      value: '<h1>Main Title</h1>',
      selector: 'h1',
      severity: 'info',
    },
  ];

  describe('processEvidence', () => {
    it('should process basic evidence without enhancement', () => {
      const result = EvidenceProcessor.processEvidence(mockEvidence);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        type: 'code',
        description: 'Missing alt attribute',
        value: '<img src="test.jpg">',
        selector: 'img:nth-child(1)',
        severity: 'error',
      });
    });

    it('should process evidence with enhanced fields', () => {
      const enhancedEvidence = [
        {
          ...mockEvidence[0],
          elementCount: 5,
          affectedSelectors: ['img', 'img[src]'],
          fixExample: {
            before: '<img src="test.jpg">',
            after: '<img src="test.jpg" alt="Test image">',
            description: 'Add alt attribute',
          },
          metadata: {
            scanDuration: 150,
            elementsAnalyzed: 10,
          },
        } as any,
      ];

      const result = EvidenceProcessor.processEvidence(enhancedEvidence);

      expect(result[0]).toMatchObject({
        elementCount: 5,
        affectedSelectors: ['img', 'img[src]'],
        fixExample: {
          before: '<img src="test.jpg">',
          after: '<img src="test.jpg" alt="Test image">',
          description: 'Add alt attribute',
        },
        metadata: {
          scanDuration: 150,
          elementsAnalyzed: 10,
        },
      });
    });

    it('should handle processing options', () => {
      const enhancedEvidence = [
        {
          ...mockEvidence[0],
          elementCount: 5,
          fixExample: { before: 'test', after: 'test', description: 'test' },
        } as any,
      ];

      const result = EvidenceProcessor.processEvidence(enhancedEvidence, {
        includeElementCounts: false,
        generateFixExamples: false,
      });

      expect(result[0]).not.toHaveProperty('elementCount');
      expect(result[0]).not.toHaveProperty('fixExample');
    });
  });

  describe('toLegacyFormat', () => {
    it('should convert enhanced evidence to legacy format', () => {
      const enhancedEvidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test evidence',
          value: '<div>test</div>',
          selector: 'div',
          severity: 'error',
          elementCount: 5,
          affectedSelectors: ['div'],
          fixExample: {
            before: '<div>',
            after: '<div role="button">',
            description: 'Add role',
          },
        },
      ];

      const result = EvidenceProcessor.toLegacyFormat(enhancedEvidence);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        type: 'code',
        description: 'Test evidence',
        value: '<div>test</div>',
        selector: 'div',
        screenshot: undefined,
        severity: 'error',
        message: undefined,
        element: undefined,
        details: undefined,
      });

      // Should not have enhanced fields
      expect(result[0]).not.toHaveProperty('elementCount');
      expect(result[0]).not.toHaveProperty('fixExample');
    });
  });

  describe('generateFixExamples', () => {
    it('should generate fix examples for WCAG-024 (HTML lang)', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'error',
          description: 'Missing lang attribute',
          value: '<html>',
          selector: 'html',
          severity: 'error',
        },
      ];

      const result = EvidenceProcessor.generateFixExamples(evidence, 'WCAG-024');

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        before: '<html>',
        description: 'Add lang attribute to html element',
        resources: expect.arrayContaining([
          'https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html',
        ]),
      });
    });

    it('should generate fix examples for WCAG-025 (Landmarks)', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'error',
          description: 'Content without landmarks',
          value: '<div>Content</div>',
          selector: 'div',
          severity: 'error',
        },
      ];

      const result = EvidenceProcessor.generateFixExamples(evidence, 'WCAG-025');

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        description: 'Wrap content in appropriate landmark elements',
        resources: expect.arrayContaining([
          'https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/',
        ]),
      });
    });

    it('should generate fix examples for WCAG-026 (Link purpose)', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'error',
          description: 'Generic link text',
          value: '<a href="/page">Click here</a>',
          selector: 'a',
          severity: 'error',
        },
      ];

      const result = EvidenceProcessor.generateFixExamples(evidence, 'WCAG-026');

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        description: 'Use descriptive link text that explains the purpose',
        resources: expect.arrayContaining([
          'https://www.w3.org/WAI/WCAG21/Understanding/link-purpose-in-context.html',
        ]),
      });
    });

    it('should return empty array for unknown rule ID', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'error',
          description: 'Test',
          value: 'test',
          severity: 'error',
        },
      ];

      const result = EvidenceProcessor.generateFixExamples(evidence, 'WCAG-999' as any);

      expect(result).toHaveLength(0);
    });

    it('should filter out non-error evidence', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'info',
          description: 'Info evidence',
          value: 'test',
          severity: 'info',
        },
        {
          type: 'error',
          description: 'Error evidence',
          value: 'test',
          severity: 'error',
        },
      ];

      const result = EvidenceProcessor.generateFixExamples(evidence, 'WCAG-024');

      expect(result).toHaveLength(1);
    });
  });

  describe('enhanceWithMetadata', () => {
    it('should enhance evidence with metadata', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test evidence',
          value: 'test',
        },
      ];

      const result = EvidenceProcessor.enhanceWithMetadata(evidence, 500, 10);

      expect(result[0]).toMatchObject({
        elementCount: 1,
        metadata: {
          scanDuration: 500,
          elementsAnalyzed: 10,
          checkSpecificData: {
            enhancedProcessing: true,
          },
        },
      });
    });

    it('should preserve existing metadata', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test evidence',
          value: 'test',
          elementCount: 5,
          metadata: {
            checkSpecificData: {
              existingData: 'preserved',
            },
          },
        },
      ];

      const result = EvidenceProcessor.enhanceWithMetadata(evidence, 500, 10);

      expect(result[0].metadata?.checkSpecificData).toMatchObject({
        existingData: 'preserved',
        enhancedProcessing: true,
      });
    });
  });

  describe('validateEnhancedEvidence', () => {
    it('should validate correct evidence', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Valid evidence',
          value: 'test value',
          elementCount: 5,
          affectedSelectors: ['div', 'span'],
        },
      ];

      const result = EvidenceProcessor.validateEnhancedEvidence(evidence);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing required fields', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: '',
          value: '',
        } as any,
      ];

      const result = EvidenceProcessor.validateEnhancedEvidence(evidence);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Evidence item 0: Missing required fields');
    });

    it('should detect invalid element count', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test',
          value: 'test',
          elementCount: -1,
        },
      ];

      const result = EvidenceProcessor.validateEnhancedEvidence(evidence);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Evidence item 0: elementCount cannot be negative');
    });

    it('should detect invalid affected selectors', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test',
          value: 'test',
          affectedSelectors: 'not an array' as any,
        },
      ];

      const result = EvidenceProcessor.validateEnhancedEvidence(evidence);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Evidence item 0: affectedSelectors must be an array');
    });

    it('should warn about incomplete fix examples', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test',
          value: 'test',
          fixExample: {
            before: 'test',
            after: '',
            description: '',
          },
        },
      ];

      const result = EvidenceProcessor.validateEnhancedEvidence(evidence);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Evidence item 0: Incomplete fix example');
    });
  });

  describe('calculatePerformanceMetrics', () => {
    it('should calculate performance metrics', () => {
      const evidence: WcagEvidenceEnhanced[] = [
        {
          type: 'code',
          description: 'Test 1',
          value: 'test',
          elementCount: 5,
        },
        {
          type: 'info',
          description: 'Test 2',
          value: 'test',
          fixExample: {
            before: 'test',
            after: 'test',
            description: 'test',
          },
        },
      ];

      const startTime = Date.now() - 1000; // 1 second ago
      const result = EvidenceProcessor.calculatePerformanceMetrics(evidence, startTime);

      expect(result.totalProcessingTime).toBeGreaterThan(900);
      expect(result.averageProcessingTime).toBeGreaterThan(450);
      expect(result.enhancedItemsCount).toBe(2);
      expect(result.enhancementRate).toBe(1.0);
    });

    it('should handle empty evidence array', () => {
      const result = EvidenceProcessor.calculatePerformanceMetrics([], Date.now());

      expect(result.averageProcessingTime).toBe(0);
      expect(result.enhancedItemsCount).toBe(0);
      expect(result.enhancementRate).toBe(0);
    });
  });
});
