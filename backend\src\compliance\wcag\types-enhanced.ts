/**
 * Enhanced WCAG Types - Backward Compatible Extensions
 * All interfaces extend existing types without breaking changes
 */

import {
  WcagEvidence,
  WcagCheckResult,
  WcagScanResult,
  WcagManualReviewItem,
  WcagScanMetadata,
  WcagScanOptions,
  WcagVersion,
  WcagLevel,
} from './types';

// ✅ ENHANCED EVIDENCE (extends existing interface)
export interface WcagEvidenceEnhanced extends WcagEvidence {
  // NEW: Enhanced fields for better reporting (all optional)
  elementCount?: number;
  affectedSelectors?: string[];
  fixExample?: WcagFixExample;
  metadata?: WcagEvidenceMetadata;
}

export interface WcagFixExample {
  before: string;
  after: string;
  description: string;
  codeExample?: string;
  resources?: string[];
}

export interface WcagEvidenceMetadata {
  scanDuration?: number;
  elementsAnalyzed?: number;
  checkSpecificData?: Record<string, string | number | boolean>;
  performanceMetrics?: {
    domParseTime: number;
    selectorQueryTime: number;
    evaluationTime: number;
  };
}

// ✅ ENHANCED CHECK RESULT (extends existing interface)
export interface WcagCheckResultEnhanced extends WcagCheckResult {
  evidence: WcagEvidenceEnhanced[];
  elementCounts?: WcagElementCounts;
  performance?: WcagPerformanceMetrics;
  checkMetadata?: WcagCheckMetadata;
}

export interface WcagElementCounts {
  total: number;
  failed: number;
  passed: number;
}

export interface WcagPerformanceMetrics {
  scanDuration: number;
  elementsAnalyzed: number;
  cacheHitRate?: number;
  memoryUsage?: number;
}

export interface WcagCheckMetadata {
  version: string;
  algorithm: string;
  confidence: number;
  additionalData?: Record<string, unknown>;
}

// ✅ ENHANCED SCAN RESULT (extends existing interface)
export interface WcagScanResultEnhanced extends WcagScanResult {
  checks: WcagCheckResultEnhanced[];
  enhancedSummary?: WcagEnhancedSummary;
  metadata: WcagScanMetadataEnhanced;
}

export interface WcagEnhancedSummary {
  totalElementsScanned: number;
  totalFailedElements: number;
  averageScanTime: number;
  cacheHitRate: number;
  performanceMetrics: {
    totalScanDuration: number;
    averageCheckDuration: number;
    slowestCheck: string;
    fastestCheck: string;
  };
}

export interface WcagScanMetadataEnhanced extends WcagScanMetadata {
  enhancedFeatures?: string[];
  apiVersion?: string;
  generatedAt?: string;
}

// ✅ ENHANCED MANUAL REVIEW (extends existing interface)
export interface WcagManualReviewEnhanced extends WcagManualReviewItem {
  relatedAutomatedEvidence?: WcagEvidenceEnhanced[];
  automatedElementCount?: number;
  suggestedFixes?: WcagFixExample[];
  priorityScore?: number;
}

// ✅ TYPE-SAFE UTILITY TYPES
export type WcagEvidenceArray = WcagEvidenceEnhanced[];
export type WcagJsonColumn<T> = T | null;
export type WcagOptionalEnhancement<T> = T | undefined;

// ✅ ENHANCED RULE ID TYPE (extends existing)
export type WcagRuleIdEnhanced =
  | 'WCAG-001'
  | 'WCAG-002'
  | 'WCAG-003'
  | 'WCAG-004'
  | 'WCAG-005'
  | 'WCAG-006'
  | 'WCAG-007'
  | 'WCAG-008'
  | 'WCAG-009'
  | 'WCAG-010'
  | 'WCAG-011'
  | 'WCAG-012'
  | 'WCAG-013'
  | 'WCAG-014'
  | 'WCAG-015'
  | 'WCAG-016'
  | 'WCAG-017'
  | 'WCAG-018'
  | 'WCAG-019'
  | 'WCAG-020'
  | 'WCAG-021'
  | 'WCAG-022'
  | 'WCAG-023'
  | 'WCAG-024'
  | 'WCAG-025'
  | 'WCAG-026'
  | 'WCAG-027'
  | 'WCAG-028'
  | 'WCAG-029'
  | 'WCAG-030';

// ✅ ENHANCED API TYPES
export interface WcagScanRequestEnhanced {
  targetUrl: string;
  scanOptions?: WcagScanOptionsEnhanced;
}

export interface WcagScanOptionsEnhanced extends WcagScanOptions {
  // NEW: Enhanced options (all optional)
  includeElementCounts?: boolean;
  generateFixExamples?: boolean;
  enablePerformanceMetrics?: boolean;
  cacheStrategy?: 'memory' | 'redis' | 'none';
  enhancedReporting?: boolean;
}

export interface WcagScanResponseEnhanced {
  success: boolean;
  data: WcagScanResultEnhanced;
  requestId: string;
  processingTime: number;
  metadata?: {
    apiVersion: string;
    enhancedFeatures: string[];
    generatedAt: string;
  };
}

// ✅ ENHANCED DATABASE MODEL TYPES
export interface WcagAutomatedResultModelEnhanced {
  // Existing fields (from current database schema)
  id: string;
  scan_id: string;
  rule_id: string;
  rule_name: string;
  category: string;
  wcag_version: string;
  success_criterion: string;
  level: string;
  status: string;
  score: number;
  max_score: number;
  weight: number;
  automated: boolean;
  evidence: WcagEvidenceEnhanced[];
  recommendations: string[];
  execution_time: number;
  error_message?: string;
  created_at: Date;
  updated_at: Date;

  // NEW: Enhanced fields (all optional for backward compatibility)
  total_element_count?: number;
  failed_element_count?: number;
  affected_selectors?: string[];
  fix_examples?: WcagFixExample[];
  evidence_metadata?: WcagEvidenceMetadata;
  scan_duration_ms?: number;
  elements_analyzed?: number;
  check_metadata?: WcagCheckMetadata;
}

// ✅ ENHANCED SCAN CONFIGURATION
export interface WcagScanConfigEnhanced {
  targetUrl: string;
  scanOptions?: WcagScanOptionsEnhanced;
  userId: string;
  requestId: string;
  enhancedFeatures?: {
    includeElementCounts: boolean;
    generateFixExamples: boolean;
    enablePerformanceMetrics: boolean;
    cacheStrategy: 'memory' | 'redis' | 'none';
    enhancedReporting: boolean;
  };
}

// ✅ ENHANCED PROCESSING UTILITIES
export interface EvidenceProcessingOptions {
  includeElementCounts: boolean;
  generateFixExamples: boolean;
  enablePerformanceMetrics: boolean;
  includeMetadata: boolean;
}

export interface FixExampleTemplate {
  ruleId: WcagRuleIdEnhanced;
  description: string;
  beforePattern: string;
  afterPattern: string;
  codeExample: string;
  resources: string[];
}

// ✅ ENHANCED VALIDATION TYPES
export interface WcagValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  enhancedFeatures: string[];
}

export interface WcagCompatibilityCheck {
  backwardCompatible: boolean;
  breakingChanges: string[];
  deprecatedFeatures: string[];
  newFeatures: string[];
}
