/**
 * Frontend WCAG Type Definitions
 * Client-side types for WCAG functionality
 * Enhanced with backward-compatible extensions
 */

// Define frontend types that match backend types
export type WcagVersion = '2.1' | '2.2' | '3.0';
export type WcagLevel = 'A' | 'AA' | 'AAA';
export type WcagCategory = 'perceivable' | 'operable' | 'understandable' | 'robust';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type WcagRiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type RiskLevel = WcagRiskLevel;
export type WcagAutomationLevel = 'automated' | 'semi-automated' | 'manual';
export type WcagCheckResult = 'pass' | 'fail' | 'inapplicable' | 'cantTell';
export type WcagCheckType = 'automated' | 'manual';

export interface WcagCategoryScores {
  perceivable: number;
  operable: number;
  understandable: number;
  robust: number;
}

export interface WcagVersionScores {
  wcag21: number;
  wcag22: number;
  wcag30: number;
}

export interface WcagScanSummary {
  totalAutomatedChecks: number;
  passedAutomatedChecks: number;
  failedAutomatedChecks: number;
  automatedScore: number;
  automationRate: number;
  manualReviewItems: number;
  categoryScores: WcagCategoryScores;
  versionScores: WcagVersionScores;
}

export interface WcagCheckEvidence {
  selector?: string;
  html?: string;
  screenshot?: string;
  description: string;
}

export interface WcagCheck {
  checkId: string;
  ruleId: string;
  title: string;
  description: string;
  category: WcagCategory;
  level: WcagLevel;
  wcagVersion: WcagVersion;
  result: WcagCheckResult;
  automationLevel: WcagAutomationLevel;
  evidence?: WcagCheckEvidence[];
  impact?: 'minor' | 'moderate' | 'serious' | 'critical';
  tags?: string[];
}

export interface WcagRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: WcagCategory;
  estimatedEffort: string;
  resources?: string[];
}

export interface WcagScanMetadata {
  scanId: string;
  userId: string;
  requestId: string;
  startTime: Date;
  duration: number;
  userAgent: string;
  viewport: { width: number; height: number };
  environment: string;
  version: string;
}

export interface WcagScanOptions {
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  enableManualReview?: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;
  timeout?: number;
  // Aliases used in the frontend
  contrast?: boolean; // Alias for enableContrastAnalysis
  keyboard?: boolean; // Alias for enableKeyboardTesting
  focus?: boolean; // Alias for enableFocusAnalysis
  semantic?: boolean; // Alias for enableSemanticValidation
  pageLimit?: number; // Alias for maxPages
}

export interface WcagScanConfig {
  targetUrl: string;
  scanOptions: WcagScanOptions;
  userId: string;
  requestId: string;
}

export interface WcagScanResult {
  scanId: string;
  targetUrl: string;
  status: ScanStatus;
  overallScore: number;
  levelAchieved: WcagLevel;
  riskLevel: WcagRiskLevel;
  summary: WcagScanSummary;
  metadata: WcagScanMetadata;
  checks: WcagCheck[];
  recommendations: WcagRecommendation[];
  // Additional properties used in the frontend
  url: string; // Alias for targetUrl for backward compatibility
  scanTimestamp: string; // ISO string of when the scan was initiated
  completionTimestamp?: string; // When the scan was completed
  wcagVersion: WcagVersion | 'all'; // WCAG version used for the scan
  complianceLevel: WcagLevel; // Compliance level (A, AA, AAA)
  scanOptions: WcagScanOptions; // Options used for the scan
  manualReviewData?: Array<{
    id: string;
    ruleId: string;
    selector: string;
    description: string;
    automatedFindings: string;
    reviewRequired: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: number;
    reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
    reviewerNotes?: string;
    reviewAssessment?: string;
    reviewedAt?: Date;
    reviewedBy?: string;
  }>;
}

// Frontend-specific types
export interface WcagScanFormData {
  targetUrl: string;
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  enableManualReview?: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;

  // Enhanced Phase 1-3 Options
  enableBrowserPool?: boolean;
  enableSmartCache?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableCMSDetection?: boolean;
  enableEcommerceAnalysis?: boolean;
  enableFrameworkDetection?: boolean;
  enableMediaAnalysis?: boolean;
  enableVPSOptimization?: boolean;
  timeout?: number;

  // Advanced Options (from orphaned component)
  includeHiddenElements?: boolean;
  enableScreenshots?: boolean;
  customRules?: string[];
  excludeRules?: string[];

  // Authentication Options (from orphaned component)
  requiresAuth?: boolean;
  authType?: 'none' | 'basic' | 'form' | 'oauth';
  authCredentials?: {
    username: string;
    password: string;
  };
  customHeaders?: { [key: string]: string };
}

export interface WcagDashboardState {
  currentScan?: WcagScanResult;
  recentScans: WcagScanResult[];
  isScanning: boolean;
  scanProgress: number;
  error?: string;
  selectedScanId?: string;
}

export interface WcagComponentProps {
  scanResult?: WcagScanResult;
  onScanStart?: (config: WcagScanConfig) => void;
  onExport?: (scanId: string, format: 'pdf' | 'json' | 'csv') => void;
  onRescan?: (scanId: string) => void;
  onDelete?: (scanId: string) => void;
}

export interface ScanProgressInfo {
  scanId: string;
  status: ScanStatus;
  currentCheck?: string;
  completedChecks: number;
  totalChecks: number;
  progress: number;
  estimatedTimeRemaining?: number;
  checksCompleted?: number;
}

export interface QueueStatusInfo {
  totalQueued: number;
  currentlyProcessing: number;
  maxConcurrent: number;
  averageWaitTime: number;
  estimatedWaitTime: number;
  position?: number;
  pending?: number;
  running?: number;
  completed?: number;
}

// UI State types
export interface WcagUIState {
  activeTab: 'overview' | 'details' | 'recommendations' | 'history';
  selectedCategory?: WcagCategory;
  selectedLevel?: WcagLevel;
  showOnlyFailed: boolean;
  sortBy: 'ruleId' | 'score' | 'category' | 'level';
  sortOrder: 'asc' | 'desc';
  pageSize: number;
  currentPage: number;
}

// Chart data types
export interface ScoreChartData {
  category: string;
  score: number;
  maxScore: number;
  color: string;
}

export interface TrendChartData {
  date: string;
  score: number;
  level: string;
}

export interface ComplianceBreakdownData {
  level: WcagLevel;
  passed: number;
  failed: number;
  total: number;
  percentage: number;
}

// ✅ ENHANCED TYPES - Backward Compatible Extensions

export interface WcagFixExample {
  before: string;
  after: string;
  description: string;
  codeExample?: string;
  resources?: string[];
}

export interface WcagEvidenceMetadata {
  scanDuration?: number;
  elementsAnalyzed?: number;
  checkSpecificData?: Record<string, string | number | boolean>;
  performanceMetrics?: {
    domParseTime: number;
    selectorQueryTime: number;
    evaluationTime: number;
  };
}

export interface WcagEvidenceEnhanced extends WcagCheckEvidence {
  type: 'text' | 'image' | 'code' | 'measurement' | 'interaction' | 'info' | 'warning' | 'error';
  value: string;
  severity?: 'info' | 'warning' | 'error' | 'critical';
  message?: string;
  element?: string;
  details?: string;
  // Enhanced fields (all optional for backward compatibility)
  elementCount?: number;
  affectedSelectors?: string[];
  fixExample?: WcagFixExample;
  metadata?: WcagEvidenceMetadata;
}

export interface WcagElementCounts {
  total: number;
  failed: number;
  passed: number;
}

export interface WcagPerformanceMetrics {
  scanDuration: number;
  elementsAnalyzed: number;
  cacheHitRate?: number;
  memoryUsage?: number;
}

export interface WcagCheckMetadata {
  version: string;
  algorithm: string;
  confidence: number;
  additionalData?: Record<string, unknown>;
}

export interface WcagCheckEnhanced extends WcagCheck {
  evidence: WcagEvidenceEnhanced[];
  elementCounts?: WcagElementCounts;
  performance?: WcagPerformanceMetrics;
  checkMetadata?: WcagCheckMetadata;
}

export interface WcagEnhancedSummary {
  totalElementsScanned: number;
  totalFailedElements: number;
  averageScanTime: number;
  cacheHitRate: number;
  performanceMetrics: {
    totalScanDuration: number;
    averageCheckDuration: number;
    slowestCheck: string;
    fastestCheck: string;
  };
}

export interface WcagScanResultEnhanced extends WcagScanResult {
  checks: WcagCheckEnhanced[];
  enhancedSummary?: WcagEnhancedSummary;
  metadata: WcagScanMetadata & {
    enhancedFeatures?: string[];
    apiVersion?: string;
    generatedAt?: string;
  };
}

export interface WcagScanOptionsEnhanced extends WcagScanFormData {
  // Enhanced options (all optional)
  includeElementCounts?: boolean;
  generateFixExamples?: boolean;
  enablePerformanceMetrics?: boolean;
  cacheStrategy?: 'memory' | 'redis' | 'none';
  enhancedReporting?: boolean;
}

// Enhanced UI component props
export interface EnhancedEvidenceDisplayProps {
  evidence: WcagEvidenceEnhanced[];
  showFixExamples?: boolean;
  showElementCounts?: boolean;
  showPerformanceMetrics?: boolean;
}

export interface FixExampleDisplayProps {
  fixExample: WcagFixExample;
  className?: string;
}

export interface ElementCountsDisplayProps {
  elementCounts: WcagElementCounts;
  className?: string;
}

export interface PerformanceMetricsDisplayProps {
  performance: WcagPerformanceMetrics;
  className?: string;
}

// Enhanced API response types
export interface WcagScanResponseEnhanced {
  success: boolean;
  data: WcagScanResultEnhanced;
  requestId: string;
  processingTime: number;
  metadata?: {
    apiVersion: string;
    enhancedFeatures: string[];
    generatedAt: string;
  };
}

// Enhanced form data types
export interface WcagEnhancedFormData extends WcagScanFormData {
  includeElementCounts: boolean;
  generateFixExamples: boolean;
  enablePerformanceMetrics: boolean;
  cacheStrategy: 'memory' | 'redis' | 'none';
  enhancedReporting: boolean;
}

// Enhanced validation types
export interface WcagValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  enhancedFeatures: string[];
}

// Enhanced utility types
export type WcagEvidenceArray = WcagEvidenceEnhanced[];
export type WcagOptionalEnhancement<T> = T | undefined;

// Enhanced rule ID type
export type WcagRuleIdEnhanced =
  | 'WCAG-001' | 'WCAG-002' | 'WCAG-003' | 'WCAG-004' | 'WCAG-005'
  | 'WCAG-006' | 'WCAG-007' | 'WCAG-008' | 'WCAG-009' | 'WCAG-010'
  | 'WCAG-011' | 'WCAG-012' | 'WCAG-013' | 'WCAG-014' | 'WCAG-015'
  | 'WCAG-016' | 'WCAG-017' | 'WCAG-018' | 'WCAG-019' | 'WCAG-020'
  | 'WCAG-021' | 'WCAG-022' | 'WCAG-023' | 'WCAG-024' | 'WCAG-025'
  | 'WCAG-026' | 'WCAG-027' | 'WCAG-028' | 'WCAG-029' | 'WCAG-030';
