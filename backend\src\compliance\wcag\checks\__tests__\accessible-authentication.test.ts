/**
 * Tests for WCAG-022: Accessible Authentication (Minimum)
 */

import { AccessibleAuthenticationCheck } from '../accessible-authentication';
import { createMockPage, createMockConfig } from '../../utils/__tests__/test-helpers';

describe('AccessibleAuthenticationCheck', () => {
  let check: AccessibleAuthenticationCheck;
  let mockPage: any;
  let mockConfig: any;

  beforeEach(() => {
    check = new AccessibleAuthenticationCheck();
    mockPage = createMockPage();
    mockConfig = createMockConfig();
  });

  describe('Basic Authentication Detection', () => {
    it('should detect password fields', async () => {
      mockPage.evaluate.mockResolvedValue([
        {
          selector: 'input[type="password"]',
          type: 'password',
          inputType: 'password',
          hasAlternative: false,
          requiresMemory: true,
          requiresCognitive: false,
          description: 'Password input field',
        },
      ]);

      const result = await check.performCheck(mockConfig);

      expect(result.score).toBeGreaterThan(0);
      expect(result.evidence).toContainEqual(
        expect.objectContaining({
          type: 'interaction',
          description: 'Authentication element: password',
        }),
      );
    });

    it('should detect CAPTCHA without alternatives', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: '[class*="captcha"]',
            type: 'captcha',
            inputType: 'text',
            hasAlternative: false,
            requiresMemory: false,
            requiresCognitive: true,
            description: 'CAPTCHA verification',
          },
        ])
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.issues).toContain(
        'CAPTCHA found without accessible alternative: [class*="captcha"]',
      );
      expect(result.recommendations).toContain(
        'Provide alternative authentication method for CAPTCHA',
      );
    });

    it('should detect security questions without alternatives', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: '[id*="security-question"]',
            type: 'security-question',
            inputType: 'text',
            hasAlternative: false,
            requiresMemory: true,
            requiresCognitive: false,
            description: 'Security question field',
          },
        ])
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.issues).toContain(
        'Security question requires memory without alternative: [id*="security-question"]',
      );
      expect(result.recommendations).toContain(
        'Provide alternative authentication that does not rely on memory',
      );
    });
  });

  describe('Authentication Flow Analysis', () => {
    it('should analyze high cognitive load flows', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: 'input[type="password"]',
            type: 'password',
            inputType: 'password',
            hasAlternative: false,
            requiresMemory: true,
            requiresCognitive: false,
            description: 'Password field',
          },
        ])
        .mockResolvedValueOnce([
          {
            steps: [
              {
                selector: 'input[type="password"]',
                type: 'password',
                requiresMemory: true,
              },
            ],
            hasAccessibleAlternative: false,
            cognitiveLoad: 'high',
            memoryRequirements: ['Password recall'],
            recommendations: ['Provide password manager support'],
          },
        ])
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.manualReviewItems).toContainEqual(
        expect.objectContaining({
          description: 'High cognitive load authentication flow',
          priority: 'high',
        }),
      );
    });
  });

  describe('Cognitive Function Tests', () => {
    it('should detect cognitive tests without alternatives', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([]) // authElements
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([
          {
            selector: '[class*="puzzle"]',
            type: 'puzzle',
            description: 'Visual puzzle challenge',
            hasAlternative: false,
          },
        ])
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.issues).toContain(
        'Cognitive function test without alternative: Visual puzzle challenge',
      );
      expect(result.recommendations).toContain(
        'Provide authentication method that does not require cognitive function test',
      );
    });
  });

  describe('Biometric Authentication', () => {
    it('should create manual review for biometric authentication', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: '[class*="fingerprint"]',
            type: 'biometric',
            inputType: 'button',
            hasAlternative: true,
            requiresMemory: false,
            requiresCognitive: false,
            description: 'Fingerprint authentication',
          },
        ])
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.manualReviewItems).toContainEqual(
        expect.objectContaining({
          description: 'Biometric authentication accessibility',
          reviewRequired: expect.stringContaining(
            'biometric authentication has accessible alternatives',
          ),
        }),
      );
    });
  });

  describe('No Authentication Scenarios', () => {
    it('should handle pages without authentication', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([]) // authElements
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.score).toBe(100);
      expect(result.evidence).toContainEqual(
        expect.objectContaining({
          description: 'No authentication mechanisms detected on this page',
          severity: 'info',
        }),
      );
    });
  });

  describe('Alternative Authentication Methods', () => {
    it('should detect and report alternative methods', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: 'input[type="password"]',
            type: 'password',
            inputType: 'password',
            hasAlternative: true,
            requiresMemory: true,
            requiresCognitive: false,
            description: 'Password field',
          },
        ])
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce(['[class*="social-login"]', '[class*="magic-link"]']); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.evidence).toContainEqual(
        expect.objectContaining({
          description: 'Accessible authentication alternatives found',
          value: '2 alternative methods detected',
        }),
      );
    });
  });

  describe('Manual Review Items', () => {
    it('should generate comprehensive manual review items', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: 'input[type="password"]',
            type: 'password',
            inputType: 'password',
            hasAlternative: false,
            requiresMemory: true,
            requiresCognitive: false,
            description: 'Password field',
          },
        ])
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      expect(result.manualReviewItems).toContainEqual(
        expect.objectContaining({
          description: 'Authentication mechanism accessibility',
          reviewRequired: expect.stringContaining(
            'does not rely solely on cognitive function tests',
          ),
          priority: 'medium',
          estimatedTime: 20,
        }),
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle evaluation errors gracefully', async () => {
      mockPage.evaluate.mockRejectedValue(new Error('Page evaluation failed'));

      const result = await check.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('Failed to analyze authentication accessibility');
      expect(result.evidence).toContainEqual(
        expect.objectContaining({
          type: 'error',
          description: 'Error during authentication analysis',
        }),
      );
    });
  });

  describe('Scoring Logic', () => {
    it('should calculate correct scores for mixed scenarios', async () => {
      mockPage.evaluate
        .mockResolvedValueOnce([
          {
            selector: 'input[type="password"]',
            type: 'password',
            hasAlternative: true,
            requiresMemory: true,
            requiresCognitive: false,
          },
          {
            selector: '[class*="captcha"]',
            type: 'captcha',
            hasAlternative: false,
            requiresMemory: false,
            requiresCognitive: true,
          },
        ])
        .mockResolvedValueOnce([]) // authFlows
        .mockResolvedValueOnce([]) // cognitiveTests
        .mockResolvedValueOnce([]); // alternatives

      const result = await check.performCheck(mockConfig);

      // Should have partial score - password passes, CAPTCHA fails
      expect(result.score).toBeGreaterThan(0);
      expect(result.score).toBeLessThan(100);
      expect(result.issues.length).toBeGreaterThan(0);
    });
  });
});
