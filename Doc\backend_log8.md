Microsoft Windows [Version 10.0.19045.6093]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\Web projects\Comply Checker\backend"

D:\Web projects\Comply Checker\backend>npm run dev

> backend@0.1.0 dev
> cross-env DEBUG=keycloak* nodemon --exec ts-node -r tsconfig-paths/register src/index.ts

[nodemon] 3.1.10
[nodemon] to restart at any time, enter `rs`
[nodemon] watching path(s): src\**\* ..\lib\**\*
[nodemon] watching extensions: ts,json
[nodemon] starting `ts-node -r tsconfig-paths/register src/index.ts`
[ENV_DEBUG] NODE_ENV: development
[ENV_DEBUG] isTestEnv: false
[ENV_DEBUG] process.env.POSTGRES_HOST (raw): localhost
[ENV_DEBUG] parsedEnv.POSTGRES_HOST (from Zod): localhost
[ENV_DEBUG] dbHost chosen: localhost
[ENV_DEBUG] Constructed DATABASE_URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
2025-07-12T07:47:37.203Z [INFO] - 🗄️ Smart cache initialized - {"maxSize":200,"maxEntries":1000,"de faultTTL":14400000,"defaultTTLMinutes":240}
2025-07-12T07:47:37.892Z [DEBUG] - Readability analysis library not available
2025-07-12T07:47:37.901Z [DEBUG] - Language detection library not available
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🚀 Browser pool initialized: 2 max browsers, 3 max pages per browser
2025-07-12T07:47:51.403Z [DEBUG] - Initialized 5 core accessibility patterns
2025-07-12T07:47:51.404Z [INFO] - 📚 Accessibility Pattern Library initialized - {"totalPatterns":5,"enabledCategories":6,"strictMode":false}
2025-07-12T07:47:51.405Z [INFO] - 🤖 AI-Powered Semantic Validator initialized - {"nlpAnalysis":true,"patternLibrary":true,"contextualAnalysis":true}
2025-07-12T07:47:51.406Z [INFO] - 📊 Enhanced Content Quality Analyzer initialized - {"readabilityAnalysis":true,"semanticAnalysis":true,"languageDetection":true}
2025-07-12T07:47:51.407Z [INFO] - ⚡ Modern Framework Optimizer initialized - {"svelte":true,"solidjs":true,"qwik":true,"buildTools":true}
2025-07-12T07:47:51.410Z [INFO] - 🏗️ Component Library Detector initialized - {"materialUI":true,"a ntDesign":true,"chakraUI":true,"deepAnalysis":true}
2025-07-12T07:47:51.414Z [INFO] - EnhancedColorAnalyzer initialized - {"getContrastLibrary":true,"colorJSLibrary":true,"enhancedAccuracy":true}
2025-07-12T07:47:51.415Z [INFO] - 📄 Headless CMS Detector initialized - {"strapi":true,"contentful":true,"sanity":true,"jamstack":true}
2025-07-12T07:47:51.416Z [INFO] - 🔧 Utility Integration Manager initialized
2025-07-12T07:47:51.419Z [INFO] - 🚀 Enhanced Performance Monitor initialized - {"trendAnalysis":true,"autoOptimization":true,"resourceCorrelation":true,"realTimeAlerts":true}
2025-07-12T07:47:51.420Z [INFO] - 🔗 Performance Integration Bridge initialized
2025-07-12T07:47:51.421Z [INFO] - 📊 Real-Time Monitoring Dashboard initialized
2025-07-12T07:47:51.422Z [INFO] - 🔮 Initialized 4 predictive models
2025-07-12T07:47:51.423Z [INFO] - 🔮 Predictive Performance Analytics initialized
2025-07-12T07:47:51.424Z [INFO] - 🤖 Automated Optimization Engine initialized
2025-07-12T07:47:51.425Z [INFO] - 🔌 Dashboard WebSocket Service initialized
2025-07-12T07:47:51.426Z [INFO] - 🎛️ Performance Automation Controller initialized
2025-07-12T07:47:51.566Z [INFO] - 🌐 Browser pool initialized - {"maxBrowsers":1,"maxPagesPerBrowser":3,"memoryThreshold":2048}
2025-07-12T07:47:51.570Z [INFO] - Detected VPS profile: xlarge - {"cpu":4,"memory":12,"recommendedLimits":{"maxConcurrentScans":12,"maxBrowserInstances":4,"maxPagesPerBrowser":4,"maxCacheSize":400,"maxMemoryUsage":12800,"scanTimeout":60000}}
2025-07-12T07:47:51.573Z [DEBUG] - Worker processes disabled - {"enableWorkerProcesses":false,"isMaster":true,"nodeEnv":"development"}
2025-07-12T07:47:51.575Z [DEBUG] - Connection pools configured - {"maxSockets":50,"keepAlive":true}
[INFO] Registering test-auth routes (development only)
🚀 Starting Comply Checker Backend in development mode
📋 Port: 3001
📋 Setting up API routes...
📋 Attempting to start server on port 3001...
✅ Server started successfully!
🌐 Server running on: http://localhost:3001
🏥 Health check: http://localhost:3001/health
🧪 HIPAA Analysis: http://localhost:3001/api/v1/compliance/scan
📋 Environment: development
🚀 Ready to accept HIPAA compliance requests!
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
2025-07-12T07:48:43.988Z [INFO] - 🔓 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Development mode: Mock authentication applied
2025-07-12T07:48:43.990Z [INFO] - 🚀 [14418698-1780-4e6f-b5c6-9a84c4295ed0] WCAG scan request received
2025-07-12T07:48:43.991Z [INFO] - 📋 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Request headers: - {"content-type":"application/json","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","authorization":"Bearer [REDACTED]"}
2025-07-12T07:48:43.993Z [INFO] - 👤 [14418698-1780-4e6f-b5c6-9a84c4295ed0] User context: - {"userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","email":"<EMAIL>","permissions":["wcag:scan","wcag:view","wcag:export"]}
2025-07-12T07:48:43.994Z [INFO] - 🎯 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Target URL: https://tigerconnect.com/
2025-07-12T07:48:43.995Z [INFO] - ⚙️ [14418698-1780-4e6f-b5c6-9a84c4295ed0] Scan options: - {"enableContrastAnalysis":true,"enableKeyboardTesting":true,"enableFocusAnalysis":true,"enableSemanticValidation":true,"enableManualReview":true,"wcagVersion":"all","level":"AAA","maxPages":5,"timeout":60000}
2025-07-12T07:48:43.996Z [INFO] - ✅ [14418698-1780-4e6f-b5c6-9a84c4295ed0] Target URL validation passed
2025-07-12T07:48:43.997Z [INFO] - 🔧 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Scan configuration created: - {"targetUrl":"https://tigerconnect.com/","userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","level":"AAA","timeout":60000}
2025-07-12T07:48:43.999Z [INFO] - 🚀 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Starting comprehensive WCAG scan with orchestrator
2025-07-12T07:48:44.005Z [INFO] - 📝 Creating WCAG scan record...
2025-07-12T07:48:44.006Z [INFO] - 🆔 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Scan initiated with ID: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.007Z [INFO] - 📊 [14418698-1780-4e6f-b5c6-9a84c4295ed0] Scan started successfully: - {"scanId":"52f3c859-aeff-4274-8d32-62129992f841","status":"pending","targetUrl":"https://tigerconnect.com/"}
2025-07-12T07:48:44.008Z [INFO] - ✅ [14418698-1780-4e6f-b5c6-9a84c4295ed0] WCAG scan initiated successfully in 18ms
2025-07-12T07:48:44.020Z [INFO] - ✅ WCAG scan record created with ID: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.021Z [INFO] - ✅ WCAG scan record created with ID: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.023Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> pending
2025-07-12T07:48:44.030Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> pending
2025-07-12T07:48:44.031Z [INFO] - 🚀 Initialized scan 52f3c859-aeff-4274-8d32-62129992f841 with 66 rules
2025-07-12T07:48:44.032Z [DEBUG] - 📊 Started performance monitoring for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.035Z [DEBUG] - 📊 Started performance monitoring for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.036Z [DEBUG] - 📊 Started performance monitoring for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.037Z [DEBUG] - 🔗 Integrated monitoring started for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:44.038Z [DEBUG] - 📊 Registered active scan: 52f3c859-aeff-4274-8d32-62129992f841 (total: 1)
2025-07-12T07:48:44.039Z [INFO] - 🎛️ Starting Performance Automation System...
2025-07-12T07:48:44.042Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T07:48:44.043Z [INFO] - 📊 Real-Time Monitoring Dashboard started
2025-07-12T07:48:44.045Z [INFO] - ✅ Dashboard monitoring started
2025-07-12T07:48:44.051Z [INFO] - 🔌 Dashboard WebSocket Service started on port 8081
2025-07-12T07:48:44.053Z [INFO] - ✅ WebSocket service started
2025-07-12T07:48:44.054Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-12T07:48:44.055Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-12T07:48:44.057Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-12T07:48:44.057Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-12T07:48:44.058Z [INFO] - 🔮 Predictive Performance Analytics started
2025-07-12T07:48:44.059Z [INFO] - ✅ Predictive analytics started
2025-07-12T07:48:44.060Z [INFO] - 🎛️ Performance Automation System fully operational
2025-07-12T07:48:44.064Z [INFO] - 🎛️ Started Performance Automation Controller for scan monitoring
2025-07-12T07:48:44.066Z [DEBUG] - 🔍 Getting page for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:45.832Z [INFO] - ✅ Created new browser: browser-1752306525832-58fqzpymn
2025-07-12T07:48:46.235Z [DEBUG] - 🆕 Created new page for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:46.235Z [DEBUG] - ✅ URL validation passed for: https://tigerconnect.com/
2025-07-12T07:48:46.238Z [DEBUG] - 🔍 Checking connectivity for: https://tigerconnect.com/
2025-07-12T07:48:47.593Z [DEBUG] - ✅ Connectivity check passed for: https://tigerconnect.com/ (status: 200)
2025-07-12T07:48:47.594Z [INFO] - 🔗 Navigating to: https://tigerconnect.com/ (timeout: 60000ms)
2025-07-12T07:48:49.059Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T07:48:51.593Z [DEBUG] - 📊 Memory usage: 456MB / 486MB (growth: -431.8MB/min)
2025-07-12T07:48:51.593Z [WARN] - ⚠️ High memory usage detected: 456MB
2025-07-12T07:48:51.597Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-12T07:48:51.598Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-12T07:48:51.599Z [INFO] - ✅ Proactive cleanup completed
2025-07-12T07:48:52.703Z [DEBUG] - 📝 Registered session: 52f3c859-aeff-4274-8d32-62129992f841:https://tigerconnect.com/
2025-07-12T07:48:52.704Z [DEBUG] - 📝 Registered session for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:48:52.706Z [INFO] - ✅ Navigation completed
2025-07-12T07:48:52.709Z [INFO] - 🚀 Starting Phase 2 analysis: Website type detection and optimization
2025-07-12T07:48:52.710Z [DEBUG] - 🔍 Starting CMS platform detection
2025-07-12T07:48:52.714Z [DEBUG] - 🛒 Starting e-commerce accessibility analysis
2025-07-12T07:48:52.716Z [DEBUG] - 🎬 Starting media accessibility analysis
2025-07-12T07:48:52.720Z [DEBUG] - ⚛️ Starting framework-specific accessibility analysis
2025-07-12T07:48:52.741Z [WARN] - ⚠️ Phase 2 analysis encountered errors, continuing with standard analysis - {"error":"CMS detection failed - no results returned"}
2025-07-12T07:48:52.743Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:52.750Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:52.751Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 0% (0/66)
2025-07-12T07:48:52.752Z [INFO] - 🔍 Executing 66 WCAG checks...
2025-07-12T07:48:52.754Z [INFO] - 🔥 Pre-warming cache for WCAG scan...
2025-07-12T07:48:52.755Z [INFO] - 🔥 Pre-warming cache for WCAG scan: https://tigerconnect.com/
2025-07-12T07:48:52.756Z [INFO] - 🔥 Warming up cache with 1 URLs
2025-07-12T07:48:52.797Z [DEBUG] - 💾 Cache saved to file: bebee4be960508b81bf52717657b8895.json (key: https://tigerconnect.com/:ce50...)
2025-07-12T07:48:52.798Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:ce50a093 (62 bytes)
2025-07-12T07:48:52.805Z [DEBUG] - 💾 Cache saved to file: 95f19ad0cce33ee263801de200e8a74a.json (key: https://tigerconnect.com/:a43c...)
2025-07-12T07:48:52.806Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a43c1b0a (61 bytes)
2025-07-12T07:48:52.814Z [DEBUG] - 💾 Cache saved to file: 534c19df0f92f221836ddb58a770c182.json (key: https://tigerconnect.com/:0cc1...)
2025-07-12T07:48:52.814Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:0cc175b9 (57 bytes)
2025-07-12T07:48:52.820Z [DEBUG] - 💾 Cache saved to file: e5d81c1831723c1a497c41a53662ed26.json (key: https://tigerconnect.com/:b798...)
2025-07-12T07:48:52.820Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:b798abe6 (59 bytes)
2025-07-12T07:48:52.825Z [DEBUG] - 💾 Cache saved to file: 663792f6de7c4bd08e95ed6047b0752f.json (key: https://tigerconnect.com/:3fcd...)
2025-07-12T07:48:52.826Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3fcdb73d (60 bytes)
2025-07-12T07:48:52.832Z [DEBUG] - 💾 Cache saved to file: 0a43342a413174a1d2603306ccf44deb.json (key: https://tigerconnect.com/:d72e...)
2025-07-12T07:48:52.833Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d72e5ee9 (59 bytes)
2025-07-12T07:48:52.835Z [DEBUG] - 💾 Cache saved to file: 35c8746de252dfe5065a332b43442ab3.json (key: https://tigerconnect.com/:fad5...)
2025-07-12T07:48:52.836Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:fad58de7 (60 bytes)
2025-07-12T07:48:52.839Z [DEBUG] - 💾 Cache saved to file: f1f8620c79b22a3449d8feaceb632cfc.json (key: https://tigerconnect.com/:099f...)
2025-07-12T07:48:52.842Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:099fb995 (62 bytes)
2025-07-12T07:48:52.847Z [DEBUG] - 💾 Cache saved to file: 1a8ca4ce0660cb82f14b79daae26c351.json (key: https://tigerconnect.com/:251d...)
2025-07-12T07:48:52.847Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:251d1646 (62 bytes)
2025-07-12T07:48:52.850Z [DEBUG] - 💾 Cache saved to file: a5521ee36e71b2c9433c715f93408670.json (key: https://tigerconnect.com/:3f38...)
2025-07-12T07:48:52.852Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3f3852a9 (73 bytes)
2025-07-12T07:48:52.856Z [DEBUG] - 💾 Cache saved to file: 712629e01944fb3e69bd1e0f8c42ff5a.json (key: https://tigerconnect.com/:14e1...)
2025-07-12T07:48:52.857Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:14e1a17f (71 bytes)
2025-07-12T07:48:52.861Z [DEBUG] - 💾 Cache saved to file: bd8bd7fbd915dbba54e7f054fc53f744.json (key: https://tigerconnect.com/:2e71...)
2025-07-12T07:48:52.862Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:2e716448 (68 bytes)
2025-07-12T07:48:52.865Z [DEBUG] - 💾 Cache saved to file: ecec730c5f49a267feabefa9cd405473.json (key: https://tigerconnect.com/:cacf...)
2025-07-12T07:48:52.865Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:cacf55f1 (61 bytes)
2025-07-12T07:48:52.869Z [DEBUG] - 💾 Cache saved to file: 28d51e63f13fde14df4973c59c70fc6c.json (key: https://tigerconnect.com/:af04...)
2025-07-12T07:48:52.871Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:af04abc2 (66 bytes)
2025-07-12T07:48:52.874Z [DEBUG] - 💾 Cache saved to file: afcddacf2eb740820a22ed45014963ec.json (key: https://tigerconnect.com/:346b...)
2025-07-12T07:48:52.874Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:346b81a3 (58 bytes)
2025-07-12T07:48:52.878Z [DEBUG] - 💾 Cache saved to file: e5f6dfa7d9f997dcad6a7e1fdca8e5b5.json (key: https://tigerconnect.com/:490b...)
2025-07-12T07:48:52.878Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:490b2834 (58 bytes)
2025-07-12T07:48:52.881Z [DEBUG] - 💾 Cache saved to file: e1fadcb2de37bd42402f1408934ee8fa.json (key: https://tigerconnect.com/:6f20...)
2025-07-12T07:48:52.881Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:6f207f8b (58 bytes)
2025-07-12T07:48:52.884Z [DEBUG] - 💾 Cache saved to file: 4b977d1289dde40c75e5b724b0e5ddab.json (key: https://tigerconnect.com/:ce1b...)
2025-07-12T07:48:52.885Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:ce1b1e8c (58 bytes)
2025-07-12T07:48:52.891Z [DEBUG] - 💾 Cache saved to file: 8dd69209d8df0c5749198f87713c0c77.json (key: https://tigerconnect.com/:7723...)
2025-07-12T07:48:52.892Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:77230e94 (58 bytes)
2025-07-12T07:48:52.895Z [DEBUG] - 💾 Cache saved to file: bf790dbb37199cfaa73dde1b45b28211.json (key: https://tigerconnect.com/:d854...)
2025-07-12T07:48:52.896Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d8544ba1 (58 bytes)
2025-07-12T07:48:52.899Z [DEBUG] - 💾 Cache saved to file: b98a00e39bad8aaebc681168fb4179bd.json (key: https://tigerconnect.com/:73d5...)
2025-07-12T07:48:52.899Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:73d5342e (63 bytes)
2025-07-12T07:48:52.903Z [DEBUG] - 💾 Cache saved to file: b123149d68166737fedff453406f1877.json (key: https://tigerconnect.com/:92a2...)
2025-07-12T07:48:52.904Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:92a2b5cb (63 bytes)
2025-07-12T07:48:52.907Z [DEBUG] - 💾 Cache saved to file: 7b4bdc44c78075314d0a544b17c078b1.json (key: https://tigerconnect.com/:421b...)
2025-07-12T07:48:52.908Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:421b47ff (61 bytes)
2025-07-12T07:48:52.911Z [DEBUG] - 💾 Cache saved to file: c3a1426057f2930eac9f74423b62d8f0.json (key: https://tigerconnect.com/:a5ca...)
2025-07-12T07:48:52.912Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a5ca0b58 (61 bytes)
2025-07-12T07:48:52.915Z [DEBUG] - 💾 Cache saved to file: c4cf726155115372e8af6959ac180908.json (key: https://tigerconnect.com/:a598...)
2025-07-12T07:48:52.915Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a598e4f2 (62 bytes)
2025-07-12T07:48:52.927Z [DEBUG] - 💾 Cache saved to file: 5c4d62c175a69c1e9d3911b51ec38373.json (key: https://tigerconnect.com/:a8cf...)
2025-07-12T07:48:52.928Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a8cfde63 (62 bytes)
2025-07-12T07:48:52.932Z [DEBUG] - 💾 Cache saved to file: bd000830ab45a8d7cdac2f207437912b.json (key: https://tigerconnect.com/:2696...)
2025-07-12T07:48:52.932Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:269605d4 (61 bytes)
2025-07-12T07:48:52.936Z [DEBUG] - 💾 Cache saved to file: 89160a289dabf8718c30977330411c01.json (key: https://tigerconnect.com/:d304...)
2025-07-12T07:48:52.937Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d304ba20 (61 bytes)
2025-07-12T07:48:52.939Z [DEBUG] - 💾 Cache saved to file: da1cd83794fb9244cb67a6f4e4bf629c.json (key: https://tigerconnect.com/:6396...)
2025-07-12T07:48:52.940Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:639612e6 (64 bytes)
2025-07-12T07:48:52.943Z [DEBUG] - 💾 Cache saved to file: ef36f0c11a1d6cd780326394e9ce2933.json (key: https://tigerconnect.com/:3d4d...)
2025-07-12T07:48:52.944Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3d4dcd6f (62 bytes)
2025-07-12T07:48:52.948Z [DEBUG] - 💾 Cache saved to file: 38cdf473277f920fa85ca5f9f4f2289d.json (key: https://tigerconnect.com/:9993...)
2025-07-12T07:48:52.949Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:99938282 (62 bytes)
2025-07-12T07:48:52.952Z [DEBUG] - 💾 Cache saved to file: 36a1e272427f69e6d72075e2e281f974.json (key: https://tigerconnect.com/:6394...)
2025-07-12T07:48:52.952Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:6394d816 (64 bytes)
2025-07-12T07:48:52.955Z [DEBUG] - 💾 Cache saved to file: 855230df3153f47bb32a65b46f01fe64.json (key: https://tigerconnect.com/:aab9...)
2025-07-12T07:48:52.955Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:aab9e1de (61 bytes)
2025-07-12T07:48:52.958Z [DEBUG] - 💾 Cache saved to file: 96925a000e150d4777fee5b1efdb0276.json (key: https://tigerconnect.com/:1fdc...)
2025-07-12T07:48:52.959Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:1fdc0f89 (58 bytes)
2025-07-12T07:48:52.964Z [DEBUG] - 💾 Cache saved to file: 14ad258288e72d4a6b61f0140e6d7d49.json (key: https://tigerconnect.com/:6267...)
2025-07-12T07:48:52.964Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:626726e6 (58 bytes)
2025-07-12T07:48:52.968Z [DEBUG] - 💾 Cache saved to file: 02ae13ea624f7e894ae36e7f362be6a7.json (key: https://tigerconnect.com/:7a7d...)
2025-07-12T07:48:52.968Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:7a7dc1cd (63 bytes)
2025-07-12T07:48:52.971Z [DEBUG] - 💾 Cache saved to file: ad72b5a662be888865387d8391c79d1b.json (key: https://tigerconnect.com/:imag...)
2025-07-12T07:48:52.972Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:image-analysis (82 bytes)
2025-07-12T07:48:52.975Z [DEBUG] - 💾 Cache saved to file: 8cab73c21d5b2e5d804f9a01f886d1fa.json (key: https://tigerconnect.com/:link...)
2025-07-12T07:48:52.975Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:link-analysis (81 bytes)
2025-07-12T07:48:52.982Z [DEBUG] - 💾 Cache saved to file: b5ef386e77eb6386a09f2f179305d2f3.json (key: https://tigerconnect.com/:form...)
2025-07-12T07:48:52.982Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis (81 bytes)
2025-07-12T07:48:52.985Z [DEBUG] - 💾 Cache saved to file: 20ab0109264dc706ee7cc1921f847428.json (key: https://tigerconnect.com/:head...)
2025-07-12T07:48:52.986Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:heading-analysis (84 bytes)
2025-07-12T07:48:52.989Z [DEBUG] - 💾 Cache saved to file: 088fc800f61cab6c26bb949d47942450.json (key: https://tigerconnect.com/:colo...)
2025-07-12T07:48:52.991Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:color-contrast (82 bytes)
2025-07-12T07:48:52.998Z [DEBUG] - 💾 Cache saved to file: e16102238ff5e7451282d7e044253334.json (key: https://tigerconnect.com/:focu...)
2025-07-12T07:48:52.999Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:focus-management (84 bytes)
2025-07-12T07:48:53.002Z [DEBUG] - 💾 Cache saved to file: de769a9b5f84a8f90831e7232e232037.json (key: https://tigerconnect.com/:keyb...)
2025-07-12T07:48:53.002Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:keyboard-navigation (87 bytes)
2025-07-12T07:48:53.010Z [DEBUG] - 💾 Cache saved to file: 463c8f0eb64ca4ba49b84deb80487cb0.json (key: https://tigerconnect.com/:aria...)
2025-07-12T07:48:53.012Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:aria-validation (83 bytes)
2025-07-12T07:48:53.013Z [DEBUG] - 🔥 Warmed up cache for: https://tigerconnect.com/
2025-07-12T07:48:53.014Z [INFO] - 🔥 Cache warming completed for 1 URLs
2025-07-12T07:48:53.015Z [DEBUG] - 🔥 Pre-warming rule-specific caches for: https://tigerconnect.com/
2025-07-12T07:48:53.018Z [DEBUG] - 💾 Cache saved to file: 0c680842e4e43e2b60ed5369f3762039.json (key: WCAG-001:053b13d2:7e800487...)
2025-07-12T07:48:53.019Z [DEBUG] - 💾 Cached: rule:WCAG-001:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.025Z [DEBUG] - 💾 Cache saved to file: a75e35dd209843948b36c84329d22a7d.json (key: WCAG-002:053b13d2:7e800487...)
2025-07-12T07:48:53.026Z [DEBUG] - 💾 Cached: rule:WCAG-002:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.032Z [DEBUG] - 💾 Cache saved to file: 9cb4dc32d5df423f385c05f9b3176ed5.json (key: WCAG-003:053b13d2:7e800487...)
2025-07-12T07:48:53.032Z [DEBUG] - 💾 Cached: rule:WCAG-003:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.036Z [DEBUG] - 💾 Cache saved to file: 6a3ba030ed4f6bdd1430c84c076923c4.json (key: WCAG-004:053b13d2:7e800487...)
2025-07-12T07:48:53.039Z [DEBUG] - 💾 Cached: rule:WCAG-004:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.042Z [DEBUG] - 💾 Cache saved to file: 982de888cf5260e212204a91473b1660.json (key: WCAG-005:053b13d2:7e800487...)
2025-07-12T07:48:53.042Z [DEBUG] - 💾 Cached: rule:WCAG-005:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.046Z [DEBUG] - 💾 Cache saved to file: e2c4ec65380b1d83c294ae7167544945.json (key: WCAG-006:053b13d2:7e800487...)
2025-07-12T07:48:53.047Z [DEBUG] - 💾 Cached: rule:WCAG-006:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.050Z [DEBUG] - 💾 Cache saved to file: 44542e0fa17221195424bce0cb661872.json (key: WCAG-007:053b13d2:7e800487...)
2025-07-12T07:48:53.050Z [DEBUG] - 💾 Cached: rule:WCAG-007:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.057Z [DEBUG] - 💾 Cache saved to file: 088a0703edaba86c5235f3435f5dd24a.json (key: WCAG-008:053b13d2:7e800487...)
2025-07-12T07:48:53.058Z [DEBUG] - 💾 Cached: rule:WCAG-008:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.061Z [DEBUG] - 💾 Cache saved to file: b0e2c61f5c13d8949e43d1312f7b01b6.json (key: WCAG-009:053b13d2:7e800487...)
2025-07-12T07:48:53.062Z [DEBUG] - 💾 Cached: rule:WCAG-009:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.064Z [DEBUG] - 💾 Cache saved to file: 00004cdfca760e473a4e6b26f7b6b994.json (key: WCAG-010:053b13d2:7e800487...)
2025-07-12T07:48:53.064Z [DEBUG] - 💾 Cached: rule:WCAG-010:053b13d2:7e800487 (61 bytes)
2025-07-12T07:48:53.068Z [DEBUG] - 💾 Cache saved to file: 7fcabdbedc224ce24e39fafab2ef7405.json (key: https://tigerconnect.com/:sema...)
2025-07-12T07:48:53.069Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:semantic-validation (112 bytes)
2025-07-12T07:48:53.072Z [DEBUG] - 💾 Cache saved to file: 94a1fb81d4021b5adcf4359192244915.json (key: https://tigerconnect.com/:cms-...)
2025-07-12T07:48:53.072Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:cms-analysis (105 bytes)
2025-07-12T07:48:53.074Z [DEBUG] - 💾 Cache saved to file: 4b801483243e34625c13b8bedfdbbda8.json (key: https://tigerconnect.com/:enha...)
2025-07-12T07:48:53.075Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:enhanced-contrast (110 bytes)
2025-07-12T07:48:53.078Z [DEBUG] - 💾 Cache saved to file: b5ef386e77eb6386a09f2f179305d2f3.json (key: https://tigerconnect.com/:form...)
2025-07-12T07:48:53.078Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis (106 bytes)
2025-07-12T07:48:53.081Z [DEBUG] - 💾 Cache saved to file: ad72b5a662be888865387d8391c79d1b.json (key: https://tigerconnect.com/:imag...)
2025-07-12T07:48:53.081Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:image-analysis (107 bytes)
2025-07-12T07:48:53.087Z [DEBUG] - 💾 Cache saved to file: 8cab73c21d5b2e5d804f9a01f886d1fa.json (key: https://tigerconnect.com/:link...)
2025-07-12T07:48:53.087Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:link-analysis (106 bytes)
2025-07-12T07:48:53.090Z [DEBUG] - 💾 Cache saved to file: 20ab0109264dc706ee7cc1921f847428.json (key: https://tigerconnect.com/:head...)
2025-07-12T07:48:53.090Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:heading-analysis (109 bytes)
2025-07-12T07:48:53.092Z [DEBUG] - 💾 Cache saved to file: 088fc800f61cab6c26bb949d47942450.json (key: https://tigerconnect.com/:colo...)
2025-07-12T07:48:53.093Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:color-contrast (107 bytes)
2025-07-12T07:48:53.096Z [DEBUG] - 💾 Cache saved to file: e16102238ff5e7451282d7e044253334.json (key: https://tigerconnect.com/:focu...)
2025-07-12T07:48:53.096Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:focus-management (109 bytes)
2025-07-12T07:48:53.102Z [DEBUG] - 💾 Cache saved to file: de769a9b5f84a8f90831e7232e232037.json (key: https://tigerconnect.com/:keyb...)
2025-07-12T07:48:53.102Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:keyboard-navigation (112 bytes)
2025-07-12T07:48:53.104Z [DEBUG] - 💾 Cache saved to file: 463c8f0eb64ca4ba49b84deb80487cb0.json (key: https://tigerconnect.com/:aria...)
2025-07-12T07:48:53.105Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:aria-validation (108 bytes)
2025-07-12T07:48:53.105Z [DEBUG] - ✅ Rule-specific cache warming completed for 10 rules and 11 analysis types
2025-07-12T07:48:53.106Z [INFO] - ✅ Cache pre-warming completed for: https://tigerconnect.com/
2025-07-12T07:48:53.107Z [INFO] - ✅ Cache pre-warming completed in 353ms
2025-07-12T07:48:53.108Z [INFO] - 📋 Extracting unified page structure for all checks...
2025-07-12T07:48:53.128Z [DEBUG] - 📁 Cache file valid: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (114min remaining)
2025-07-12T07:48:53.128Z [DEBUG] - 📁 Cache loaded from file: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (key: dom-structure:https://tigercon...)
2025-07-12T07:48:53.130Z [DEBUG] - 📁 Restored from file cache: dom:dom-structure:https://tigerconnect.com/:053b13d2...
2025-07-12T07:48:53.132Z [DEBUG] - ✅ Cache hit: dom:dom-structure:https://tigerconnect.com/:053b13d2... (accessed 2 times, age: 7540s)
2025-07-12T07:48:53.133Z [DEBUG] - 📋 Using cached page structure (persistent)
2025-07-12T07:48:53.133Z [INFO] - 📋 Page structure extracted successfully: 761 elements found
2025-07-12T07:48:53.134Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.139Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.139Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 0% (0/66)
2025-07-12T07:48:53.140Z [INFO] - 🔍 Executing rule: Non-text Content (WCAG-001)
2025-07-12T07:48:53.142Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-001: Non-text Content
2025-07-12T07:48:53.147Z [DEBUG] - 📁 Cache file valid: b1c2386f72a4e403f0a9afccef14ee51.json (114min remaining)
2025-07-12T07:48:53.147Z [DEBUG] - 📁 Cache loaded from file: b1c2386f72a4e403f0a9afccef14ee51.json (key: WCAG-001:053b13d2:add92319...)
2025-07-12T07:48:53.148Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-001:053b13d2:add92319...
2025-07-12T07:48:53.149Z [DEBUG] - ✅ Cache hit: rule:WCAG-001:053b13d2:add92319... (accessed 2 times, age: 7540s)
2025-07-12T07:48:53.151Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-001
2025-07-12T07:48:53.153Z [DEBUG] - 📁 Cache file valid: 6bccbd724edc1ba02226d2db65245661.json (114min remaining)
2025-07-12T07:48:53.154Z [DEBUG] - 📁 Cache loaded from file: 6bccbd724edc1ba02226d2db65245661.json (key: WCAG-001:WCAG-001...)
2025-07-12T07:48:53.154Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-001:WCAG-001...
2025-07-12T07:48:53.155Z [DEBUG] - ✅ Cache hit: rule:WCAG-001:WCAG-001... (accessed 2 times, age: 7540s)
2025-07-12T07:48:53.156Z [DEBUG] - 📋 Using cached evidence for WCAG-001
2025-07-12T07:48:53.162Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-001 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:53.163Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:53.165Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:53.167Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:53.169Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:53.171Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:53.181Z [DEBUG] - 📁 Cache file valid: 80e3a546bf63e5f250cbac2e49f14df2.json (114min remaining)
2025-07-12T07:48:53.182Z [DEBUG] - 📁 Cache loaded from file: 80e3a546bf63e5f250cbac2e49f14df2.json (key: https://tigerconnect.com/:sema...)
2025-07-12T07:48:53.183Z [DEBUG] - 📁 Restored from file cache: site:https://tigerconnect.com/:semantic-validation-{"va...
2025-07-12T07:48:53.184Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 2 times, age: 7540s)
2025-07-12T07:48:53.184Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:53.185Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:53.187Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:53.189Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:53.191Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:53.239Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:53.262Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:53.331Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":1,"validPatterns":0,"overallScore":0}
2025-07-12T07:48:53.332Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-001 - {"utilitiesUsed":["content-quality","semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8999999999999999,"executionTime":170}
2025-07-12T07:48:53.336Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:53.336Z [DEBUG] - 🔧 Utility performance recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:53.337Z [DEBUG] - 🔧 Utility analysis completed for WCAG-001: - {"utilitiesUsed":5,"errors":0,"executionTime":194}
2025-07-12T07:48:53.338Z [DEBUG] - ⏱️ Check WCAG-001 completed in 198ms (success: true)
2025-07-12T07:48:53.339Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.346Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.346Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 2% (1/66)
2025-07-12T07:48:53.347Z [INFO] - ✅ Rule WCAG-001 completed: failed (0/100)
2025-07-12T07:48:53.348Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.353Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.354Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 2% (1/66)
2025-07-12T07:48:53.355Z [INFO] - 🔍 Executing rule: Captions (Prerecorded) (WCAG-002)
2025-07-12T07:48:53.356Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-002: Captions
2025-07-12T07:48:53.358Z [DEBUG] - 📁 Cache file valid: b5fceffd89ea9b0940a3056d55e299b1.json (114min remaining)
2025-07-12T07:48:53.359Z [DEBUG] - 📁 Cache loaded from file: b5fceffd89ea9b0940a3056d55e299b1.json (key: WCAG-002:053b13d2:add92319...)
2025-07-12T07:48:53.362Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-002:053b13d2:add92319...
2025-07-12T07:48:53.362Z [DEBUG] - ✅ Cache hit: rule:WCAG-002:053b13d2:add92319... (accessed 2 times, age: 7540s)
2025-07-12T07:48:53.363Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-002
2025-07-12T07:48:53.365Z [DEBUG] - 📁 Cache file valid: adad9a0534e9436bc170dbd5c01b3603.json (114min remaining)
2025-07-12T07:48:53.365Z [DEBUG] - 📁 Cache loaded from file: adad9a0534e9436bc170dbd5c01b3603.json (key: WCAG-002:WCAG-002...)
2025-07-12T07:48:53.366Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-002:WCAG-002...
2025-07-12T07:48:53.367Z [DEBUG] - ✅ Cache hit: rule:WCAG-002:WCAG-002... (accessed 2 times, age: 7540s)
2025-07-12T07:48:53.367Z [DEBUG] - 📋 Using cached evidence for WCAG-002
2025-07-12T07:48:53.368Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-002 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"enhance","maxExecutionTime":8000}}
2025-07-12T07:48:53.369Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:53.370Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 3 times, age: 7540s)
2025-07-12T07:48:53.370Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:53.372Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:48:53.376Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 2 times, age: 0s)
2025-07-12T07:48:53.378Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:53.378Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:48:53.379Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:53.381Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:53.382Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:53.383Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:53.404Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:53.424Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:53.427Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-002 - {"utilitiesUsed":["cms-detection","content-quality","semantic-validation"],"confidence":0.8,"accuracy":0.35,"executionTime":59}
2025-07-12T07:48:53.428Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-002: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:53.428Z [DEBUG] - 🔧 Utility performance recorded for WCAG-002: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:53.429Z [DEBUG] - 🔧 Utility analysis completed for WCAG-002: - {"utilitiesUsed":3,"errors":0,"executionTime":74}
2025-07-12T07:48:53.430Z [DEBUG] - ⏱️ Check WCAG-002 completed in 76ms (success: true)
2025-07-12T07:48:53.431Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.462Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.462Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 3% (2/66)
2025-07-12T07:48:53.464Z [INFO] - ✅ Rule WCAG-002 completed: failed (0/100)
2025-07-12T07:48:53.466Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.471Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.471Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 3% (2/66)
2025-07-12T07:48:53.472Z [INFO] - 🔍 Executing rule: Info and Relationships (WCAG-003)
2025-07-12T07:48:53.473Z [INFO] - 🌈 Wide Gamut Color Analyzer initialized - {"p3Analysis":true,"rec2020Analysis":true,"dynamicMonitoring":true}
2025-07-12T07:48:53.474Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-003: Info and Relationships
2025-07-12T07:48:53.477Z [DEBUG] - 📁 Cache file valid: 6b51c453280c02d19238652f55728191.json (114min remaining)
2025-07-12T07:48:53.477Z [DEBUG] - 📁 Cache loaded from file: 6b51c453280c02d19238652f55728191.json (key: WCAG-003:053b13d2:add92319...)
2025-07-12T07:48:53.478Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-003:053b13d2:add92319...
2025-07-12T07:48:53.482Z [DEBUG] - ✅ Cache hit: rule:WCAG-003:053b13d2:add92319... (accessed 2 times, age: 7538s)
2025-07-12T07:48:53.483Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-003
2025-07-12T07:48:53.486Z [DEBUG] - 📁 Cache file valid: a75e90580e5662e79e418867b28e1836.json (114min remaining)
2025-07-12T07:48:53.487Z [DEBUG] - 📁 Cache loaded from file: a75e90580e5662e79e418867b28e1836.json (key: WCAG-003:WCAG-003...)
2025-07-12T07:48:53.487Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-003:WCAG-003...
2025-07-12T07:48:53.488Z [DEBUG] - ✅ Cache hit: rule:WCAG-003:WCAG-003... (accessed 2 times, age: 7538s)
2025-07-12T07:48:53.489Z [DEBUG] - 📋 Using cached evidence for WCAG-003
2025-07-12T07:48:53.490Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-003 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:53.490Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:53.491Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 4 times, age: 7540s)
2025-07-12T07:48:53.492Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:53.494Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:53.495Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:53.501Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:53.501Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:53.502Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:53.503Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:53.504Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:53.523Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:53.552Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:53.569Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:48:53.569Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-003 - {"utilitiesUsed":["content-quality","semantic-validation","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.7,"executionTime":79}
2025-07-12T07:48:53.571Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:53.572Z [DEBUG] - 🔧 Utility performance recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:53.574Z [DEBUG] - 🔧 Utility analysis completed for WCAG-003: - {"utilitiesUsed":4,"errors":0,"executionTime":99}
2025-07-12T07:48:53.575Z [DEBUG] - ⏱️ Check WCAG-003 completed in 103ms (success: true)
2025-07-12T07:48:53.576Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.587Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.587Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 5% (3/66)
2025-07-12T07:48:53.588Z [INFO] - ✅ Rule WCAG-003 completed: failed (0/100)
2025-07-12T07:48:53.590Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.594Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.594Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 5% (3/66)
2025-07-12T07:48:53.595Z [INFO] - 🔍 Executing rule: Contrast (Minimum) (WCAG-004)
2025-07-12T07:48:53.596Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-004: Contrast (Minimum)
2025-07-12T07:48:53.598Z [DEBUG] - 📁 Cache file valid: 79931a2e7b38e3a273482da1d4531a95.json (114min remaining)
2025-07-12T07:48:53.598Z [DEBUG] - 📁 Cache loaded from file: 79931a2e7b38e3a273482da1d4531a95.json (key: WCAG-004:053b13d2:add92319...)
2025-07-12T07:48:53.599Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-004:053b13d2:add92319...
2025-07-12T07:48:53.600Z [DEBUG] - ✅ Cache hit: rule:WCAG-004:053b13d2:add92319... (accessed 2 times, age: 7538s)
2025-07-12T07:48:53.602Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-004
2025-07-12T07:48:53.605Z [DEBUG] - 📁 Cache file valid: d2ad183430c8e3077869fa832c90b2ca.json (114min remaining)
2025-07-12T07:48:53.606Z [DEBUG] - 📁 Cache loaded from file: d2ad183430c8e3077869fa832c90b2ca.json (key: WCAG-004:WCAG-004...)
2025-07-12T07:48:53.606Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-004:WCAG-004...
2025-07-12T07:48:53.607Z [DEBUG] - ✅ Cache hit: rule:WCAG-004:WCAG-004... (accessed 2 times, age: 7538s)
2025-07-12T07:48:53.608Z [DEBUG] - 📋 Using cached evidence for WCAG-004
2025-07-12T07:48:53.608Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-004 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:53.610Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:53.611Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:53.612Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:53.834Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:53.835Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-004 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":227}
2025-07-12T07:48:53.836Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:53.838Z [DEBUG] - 🔧 Utility performance recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:53.839Z [DEBUG] - 🔧 Utility analysis completed for WCAG-004: - {"utilitiesUsed":3,"errors":0,"executionTime":241}
2025-07-12T07:48:53.840Z [DEBUG] - ⏱️ Check WCAG-004 completed in 245ms (success: true)
2025-07-12T07:48:53.841Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.847Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.848Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 6% (4/66)
2025-07-12T07:48:53.849Z [INFO] - ✅ Rule WCAG-004 completed: passed (100/100)
2025-07-12T07:48:53.850Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.854Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.854Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 6% (4/66)
2025-07-12T07:48:53.856Z [INFO] - 🔍 Executing rule: Keyboard (WCAG-005)
2025-07-12T07:48:53.860Z [INFO] - 🎯 Advanced Focus Tracker initialized - {"customIndicators":true,"flowAnalysis":true,"accessibilityTree":true}
2025-07-12T07:48:53.861Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-005: Keyboard
2025-07-12T07:48:53.864Z [DEBUG] - 📁 Cache file valid: a6ecce5051f9339949683209a1de4a1a.json (114min remaining)
2025-07-12T07:48:53.864Z [DEBUG] - 📁 Cache loaded from file: a6ecce5051f9339949683209a1de4a1a.json (key: WCAG-005:053b13d2:add92319...)
2025-07-12T07:48:53.865Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-005:053b13d2:add92319...
2025-07-12T07:48:53.866Z [DEBUG] - ✅ Cache hit: rule:WCAG-005:053b13d2:add92319... (accessed 2 times, age: 7537s)
2025-07-12T07:48:53.867Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-005
2025-07-12T07:48:53.868Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-005 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","maxExecutionTime":6000}}
2025-07-12T07:48:53.869Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:53.870Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 5 times, age: 7541s)
2025-07-12T07:48:53.871Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:53.875Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:53.876Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:53.878Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:53.879Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:53.880Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:53.881Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:53.882Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:53.892Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:53.931Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:48:53.932Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-005 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":64}
2025-07-12T07:48:53.933Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:53.935Z [DEBUG] - 🔧 Utility performance recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:53.936Z [DEBUG] - 🔧 Utility analysis completed for WCAG-005: - {"utilitiesUsed":4,"errors":0,"executionTime":77}
2025-07-12T07:48:53.937Z [DEBUG] - ⏱️ Check WCAG-005 completed in 82ms (success: true)
2025-07-12T07:48:53.938Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.943Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.944Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 8% (5/66)
2025-07-12T07:48:53.944Z [INFO] - ✅ Rule WCAG-005 completed: failed (0/100)
2025-07-12T07:48:53.945Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.950Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:53.950Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 8% (5/66)
2025-07-12T07:48:53.952Z [INFO] - 🔍 Executing rule: Focus Order (WCAG-006)
2025-07-12T07:48:53.953Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-006: Focus Order
2025-07-12T07:48:53.955Z [DEBUG] - 📁 Cache file valid: c1c224c4d926d4c3a0d059b8492adcc4.json (114min remaining)
2025-07-12T07:48:53.955Z [DEBUG] - 📁 Cache loaded from file: c1c224c4d926d4c3a0d059b8492adcc4.json (key: WCAG-006:053b13d2:add92319...)
2025-07-12T07:48:53.956Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-006:053b13d2:add92319...
2025-07-12T07:48:53.956Z [DEBUG] - ✅ Cache hit: rule:WCAG-006:053b13d2:add92319... (accessed 2 times, age: 7536s)
2025-07-12T07:48:53.957Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-006
2025-07-12T07:48:53.958Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-006 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:53.959Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:53.960Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:53.964Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:54.059Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T07:48:54.060Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T07:48:54.211Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:54.211Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-006 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":253}
2025-07-12T07:48:54.214Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:54.216Z [DEBUG] - 🔧 Utility performance recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:54.217Z [DEBUG] - 🔧 Utility analysis completed for WCAG-006: - {"utilitiesUsed":3,"errors":0,"executionTime":262}
2025-07-12T07:48:54.218Z [DEBUG] - ⏱️ Check WCAG-006 completed in 267ms (success: true)
2025-07-12T07:48:54.219Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.223Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.223Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 9% (6/66)
2025-07-12T07:48:54.224Z [INFO] - ✅ Rule WCAG-006 completed: failed (0/100)
2025-07-12T07:48:54.225Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.229Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.229Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 9% (6/66)
2025-07-12T07:48:54.230Z [INFO] - 🔍 Executing rule: Focus Visible (WCAG-007)
2025-07-12T07:48:54.231Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-007: Focus Visible
2025-07-12T07:48:54.236Z [DEBUG] - 📁 Cache file valid: 1f4c31fe7cc78e443a634f63156f8b6a.json (115min remaining)
2025-07-12T07:48:54.236Z [DEBUG] - 📁 Cache loaded from file: 1f4c31fe7cc78e443a634f63156f8b6a.json (key: WCAG-007:053b13d2:add92319...)
2025-07-12T07:48:54.237Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-007:053b13d2:add92319...
2025-07-12T07:48:54.238Z [DEBUG] - ✅ Cache hit: rule:WCAG-007:053b13d2:add92319... (accessed 2 times, age: 7529s)
2025-07-12T07:48:54.238Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-007
2025-07-12T07:48:54.240Z [DEBUG] - 📁 Cache file valid: 92e29ae80832f8cf81c9ef789399e501.json (115min remaining)
2025-07-12T07:48:54.241Z [DEBUG] - 📁 Cache loaded from file: 92e29ae80832f8cf81c9ef789399e501.json (key: WCAG-007:WCAG-007...)
2025-07-12T07:48:54.241Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-007:WCAG-007...
2025-07-12T07:48:54.242Z [DEBUG] - ✅ Cache hit: rule:WCAG-007:WCAG-007... (accessed 2 times, age: 7529s)
2025-07-12T07:48:54.243Z [DEBUG] - 📋 Using cached evidence for WCAG-007
2025-07-12T07:48:54.244Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-007 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:54.245Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:54.247Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:54.250Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:54.451Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:54.452Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-007 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":208}
2025-07-12T07:48:54.453Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:54.455Z [DEBUG] - 🔧 Utility performance recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:54.456Z [DEBUG] - 🔧 Utility analysis completed for WCAG-007: - {"utilitiesUsed":3,"errors":0,"executionTime":223}
2025-07-12T07:48:54.457Z [DEBUG] - ⏱️ Check WCAG-007 completed in 227ms (success: true)
2025-07-12T07:48:54.458Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.463Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.464Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 11% (7/66)
2025-07-12T07:48:54.464Z [INFO] - ✅ Rule WCAG-007 completed: failed (0/100)
2025-07-12T07:48:54.465Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.469Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.469Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 11% (7/66)
2025-07-12T07:48:54.473Z [INFO] - 🔍 Executing rule: Error Identification (WCAG-008)
2025-07-12T07:48:54.474Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-008: Error Identification
2025-07-12T07:48:54.476Z [DEBUG] - 📁 Cache file valid: 06edb7a0699869f2657c51a560ea933c.json (115min remaining)
2025-07-12T07:48:54.477Z [DEBUG] - 📁 Cache loaded from file: 06edb7a0699869f2657c51a560ea933c.json (key: WCAG-008:053b13d2:add92319...)
2025-07-12T07:48:54.477Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-008:053b13d2:add92319...
2025-07-12T07:48:54.478Z [DEBUG] - ✅ Cache hit: rule:WCAG-008:053b13d2:add92319... (accessed 2 times, age: 7524s)
2025-07-12T07:48:54.479Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-008
2025-07-12T07:48:54.481Z [DEBUG] - 📁 Cache file valid: e98e5525ae11e3cecb7834f06806f786.json (115min remaining)
2025-07-12T07:48:54.483Z [DEBUG] - 📁 Cache loaded from file: e98e5525ae11e3cecb7834f06806f786.json (key: WCAG-008:WCAG-008...)
2025-07-12T07:48:54.483Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-008:WCAG-008...
2025-07-12T07:48:54.485Z [DEBUG] - ✅ Cache hit: rule:WCAG-008:WCAG-008... (accessed 2 times, age: 7524s)
2025-07-12T07:48:54.486Z [DEBUG] - 📋 Using cached evidence for WCAG-008
2025-07-12T07:48:54.487Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-008 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:54.488Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:54.488Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 6 times, age: 7541s)
2025-07-12T07:48:54.489Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:54.491Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:54.493Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:54.495Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:54.497Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:54.499Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:54.500Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:54.510Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:54.540Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:48:54.540Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-008 - {"utilitiesUsed":["semantic-validation","component-library","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.55,"executionTime":53}
2025-07-12T07:48:54.542Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:54.545Z [DEBUG] - 🔧 Utility performance recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:54.546Z [DEBUG] - 🔧 Utility analysis completed for WCAG-008: - {"utilitiesUsed":3,"errors":0,"executionTime":69}
2025-07-12T07:48:54.546Z [DEBUG] - ⏱️ Check WCAG-008 completed in 73ms (success: true)
2025-07-12T07:48:54.547Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.553Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.553Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 12% (8/66)
2025-07-12T07:48:54.554Z [INFO] - ✅ Rule WCAG-008 completed: failed (0/100)
2025-07-12T07:48:54.554Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.558Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.559Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 12% (8/66)
2025-07-12T07:48:54.562Z [INFO] - 🔍 Executing rule: Name, Role, Value (WCAG-009)
2025-07-12T07:48:54.564Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-009: Name, Role, Value
2025-07-12T07:48:54.572Z [DEBUG] - 📁 Cache file valid: 6411962e15bff5d711f1d34d86b5a178.json (115min remaining)
2025-07-12T07:48:54.572Z [DEBUG] - 📁 Cache loaded from file: 6411962e15bff5d711f1d34d86b5a178.json (key: WCAG-009:053b13d2:add92319...)
2025-07-12T07:48:54.573Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-009:053b13d2:add92319...
2025-07-12T07:48:54.574Z [DEBUG] - ✅ Cache hit: rule:WCAG-009:053b13d2:add92319... (accessed 2 times, age: 7524s)
2025-07-12T07:48:54.574Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-009
2025-07-12T07:48:54.577Z [DEBUG] - 📁 Cache file valid: a3bf5ff732223bebe7ee44260f121f3e.json (115min remaining)
2025-07-12T07:48:54.578Z [DEBUG] - 📁 Cache loaded from file: a3bf5ff732223bebe7ee44260f121f3e.json (key: WCAG-009:WCAG-009...)
2025-07-12T07:48:54.579Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-009:WCAG-009...
2025-07-12T07:48:54.579Z [DEBUG] - ✅ Cache hit: rule:WCAG-009:WCAG-009... (accessed 2 times, age: 7524s)
2025-07-12T07:48:54.580Z [DEBUG] - 📋 Using cached evidence for WCAG-009
2025-07-12T07:48:54.581Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-009 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:54.581Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:54.582Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 7 times, age: 7542s)
2025-07-12T07:48:54.583Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:54.584Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:54.586Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:54.594Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:54.595Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:54.596Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:54.597Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:54.598Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:54.612Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:54.655Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:48:54.655Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-009 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":74}
2025-07-12T07:48:54.657Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:54.659Z [DEBUG] - 🔧 Utility performance recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:54.660Z [DEBUG] - 🔧 Utility analysis completed for WCAG-009: - {"utilitiesUsed":4,"errors":0,"executionTime":95}
2025-07-12T07:48:54.660Z [DEBUG] - ⏱️ Check WCAG-009 completed in 98ms (success: true)
2025-07-12T07:48:54.661Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.667Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.667Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 14% (9/66)
2025-07-12T07:48:54.668Z [INFO] - ✅ Rule WCAG-009 completed: failed (0/100)
2025-07-12T07:48:54.669Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.674Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.675Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 14% (9/66)
2025-07-12T07:48:54.676Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Minimum) (WCAG-010)
2025-07-12T07:48:54.677Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-010: Focus Not Obscured (Minimum)
2025-07-12T07:48:54.680Z [DEBUG] - 📁 Cache file valid: d60e2dd8f7e1b2b08da70c1edb0ebaf5.json (115min remaining)
2025-07-12T07:48:54.682Z [DEBUG] - 📁 Cache loaded from file: d60e2dd8f7e1b2b08da70c1edb0ebaf5.json (key: WCAG-010:053b13d2:add92319...)
2025-07-12T07:48:54.683Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-010:053b13d2:add92319...
2025-07-12T07:48:54.684Z [DEBUG] - ✅ Cache hit: rule:WCAG-010:053b13d2:add92319... (accessed 2 times, age: 7519s)
2025-07-12T07:48:54.684Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-010
2025-07-12T07:48:54.686Z [DEBUG] - 📁 Cache file valid: b58e4c6bbef16c78192296d9cc3e9207.json (115min remaining)
2025-07-12T07:48:54.687Z [DEBUG] - 📁 Cache loaded from file: b58e4c6bbef16c78192296d9cc3e9207.json (key: WCAG-010:WCAG-010...)
2025-07-12T07:48:54.688Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-010:WCAG-010...
2025-07-12T07:48:54.689Z [DEBUG] - ✅ Cache hit: rule:WCAG-010:WCAG-010... (accessed 2 times, age: 7519s)
2025-07-12T07:48:54.689Z [DEBUG] - 📋 Using cached evidence for WCAG-010
2025-07-12T07:48:54.690Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:54.691Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:54.693Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:54.694Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:54.909Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:54.909Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":219}
2025-07-12T07:48:54.911Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:54.913Z [DEBUG] - 🔧 Utility performance recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:54.914Z [DEBUG] - 🔧 Utility analysis completed for WCAG-010: - {"utilitiesUsed":3,"errors":0,"executionTime":235}
2025-07-12T07:48:54.915Z [DEBUG] - ⏱️ Check WCAG-010 completed in 239ms (success: true)
2025-07-12T07:48:54.915Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.921Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.922Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 15% (10/66)
2025-07-12T07:48:54.922Z [INFO] - ✅ Rule WCAG-010 completed: failed (0/100)
2025-07-12T07:48:54.923Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.928Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:54.928Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 15% (10/66)
2025-07-12T07:48:54.929Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Enhanced) (WCAG-011)
2025-07-12T07:48:54.932Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-011: Focus Not Obscured (Enhanced)
2025-07-12T07:48:54.935Z [DEBUG] - 📁 Cache file valid: 03cd0bc1a4ee73aa53c096bc5d54750a.json (115min remaining)
2025-07-12T07:48:54.935Z [DEBUG] - 📁 Cache loaded from file: 03cd0bc1a4ee73aa53c096bc5d54750a.json (key: WCAG-011:053b13d2:add92319...)
2025-07-12T07:48:54.936Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-011:053b13d2:add92319...
2025-07-12T07:48:54.937Z [DEBUG] - ✅ Cache hit: rule:WCAG-011:053b13d2:add92319... (accessed 2 times, age: 7515s)
2025-07-12T07:48:54.937Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-011
2025-07-12T07:48:54.940Z [DEBUG] - 📁 Cache file valid: 1903ba2567cc5b56b159dd13b2eeb39d.json (115min remaining)
2025-07-12T07:48:54.940Z [DEBUG] - 📁 Cache loaded from file: 1903ba2567cc5b56b159dd13b2eeb39d.json (key: WCAG-011:WCAG-011...)
2025-07-12T07:48:54.941Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-011:WCAG-011...
2025-07-12T07:48:54.941Z [DEBUG] - ✅ Cache hit: rule:WCAG-011:WCAG-011... (accessed 2 times, age: 7515s)
2025-07-12T07:48:54.942Z [DEBUG] - 📋 Using cached evidence for WCAG-011
2025-07-12T07:48:54.943Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:54.944Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:54.946Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:54.950Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:55.160Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:55.161Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":218}
2025-07-12T07:48:55.162Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:55.164Z [DEBUG] - 🔧 Utility performance recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:55.165Z [DEBUG] - 🔧 Utility analysis completed for WCAG-011: - {"utilitiesUsed":3,"errors":0,"executionTime":233}
2025-07-12T07:48:55.166Z [DEBUG] - ⏱️ Check WCAG-011 completed in 237ms (success: true)
2025-07-12T07:48:55.167Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.172Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.172Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 17% (11/66)
2025-07-12T07:48:55.173Z [INFO] - ✅ Rule WCAG-011 completed: passed (96/100)
2025-07-12T07:48:55.174Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.180Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.182Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 17% (11/66)
2025-07-12T07:48:55.183Z [INFO] - 🔍 Executing rule: Focus Appearance (WCAG-012)
2025-07-12T07:48:55.184Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-012: Focus Appearance
2025-07-12T07:48:55.187Z [DEBUG] - 📁 Cache file valid: 72aedcf6766a997165d0b6bb2a48954e.json (115min remaining)
2025-07-12T07:48:55.188Z [DEBUG] - 📁 Cache loaded from file: 72aedcf6766a997165d0b6bb2a48954e.json (key: WCAG-012:053b13d2:add92319...)
2025-07-12T07:48:55.189Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-012:053b13d2:add92319...
2025-07-12T07:48:55.189Z [DEBUG] - ✅ Cache hit: rule:WCAG-012:053b13d2:add92319... (accessed 2 times, age: 7507s)
2025-07-12T07:48:55.190Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-012
2025-07-12T07:48:55.194Z [DEBUG] - 📁 Cache file valid: c6ed0a7d6ff63e2693b81632a40525f4.json (115min remaining)
2025-07-12T07:48:55.196Z [DEBUG] - 📁 Cache loaded from file: c6ed0a7d6ff63e2693b81632a40525f4.json (key: WCAG-012:WCAG-012...)
2025-07-12T07:48:55.197Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-012:WCAG-012...
2025-07-12T07:48:55.198Z [DEBUG] - ✅ Cache hit: rule:WCAG-012:WCAG-012... (accessed 2 times, age: 7507s)
2025-07-12T07:48:55.199Z [DEBUG] - 📋 Using cached evidence for WCAG-012
2025-07-12T07:48:55.200Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:55.201Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:55.203Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:55.204Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:55.449Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:55.450Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":250}
2025-07-12T07:48:55.452Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:55.454Z [DEBUG] - 🔧 Utility performance recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:55.455Z [DEBUG] - 🔧 Utility analysis completed for WCAG-012: - {"utilitiesUsed":3,"errors":0,"executionTime":269}
2025-07-12T07:48:55.455Z [DEBUG] - ⏱️ Check WCAG-012 completed in 272ms (success: true)
2025-07-12T07:48:55.456Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.461Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.462Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 18% (12/66)
2025-07-12T07:48:55.462Z [INFO] - ✅ Rule WCAG-012 completed: failed (0/100)
2025-07-12T07:48:55.463Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.468Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.469Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 18% (12/66)
2025-07-12T07:48:55.470Z [INFO] - 🔍 Executing rule: Dragging Movements (WCAG-013)
2025-07-12T07:48:55.471Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-013: Dragging Movements
2025-07-12T07:48:55.473Z [DEBUG] - 📁 Cache file valid: d7c811b39f1be98996f445fb6f9bd998.json (115min remaining)
2025-07-12T07:48:55.473Z [DEBUG] - 📁 Cache loaded from file: d7c811b39f1be98996f445fb6f9bd998.json (key: WCAG-013:053b13d2:add92319...)
2025-07-12T07:48:55.474Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-013:053b13d2:add92319...
2025-07-12T07:48:55.475Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:053b13d2:add92319... (accessed 2 times, age: 7506s)
2025-07-12T07:48:55.476Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-013
2025-07-12T07:48:55.478Z [DEBUG] - 📁 Cache file valid: 27e7e292431047de714c2100e736b7bd.json (115min remaining)
2025-07-12T07:48:55.478Z [DEBUG] - 📁 Cache loaded from file: 27e7e292431047de714c2100e736b7bd.json (key: WCAG-013:WCAG-013...)
2025-07-12T07:48:55.479Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-013:WCAG-013...
2025-07-12T07:48:55.479Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:WCAG-013... (accessed 2 times, age: 7506s)
2025-07-12T07:48:55.480Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-12T07:48:55.481Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:55.484Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:55.486Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:55.675Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:55.675Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":194}
2025-07-12T07:48:55.677Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:55.679Z [DEBUG] - 🔧 Utility performance recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:55.680Z [DEBUG] - 🔧 Utility analysis completed for WCAG-013: - {"utilitiesUsed":2,"errors":0,"executionTime":207}
2025-07-12T07:48:55.680Z [DEBUG] - ⏱️ Check WCAG-013 completed in 210ms (success: true)
2025-07-12T07:48:55.681Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.686Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.686Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 20% (13/66)
2025-07-12T07:48:55.687Z [INFO] - ✅ Rule WCAG-013 completed: failed (0/100)
2025-07-12T07:48:55.687Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.692Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.692Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 20% (13/66)
2025-07-12T07:48:55.693Z [INFO] - 🔍 Executing rule: Target Size (Minimum) (WCAG-014)
2025-07-12T07:48:55.694Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-014: Target Size
2025-07-12T07:48:55.699Z [DEBUG] - 📁 Cache file valid: 8b42333cd82cdfc12b4027f127683b1b.json (115min remaining)
2025-07-12T07:48:55.700Z [DEBUG] - 📁 Cache loaded from file: 8b42333cd82cdfc12b4027f127683b1b.json (key: WCAG-014:053b13d2:add92319...)
2025-07-12T07:48:55.701Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-014:053b13d2:add92319...
2025-07-12T07:48:55.701Z [DEBUG] - ✅ Cache hit: rule:WCAG-014:053b13d2:add92319... (accessed 2 times, age: 7505s)
2025-07-12T07:48:55.702Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-014
2025-07-12T07:48:55.704Z [DEBUG] - 📁 Cache file valid: 45019f21152e7ac5a9a16de79e14d653.json (115min remaining)
2025-07-12T07:48:55.705Z [DEBUG] - 📁 Cache loaded from file: 45019f21152e7ac5a9a16de79e14d653.json (key: WCAG-014:WCAG-014...)
2025-07-12T07:48:55.706Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-014:WCAG-014...
2025-07-12T07:48:55.706Z [DEBUG] - ✅ Cache hit: rule:WCAG-014:WCAG-014... (accessed 2 times, age: 7505s)
2025-07-12T07:48:55.707Z [DEBUG] - 📋 Using cached evidence for WCAG-014
2025-07-12T07:48:55.708Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:55.710Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:55.715Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:55.929Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:55.930Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":222}
2025-07-12T07:48:55.931Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:55.933Z [DEBUG] - 🔧 Utility performance recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:55.934Z [DEBUG] - 🔧 Utility analysis completed for WCAG-014: - {"utilitiesUsed":2,"errors":0,"executionTime":238}
2025-07-12T07:48:55.935Z [DEBUG] - ⏱️ Check WCAG-014 completed in 242ms (success: true)
2025-07-12T07:48:55.935Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.942Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.943Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 21% (14/66)
2025-07-12T07:48:55.944Z [INFO] - ✅ Rule WCAG-014 completed: passed (81/100)
2025-07-12T07:48:55.946Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.950Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:55.951Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 21% (14/66)
2025-07-12T07:48:55.951Z [INFO] - 🔍 Executing rule: Consistent Help (WCAG-015)
2025-07-12T07:48:55.952Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-015: Consistent Help
2025-07-12T07:48:55.954Z [DEBUG] - 📁 Cache file valid: 697e44ab0d03e917e7e24943c6f491ae.json (115min remaining)
2025-07-12T07:48:55.954Z [DEBUG] - 📁 Cache loaded from file: 697e44ab0d03e917e7e24943c6f491ae.json (key: WCAG-015:053b13d2:add92319...)
2025-07-12T07:48:55.955Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-015:053b13d2:add92319...
2025-07-12T07:48:55.956Z [DEBUG] - ✅ Cache hit: rule:WCAG-015:053b13d2:add92319... (accessed 2 times, age: 7504s)
2025-07-12T07:48:55.960Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-015
2025-07-12T07:48:55.962Z [DEBUG] - 📁 Cache file valid: 8b605d087733e8f3be3611d94b7ebdae.json (115min remaining)
2025-07-12T07:48:55.963Z [DEBUG] - 📁 Cache loaded from file: 8b605d087733e8f3be3611d94b7ebdae.json (key: WCAG-035:WCAG-035...)
2025-07-12T07:48:55.963Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-035:WCAG-035...
2025-07-12T07:48:55.964Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:WCAG-035... (accessed 2 times, age: 7504s)
2025-07-12T07:48:55.965Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-12T07:48:55.966Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:55.966Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:55.968Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:56.006Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:56.169Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:56.169Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":203}
2025-07-12T07:48:56.172Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:56.174Z [DEBUG] - 🔧 Utility performance recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:56.175Z [DEBUG] - 🔧 Utility analysis completed for WCAG-015: - {"utilitiesUsed":2,"errors":0,"executionTime":221}
2025-07-12T07:48:56.176Z [DEBUG] - ⏱️ Check WCAG-015 completed in 225ms (success: true)
2025-07-12T07:48:56.176Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.182Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.182Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 23% (15/66)
2025-07-12T07:48:56.183Z [INFO] - ✅ Rule WCAG-015 completed: passed (100/100)
2025-07-12T07:48:56.184Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.189Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.189Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 23% (15/66)
2025-07-12T07:48:56.190Z [INFO] - 🔍 Executing rule: Redundant Entry (WCAG-016)
2025-07-12T07:48:56.192Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-016: Redundant Entry
2025-07-12T07:48:56.194Z [DEBUG] - 📁 Cache file valid: fa41840651a51956c64ee2b37855c8da.json (115min remaining)
2025-07-12T07:48:56.194Z [DEBUG] - 📁 Cache loaded from file: fa41840651a51956c64ee2b37855c8da.json (key: WCAG-016:053b13d2:add92319...)
2025-07-12T07:48:56.195Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-016:053b13d2:add92319...
2025-07-12T07:48:56.195Z [DEBUG] - ✅ Cache hit: rule:WCAG-016:053b13d2:add92319... (accessed 2 times, age: 7503s)
2025-07-12T07:48:56.196Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-016
2025-07-12T07:48:56.198Z [DEBUG] - 📁 Cache file valid: 00ec64e924e2b9439f3c7ff096f625cd.json (115min remaining)
2025-07-12T07:48:56.198Z [DEBUG] - 📁 Cache loaded from file: 00ec64e924e2b9439f3c7ff096f625cd.json (key: WCAG-016:WCAG-016...)
2025-07-12T07:48:56.199Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-016:WCAG-016...
2025-07-12T07:48:56.200Z [DEBUG] - ✅ Cache hit: rule:WCAG-016:WCAG-016... (accessed 2 times, age: 7503s)
2025-07-12T07:48:56.200Z [DEBUG] - 📋 Using cached evidence for WCAG-016
2025-07-12T07:48:56.201Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:56.202Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:56.206Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:56.432Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:56.432Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":231}
2025-07-12T07:48:56.434Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:56.436Z [DEBUG] - 🔧 Utility performance recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:56.437Z [DEBUG] - 🔧 Utility analysis completed for WCAG-016: - {"utilitiesUsed":2,"errors":0,"executionTime":244}
2025-07-12T07:48:56.438Z [DEBUG] - ⏱️ Check WCAG-016 completed in 248ms (success: true)
2025-07-12T07:48:56.438Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.443Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.443Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 24% (16/66)
2025-07-12T07:48:56.444Z [INFO] - ✅ Rule WCAG-016 completed: failed (0/100)
2025-07-12T07:48:56.445Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.449Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.452Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 24% (16/66)
2025-07-12T07:48:56.453Z [INFO] - 🔍 Executing rule: Image Alternatives (WCAG-017)
2025-07-12T07:48:56.454Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-017: Image Alternatives 3.0
2025-07-12T07:48:56.457Z [DEBUG] - 📁 Cache file valid: 2bd69c6d0cff0a882c80dbccc0ddbd2e.json (115min remaining)
2025-07-12T07:48:56.457Z [DEBUG] - 📁 Cache loaded from file: 2bd69c6d0cff0a882c80dbccc0ddbd2e.json (key: WCAG-017:053b13d2:add92319...)
2025-07-12T07:48:56.458Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-017:053b13d2:add92319...
2025-07-12T07:48:56.459Z [DEBUG] - ✅ Cache hit: rule:WCAG-017:053b13d2:add92319... (accessed 2 times, age: 7503s)
2025-07-12T07:48:56.460Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-017
2025-07-12T07:48:56.462Z [DEBUG] - 📁 Cache file valid: 5d32c0d91398f2b5e88f9e138f971a46.json (115min remaining)
2025-07-12T07:48:56.462Z [DEBUG] - 📁 Cache loaded from file: 5d32c0d91398f2b5e88f9e138f971a46.json (key: WCAG-017:WCAG-017...)
2025-07-12T07:48:56.463Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-017:WCAG-017...
2025-07-12T07:48:56.464Z [DEBUG] - ✅ Cache hit: rule:WCAG-017:WCAG-017... (accessed 2 times, age: 7503s)
2025-07-12T07:48:56.465Z [DEBUG] - 📋 Using cached evidence for WCAG-017
2025-07-12T07:48:56.468Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-017 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:56.469Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:56.470Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 8 times, age: 7543s)
2025-07-12T07:48:56.470Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:56.472Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:56.473Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:56.473Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:56.475Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:56.476Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:56.477Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:56.503Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:56.521Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:56.530Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:48:56.530Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-017 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":62}
2025-07-12T07:48:56.531Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:56.533Z [DEBUG] - 🔧 Utility performance recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:56.534Z [DEBUG] - 🔧 Utility analysis completed for WCAG-017: - {"utilitiesUsed":3,"errors":0,"executionTime":78}
2025-07-12T07:48:56.534Z [DEBUG] - ⏱️ Check WCAG-017 completed in 81ms (success: true)
2025-07-12T07:48:56.535Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.542Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.544Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 26% (17/66)
2025-07-12T07:48:56.544Z [INFO] - ✅ Rule WCAG-017 completed: failed (0/100)
2025-07-12T07:48:56.546Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.551Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.551Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 26% (17/66)
2025-07-12T07:48:56.552Z [INFO] - 🔍 Executing rule: Text and Wording (WCAG-018)
2025-07-12T07:48:56.553Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-018: Text and Wording
2025-07-12T07:48:56.554Z [DEBUG] - 📁 Cache file valid: e7fe6692ea6f27ac095ad2f9449b6b9c.json (115min remaining)
2025-07-12T07:48:56.554Z [DEBUG] - 📁 Cache loaded from file: e7fe6692ea6f27ac095ad2f9449b6b9c.json (key: WCAG-018:053b13d2:add92319...)
2025-07-12T07:48:56.555Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-018:053b13d2:add92319...
2025-07-12T07:48:56.556Z [DEBUG] - ✅ Cache hit: rule:WCAG-018:053b13d2:add92319... (accessed 2 times, age: 7502s)
2025-07-12T07:48:56.558Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-018
2025-07-12T07:48:56.561Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-018 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"enableCMSDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:56.561Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:48:56.563Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 9 times, age: 7544s)
2025-07-12T07:48:56.564Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:56.565Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:48:56.565Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 3 times, age: 3s)
2025-07-12T07:48:56.567Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:48:56.567Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:48:56.568Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:48:56.569Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:48:56.570Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:48:56.571Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:48:56.594Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:48:56.612Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:56.613Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-018 - {"utilitiesUsed":["cms-detection","content-quality","semantic-validation"],"confidence":0.8,"accuracy":0.35,"executionTime":52}
2025-07-12T07:48:56.614Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-018: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:56.615Z [DEBUG] - 🔧 Utility performance recorded for WCAG-018: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:56.615Z [DEBUG] - 🔧 Utility analysis completed for WCAG-018: - {"utilitiesUsed":3,"errors":0,"executionTime":62}
2025-07-12T07:48:56.616Z [DEBUG] - ⏱️ Check WCAG-018 completed in 64ms (success: true)
2025-07-12T07:48:56.617Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.625Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.625Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 27% (18/66)
2025-07-12T07:48:56.626Z [INFO] - ✅ Rule WCAG-018 completed: failed (0/100)
2025-07-12T07:48:56.627Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.631Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.632Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 27% (18/66)
2025-07-12T07:48:56.634Z [INFO] - 🔍 Executing rule: Keyboard Focus (WCAG-019)
2025-07-12T07:48:56.635Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-019: Keyboard Focus 3.0
2025-07-12T07:48:56.638Z [DEBUG] - 📁 Cache file valid: fe1748cdfb619e21f2bb4a61da77c615.json (115min remaining)
2025-07-12T07:48:56.638Z [DEBUG] - 📁 Cache loaded from file: fe1748cdfb619e21f2bb4a61da77c615.json (key: WCAG-019:053b13d2:add92319...)
2025-07-12T07:48:56.639Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-019:053b13d2:add92319...
2025-07-12T07:48:56.640Z [DEBUG] - ✅ Cache hit: rule:WCAG-019:053b13d2:add92319... (accessed 2 times, age: 7502s)
2025-07-12T07:48:56.641Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-019
2025-07-12T07:48:56.643Z [DEBUG] - 📁 Cache file valid: bc9fe1ac640ba8a7c64083f4dce51eff.json (115min remaining)
2025-07-12T07:48:56.643Z [DEBUG] - 📁 Cache loaded from file: bc9fe1ac640ba8a7c64083f4dce51eff.json (key: WCAG-019:WCAG-019...)
2025-07-12T07:48:56.644Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-019:WCAG-019...
2025-07-12T07:48:56.645Z [DEBUG] - ✅ Cache hit: rule:WCAG-019:WCAG-019... (accessed 2 times, age: 7502s)
2025-07-12T07:48:56.646Z [DEBUG] - 📋 Using cached evidence for WCAG-019
2025-07-12T07:48:56.646Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-019 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:56.647Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:56.649Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:56.653Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:56.860Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:56.860Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-019 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":214}
2025-07-12T07:48:56.862Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:56.864Z [DEBUG] - 🔧 Utility performance recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:56.865Z [DEBUG] - 🔧 Utility analysis completed for WCAG-019: - {"utilitiesUsed":3,"errors":0,"executionTime":228}
2025-07-12T07:48:56.866Z [DEBUG] - ⏱️ Check WCAG-019 completed in 232ms (success: true)
2025-07-12T07:48:56.867Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.873Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.874Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 29% (19/66)
2025-07-12T07:48:56.875Z [INFO] - ✅ Rule WCAG-019 completed: failed (0/100)
2025-07-12T07:48:56.876Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.883Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:56.883Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 29% (19/66)
2025-07-12T07:48:56.884Z [INFO] - 🔍 Executing rule: Motor (WCAG-020)
2025-07-12T07:48:56.885Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-058: Motor
2025-07-12T07:48:56.888Z [DEBUG] - 📁 Cache file valid: 4c83a99c9da2c4342974e94427876acd.json (115min remaining)
2025-07-12T07:48:56.889Z [DEBUG] - 📁 Cache loaded from file: 4c83a99c9da2c4342974e94427876acd.json (key: WCAG-058:053b13d2:add92319...)
2025-07-12T07:48:56.889Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-058:053b13d2:add92319...
2025-07-12T07:48:56.891Z [DEBUG] - ✅ Cache hit: rule:WCAG-058:053b13d2:add92319... (accessed 2 times, age: 7500s)
2025-07-12T07:48:56.891Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-058
2025-07-12T07:48:56.895Z [DEBUG] - 📁 Cache file valid: e75c3291ea913f99e367de4c994bd504.json (115min remaining)
2025-07-12T07:48:56.896Z [DEBUG] - 📁 Cache loaded from file: e75c3291ea913f99e367de4c994bd504.json (key: WCAG-058:WCAG-058...)
2025-07-12T07:48:56.898Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-058:WCAG-058...
2025-07-12T07:48:56.898Z [DEBUG] - ✅ Cache hit: rule:WCAG-058:WCAG-058... (accessed 2 times, age: 7500s)
2025-07-12T07:48:56.899Z [DEBUG] - 📋 Using cached evidence for WCAG-058
2025-07-12T07:48:56.900Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-020 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:56.901Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:56.902Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:56.904Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:57.123Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:57.123Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-020 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":223}
2025-07-12T07:48:57.125Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:57.127Z [DEBUG] - 🔧 Utility performance recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:57.128Z [DEBUG] - 🔧 Utility analysis completed for WCAG-020: - {"utilitiesUsed":3,"errors":0,"executionTime":241}
2025-07-12T07:48:57.129Z [DEBUG] - ⏱️ Check WCAG-020 completed in 245ms (success: true)
2025-07-12T07:48:57.130Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.136Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.136Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 30% (20/66)
2025-07-12T07:48:57.137Z [INFO] - ✅ Rule WCAG-020 completed: failed (0/100)
2025-07-12T07:48:57.138Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.146Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.146Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 30% (20/66)
2025-07-12T07:48:57.148Z [INFO] - 🔍 Executing rule: Pronunciation & Meaning (WCAG-021)
2025-07-12T07:48:57.148Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-065: Pronunciation & Meaning
2025-07-12T07:48:57.150Z [DEBUG] - 📁 Cache file valid: 7bdf34e25514cb352be82815f91affad.json (115min remaining)
2025-07-12T07:48:57.151Z [DEBUG] - 📁 Cache loaded from file: 7bdf34e25514cb352be82815f91affad.json (key: WCAG-065:053b13d2:add92319...)
2025-07-12T07:48:57.152Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-065:053b13d2:add92319...
2025-07-12T07:48:57.152Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:053b13d2:add92319... (accessed 2 times, age: 7500s)
2025-07-12T07:48:57.153Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-065
2025-07-12T07:48:57.160Z [DEBUG] - 📁 Cache file valid: ee411a3562e329e251a37ada2a1f5641.json (115min remaining)
2025-07-12T07:48:57.160Z [DEBUG] - 📁 Cache loaded from file: ee411a3562e329e251a37ada2a1f5641.json (key: WCAG-065:WCAG-065...)
2025-07-12T07:48:57.161Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-065:WCAG-065...
2025-07-12T07:48:57.162Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:WCAG-065... (accessed 2 times, age: 7500s)
2025-07-12T07:48:57.163Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-12T07:48:57.164Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-021 - {"config":{"enableFrameworkOptimization":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:57.164Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:57.166Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:57.202Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:57.214Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-021 - {"utilitiesUsed":["content-quality","framework-optimization"],"confidence":0.7,"accuracy":0.35,"executionTime":51}
2025-07-12T07:48:57.215Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:57.216Z [DEBUG] - 🔧 Utility performance recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:57.216Z [DEBUG] - 🔧 Utility analysis completed for WCAG-021: - {"utilitiesUsed":2,"errors":0,"executionTime":68}
2025-07-12T07:48:57.217Z [DEBUG] - ⏱️ Check WCAG-021 completed in 70ms (success: true)
2025-07-12T07:48:57.218Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.223Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.224Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 32% (21/66)
2025-07-12T07:48:57.225Z [INFO] - ✅ Rule WCAG-021 completed: passed (75/100)
2025-07-12T07:48:57.228Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.234Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.235Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 32% (21/66)
2025-07-12T07:48:57.236Z [INFO] - 🔍 Executing rule: Accessible Authentication (Minimum) (WCAG-022)
2025-07-12T07:48:57.237Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-022: Accessible Authentication (Minimum)
2025-07-12T07:48:57.239Z [DEBUG] - 📁 Cache file valid: bc5bd398704b1dbd65c4a3ece6d5f341.json (115min remaining)
2025-07-12T07:48:57.240Z [DEBUG] - 📁 Cache loaded from file: bc5bd398704b1dbd65c4a3ece6d5f341.json (key: WCAG-022:053b13d2:add92319...)
2025-07-12T07:48:57.243Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-022:053b13d2:add92319...
2025-07-12T07:48:57.244Z [DEBUG] - ✅ Cache hit: rule:WCAG-022:053b13d2:add92319... (accessed 2 times, age: 7499s)
2025-07-12T07:48:57.246Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-022
2025-07-12T07:48:57.248Z [DEBUG] - 📁 Cache file valid: 07a72359a8282ab6dba353d773a225d3.json (115min remaining)
2025-07-12T07:48:57.248Z [DEBUG] - 📁 Cache loaded from file: 07a72359a8282ab6dba353d773a225d3.json (key: WCAG-022:WCAG-022...)
2025-07-12T07:48:57.249Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-022:WCAG-022...
2025-07-12T07:48:57.250Z [DEBUG] - ✅ Cache hit: rule:WCAG-022:WCAG-022... (accessed 2 times, age: 7499s)
2025-07-12T07:48:57.251Z [DEBUG] - 📋 Using cached evidence for WCAG-022
2025-07-12T07:48:57.252Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:57.253Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:57.255Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:57.259Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:57.462Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:57.462Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":210}
2025-07-12T07:48:57.464Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:57.466Z [DEBUG] - 🔧 Utility performance recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:57.467Z [DEBUG] - 🔧 Utility analysis completed for WCAG-022: - {"utilitiesUsed":3,"errors":0,"executionTime":228}
2025-07-12T07:48:57.468Z [DEBUG] - ⏱️ Check WCAG-022 completed in 232ms (success: true)
2025-07-12T07:48:57.469Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.476Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.476Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 33% (22/66)
2025-07-12T07:48:57.477Z [INFO] - ✅ Rule WCAG-022 completed: failed (0/100)
2025-07-12T07:48:57.479Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.484Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.484Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 33% (22/66)
2025-07-12T07:48:57.485Z [INFO] - 🔍 Executing rule: Accessible Authentication (Enhanced) (WCAG-023)
🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting WCAG-037: Accessible Authentication (Enhanced) (40% automated)
✅ [52f3c859-aeff-4274-8d32-62129992f841] Completed WCAG-037 in 16ms - Status: passed (100/100, Manual items: 1)
2025-07-12T07:48:57.504Z [DEBUG] - 📁 Cache file valid: e8778cf0e75a1f0e38b3c8636943fd56.json (115min remaining)
2025-07-12T07:48:57.505Z [DEBUG] - 📁 Cache loaded from file: e8778cf0e75a1f0e38b3c8636943fd56.json (key: WCAG-037:WCAG-037...)
2025-07-12T07:48:57.506Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-037:WCAG-037...
2025-07-12T07:48:57.507Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:WCAG-037... (accessed 2 times, age: 7499s)
2025-07-12T07:48:57.508Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-12T07:48:57.509Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-023 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:57.511Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:57.514Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:57.515Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:57.715Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:57.715Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-023 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":206}
2025-07-12T07:48:57.717Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:57.719Z [DEBUG] - 🔧 Utility performance recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:57.720Z [DEBUG] - 🔧 Utility analysis completed for WCAG-023: - {"utilitiesUsed":3,"errors":0,"executionTime":232}
2025-07-12T07:48:57.721Z [DEBUG] - ⏱️ Check WCAG-023 completed in 236ms (success: true)
2025-07-12T07:48:57.721Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.727Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.729Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 35% (23/66)
2025-07-12T07:48:57.730Z [INFO] - ✅ Rule WCAG-023 completed: passed (100/100)
2025-07-12T07:48:57.732Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.736Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.737Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 35% (23/66)
2025-07-12T07:48:57.738Z [INFO] - 🔍 Executing rule: Timing Adjustable (WCAG-044)
2025-07-12T07:48:57.739Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-044: Timing Adjustable
2025-07-12T07:48:57.745Z [DEBUG] - 📁 Cache file valid: 98e045dbc9aa729028791ef5084c3d73.json (115min remaining)
2025-07-12T07:48:57.745Z [DEBUG] - 📁 Cache loaded from file: 98e045dbc9aa729028791ef5084c3d73.json (key: WCAG-044:053b13d2:add92319...)
2025-07-12T07:48:57.746Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-044:053b13d2:add92319...
2025-07-12T07:48:57.748Z [DEBUG] - ✅ Cache hit: rule:WCAG-044:053b13d2:add92319... (accessed 2 times, age: 7499s)
2025-07-12T07:48:57.749Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-044
2025-07-12T07:48:57.751Z [DEBUG] - 📁 Cache file valid: d1e2f48c659bd8e8b7ae07436b74c326.json (115min remaining)
2025-07-12T07:48:57.751Z [DEBUG] - 📁 Cache loaded from file: d1e2f48c659bd8e8b7ae07436b74c326.json (key: WCAG-044:WCAG-044...)
2025-07-12T07:48:57.752Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-044:WCAG-044...
2025-07-12T07:48:57.753Z [DEBUG] - ✅ Cache hit: rule:WCAG-044:WCAG-044... (accessed 2 times, age: 7499s)
2025-07-12T07:48:57.754Z [DEBUG] - 📋 Using cached evidence for WCAG-044
2025-07-12T07:48:57.755Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-044 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:57.755Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:57.757Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:57.937Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:57.937Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-044 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":183}
2025-07-12T07:48:57.941Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:57.942Z [DEBUG] - 🔧 Utility performance recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:57.943Z [DEBUG] - 🔧 Utility analysis completed for WCAG-044: - {"utilitiesUsed":2,"errors":0,"executionTime":203}
2025-07-12T07:48:57.944Z [DEBUG] - ⏱️ Check WCAG-044 completed in 206ms (success: true)
2025-07-12T07:48:57.944Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.951Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.951Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 36% (24/66)
2025-07-12T07:48:57.952Z [INFO] - ✅ Rule WCAG-044 completed: passed (75/100)
2025-07-12T07:48:57.953Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.957Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:57.957Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 36% (24/66)
2025-07-12T07:48:57.958Z [INFO] - 🔍 Executing rule: Pause, Stop, Hide (WCAG-045)
2025-07-12T07:48:57.959Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-045: Pause, Stop, Hide
2025-07-12T07:48:57.964Z [DEBUG] - 📁 Cache file valid: e107a6a933c5dce72c582a57daf2513c.json (115min remaining)
2025-07-12T07:48:57.964Z [DEBUG] - 📁 Cache loaded from file: e107a6a933c5dce72c582a57daf2513c.json (key: WCAG-045:053b13d2:add92319...)
2025-07-12T07:48:57.965Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-045:053b13d2:add92319...
2025-07-12T07:48:57.966Z [DEBUG] - ✅ Cache hit: rule:WCAG-045:053b13d2:add92319... (accessed 2 times, age: 7498s)
2025-07-12T07:48:57.967Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-045
2025-07-12T07:48:57.969Z [DEBUG] - 📁 Cache file valid: 8073e0345c1bb47130e00ead626dd137.json (115min remaining)
2025-07-12T07:48:57.969Z [DEBUG] - 📁 Cache loaded from file: 8073e0345c1bb47130e00ead626dd137.json (key: WCAG-045:WCAG-045...)
2025-07-12T07:48:57.970Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-045:WCAG-045...
2025-07-12T07:48:57.971Z [DEBUG] - ✅ Cache hit: rule:WCAG-045:WCAG-045... (accessed 2 times, age: 7498s)
2025-07-12T07:48:57.972Z [DEBUG] - 📋 Using cached evidence for WCAG-045
2025-07-12T07:48:57.972Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-045 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:57.974Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:57.975Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:58.165Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:58.165Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-045 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":193}
2025-07-12T07:48:58.167Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:58.169Z [DEBUG] - 🔧 Utility performance recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:58.170Z [DEBUG] - 🔧 Utility analysis completed for WCAG-045: - {"utilitiesUsed":2,"errors":0,"executionTime":209}
2025-07-12T07:48:58.171Z [DEBUG] - ⏱️ Check WCAG-045 completed in 213ms (success: true)
2025-07-12T07:48:58.172Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.178Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.178Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 38% (25/66)
2025-07-12T07:48:58.179Z [INFO] - ✅ Rule WCAG-045 completed: failed (0/100)
2025-07-12T07:48:58.182Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.212Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.212Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 38% (25/66)
2025-07-12T07:48:58.214Z [INFO] - 🔍 Executing rule: Three Flashes or Below Threshold (WCAG-046)
2025-07-12T07:48:58.217Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-046: Three Flashes or Below Threshold
2025-07-12T07:48:58.219Z [DEBUG] - 📁 Cache file valid: a98794df9865343e56915dbbe43cd6a7.json (115min remaining)
2025-07-12T07:48:58.219Z [DEBUG] - 📁 Cache loaded from file: a98794df9865343e56915dbbe43cd6a7.json (key: WCAG-046:053b13d2:add92319...)
2025-07-12T07:48:58.220Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-046:053b13d2:add92319...
2025-07-12T07:48:58.221Z [DEBUG] - ✅ Cache hit: rule:WCAG-046:053b13d2:add92319... (accessed 2 times, age: 7498s)
2025-07-12T07:48:58.222Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-046
2025-07-12T07:48:58.226Z [DEBUG] - 📁 Cache file valid: 0effabcb96214c0f882ee2a31620da53.json (115min remaining)
2025-07-12T07:48:58.227Z [DEBUG] - 📁 Cache loaded from file: 0effabcb96214c0f882ee2a31620da53.json (key: WCAG-046:WCAG-046...)
2025-07-12T07:48:58.228Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-046:WCAG-046...
2025-07-12T07:48:58.230Z [DEBUG] - ✅ Cache hit: rule:WCAG-046:WCAG-046... (accessed 2 times, age: 7498s)
2025-07-12T07:48:58.231Z [DEBUG] - 📋 Using cached evidence for WCAG-046
2025-07-12T07:48:58.232Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-046 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:48:58.232Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:58.234Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:58.837Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:58.838Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-046 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":607}
2025-07-12T07:48:58.840Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:58.841Z [DEBUG] - 🔧 Utility performance recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:58.843Z [DEBUG] - 🔧 Utility analysis completed for WCAG-046: - {"utilitiesUsed":2,"errors":0,"executionTime":625}
2025-07-12T07:48:58.844Z [DEBUG] - ⏱️ Check WCAG-046 completed in 630ms (success: true)
2025-07-12T07:48:58.845Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.851Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.852Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 39% (26/66)
2025-07-12T07:48:58.853Z [INFO] - ✅ Rule WCAG-046 completed: passed (99/100)
2025-07-12T07:48:58.854Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.876Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:58.876Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 39% (26/66)
2025-07-12T07:48:58.879Z [INFO] - 🔍 Executing rule: Resize Text (WCAG-037)
2025-07-12T07:48:58.881Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-037: Resize Text
2025-07-12T07:48:58.886Z [DEBUG] - 📁 Cache file valid: 8b199614ebef71562728e34c7c5cbe56.json (115min remaining)
2025-07-12T07:48:58.886Z [DEBUG] - 📁 Cache loaded from file: 8b199614ebef71562728e34c7c5cbe56.json (key: WCAG-037:053b13d2:add92319...)
2025-07-12T07:48:58.887Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-037:053b13d2:add92319...
2025-07-12T07:48:58.889Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:053b13d2:add92319... (accessed 2 times, age: 7489s)
2025-07-12T07:48:58.893Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-037
2025-07-12T07:48:58.894Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:WCAG-037... (accessed 3 times, age: 7501s)
2025-07-12T07:48:58.896Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-12T07:48:58.897Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-037 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:58.898Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:58.899Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:58.901Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:59.059Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T07:48:59.059Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T07:48:59.103Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:59.103Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-037 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":206}
2025-07-12T07:48:59.107Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:59.108Z [DEBUG] - 🔧 Utility performance recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:59.109Z [DEBUG] - 🔧 Utility analysis completed for WCAG-037: - {"utilitiesUsed":3,"errors":0,"executionTime":228}
2025-07-12T07:48:59.110Z [DEBUG] - ⏱️ Check WCAG-037 completed in 231ms (success: true)
2025-07-12T07:48:59.111Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.129Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.129Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 41% (27/66)
2025-07-12T07:48:59.130Z [INFO] - ✅ Rule WCAG-037 completed: failed (0/100)
2025-07-12T07:48:59.133Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.139Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.139Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 41% (27/66)
2025-07-12T07:48:59.140Z [INFO] - 🔍 Executing rule: Images of Text (WCAG-039)
2025-07-12T07:48:59.141Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-039: Images of Text
2025-07-12T07:48:59.144Z [DEBUG] - 📁 Cache file valid: 0b9377f001d8de85e74a9756ae50f61f.json (115min remaining)
2025-07-12T07:48:59.145Z [DEBUG] - 📁 Cache loaded from file: 0b9377f001d8de85e74a9756ae50f61f.json (key: WCAG-039:053b13d2:add92319...)
2025-07-12T07:48:59.146Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-039:053b13d2:add92319...
2025-07-12T07:48:59.147Z [DEBUG] - ✅ Cache hit: rule:WCAG-039:053b13d2:add92319... (accessed 2 times, age: 7489s)
2025-07-12T07:48:59.148Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-039
2025-07-12T07:48:59.150Z [DEBUG] - 📁 Cache file valid: 5b4d8451893f90896a28cbbb74a4e6ea.json (115min remaining)
2025-07-12T07:48:59.151Z [DEBUG] - 📁 Cache loaded from file: 5b4d8451893f90896a28cbbb74a4e6ea.json (key: WCAG-039:WCAG-039...)
2025-07-12T07:48:59.152Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-039:WCAG-039...
2025-07-12T07:48:59.152Z [DEBUG] - ✅ Cache hit: rule:WCAG-039:WCAG-039... (accessed 2 times, age: 7489s)
2025-07-12T07:48:59.153Z [DEBUG] - 📋 Using cached evidence for WCAG-039
2025-07-12T07:48:59.154Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-039 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:59.155Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:59.159Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:48:59.204Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:48:59.360Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:59.360Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-039 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":206}
2025-07-12T07:48:59.362Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:59.364Z [DEBUG] - 🔧 Utility performance recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:59.365Z [DEBUG] - 🔧 Utility analysis completed for WCAG-039: - {"utilitiesUsed":2,"errors":0,"executionTime":222}
2025-07-12T07:48:59.365Z [DEBUG] - ⏱️ Check WCAG-039 completed in 225ms (success: true)
2025-07-12T07:48:59.366Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.372Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.374Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 42% (28/66)
2025-07-12T07:48:59.375Z [INFO] - ✅ Rule WCAG-039 completed: failed (0/100)
2025-07-12T07:48:59.376Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.380Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.380Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 42% (28/66)
2025-07-12T07:48:59.381Z [INFO] - 🔍 Executing rule: Reflow (WCAG-040)
2025-07-12T07:48:59.382Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-040: Reflow
2025-07-12T07:48:59.385Z [DEBUG] - 📁 Cache file valid: a90e5993c16b32f6ab78b892a8127444.json (115min remaining)
2025-07-12T07:48:59.385Z [DEBUG] - 📁 Cache loaded from file: a90e5993c16b32f6ab78b892a8127444.json (key: WCAG-040:053b13d2:add92319...)
2025-07-12T07:48:59.387Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-040:053b13d2:add92319...
2025-07-12T07:48:59.391Z [DEBUG] - ✅ Cache hit: rule:WCAG-040:053b13d2:add92319... (accessed 2 times, age: 7483s)
2025-07-12T07:48:59.392Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-040
2025-07-12T07:48:59.394Z [DEBUG] - 📁 Cache file valid: c1d7238a0d406c4e4d2b9057a9b92850.json (115min remaining)
2025-07-12T07:48:59.395Z [DEBUG] - 📁 Cache loaded from file: c1d7238a0d406c4e4d2b9057a9b92850.json (key: WCAG-041:WCAG-041...)
2025-07-12T07:48:59.395Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-041:WCAG-041...
2025-07-12T07:48:59.396Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:WCAG-041... (accessed 2 times, age: 7483s)
2025-07-12T07:48:59.397Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-12T07:48:59.398Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-040 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:59.399Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:59.400Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:59.566Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:59.566Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-040 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":168}
2025-07-12T07:48:59.568Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:59.570Z [DEBUG] - 🔧 Utility performance recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:59.570Z [DEBUG] - 🔧 Utility analysis completed for WCAG-040: - {"utilitiesUsed":2,"errors":0,"executionTime":187}
2025-07-12T07:48:59.571Z [DEBUG] - ⏱️ Check WCAG-040 completed in 190ms (success: true)
2025-07-12T07:48:59.572Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.577Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.578Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 44% (29/66)
2025-07-12T07:48:59.578Z [INFO] - ✅ Rule WCAG-040 completed: failed (0/100)
2025-07-12T07:48:59.580Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.584Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.585Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 44% (29/66)
2025-07-12T07:48:59.585Z [INFO] - 🔍 Executing rule: Non-text Contrast (WCAG-041)
2025-07-12T07:48:59.586Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-041: Non-text Contrast
2025-07-12T07:48:59.589Z [DEBUG] - 📁 Cache file valid: 51c48e5d1d70bfe79193b22e30987da9.json (115min remaining)
2025-07-12T07:48:59.589Z [DEBUG] - 📁 Cache loaded from file: 51c48e5d1d70bfe79193b22e30987da9.json (key: WCAG-041:053b13d2:add92319...)
2025-07-12T07:48:59.590Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-041:053b13d2:add92319...
2025-07-12T07:48:59.591Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:053b13d2:add92319... (accessed 2 times, age: 7477s)
2025-07-12T07:48:59.594Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-041
2025-07-12T07:48:59.596Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:WCAG-041... (accessed 3 times, age: 7484s)
2025-07-12T07:48:59.597Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-12T07:48:59.597Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-041 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:59.598Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:59.599Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:48:59.775Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:48:59.776Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-041 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":179}
2025-07-12T07:48:59.778Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:48:59.780Z [DEBUG] - 🔧 Utility performance recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:48:59.780Z [DEBUG] - 🔧 Utility analysis completed for WCAG-041: - {"utilitiesUsed":2,"errors":0,"executionTime":193}
2025-07-12T07:48:59.781Z [DEBUG] - ⏱️ Check WCAG-041 completed in 196ms (success: true)
2025-07-12T07:48:59.782Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.787Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.787Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 45% (30/66)
2025-07-12T07:48:59.788Z [INFO] - ✅ Rule WCAG-041 completed: failed (0/100)
2025-07-12T07:48:59.789Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.795Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:48:59.795Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 45% (30/66)
2025-07-12T07:48:59.796Z [INFO] - 🔍 Executing rule: Text Spacing (WCAG-042)
2025-07-12T07:48:59.797Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-042: Text Spacing
2025-07-12T07:48:59.811Z [DEBUG] - 📁 Cache file valid: a6023bea83e9245d90991fcca6d694c6.json (115min remaining)
2025-07-12T07:48:59.811Z [DEBUG] - 📁 Cache loaded from file: a6023bea83e9245d90991fcca6d694c6.json (key: WCAG-042:053b13d2:add92319...)
2025-07-12T07:48:59.812Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-042:053b13d2:add92319...
2025-07-12T07:48:59.814Z [DEBUG] - ✅ Cache hit: rule:WCAG-042:053b13d2:add92319... (accessed 2 times, age: 7476s)
2025-07-12T07:48:59.815Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-042
2025-07-12T07:48:59.824Z [DEBUG] - 📁 Cache file valid: 153cca37beb6d4b5378b7047a7af55f3.json (115min remaining)
2025-07-12T07:48:59.824Z [DEBUG] - 📁 Cache loaded from file: 153cca37beb6d4b5378b7047a7af55f3.json (key: WCAG-042:WCAG-042...)
2025-07-12T07:48:59.826Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-042:WCAG-042...
2025-07-12T07:48:59.828Z [DEBUG] - ✅ Cache hit: rule:WCAG-042:WCAG-042... (accessed 2 times, age: 7476s)
2025-07-12T07:48:59.829Z [DEBUG] - 📋 Using cached evidence for WCAG-042
2025-07-12T07:48:59.829Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-042 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:48:59.830Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:48:59.832Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:48:59.833Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:00.043Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:00.043Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-042 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":214}
2025-07-12T07:49:00.045Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:00.047Z [DEBUG] - 🔧 Utility performance recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:00.047Z [DEBUG] - 🔧 Utility analysis completed for WCAG-042: - {"utilitiesUsed":3,"errors":0,"executionTime":249}
2025-07-12T07:49:00.048Z [DEBUG] - ⏱️ Check WCAG-042 completed in 252ms (success: true)
2025-07-12T07:49:00.049Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.053Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.053Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 47% (31/66)
2025-07-12T07:49:00.054Z [INFO] - ✅ Rule WCAG-042 completed: failed (0/100)
2025-07-12T07:49:00.056Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.059Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.059Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 47% (31/66)
2025-07-12T07:49:00.060Z [INFO] - 🔍 Executing rule: Content on Hover or Focus (WCAG-043)
2025-07-12T07:49:00.061Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-043: Content on Hover or Focus
2025-07-12T07:49:00.063Z [DEBUG] - 📁 Cache file valid: 5214d88911d20698ac817297c9833426.json (115min remaining)
2025-07-12T07:49:00.063Z [DEBUG] - 📁 Cache loaded from file: 5214d88911d20698ac817297c9833426.json (key: WCAG-043:053b13d2:add92319...)
2025-07-12T07:49:00.064Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-043:053b13d2:add92319...
2025-07-12T07:49:00.065Z [DEBUG] - ✅ Cache hit: rule:WCAG-043:053b13d2:add92319... (accessed 2 times, age: 7474s)
2025-07-12T07:49:00.069Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-043
2025-07-12T07:49:00.072Z [DEBUG] - 📁 Cache file valid: 1b5418cf3c968be17db0f146897c9c17.json (115min remaining)
2025-07-12T07:49:00.072Z [DEBUG] - 📁 Cache loaded from file: 1b5418cf3c968be17db0f146897c9c17.json (key: WCAG-043:WCAG-043...)
2025-07-12T07:49:00.073Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-043:WCAG-043...
2025-07-12T07:49:00.074Z [DEBUG] - ✅ Cache hit: rule:WCAG-043:WCAG-043... (accessed 2 times, age: 7474s)
2025-07-12T07:49:00.074Z [DEBUG] - 📋 Using cached evidence for WCAG-043
2025-07-12T07:49:00.075Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-043 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:00.076Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:00.077Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:00.078Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:00.280Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:00.280Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-043 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":205}
2025-07-12T07:49:00.282Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:00.284Z [DEBUG] - 🔧 Utility performance recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:00.285Z [DEBUG] - 🔧 Utility analysis completed for WCAG-043: - {"utilitiesUsed":3,"errors":0,"executionTime":222}
2025-07-12T07:49:00.286Z [DEBUG] - ⏱️ Check WCAG-043 completed in 226ms (success: true)
2025-07-12T07:49:00.286Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.292Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.292Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 48% (32/66)
2025-07-12T07:49:00.293Z [INFO] - ✅ Rule WCAG-043 completed: failed (0/100)
2025-07-12T07:49:00.294Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.297Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.297Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 48% (32/66)
2025-07-12T07:49:00.300Z [INFO] - 🔍 Executing rule: Audio Control (WCAG-050)
2025-07-12T07:49:00.304Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-050: Audio Control
2025-07-12T07:49:00.306Z [DEBUG] - 📁 Cache file valid: 03a937c64b250f0e5f97bd540ecbe76c.json (115min remaining)
2025-07-12T07:49:00.306Z [DEBUG] - 📁 Cache loaded from file: 03a937c64b250f0e5f97bd540ecbe76c.json (key: WCAG-050:053b13d2:add92319...)
2025-07-12T07:49:00.307Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-050:053b13d2:add92319...
2025-07-12T07:49:00.308Z [DEBUG] - ✅ Cache hit: rule:WCAG-050:053b13d2:add92319... (accessed 2 times, age: 7473s)
2025-07-12T07:49:00.308Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-050
2025-07-12T07:49:00.310Z [DEBUG] - 📁 Cache file valid: 22c5971f253d1728b987c254eb8f0974.json (115min remaining)
2025-07-12T07:49:00.310Z [DEBUG] - 📁 Cache loaded from file: 22c5971f253d1728b987c254eb8f0974.json (key: WCAG-050:WCAG-050...)
2025-07-12T07:49:00.311Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-050:WCAG-050...
2025-07-12T07:49:00.312Z [DEBUG] - ✅ Cache hit: rule:WCAG-050:WCAG-050... (accessed 2 times, age: 7473s)
2025-07-12T07:49:00.313Z [DEBUG] - 📋 Using cached evidence for WCAG-050
2025-07-12T07:49:00.313Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-050 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:00.314Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:00.315Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:00.498Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:00.498Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-050 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":185}
2025-07-12T07:49:00.500Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:00.502Z [DEBUG] - 🔧 Utility performance recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:00.503Z [DEBUG] - 🔧 Utility analysis completed for WCAG-050: - {"utilitiesUsed":2,"errors":0,"executionTime":200}
2025-07-12T07:49:00.504Z [DEBUG] - ⏱️ Check WCAG-050 completed in 204ms (success: true)
2025-07-12T07:49:00.505Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.510Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.510Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 50% (33/66)
2025-07-12T07:49:00.511Z [INFO] - ✅ Rule WCAG-050 completed: passed (100/100)
2025-07-12T07:49:00.512Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.515Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.515Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 50% (33/66)
2025-07-12T07:49:00.516Z [INFO] - 🔍 Executing rule: Keyboard Accessible (WCAG-051)
2025-07-12T07:49:00.517Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-051: Keyboard Accessible
2025-07-12T07:49:00.519Z [DEBUG] - 📁 Cache file valid: 33a17a36ed1c6d42ea750fc0ef49cd03.json (115min remaining)
2025-07-12T07:49:00.519Z [DEBUG] - 📁 Cache loaded from file: 33a17a36ed1c6d42ea750fc0ef49cd03.json (key: WCAG-051:053b13d2:add92319...)
2025-07-12T07:49:00.520Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-051:053b13d2:add92319...
2025-07-12T07:49:00.524Z [DEBUG] - ✅ Cache hit: rule:WCAG-051:053b13d2:add92319... (accessed 2 times, age: 7471s)
2025-07-12T07:49:00.525Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-051
2025-07-12T07:49:00.527Z [DEBUG] - 📁 Cache file valid: 0782c8c437935165aea40991dcb4dc4a.json (115min remaining)
2025-07-12T07:49:00.527Z [DEBUG] - 📁 Cache loaded from file: 0782c8c437935165aea40991dcb4dc4a.json (key: WCAG-051:WCAG-051...)
2025-07-12T07:49:00.528Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-051:WCAG-051...
2025-07-12T07:49:00.529Z [DEBUG] - ✅ Cache hit: rule:WCAG-051:WCAG-051... (accessed 2 times, age: 7471s)
2025-07-12T07:49:00.530Z [DEBUG] - 📋 Using cached evidence for WCAG-051
2025-07-12T07:49:00.531Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-051 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:00.531Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:00.533Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:00.700Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:00.700Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-051 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":169}
2025-07-12T07:49:00.702Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:00.704Z [DEBUG] - 🔧 Utility performance recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:00.704Z [DEBUG] - 🔧 Utility analysis completed for WCAG-051: - {"utilitiesUsed":2,"errors":0,"executionTime":186}
2025-07-12T07:49:00.705Z [DEBUG] - ⏱️ Check WCAG-051 completed in 189ms (success: true)
2025-07-12T07:49:00.706Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.711Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.711Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 52% (34/66)
2025-07-12T07:49:00.712Z [INFO] - ✅ Rule WCAG-051 completed: failed (0/100)
2025-07-12T07:49:00.715Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.719Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.720Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 52% (34/66)
2025-07-12T07:49:00.721Z [INFO] - 🔍 Executing rule: Character Key Shortcuts (WCAG-052)
2025-07-12T07:49:00.722Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-052: Character Key Shortcuts
2025-07-12T07:49:00.723Z [DEBUG] - 📁 Cache file valid: 6cfd95a26006ae76535b7fc128712b8a.json (115min remaining)
2025-07-12T07:49:00.724Z [DEBUG] - 📁 Cache loaded from file: 6cfd95a26006ae76535b7fc128712b8a.json (key: WCAG-052:053b13d2:add92319...)
2025-07-12T07:49:00.724Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-052:053b13d2:add92319...
2025-07-12T07:49:00.725Z [DEBUG] - ✅ Cache hit: rule:WCAG-052:053b13d2:add92319... (accessed 2 times, age: 7470s)
2025-07-12T07:49:00.726Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-052
2025-07-12T07:49:00.727Z [DEBUG] - 📁 Cache file valid: c49e76409c1749df890ade80ef803b4f.json (115min remaining)
2025-07-12T07:49:00.729Z [DEBUG] - 📁 Cache loaded from file: c49e76409c1749df890ade80ef803b4f.json (key: WCAG-052:WCAG-052...)
2025-07-12T07:49:00.731Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-052:WCAG-052...
2025-07-12T07:49:00.732Z [DEBUG] - ✅ Cache hit: rule:WCAG-052:WCAG-052... (accessed 2 times, age: 7470s)
2025-07-12T07:49:00.733Z [DEBUG] - 📋 Using cached evidence for WCAG-052
2025-07-12T07:49:00.734Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-052 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:00.735Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:00.736Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:00.899Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:00.899Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-052 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":165}
2025-07-12T07:49:00.901Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:00.903Z [DEBUG] - 🔧 Utility performance recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:00.904Z [DEBUG] - 🔧 Utility analysis completed for WCAG-052: - {"utilitiesUsed":2,"errors":0,"executionTime":180}
2025-07-12T07:49:00.905Z [DEBUG] - ⏱️ Check WCAG-052 completed in 184ms (success: true)
2025-07-12T07:49:00.905Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.914Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.914Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 53% (35/66)
2025-07-12T07:49:00.915Z [INFO] - ✅ Rule WCAG-052 completed: passed (75/100)
2025-07-12T07:49:00.917Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.927Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:00.928Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 53% (35/66)
2025-07-12T07:49:00.929Z [INFO] - 🔍 Executing rule: Pointer Gestures (WCAG-053)
2025-07-12T07:49:00.931Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-053: Pointer Gestures
2025-07-12T07:49:00.933Z [DEBUG] - 📁 Cache file valid: 18f9e9e7dfa6a0644a8fbb82748d1d26.json (116min remaining)
2025-07-12T07:49:00.933Z [DEBUG] - 📁 Cache loaded from file: 18f9e9e7dfa6a0644a8fbb82748d1d26.json (key: WCAG-053:053b13d2:add92319...)
2025-07-12T07:49:00.934Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-053:053b13d2:add92319...
2025-07-12T07:49:00.934Z [DEBUG] - ✅ Cache hit: rule:WCAG-053:053b13d2:add92319... (accessed 2 times, age: 7470s)
2025-07-12T07:49:00.935Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-053
2025-07-12T07:49:00.938Z [DEBUG] - 📁 Cache file valid: 12cde2393923c93e264e54e16b815352.json (116min remaining)
2025-07-12T07:49:00.938Z [DEBUG] - 📁 Cache loaded from file: 12cde2393923c93e264e54e16b815352.json (key: WCAG-049:WCAG-049...)
2025-07-12T07:49:00.939Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-049:WCAG-049...
2025-07-12T07:49:00.939Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:WCAG-049... (accessed 2 times, age: 7470s)
2025-07-12T07:49:00.940Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-12T07:49:00.942Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-053 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:00.946Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:00.948Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:01.169Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:01.169Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-053 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":227}
2025-07-12T07:49:01.171Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:01.173Z [DEBUG] - 🔧 Utility performance recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:01.174Z [DEBUG] - 🔧 Utility analysis completed for WCAG-053: - {"utilitiesUsed":2,"errors":0,"executionTime":243}
2025-07-12T07:49:01.175Z [DEBUG] - ⏱️ Check WCAG-053 completed in 247ms (success: true)
2025-07-12T07:49:01.175Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.180Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.181Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 55% (36/66)
2025-07-12T07:49:01.182Z [INFO] - ✅ Rule WCAG-053 completed: passed (100/100)
2025-07-12T07:49:01.182Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.186Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.186Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 55% (36/66)
2025-07-12T07:49:01.187Z [INFO] - 🔍 Executing rule: Pointer Cancellation (WCAG-054)
2025-07-12T07:49:01.189Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-054: Pointer Cancellation
2025-07-12T07:49:01.190Z [DEBUG] - 📁 Cache file valid: c98dc2b02e1b577a00e483906e8ca192.json (116min remaining)
2025-07-12T07:49:01.191Z [DEBUG] - 📁 Cache loaded from file: c98dc2b02e1b577a00e483906e8ca192.json (key: WCAG-054:053b13d2:add92319...)
2025-07-12T07:49:01.191Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-054:053b13d2:add92319...
2025-07-12T07:49:01.192Z [DEBUG] - ✅ Cache hit: rule:WCAG-054:053b13d2:add92319... (accessed 2 times, age: 7470s)
2025-07-12T07:49:01.193Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-054
2025-07-12T07:49:01.195Z [DEBUG] - 📁 Cache file valid: d282ada86df9f29b48342af5f0aa8881.json (116min remaining)
2025-07-12T07:49:01.198Z [DEBUG] - 📁 Cache loaded from file: d282ada86df9f29b48342af5f0aa8881.json (key: WCAG-054:WCAG-054...)
2025-07-12T07:49:01.199Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-054:WCAG-054...
2025-07-12T07:49:01.199Z [DEBUG] - ✅ Cache hit: rule:WCAG-054:WCAG-054... (accessed 2 times, age: 7470s)
2025-07-12T07:49:01.200Z [DEBUG] - 📋 Using cached evidence for WCAG-054
2025-07-12T07:49:01.201Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-054 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:01.202Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:01.203Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:01.377Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:01.378Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-054 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":177}
2025-07-12T07:49:01.379Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:01.381Z [DEBUG] - 🔧 Utility performance recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:01.383Z [DEBUG] - 🔧 Utility analysis completed for WCAG-054: - {"utilitiesUsed":2,"errors":0,"executionTime":192}
2025-07-12T07:49:01.383Z [DEBUG] - ⏱️ Check WCAG-054 completed in 196ms (success: true)
2025-07-12T07:49:01.384Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.389Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.390Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 56% (37/66)
2025-07-12T07:49:01.390Z [INFO] - ✅ Rule WCAG-054 completed: passed (100/100)
2025-07-12T07:49:01.393Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.398Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.398Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 56% (37/66)
2025-07-12T07:49:01.399Z [INFO] - 🔍 Executing rule: Label in Name (WCAG-055)
2025-07-12T07:49:01.400Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-055: Label in Name
2025-07-12T07:49:01.401Z [DEBUG] - 📁 Cache file valid: a2f4f89f62b43b45ced080eefd45fdef.json (116min remaining)
2025-07-12T07:49:01.402Z [DEBUG] - 📁 Cache loaded from file: a2f4f89f62b43b45ced080eefd45fdef.json (key: WCAG-055:053b13d2:add92319...)
2025-07-12T07:49:01.403Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-055:053b13d2:add92319...
2025-07-12T07:49:01.404Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:053b13d2:add92319... (accessed 2 times, age: 7464s)
2025-07-12T07:49:01.405Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-055
2025-07-12T07:49:01.407Z [DEBUG] - 📁 Cache file valid: 5da1af541ddaa467b915848dbb0b3030.json (116min remaining)
2025-07-12T07:49:01.409Z [DEBUG] - 📁 Cache loaded from file: 5da1af541ddaa467b915848dbb0b3030.json (key: WCAG-055:WCAG-055...)
2025-07-12T07:49:01.409Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-055:WCAG-055...
2025-07-12T07:49:01.411Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:WCAG-055... (accessed 2 times, age: 7464s)
2025-07-12T07:49:01.412Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-12T07:49:01.413Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-055 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:01.413Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:01.414Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 10 times, age: 7548s)
2025-07-12T07:49:01.415Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:01.416Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:01.417Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:01.418Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:01.419Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:01.420Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:01.433Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:01.444Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:01.444Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-055 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":31}
2025-07-12T07:49:01.445Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:01.447Z [DEBUG] - 🔧 Utility performance recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:01.448Z [DEBUG] - 🔧 Utility analysis completed for WCAG-055: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T07:49:01.449Z [DEBUG] - ⏱️ Check WCAG-055 completed in 50ms (success: true)
2025-07-12T07:49:01.450Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.454Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.456Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 58% (38/66)
2025-07-12T07:49:01.456Z [INFO] - ✅ Rule WCAG-055 completed: failed (0/100)
2025-07-12T07:49:01.458Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.462Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.462Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 58% (38/66)
2025-07-12T07:49:01.463Z [INFO] - 🔍 Executing rule: Motion Actuation (WCAG-056)
2025-07-12T07:49:01.464Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-056: Motion Actuation
2025-07-12T07:49:01.466Z [DEBUG] - 📁 Cache file valid: 4871a48558d3f2cd5e11bf922bb8c891.json (116min remaining)
2025-07-12T07:49:01.466Z [DEBUG] - 📁 Cache loaded from file: 4871a48558d3f2cd5e11bf922bb8c891.json (key: WCAG-056:053b13d2:add92319...)
2025-07-12T07:49:01.467Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-056:053b13d2:add92319...
2025-07-12T07:49:01.468Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:053b13d2:add92319... (accessed 2 times, age: 7464s)
2025-07-12T07:49:01.468Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-056
2025-07-12T07:49:01.472Z [DEBUG] - 📁 Cache file valid: 8ff67000f1a92dc4301713b6e619f281.json (116min remaining)
2025-07-12T07:49:01.474Z [DEBUG] - 📁 Cache loaded from file: 8ff67000f1a92dc4301713b6e619f281.json (key: WCAG-056:WCAG-056...)
2025-07-12T07:49:01.476Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-056:WCAG-056...
2025-07-12T07:49:01.477Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:WCAG-056... (accessed 2 times, age: 7464s)
2025-07-12T07:49:01.478Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-12T07:49:01.479Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-056 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:01.480Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:01.481Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:01.665Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:01.665Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-056 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":186}
2025-07-12T07:49:01.667Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:01.669Z [DEBUG] - 🔧 Utility performance recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:01.670Z [DEBUG] - 🔧 Utility analysis completed for WCAG-056: - {"utilitiesUsed":2,"errors":0,"executionTime":204}
2025-07-12T07:49:01.671Z [DEBUG] - ⏱️ Check WCAG-056 completed in 208ms (success: true)
2025-07-12T07:49:01.672Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.679Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.679Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 59% (39/66)
2025-07-12T07:49:01.680Z [INFO] - ✅ Rule WCAG-056 completed: passed (100/100)
2025-07-12T07:49:01.682Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.686Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:01.687Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 59% (39/66)
2025-07-12T07:49:01.688Z [INFO] - 🔍 Executing rule: Target Size Enhanced (WCAG-058)
2025-07-12T07:49:01.689Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting WCAG-058: Target Size Enhanced
2025-07-12T07:49:01.690Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-12T07:49:02.452Z [WARN] - ⚠️ [52f3c859-aeff-4274-8d32-62129992f841] WCAG-058 failed: 0.0% (threshold: 85%) → 0% credit - FAILED
2025-07-12T07:49:02.453Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Completed WCAG-058 in 763ms - Status: failed (0/100)
2025-07-12T07:49:02.456Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:WCAG-055... (accessed 3 times, age: 7465s)
2025-07-12T07:49:02.457Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-12T07:49:02.458Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-058 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:02.459Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:02.460Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:02.682Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:02.682Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-058 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":224}
2025-07-12T07:49:02.684Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:02.686Z [DEBUG] - 🔧 Utility performance recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:02.688Z [DEBUG] - 🔧 Utility analysis completed for WCAG-058: - {"utilitiesUsed":2,"errors":0,"executionTime":996}
2025-07-12T07:49:02.689Z [DEBUG] - ⏱️ Check WCAG-058 completed in 1001ms (success: true)
2025-07-12T07:49:02.690Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.696Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.696Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 61% (40/66)
2025-07-12T07:49:02.697Z [INFO] - ✅ Rule WCAG-058 completed: failed (0/100)
2025-07-12T07:49:02.698Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.702Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.704Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 61% (40/66)
2025-07-12T07:49:02.705Z [INFO] - 🔍 Executing rule: Concurrent Input Mechanisms (WCAG-059)
2025-07-12T07:49:02.707Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-059: Concurrent Input Mechanisms
2025-07-12T07:49:02.709Z [DEBUG] - 📁 Cache file valid: de67f57cc2635b0704bae41ccf557edd.json (116min remaining)
2025-07-12T07:49:02.709Z [DEBUG] - 📁 Cache loaded from file: de67f57cc2635b0704bae41ccf557edd.json (key: WCAG-059:053b13d2:add92319...)
2025-07-12T07:49:02.710Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-059:053b13d2:add92319...
2025-07-12T07:49:02.711Z [DEBUG] - ✅ Cache hit: rule:WCAG-059:053b13d2:add92319... (accessed 2 times, age: 7462s)
2025-07-12T07:49:02.712Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-059
2025-07-12T07:49:02.714Z [DEBUG] - 📁 Cache file valid: c40334838e5426b6fddc1e74ce9760a1.json (116min remaining)
2025-07-12T07:49:02.715Z [DEBUG] - 📁 Cache loaded from file: c40334838e5426b6fddc1e74ce9760a1.json (key: WCAG-059:WCAG-059...)
2025-07-12T07:49:02.716Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-059:WCAG-059...
2025-07-12T07:49:02.721Z [DEBUG] - ✅ Cache hit: rule:WCAG-059:WCAG-059... (accessed 2 times, age: 7462s)
2025-07-12T07:49:02.722Z [DEBUG] - 📋 Using cached evidence for WCAG-059
2025-07-12T07:49:02.723Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-059 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:02.725Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:02.726Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:02.758Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-059 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.45,"executionTime":35}
2025-07-12T07:49:02.758Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:02.760Z [DEBUG] - 🔧 Utility performance recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:02.763Z [DEBUG] - 🔧 Utility analysis completed for WCAG-059: - {"utilitiesUsed":2,"errors":0,"executionTime":53}
2025-07-12T07:49:02.764Z [DEBUG] - ⏱️ Check WCAG-059 completed in 59ms (success: true)
2025-07-12T07:49:02.765Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.771Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.771Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 62% (41/66)
2025-07-12T07:49:02.772Z [INFO] - ✅ Rule WCAG-059 completed: failed (0/100)
2025-07-12T07:49:02.773Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.777Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.779Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 62% (41/66)
2025-07-12T07:49:02.781Z [INFO] - 🔍 Executing rule: Unusual Words (WCAG-060)
2025-07-12T07:49:02.783Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-060: Unusual Words
2025-07-12T07:49:02.786Z [DEBUG] - 📁 Cache file valid: f33dbf5b167f7819cd8f5243d29a7d8d.json (116min remaining)
2025-07-12T07:49:02.786Z [DEBUG] - 📁 Cache loaded from file: f33dbf5b167f7819cd8f5243d29a7d8d.json (key: WCAG-060:053b13d2:add92319...)
2025-07-12T07:49:02.787Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-060:053b13d2:add92319...
2025-07-12T07:49:02.788Z [DEBUG] - ✅ Cache hit: rule:WCAG-060:053b13d2:add92319... (accessed 2 times, age: 7462s)
2025-07-12T07:49:02.789Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-060
2025-07-12T07:49:02.791Z [DEBUG] - 📁 Cache file valid: 6e0c6281873e8247f3f140619a454c39.json (116min remaining)
2025-07-12T07:49:02.791Z [DEBUG] - 📁 Cache loaded from file: 6e0c6281873e8247f3f140619a454c39.json (key: WCAG-060:WCAG-060...)
2025-07-12T07:49:02.792Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-060:WCAG-060...
2025-07-12T07:49:02.795Z [DEBUG] - ✅ Cache hit: rule:WCAG-060:WCAG-060... (accessed 2 times, age: 7462s)
2025-07-12T07:49:02.797Z [DEBUG] - 📋 Using cached evidence for WCAG-060
2025-07-12T07:49:02.798Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-060 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:02.800Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:02.801Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 11 times, age: 7550s)
2025-07-12T07:49:02.802Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:02.804Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:02.804Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:02.806Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:02.806Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:02.808Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:02.836Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:02.855Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:02.858Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-060 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":60}
2025-07-12T07:49:02.858Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:02.860Z [DEBUG] - 🔧 Utility performance recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:02.861Z [DEBUG] - 🔧 Utility analysis completed for WCAG-060: - {"utilitiesUsed":2,"errors":0,"executionTime":77}
2025-07-12T07:49:02.862Z [DEBUG] - ⏱️ Check WCAG-060 completed in 82ms (success: true)
2025-07-12T07:49:02.863Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.869Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.871Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 64% (42/66)
2025-07-12T07:49:02.872Z [INFO] - ✅ Rule WCAG-060 completed: failed (0/100)
2025-07-12T07:49:02.873Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.878Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.878Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 64% (42/66)
2025-07-12T07:49:02.879Z [INFO] - 🔍 Executing rule: Abbreviations (WCAG-061)
2025-07-12T07:49:02.880Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-061: Abbreviations
2025-07-12T07:49:02.883Z [DEBUG] - 📁 Cache file valid: 79b5cd7a70a41bc232e7bb4b6db82dfa.json (116min remaining)
2025-07-12T07:49:02.883Z [DEBUG] - 📁 Cache loaded from file: 79b5cd7a70a41bc232e7bb4b6db82dfa.json (key: WCAG-061:053b13d2:add92319...)
2025-07-12T07:49:02.884Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-061:053b13d2:add92319...
2025-07-12T07:49:02.885Z [DEBUG] - ✅ Cache hit: rule:WCAG-061:053b13d2:add92319... (accessed 2 times, age: 7450s)
2025-07-12T07:49:02.886Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-061
2025-07-12T07:49:02.890Z [DEBUG] - 📁 Cache file valid: 444acc81fee9b5e62cda4ea16fdaf9dc.json (116min remaining)
2025-07-12T07:49:02.891Z [DEBUG] - 📁 Cache loaded from file: 444acc81fee9b5e62cda4ea16fdaf9dc.json (key: WCAG-061:WCAG-061...)
2025-07-12T07:49:02.892Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-061:WCAG-061...
2025-07-12T07:49:02.894Z [DEBUG] - ✅ Cache hit: rule:WCAG-061:WCAG-061... (accessed 2 times, age: 7450s)
2025-07-12T07:49:02.895Z [DEBUG] - 📋 Using cached evidence for WCAG-061
2025-07-12T07:49:02.896Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-061 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:02.897Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:02.897Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 12 times, age: 7550s)
2025-07-12T07:49:02.898Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:02.900Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:02.900Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:02.903Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:02.905Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:02.907Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:02.927Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:02.947Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:02.949Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-061 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":53}
2025-07-12T07:49:02.949Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:02.951Z [DEBUG] - 🔧 Utility performance recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:02.952Z [DEBUG] - 🔧 Utility analysis completed for WCAG-061: - {"utilitiesUsed":2,"errors":0,"executionTime":70}
2025-07-12T07:49:02.953Z [DEBUG] - ⏱️ Check WCAG-061 completed in 74ms (success: true)
2025-07-12T07:49:02.954Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.960Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.961Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 65% (43/66)
2025-07-12T07:49:02.962Z [INFO] - ✅ Rule WCAG-061 completed: failed (0/100)
2025-07-12T07:49:02.963Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.967Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:02.968Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 65% (43/66)
2025-07-12T07:49:02.970Z [INFO] - 🔍 Executing rule: Reading Level (WCAG-062)
2025-07-12T07:49:02.972Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-062: Reading Level
2025-07-12T07:49:02.974Z [DEBUG] - 📁 Cache file valid: 42dc7144c5be663ce950fd291e4d98d7.json (116min remaining)
2025-07-12T07:49:02.974Z [DEBUG] - 📁 Cache loaded from file: 42dc7144c5be663ce950fd291e4d98d7.json (key: WCAG-062:053b13d2:add92319...)
2025-07-12T07:49:02.975Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-062:053b13d2:add92319...
2025-07-12T07:49:02.976Z [DEBUG] - ✅ Cache hit: rule:WCAG-062:053b13d2:add92319... (accessed 2 times, age: 7450s)
2025-07-12T07:49:02.977Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-062
2025-07-12T07:49:02.979Z [DEBUG] - 📁 Cache file valid: 55656632f66aff78067600822a1d12fd.json (116min remaining)
2025-07-12T07:49:02.979Z [DEBUG] - 📁 Cache loaded from file: 55656632f66aff78067600822a1d12fd.json (key: WCAG-062:WCAG-062...)
2025-07-12T07:49:02.981Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-062:WCAG-062...
2025-07-12T07:49:02.983Z [DEBUG] - ✅ Cache hit: rule:WCAG-062:WCAG-062... (accessed 2 times, age: 7450s)
2025-07-12T07:49:02.984Z [DEBUG] - 📋 Using cached evidence for WCAG-062
2025-07-12T07:49:02.987Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-062 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:02.988Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:02.989Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 13 times, age: 7550s)
2025-07-12T07:49:02.990Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:02.991Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:02.992Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:02.995Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:02.996Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:02.998Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:03.018Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:03.039Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:03.041Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-062 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":54}
2025-07-12T07:49:03.042Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.044Z [DEBUG] - 🔧 Utility performance recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.045Z [DEBUG] - 🔧 Utility analysis completed for WCAG-062: - {"utilitiesUsed":2,"errors":0,"executionTime":72}
2025-07-12T07:49:03.046Z [DEBUG] - ⏱️ Check WCAG-062 completed in 76ms (success: true)
2025-07-12T07:49:03.046Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.052Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.052Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 67% (44/66)
2025-07-12T07:49:03.053Z [INFO] - ✅ Rule WCAG-062 completed: failed (0/100)
2025-07-12T07:49:03.054Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.058Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.058Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 67% (44/66)
2025-07-12T07:49:03.059Z [INFO] - 🔍 Executing rule: Pronunciation (WCAG-063)
2025-07-12T07:49:03.061Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-063: Pronunciation
2025-07-12T07:49:03.065Z [DEBUG] - 📁 Cache file valid: 0c14c6b55c802116f5cf12801e8a61cb.json (119min remaining)
2025-07-12T07:49:03.066Z [DEBUG] - 📁 Cache loaded from file: 0c14c6b55c802116f5cf12801e8a61cb.json (key: WCAG-063:053b13d2:add92319...)
2025-07-12T07:49:03.067Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-063:053b13d2:add92319...
2025-07-12T07:49:03.067Z [DEBUG] - ✅ Cache hit: rule:WCAG-063:053b13d2:add92319... (accessed 2 times, age: 7277s)
2025-07-12T07:49:03.069Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-063
2025-07-12T07:49:03.071Z [DEBUG] - 📁 Cache file valid: 39507b86200ef80a538f4fcd9d085cfb.json (119min remaining)
2025-07-12T07:49:03.072Z [DEBUG] - 📁 Cache loaded from file: 39507b86200ef80a538f4fcd9d085cfb.json (key: WCAG-063:WCAG-063...)
2025-07-12T07:49:03.073Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-063:WCAG-063...
2025-07-12T07:49:03.074Z [DEBUG] - ✅ Cache hit: rule:WCAG-063:WCAG-063... (accessed 2 times, age: 7277s)
2025-07-12T07:49:03.074Z [DEBUG] - 📋 Using cached evidence for WCAG-063
2025-07-12T07:49:03.076Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-063 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:03.079Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:03.080Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 14 times, age: 7550s)
2025-07-12T07:49:03.081Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:03.083Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:03.083Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:03.085Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:03.086Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:03.087Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:03.109Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:03.132Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:03.137Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-063 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":62}
2025-07-12T07:49:03.137Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.138Z [DEBUG] - 🔧 Utility performance recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.139Z [DEBUG] - 🔧 Utility analysis completed for WCAG-063: - {"utilitiesUsed":2,"errors":0,"executionTime":78}
2025-07-12T07:49:03.140Z [DEBUG] - ⏱️ Check WCAG-063 completed in 81ms (success: true)
2025-07-12T07:49:03.141Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.147Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.147Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 68% (45/66)
2025-07-12T07:49:03.148Z [INFO] - ✅ Rule WCAG-063 completed: failed (0/100)
2025-07-12T07:49:03.149Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.154Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.154Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 68% (45/66)
2025-07-12T07:49:03.155Z [INFO] - 🔍 Executing rule: Change on Request (WCAG-064)
2025-07-12T07:49:03.157Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-064: Change on Request
2025-07-12T07:49:03.159Z [DEBUG] - 📁 Cache file valid: 5a40d51c15a824f433f94fee6332db5e.json (119min remaining)
2025-07-12T07:49:03.161Z [DEBUG] - 📁 Cache loaded from file: 5a40d51c15a824f433f94fee6332db5e.json (key: WCAG-064:053b13d2:add92319...)
2025-07-12T07:49:03.162Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-064:053b13d2:add92319...
2025-07-12T07:49:03.164Z [DEBUG] - ✅ Cache hit: rule:WCAG-064:053b13d2:add92319... (accessed 2 times, age: 7276s)
2025-07-12T07:49:03.165Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-064
2025-07-12T07:49:03.168Z [DEBUG] - 📁 Cache file valid: bd402f34a3490413e5e9d577c7f15e8f.json (119min remaining)
2025-07-12T07:49:03.168Z [DEBUG] - 📁 Cache loaded from file: bd402f34a3490413e5e9d577c7f15e8f.json (key: WCAG-064:WCAG-064...)
2025-07-12T07:49:03.169Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-064:WCAG-064...
2025-07-12T07:49:03.170Z [DEBUG] - ✅ Cache hit: rule:WCAG-064:WCAG-064... (accessed 2 times, age: 7276s)
2025-07-12T07:49:03.171Z [DEBUG] - 📋 Using cached evidence for WCAG-064
2025-07-12T07:49:03.174Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-064 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:03.176Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:03.177Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:03.361Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:03.361Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-064 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":187}
2025-07-12T07:49:03.363Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.365Z [DEBUG] - 🔧 Utility performance recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.366Z [DEBUG] - 🔧 Utility analysis completed for WCAG-064: - {"utilitiesUsed":2,"errors":0,"executionTime":208}
2025-07-12T07:49:03.367Z [DEBUG] - ⏱️ Check WCAG-064 completed in 212ms (success: true)
2025-07-12T07:49:03.367Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.374Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.375Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 70% (46/66)
2025-07-12T07:49:03.376Z [INFO] - ✅ Rule WCAG-064 completed: failed (0/100)
2025-07-12T07:49:03.377Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.382Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.382Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 70% (46/66)
2025-07-12T07:49:03.383Z [INFO] - 🔍 Executing rule: Help (WCAG-065)
2025-07-12T07:49:03.384Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-065: Help
2025-07-12T07:49:03.385Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:053b13d2:add92319... (accessed 3 times, age: 7506s)
2025-07-12T07:49:03.386Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-065
2025-07-12T07:49:03.386Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:WCAG-065... (accessed 3 times, age: 7506s)
2025-07-12T07:49:03.387Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-12T07:49:03.388Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-065 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:03.390Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:03.393Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:49:03.393Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 4 times, age: 10s)
2025-07-12T07:49:03.395Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:49:03.427Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:03.427Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-065 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":39}
2025-07-12T07:49:03.429Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-065: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.431Z [DEBUG] - 🔧 Utility performance recorded for WCAG-065: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.432Z [DEBUG] - 🔧 Utility analysis completed for WCAG-065: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T07:49:03.432Z [DEBUG] - ⏱️ Check WCAG-065 completed in 49ms (success: true)
2025-07-12T07:49:03.433Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.438Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.438Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 71% (47/66)
2025-07-12T07:49:03.439Z [INFO] - ✅ Rule WCAG-065 completed: passed (75/100)
2025-07-12T07:49:03.440Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.444Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.444Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 71% (47/66)
2025-07-12T07:49:03.445Z [INFO] - 🔍 Executing rule: Error Prevention Enhanced (WCAG-066)
2025-07-12T07:49:03.446Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-066: Error Prevention Enhanced
2025-07-12T07:49:03.448Z [DEBUG] - 📁 Cache file valid: 42a6a4719458cd8c2297ef4ce76e8acd.json (119min remaining)
2025-07-12T07:49:03.451Z [DEBUG] - 📁 Cache loaded from file: 42a6a4719458cd8c2297ef4ce76e8acd.json (key: WCAG-066:053b13d2:add92319...)
2025-07-12T07:49:03.452Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-066:053b13d2:add92319...
2025-07-12T07:49:03.453Z [DEBUG] - ✅ Cache hit: rule:WCAG-066:053b13d2:add92319... (accessed 2 times, age: 7275s)
2025-07-12T07:49:03.453Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-066
2025-07-12T07:49:03.455Z [DEBUG] - 📁 Cache file valid: d9087b62feeb8ebe1c97494a35155993.json (119min remaining)
2025-07-12T07:49:03.455Z [DEBUG] - 📁 Cache loaded from file: d9087b62feeb8ebe1c97494a35155993.json (key: WCAG-066:WCAG-066...)
2025-07-12T07:49:03.456Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-066:WCAG-066...
2025-07-12T07:49:03.457Z [DEBUG] - ✅ Cache hit: rule:WCAG-066:WCAG-066... (accessed 2 times, age: 7275s)
2025-07-12T07:49:03.458Z [DEBUG] - 📋 Using cached evidence for WCAG-066
2025-07-12T07:49:03.458Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-066 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:03.459Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:03.460Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:03.640Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:03.641Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-066 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":183}
2025-07-12T07:49:03.642Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.645Z [DEBUG] - 🔧 Utility performance recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.646Z [DEBUG] - 🔧 Utility analysis completed for WCAG-066: - {"utilitiesUsed":2,"errors":0,"executionTime":197}
2025-07-12T07:49:03.647Z [DEBUG] - ⏱️ Check WCAG-066 completed in 202ms (success: true)
2025-07-12T07:49:03.647Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.653Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.654Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 73% (48/66)
2025-07-12T07:49:03.655Z [INFO] - ✅ Rule WCAG-066 completed: failed (0/100)
2025-07-12T07:49:03.655Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.660Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.661Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 73% (48/66)
2025-07-12T07:49:03.662Z [INFO] - 🔍 Executing rule: Status Messages (WCAG-057)
2025-07-12T07:49:03.663Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-057: Status Messages
2025-07-12T07:49:03.665Z [DEBUG] - 📁 Cache file valid: 45d26f3295717a92f8bd1aec0a3c8557.json (119min remaining)
2025-07-12T07:49:03.665Z [DEBUG] - 📁 Cache loaded from file: 45d26f3295717a92f8bd1aec0a3c8557.json (key: WCAG-057:053b13d2:add92319...)
2025-07-12T07:49:03.666Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-057:053b13d2:add92319...
2025-07-12T07:49:03.667Z [DEBUG] - ✅ Cache hit: rule:WCAG-057:053b13d2:add92319... (accessed 2 times, age: 7275s)
2025-07-12T07:49:03.668Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-057
2025-07-12T07:49:03.670Z [DEBUG] - 📁 Cache file valid: d5679298ae7d049fc06accf18537cdf9.json (119min remaining)
2025-07-12T07:49:03.670Z [DEBUG] - 📁 Cache loaded from file: d5679298ae7d049fc06accf18537cdf9.json (key: WCAG-057:WCAG-057...)
2025-07-12T07:49:03.671Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-057:WCAG-057...
2025-07-12T07:49:03.671Z [DEBUG] - ✅ Cache hit: rule:WCAG-057:WCAG-057... (accessed 2 times, age: 7275s)
2025-07-12T07:49:03.672Z [DEBUG] - 📋 Using cached evidence for WCAG-057
2025-07-12T07:49:03.673Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-057 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:03.674Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:03.675Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 15 times, age: 7551s)
2025-07-12T07:49:03.678Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:03.680Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:03.681Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:03.681Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:03.683Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:03.684Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:03.685Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:03.693Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:03.710Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:03.711Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-057 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":38}
2025-07-12T07:49:03.712Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.714Z [DEBUG] - 🔧 Utility performance recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.714Z [DEBUG] - 🔧 Utility analysis completed for WCAG-057: - {"utilitiesUsed":3,"errors":0,"executionTime":49}
2025-07-12T07:49:03.715Z [DEBUG] - ⏱️ Check WCAG-057 completed in 53ms (success: true)
2025-07-12T07:49:03.716Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.721Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.722Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 74% (49/66)
2025-07-12T07:49:03.723Z [INFO] - ✅ Rule WCAG-057 completed: passed (100/100)
2025-07-12T07:49:03.725Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.733Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.733Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 74% (49/66)
2025-07-12T07:49:03.734Z [INFO] - 🔍 Executing rule: Skip Links (WCAG-047)
2025-07-12T07:49:03.736Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-047: Skip Links
2025-07-12T07:49:03.739Z [DEBUG] - 📁 Cache file valid: 5003fdcdca0d68384074f375ad9bbced.json (119min remaining)
2025-07-12T07:49:03.739Z [DEBUG] - 📁 Cache loaded from file: 5003fdcdca0d68384074f375ad9bbced.json (key: WCAG-047:053b13d2:add92319...)
2025-07-12T07:49:03.740Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-047:053b13d2:add92319...
2025-07-12T07:49:03.741Z [DEBUG] - ✅ Cache hit: rule:WCAG-047:053b13d2:add92319... (accessed 2 times, age: 7272s)
2025-07-12T07:49:03.742Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-047
2025-07-12T07:49:03.745Z [DEBUG] - 📁 Cache file valid: 306a6611ec109dccc0c3cc13f1967422.json (119min remaining)
2025-07-12T07:49:03.745Z [DEBUG] - 📁 Cache loaded from file: 306a6611ec109dccc0c3cc13f1967422.json (key: WCAG-047:WCAG-047...)
2025-07-12T07:49:03.746Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-047:WCAG-047...
2025-07-12T07:49:03.746Z [DEBUG] - ✅ Cache hit: rule:WCAG-047:WCAG-047... (accessed 2 times, age: 7272s)
2025-07-12T07:49:03.747Z [DEBUG] - 📋 Using cached evidence for WCAG-047
2025-07-12T07:49:03.748Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-047 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T07:49:03.749Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:03.750Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:03.752Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:03.944Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:03.944Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-047 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":196}
2025-07-12T07:49:03.946Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:03.948Z [DEBUG] - 🔧 Utility performance recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:03.949Z [DEBUG] - 🔧 Utility analysis completed for WCAG-047: - {"utilitiesUsed":3,"errors":0,"executionTime":212}
2025-07-12T07:49:03.950Z [DEBUG] - ⏱️ Check WCAG-047 completed in 216ms (success: true)
2025-07-12T07:49:03.951Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.957Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.957Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 76% (50/66)
2025-07-12T07:49:03.958Z [INFO] - ✅ Rule WCAG-047 completed: failed (0/100)
2025-07-12T07:49:03.959Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.965Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:03.966Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 76% (50/66)
2025-07-12T07:49:03.967Z [INFO] - 🔍 Executing rule: Enhanced Focus Management (WCAG-048)
2025-07-12T07:49:03.968Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-048: Enhanced Focus Management
2025-07-12T07:49:03.970Z [DEBUG] - 📁 Cache file valid: 49b79b6e25ca864115e6cd6beea15c0b.json (119min remaining)
2025-07-12T07:49:03.971Z [DEBUG] - 📁 Cache loaded from file: 49b79b6e25ca864115e6cd6beea15c0b.json (key: WCAG-048:053b13d2:add92319...)
2025-07-12T07:49:03.972Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-048:053b13d2:add92319...
2025-07-12T07:49:03.973Z [DEBUG] - ✅ Cache hit: rule:WCAG-048:053b13d2:add92319... (accessed 2 times, age: 7272s)
2025-07-12T07:49:03.973Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-048
2025-07-12T07:49:03.974Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:WCAG-056... (accessed 3 times, age: 7466s)
2025-07-12T07:49:03.975Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-12T07:49:03.976Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-048 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T07:49:03.976Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:03.978Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:03.982Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:04.059Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T07:49:04.060Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T07:49:04.187Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:04.187Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-048 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":211}
2025-07-12T07:49:04.189Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:04.191Z [DEBUG] - 🔧 Utility performance recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:04.191Z [DEBUG] - 🔧 Utility analysis completed for WCAG-048: - {"utilitiesUsed":3,"errors":0,"executionTime":222}
2025-07-12T07:49:04.192Z [DEBUG] - ⏱️ Check WCAG-048 completed in 225ms (success: true)
2025-07-12T07:49:04.193Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.200Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.200Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 77% (51/66)
2025-07-12T07:49:04.201Z [INFO] - ✅ Rule WCAG-048 completed: failed (0/100)
2025-07-12T07:49:04.202Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.207Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.208Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 77% (51/66)
2025-07-12T07:49:04.209Z [INFO] - 🔍 Executing rule: Link Context (WCAG-049)
2025-07-12T07:49:04.210Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-049: Link Context
2025-07-12T07:49:04.212Z [DEBUG] - 📁 Cache file valid: 008eb54f4d432fcc3f7258c8e293b10f.json (119min remaining)
2025-07-12T07:49:04.212Z [DEBUG] - 📁 Cache loaded from file: 008eb54f4d432fcc3f7258c8e293b10f.json (key: WCAG-049:053b13d2:add92319...)
2025-07-12T07:49:04.213Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-049:053b13d2:add92319...
2025-07-12T07:49:04.214Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:053b13d2:add92319... (accessed 2 times, age: 7271s)
2025-07-12T07:49:04.215Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-049
2025-07-12T07:49:04.216Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:WCAG-049... (accessed 3 times, age: 7473s)
2025-07-12T07:49:04.217Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-12T07:49:04.218Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-049 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T07:49:04.219Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:04.220Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:04.224Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:04.415Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:04.415Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-049 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":197}
2025-07-12T07:49:04.419Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:04.419Z [DEBUG] - 🔧 Utility performance recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:04.420Z [DEBUG] - 🔧 Utility analysis completed for WCAG-049: - {"utilitiesUsed":3,"errors":0,"executionTime":210}
2025-07-12T07:49:04.421Z [DEBUG] - ⏱️ Check WCAG-049 completed in 213ms (success: true)
2025-07-12T07:49:04.422Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.426Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.426Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 79% (52/66)
2025-07-12T07:49:04.427Z [INFO] - ✅ Rule WCAG-049 completed: failed (0/100)
2025-07-12T07:49:04.428Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.432Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.435Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 79% (52/66)
2025-07-12T07:49:04.436Z [INFO] - 🔍 Executing rule: Language of Page (WCAG-024)
2025-07-12T07:49:04.437Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-024: Language of Page
2025-07-12T07:49:04.439Z [DEBUG] - 📁 Cache file valid: 5c1e9d7eec6a203c208331b409edb500.json (119min remaining)
2025-07-12T07:49:04.439Z [DEBUG] - 📁 Cache loaded from file: 5c1e9d7eec6a203c208331b409edb500.json (key: WCAG-024:053b13d2:add92319...)
2025-07-12T07:49:04.440Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-024:053b13d2:add92319...
2025-07-12T07:49:04.441Z [DEBUG] - ✅ Cache hit: rule:WCAG-024:053b13d2:add92319... (accessed 2 times, age: 7271s)
2025-07-12T07:49:04.442Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-024
2025-07-12T07:49:04.444Z [DEBUG] - 📁 Cache file valid: cccac7bc70a2c5813d5114243f43e9fe.json (119min remaining)
2025-07-12T07:49:04.444Z [DEBUG] - 📁 Cache loaded from file: cccac7bc70a2c5813d5114243f43e9fe.json (key: WCAG-024:WCAG-024...)
2025-07-12T07:49:04.445Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-024:WCAG-024...
2025-07-12T07:49:04.446Z [DEBUG] - ✅ Cache hit: rule:WCAG-024:WCAG-024... (accessed 2 times, age: 7271s)
2025-07-12T07:49:04.451Z [DEBUG] - 📋 Using cached evidence for WCAG-024
2025-07-12T07:49:04.453Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-024 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:04.454Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:04.455Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:04.491Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:04.656Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:04.657Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-024 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":204}
2025-07-12T07:49:04.659Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:04.660Z [DEBUG] - 🔧 Utility performance recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:04.661Z [DEBUG] - 🔧 Utility analysis completed for WCAG-024: - {"utilitiesUsed":2,"errors":0,"executionTime":222}
2025-07-12T07:49:04.662Z [DEBUG] - ⏱️ Check WCAG-024 completed in 226ms (success: true)
2025-07-12T07:49:04.663Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.669Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.669Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 80% (53/66)
2025-07-12T07:49:04.670Z [INFO] - ✅ Rule WCAG-024 completed: passed (100/100)
2025-07-12T07:49:04.671Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.676Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.676Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 80% (53/66)
2025-07-12T07:49:04.677Z [INFO] - 🔍 Executing rule: Page Content Landmarks (WCAG-025)
2025-07-12T07:49:04.678Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-025: Landmarks
2025-07-12T07:49:04.683Z [DEBUG] - 📁 Cache file valid: 04790cccdd869ffab5f77a1751f4a4f7.json (119min remaining)
2025-07-12T07:49:04.683Z [DEBUG] - 📁 Cache loaded from file: 04790cccdd869ffab5f77a1751f4a4f7.json (key: WCAG-025:053b13d2:add92319...)
2025-07-12T07:49:04.684Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-025:053b13d2:add92319...
2025-07-12T07:49:04.685Z [DEBUG] - ✅ Cache hit: rule:WCAG-025:053b13d2:add92319... (accessed 2 times, age: 7268s)
2025-07-12T07:49:04.686Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-025
2025-07-12T07:49:04.688Z [DEBUG] - 📁 Cache file valid: b7395b0b8421574ba7f2e384a624c76e.json (119min remaining)
2025-07-12T07:49:04.688Z [DEBUG] - 📁 Cache loaded from file: b7395b0b8421574ba7f2e384a624c76e.json (key: WCAG-025:WCAG-025...)
2025-07-12T07:49:04.689Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-025:WCAG-025...
2025-07-12T07:49:04.690Z [DEBUG] - ✅ Cache hit: rule:WCAG-025:WCAG-025... (accessed 2 times, age: 7268s)
2025-07-12T07:49:04.691Z [DEBUG] - 📋 Using cached evidence for WCAG-025
2025-07-12T07:49:04.692Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-025 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:04.693Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:04.696Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 16 times, age: 7552s)
2025-07-12T07:49:04.697Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:04.698Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:04.700Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:04.700Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:04.702Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:04.703Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:04.704Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:04.716Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:04.735Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:04.735Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-025 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":43}
2025-07-12T07:49:04.737Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:04.739Z [DEBUG] - 🔧 Utility performance recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:04.740Z [DEBUG] - 🔧 Utility analysis completed for WCAG-025: - {"utilitiesUsed":3,"errors":0,"executionTime":60}
2025-07-12T07:49:04.741Z [DEBUG] - ⏱️ Check WCAG-025 completed in 64ms (success: true)
2025-07-12T07:49:04.741Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.747Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.747Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 82% (54/66)
2025-07-12T07:49:04.748Z [INFO] - ✅ Rule WCAG-025 completed: passed (75/100)
2025-07-12T07:49:04.749Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.757Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.757Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 82% (54/66)
2025-07-12T07:49:04.758Z [INFO] - 🔍 Executing rule: Link Purpose (In Context) (WCAG-026)
2025-07-12T07:49:04.759Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-026: Link Purpose
2025-07-12T07:49:04.761Z [DEBUG] - 📁 Cache file valid: e87613a3972eabcdb786083f8cc131bf.json (119min remaining)
2025-07-12T07:49:04.761Z [DEBUG] - 📁 Cache loaded from file: e87613a3972eabcdb786083f8cc131bf.json (key: WCAG-026:053b13d2:add92319...)
2025-07-12T07:49:04.762Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-026:053b13d2:add92319...
2025-07-12T07:49:04.762Z [DEBUG] - ✅ Cache hit: rule:WCAG-026:053b13d2:add92319... (accessed 2 times, age: 7268s)
2025-07-12T07:49:04.763Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-026
2025-07-12T07:49:04.766Z [DEBUG] - 📁 Cache file valid: 4463855cd76edcf65e62e0abbfe2bdaf.json (119min remaining)
2025-07-12T07:49:04.768Z [DEBUG] - 📁 Cache loaded from file: 4463855cd76edcf65e62e0abbfe2bdaf.json (key: WCAG-026:WCAG-026...)
2025-07-12T07:49:04.769Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-026:WCAG-026...
2025-07-12T07:49:04.770Z [DEBUG] - ✅ Cache hit: rule:WCAG-026:WCAG-026... (accessed 2 times, age: 7268s)
2025-07-12T07:49:04.771Z [DEBUG] - 📋 Using cached evidence for WCAG-026
2025-07-12T07:49:04.772Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-026 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:04.772Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:04.773Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 17 times, age: 7552s)
2025-07-12T07:49:04.774Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:04.776Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:04.776Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:04.777Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:04.778Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:04.779Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:04.792Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:04.803Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:04.803Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-026 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":31}
2025-07-12T07:49:04.804Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:04.805Z [DEBUG] - 🔧 Utility performance recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:04.806Z [DEBUG] - 🔧 Utility analysis completed for WCAG-026: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T07:49:04.806Z [DEBUG] - ⏱️ Check WCAG-026 completed in 48ms (success: true)
2025-07-12T07:49:04.807Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.812Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.813Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 83% (55/66)
2025-07-12T07:49:04.814Z [INFO] - ✅ Rule WCAG-026 completed: passed (86/100)
2025-07-12T07:49:04.816Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.821Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:04.821Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 83% (55/66)
2025-07-12T07:49:04.822Z [INFO] - 🔍 Executing rule: No Keyboard Trap (WCAG-027)
2025-07-12T07:49:04.823Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-027: No Keyboard Trap
2025-07-12T07:49:04.825Z [DEBUG] - 📁 Cache file valid: 566fd9979fbf5569e1a137c821bf54f5.json (119min remaining)
2025-07-12T07:49:04.825Z [DEBUG] - 📁 Cache loaded from file: 566fd9979fbf5569e1a137c821bf54f5.json (key: WCAG-027:053b13d2:add92319...)
2025-07-12T07:49:04.826Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-027:053b13d2:add92319...
2025-07-12T07:49:04.827Z [DEBUG] - ✅ Cache hit: rule:WCAG-027:053b13d2:add92319... (accessed 2 times, age: 7265s)
2025-07-12T07:49:04.830Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-027
2025-07-12T07:49:04.832Z [DEBUG] - 📁 Cache file valid: d5fb2d75ab65b657ffac81435872fadc.json (119min remaining)
2025-07-12T07:49:04.833Z [DEBUG] - 📁 Cache loaded from file: d5fb2d75ab65b657ffac81435872fadc.json (key: WCAG-027:WCAG-027...)
2025-07-12T07:49:04.834Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-027:WCAG-027...
2025-07-12T07:49:04.834Z [DEBUG] - ✅ Cache hit: rule:WCAG-027:WCAG-027... (accessed 2 times, age: 7265s)
2025-07-12T07:49:04.835Z [DEBUG] - 📋 Using cached evidence for WCAG-027
2025-07-12T07:49:04.836Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-027 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:04.836Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:04.838Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:04.995Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:04.995Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-027 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":159}
2025-07-12T07:49:04.997Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:04.999Z [DEBUG] - 🔧 Utility performance recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.000Z [DEBUG] - 🔧 Utility analysis completed for WCAG-027: - {"utilitiesUsed":2,"errors":0,"executionTime":175}
2025-07-12T07:49:05.001Z [DEBUG] - ⏱️ Check WCAG-027 completed in 179ms (success: true)
2025-07-12T07:49:05.001Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.007Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.008Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 85% (56/66)
2025-07-12T07:49:05.008Z [INFO] - ✅ Rule WCAG-027 completed: failed (0/100)
2025-07-12T07:49:05.009Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.014Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.016Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 85% (56/66)
2025-07-12T07:49:05.017Z [INFO] - 🔍 Executing rule: Bypass Blocks (WCAG-028)
2025-07-12T07:49:05.018Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-028: Bypass Blocks
2025-07-12T07:49:05.020Z [DEBUG] - 📁 Cache file valid: 00899b8aae33a74dcbf491eb938cbcbd.json (119min remaining)
2025-07-12T07:49:05.020Z [DEBUG] - 📁 Cache loaded from file: 00899b8aae33a74dcbf491eb938cbcbd.json (key: WCAG-028:053b13d2:add92319...)
2025-07-12T07:49:05.021Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-028:053b13d2:add92319...
2025-07-12T07:49:05.022Z [DEBUG] - ✅ Cache hit: rule:WCAG-028:053b13d2:add92319... (accessed 2 times, age: 7265s)
2025-07-12T07:49:05.022Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-028
2025-07-12T07:49:05.024Z [DEBUG] - 📁 Cache file valid: 0372d08002092230cca2af735c55eddb.json (119min remaining)
2025-07-12T07:49:05.025Z [DEBUG] - 📁 Cache loaded from file: 0372d08002092230cca2af735c55eddb.json (key: WCAG-028:WCAG-028...)
2025-07-12T07:49:05.025Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-028:WCAG-028...
2025-07-12T07:49:05.026Z [DEBUG] - ✅ Cache hit: rule:WCAG-028:WCAG-028... (accessed 2 times, age: 7265s)
2025-07-12T07:49:05.027Z [DEBUG] - 📋 Using cached evidence for WCAG-028
2025-07-12T07:49:05.028Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-028 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:05.031Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:05.032Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 18 times, age: 7552s)
2025-07-12T07:49:05.033Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:05.035Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:05.035Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:05.036Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:05.037Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:05.038Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:05.058Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:05.079Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:05.079Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-028 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":51}
2025-07-12T07:49:05.080Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.080Z [DEBUG] - 🔧 Utility performance recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.081Z [DEBUG] - 🔧 Utility analysis completed for WCAG-028: - {"utilitiesUsed":2,"errors":0,"executionTime":63}
2025-07-12T07:49:05.082Z [DEBUG] - ⏱️ Check WCAG-028 completed in 65ms (success: true)
2025-07-12T07:49:05.083Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.088Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.088Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 86% (57/66)
2025-07-12T07:49:05.089Z [INFO] - ✅ Rule WCAG-028 completed: passed (80/100)
2025-07-12T07:49:05.093Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.098Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.099Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 86% (57/66)
2025-07-12T07:49:05.100Z [INFO] - 🔍 Executing rule: Page Titled (WCAG-029)
2025-07-12T07:49:05.101Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-029: Page Titled
2025-07-12T07:49:05.103Z [DEBUG] - 📁 Cache file valid: 0622341e9cae4a03c93eb461d777af4f.json (119min remaining)
2025-07-12T07:49:05.103Z [DEBUG] - 📁 Cache loaded from file: 0622341e9cae4a03c93eb461d777af4f.json (key: WCAG-029:053b13d2:add92319...)
2025-07-12T07:49:05.104Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-029:053b13d2:add92319...
2025-07-12T07:49:05.104Z [DEBUG] - ✅ Cache hit: rule:WCAG-029:053b13d2:add92319... (accessed 2 times, age: 7264s)
2025-07-12T07:49:05.107Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-029
2025-07-12T07:49:05.108Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:WCAG-013... (accessed 3 times, age: 7516s)
2025-07-12T07:49:05.110Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-12T07:49:05.110Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-029 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:05.111Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:05.112Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:49:05.113Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 5 times, age: 12s)
2025-07-12T07:49:05.114Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:49:05.149Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:05.150Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-029 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":40}
2025-07-12T07:49:05.152Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-029: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.154Z [DEBUG] - 🔧 Utility performance recorded for WCAG-029: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.154Z [DEBUG] - 🔧 Utility analysis completed for WCAG-029: - {"utilitiesUsed":2,"errors":0,"executionTime":52}
2025-07-12T07:49:05.155Z [DEBUG] - ⏱️ Check WCAG-029 completed in 55ms (success: true)
2025-07-12T07:49:05.156Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.161Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.161Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 88% (58/66)
2025-07-12T07:49:05.161Z [INFO] - ✅ Rule WCAG-029 completed: passed (90/100)
2025-07-12T07:49:05.162Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.167Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.168Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 88% (58/66)
2025-07-12T07:49:05.169Z [INFO] - 🔍 Executing rule: Labels or Instructions (WCAG-030)
2025-07-12T07:49:05.170Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-030: Labels or Instructions
2025-07-12T07:49:05.171Z [DEBUG] - 📁 Cache file valid: 318873c4e04faa8d89a794c60564b6a5.json (119min remaining)
2025-07-12T07:49:05.172Z [DEBUG] - 📁 Cache loaded from file: 318873c4e04faa8d89a794c60564b6a5.json (key: WCAG-030:053b13d2:add92319...)
2025-07-12T07:49:05.173Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-030:053b13d2:add92319...
2025-07-12T07:49:05.173Z [DEBUG] - ✅ Cache hit: rule:WCAG-030:053b13d2:add92319... (accessed 2 times, age: 7259s)
2025-07-12T07:49:05.174Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-030
2025-07-12T07:49:05.176Z [DEBUG] - 📁 Cache file valid: 9f51555c21b6b178e061e7b0de6eaa08.json (119min remaining)
2025-07-12T07:49:05.176Z [DEBUG] - 📁 Cache loaded from file: 9f51555c21b6b178e061e7b0de6eaa08.json (key: WCAG-030:WCAG-030...)
2025-07-12T07:49:05.177Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-030:WCAG-030...
2025-07-12T07:49:05.177Z [DEBUG] - ✅ Cache hit: rule:WCAG-030:WCAG-030... (accessed 2 times, age: 7259s)
2025-07-12T07:49:05.178Z [DEBUG] - 📋 Using cached evidence for WCAG-030
2025-07-12T07:49:05.179Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-030 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:05.180Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:05.181Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 19 times, age: 7552s)
2025-07-12T07:49:05.181Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:05.187Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:05.187Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:05.189Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:05.190Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:05.191Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:05.198Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:05.210Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:05.210Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-030 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":31}
2025-07-12T07:49:05.211Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.212Z [DEBUG] - 🔧 Utility performance recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.213Z [DEBUG] - 🔧 Utility analysis completed for WCAG-030: - {"utilitiesUsed":2,"errors":0,"executionTime":42}
2025-07-12T07:49:05.216Z [DEBUG] - ⏱️ Check WCAG-030 completed in 47ms (success: true)
2025-07-12T07:49:05.219Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.224Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.224Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 89% (59/66)
2025-07-12T07:49:05.225Z [INFO] - ✅ Rule WCAG-030 completed: failed (0/100)
2025-07-12T07:49:05.226Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.231Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.231Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 89% (59/66)
2025-07-12T07:49:05.232Z [INFO] - 🔍 Executing rule: Error Suggestion (WCAG-031)
2025-07-12T07:49:05.236Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-031: Error Suggestion
2025-07-12T07:49:05.237Z [DEBUG] - 📁 Cache file valid: afd4e8553d2527a61d9bf9674019f478.json (119min remaining)
2025-07-12T07:49:05.238Z [DEBUG] - 📁 Cache loaded from file: afd4e8553d2527a61d9bf9674019f478.json (key: WCAG-031:053b13d2:add92319...)
2025-07-12T07:49:05.238Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-031:053b13d2:add92319...
2025-07-12T07:49:05.239Z [DEBUG] - ✅ Cache hit: rule:WCAG-031:053b13d2:add92319... (accessed 2 times, age: 7254s)
2025-07-12T07:49:05.240Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-031
2025-07-12T07:49:05.242Z [DEBUG] - 📁 Cache file valid: 7f773532f54478ddb2be2ece7257b22c.json (119min remaining)
2025-07-12T07:49:05.242Z [DEBUG] - 📁 Cache loaded from file: 7f773532f54478ddb2be2ece7257b22c.json (key: WCAG-031:WCAG-031...)
2025-07-12T07:49:05.243Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-031:WCAG-031...
2025-07-12T07:49:05.246Z [DEBUG] - ✅ Cache hit: rule:WCAG-031:WCAG-031... (accessed 2 times, age: 7254s)
2025-07-12T07:49:05.248Z [DEBUG] - 📋 Using cached evidence for WCAG-031
2025-07-12T07:49:05.250Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-031 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:05.251Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:05.253Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:49:05.253Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 6 times, age: 12s)
2025-07-12T07:49:05.255Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:49:05.288Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:05.288Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-031 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":38}
2025-07-12T07:49:05.290Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-031: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.292Z [DEBUG] - 🔧 Utility performance recorded for WCAG-031: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.293Z [DEBUG] - 🔧 Utility analysis completed for WCAG-031: - {"utilitiesUsed":2,"errors":0,"executionTime":58}
2025-07-12T07:49:05.294Z [DEBUG] - ⏱️ Check WCAG-031 completed in 62ms (success: true)
2025-07-12T07:49:05.295Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.301Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.302Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 91% (60/66)
2025-07-12T07:49:05.303Z [INFO] - ✅ Rule WCAG-031 completed: failed (0/100)
2025-07-12T07:49:05.303Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.310Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.310Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 91% (60/66)
2025-07-12T07:49:05.311Z [INFO] - 🔍 Executing rule: Error Prevention (WCAG-032)
2025-07-12T07:49:05.313Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-032: Error Prevention
2025-07-12T07:49:05.315Z [DEBUG] - 📁 Cache file valid: 44c080abba45af2ff4a6ac832f660888.json (119min remaining)
2025-07-12T07:49:05.316Z [DEBUG] - 📁 Cache loaded from file: 44c080abba45af2ff4a6ac832f660888.json (key: WCAG-032:053b13d2:add92319...)
2025-07-12T07:49:05.317Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-032:053b13d2:add92319...
2025-07-12T07:49:05.318Z [DEBUG] - ✅ Cache hit: rule:WCAG-032:053b13d2:add92319... (accessed 2 times, age: 7248s)
2025-07-12T07:49:05.319Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-032
2025-07-12T07:49:05.324Z [DEBUG] - 📁 Cache file valid: e401202f7944ff2eec72489ab235f330.json (119min remaining)
2025-07-12T07:49:05.325Z [DEBUG] - 📁 Cache loaded from file: e401202f7944ff2eec72489ab235f330.json (key: WCAG-032:WCAG-032...)
2025-07-12T07:49:05.326Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-032:WCAG-032...
2025-07-12T07:49:05.326Z [DEBUG] - ✅ Cache hit: rule:WCAG-032:WCAG-032... (accessed 2 times, age: 7248s)
2025-07-12T07:49:05.327Z [DEBUG] - 📋 Using cached evidence for WCAG-032
2025-07-12T07:49:05.329Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-032 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:05.329Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:05.331Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T07:49:05.533Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:05.534Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-032 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":206}
2025-07-12T07:49:05.536Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.538Z [DEBUG] - 🔧 Utility performance recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.538Z [DEBUG] - 🔧 Utility analysis completed for WCAG-032: - {"utilitiesUsed":2,"errors":0,"executionTime":224}
2025-07-12T07:49:05.539Z [DEBUG] - ⏱️ Check WCAG-032 completed in 228ms (success: true)
2025-07-12T07:49:05.540Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.546Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.546Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 92% (61/66)
2025-07-12T07:49:05.547Z [INFO] - ✅ Rule WCAG-032 completed: failed (0/100)
2025-07-12T07:49:05.548Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.553Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.554Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 92% (61/66)
2025-07-12T07:49:05.555Z [INFO] - 🔍 Executing rule: Audio-only and Video-only (WCAG-033)
2025-07-12T07:49:05.556Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-033: Audio-only and Video-only
2025-07-12T07:49:05.558Z [DEBUG] - 📁 Cache file valid: 949b30f4e5c95acf0bd0f077c7ba9124.json (119min remaining)
2025-07-12T07:49:05.558Z [DEBUG] - 📁 Cache loaded from file: 949b30f4e5c95acf0bd0f077c7ba9124.json (key: WCAG-033:053b13d2:add92319...)
2025-07-12T07:49:05.559Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-033:053b13d2:add92319...
2025-07-12T07:49:05.560Z [DEBUG] - ✅ Cache hit: rule:WCAG-033:053b13d2:add92319... (accessed 2 times, age: 7248s)
2025-07-12T07:49:05.560Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-033
2025-07-12T07:49:05.562Z [DEBUG] - 📁 Cache file valid: 49c6f131e0988b238b6cd294050269cb.json (119min remaining)
2025-07-12T07:49:05.562Z [DEBUG] - 📁 Cache loaded from file: 49c6f131e0988b238b6cd294050269cb.json (key: WCAG-033:WCAG-033...)
2025-07-12T07:49:05.563Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-033:WCAG-033...
2025-07-12T07:49:05.564Z [DEBUG] - ✅ Cache hit: rule:WCAG-033:WCAG-033... (accessed 2 times, age: 7248s)
2025-07-12T07:49:05.565Z [DEBUG] - 📋 Using cached evidence for WCAG-033
2025-07-12T07:49:05.565Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-033 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:05.566Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:05.568Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:49:05.572Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 7 times, age: 13s)
2025-07-12T07:49:05.573Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:49:05.604Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:05.604Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-033 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":39}
2025-07-12T07:49:05.606Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-033: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.608Z [DEBUG] - 🔧 Utility performance recorded for WCAG-033: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.609Z [DEBUG] - 🔧 Utility analysis completed for WCAG-033: - {"utilitiesUsed":2,"errors":0,"executionTime":51}
2025-07-12T07:49:05.609Z [DEBUG] - ⏱️ Check WCAG-033 completed in 54ms (success: true)
2025-07-12T07:49:05.610Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.615Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.615Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 94% (62/66)
2025-07-12T07:49:05.616Z [INFO] - ✅ Rule WCAG-033 completed: passed (100/100)
2025-07-12T07:49:05.617Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.622Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.622Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 94% (62/66)
2025-07-12T07:49:05.623Z [INFO] - 🔍 Executing rule: Audio Description (WCAG-034)
2025-07-12T07:49:05.624Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-034: Audio Description
2025-07-12T07:49:05.626Z [DEBUG] - 📁 Cache file valid: 475e798d534b8f0ef7bb04c71985d0af.json (119min remaining)
2025-07-12T07:49:05.626Z [DEBUG] - 📁 Cache loaded from file: 475e798d534b8f0ef7bb04c71985d0af.json (key: WCAG-034:053b13d2:add92319...)
2025-07-12T07:49:05.627Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-034:053b13d2:add92319...
2025-07-12T07:49:05.628Z [DEBUG] - ✅ Cache hit: rule:WCAG-034:053b13d2:add92319... (accessed 2 times, age: 7247s)
2025-07-12T07:49:05.632Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-034
2025-07-12T07:49:05.635Z [DEBUG] - 📁 Cache file valid: d66098f699797d33163e6b8427f7847e.json (119min remaining)
2025-07-12T07:49:05.635Z [DEBUG] - 📁 Cache loaded from file: d66098f699797d33163e6b8427f7847e.json (key: WCAG-034:WCAG-034...)
2025-07-12T07:49:05.636Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-034:WCAG-034...
2025-07-12T07:49:05.637Z [DEBUG] - ✅ Cache hit: rule:WCAG-034:WCAG-034... (accessed 2 times, age: 7247s)
2025-07-12T07:49:05.638Z [DEBUG] - 📋 Using cached evidence for WCAG-034
2025-07-12T07:49:05.639Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-034 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:05.639Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:05.641Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T07:49:05.642Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 8 times, age: 13s)
2025-07-12T07:49:05.644Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T07:49:05.677Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:05.678Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-034 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":39}
2025-07-12T07:49:05.680Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-034: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.682Z [DEBUG] - 🔧 Utility performance recorded for WCAG-034: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.683Z [DEBUG] - 🔧 Utility analysis completed for WCAG-034: - {"utilitiesUsed":2,"errors":0,"executionTime":57}
2025-07-12T07:49:05.684Z [DEBUG] - ⏱️ Check WCAG-034 completed in 61ms (success: true)
2025-07-12T07:49:05.685Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.691Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.691Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 95% (63/66)
2025-07-12T07:49:05.692Z [INFO] - ✅ Rule WCAG-034 completed: passed (100/100)
2025-07-12T07:49:05.693Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.701Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.701Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 95% (63/66)
2025-07-12T07:49:05.702Z [INFO] - 🔍 Executing rule: Multiple Ways (WCAG-035)
2025-07-12T07:49:05.703Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-035: Multiple Ways
2025-07-12T07:49:05.705Z [DEBUG] - 📁 Cache file valid: cc45a01e4bffb1e837dd3935974aa92b.json (119min remaining)
2025-07-12T07:49:05.706Z [DEBUG] - 📁 Cache loaded from file: cc45a01e4bffb1e837dd3935974aa92b.json (key: WCAG-035:053b13d2:add92319...)
2025-07-12T07:49:05.707Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-035:053b13d2:add92319...
2025-07-12T07:49:05.708Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:053b13d2:add92319... (accessed 2 times, age: 7245s)
2025-07-12T07:49:05.709Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-035
2025-07-12T07:49:05.712Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:WCAG-035... (accessed 3 times, age: 7514s)
2025-07-12T07:49:05.713Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-12T07:49:05.715Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-035 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T07:49:05.716Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:05.717Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T07:49:05.910Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T07:49:05.910Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-035 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":195}
2025-07-12T07:49:05.912Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.914Z [DEBUG] - 🔧 Utility performance recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.915Z [DEBUG] - 🔧 Utility analysis completed for WCAG-035: - {"utilitiesUsed":2,"errors":0,"executionTime":210}
2025-07-12T07:49:05.916Z [DEBUG] - ⏱️ Check WCAG-035 completed in 214ms (success: true)
2025-07-12T07:49:05.917Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.923Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.923Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 97% (64/66)
2025-07-12T07:49:05.924Z [INFO] - ✅ Rule WCAG-035 completed: passed (100/100)
2025-07-12T07:49:05.925Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.931Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.931Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 97% (64/66)
2025-07-12T07:49:05.934Z [INFO] - 🔍 Executing rule: Headings and Labels (WCAG-036)
2025-07-12T07:49:05.935Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-036: Headings and Labels
2025-07-12T07:49:05.937Z [DEBUG] - 📁 Cache file valid: 154e205869bd5401ba0ec01a183a7960.json (189min remaining)
2025-07-12T07:49:05.938Z [DEBUG] - 📁 Cache loaded from file: 154e205869bd5401ba0ec01a183a7960.json (key: WCAG-036:053b13d2:add92319...)
2025-07-12T07:49:05.939Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-036:053b13d2:add92319...
2025-07-12T07:49:05.940Z [DEBUG] - ✅ Cache hit: rule:WCAG-036:053b13d2:add92319... (accessed 2 times, age: 3072s)
2025-07-12T07:49:05.941Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-036
2025-07-12T07:49:05.944Z [DEBUG] - 📁 Cache file valid: 0d417ffff4e46e5e78395b1c8b5fb28a.json (119min remaining)
2025-07-12T07:49:05.945Z [DEBUG] - 📁 Cache loaded from file: 0d417ffff4e46e5e78395b1c8b5fb28a.json (key: WCAG-036:WCAG-036...)
2025-07-12T07:49:05.946Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-036:WCAG-036...
2025-07-12T07:49:05.948Z [DEBUG] - ✅ Cache hit: rule:WCAG-036:WCAG-036... (accessed 2 times, age: 7241s)
2025-07-12T07:49:05.949Z [DEBUG] - 📋 Using cached evidence for WCAG-036
2025-07-12T07:49:05.950Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-036 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:05.951Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:05.952Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 20 times, age: 7553s)
2025-07-12T07:49:05.953Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T07:49:05.955Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:05.955Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:05.956Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:05.957Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:05.958Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:05.968Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:05.979Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T07:49:05.980Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-036 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":30}
2025-07-12T07:49:05.980Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-036: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:05.983Z [DEBUG] - 🔧 Utility performance recorded for WCAG-036: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:05.984Z [DEBUG] - 🔧 Utility analysis completed for WCAG-036: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T07:49:05.985Z [DEBUG] - ⏱️ Check WCAG-036 completed in 51ms (success: true)
2025-07-12T07:49:05.987Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.992Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.992Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 98% (65/66)
2025-07-12T07:49:05.993Z [INFO] - ✅ Rule WCAG-036 completed: passed (85/100)
2025-07-12T07:49:05.994Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.998Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:05.999Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 98% (65/66)
2025-07-12T07:49:06.001Z [INFO] - 🔍 Executing rule: Language of Parts (WCAG-038)
2025-07-12T07:49:06.002Z [INFO] - 🔍 [52f3c859-aeff-4274-8d32-62129992f841] Starting enhanced WCAG-038: Language of Parts
2025-07-12T07:49:06.004Z [DEBUG] - 📁 Cache file valid: d6f9f1148034ce678e20a72e06c80357.json (189min remaining)
2025-07-12T07:49:06.004Z [DEBUG] - 📁 Cache loaded from file: d6f9f1148034ce678e20a72e06c80357.json (key: WCAG-038:053b13d2:add92319...)
2025-07-12T07:49:06.005Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-038:053b13d2:add92319...
2025-07-12T07:49:06.006Z [DEBUG] - ✅ Cache hit: rule:WCAG-038:053b13d2:add92319... (accessed 2 times, age: 3072s)
2025-07-12T07:49:06.007Z [INFO] - 🎯 [52f3c859-aeff-4274-8d32-62129992f841] Cache hit for WCAG-038
2025-07-12T07:49:06.009Z [DEBUG] - 📁 Cache file valid: 80f25140fbdeab47c10554bc75405e69.json (119min remaining)
2025-07-12T07:49:06.009Z [DEBUG] - 📁 Cache loaded from file: 80f25140fbdeab47c10554bc75405e69.json (key: WCAG-038:WCAG-038...)
2025-07-12T07:49:06.010Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-038:WCAG-038...
2025-07-12T07:49:06.013Z [DEBUG] - ✅ Cache hit: rule:WCAG-038:WCAG-038... (accessed 2 times, age: 7240s)
2025-07-12T07:49:06.014Z [DEBUG] - 📋 Using cached evidence for WCAG-038
2025-07-12T07:49:06.016Z [DEBUG] - 🔧 [52f3c859-aeff-4274-8d32-62129992f841] Starting utility analysis for WCAG-038 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T07:49:06.017Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T07:49:06.018Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 21 times, age: 7553s)
2025-07-12T07:49:06.019Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T07:49:06.020Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T07:49:06.021Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T07:49:06.022Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T07:49:06.023Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T07:49:06.026Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T07:49:06.046Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T07:49:06.065Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T07:49:06.069Z [DEBUG] - ✅ [52f3c859-aeff-4274-8d32-62129992f841] Utility analysis completed for WCAG-038 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":53}
2025-07-12T07:49:06.069Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-038: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T07:49:06.070Z [DEBUG] - 🔧 Utility performance recorded for WCAG-038: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T07:49:06.072Z [DEBUG] - 🔧 Utility analysis completed for WCAG-038: - {"utilitiesUsed":2,"errors":0,"executionTime":68}
2025-07-12T07:49:06.073Z [DEBUG] - ⏱️ Check WCAG-038 completed in 72ms (success: true)
2025-07-12T07:49:06.074Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:06.079Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> running
2025-07-12T07:49:06.079Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 100% (66/66)
2025-07-12T07:49:06.080Z [INFO] - ✅ Rule WCAG-038 completed: passed (90/100)
2025-07-12T07:49:06.081Z [INFO] - ✅ All checks completed: 66 results
2025-07-12T07:49:06.082Z [INFO] - 📊 Final Cache Performance Report: - {"totalRequests":155,"hits":155,"misses":0,"hitRate":"100.0%","totalEntries":177,"totalSize":"6.60MB","evictions":0}
2025-07-12T07:49:06.085Z [INFO] - 🚀 Cache Performance Benefits: - {"hitRate":"100.0%","estimatedTimeSaved":"7750ms","cacheEfficiency":"Excellent"}
🔍 WCAG Scoring Debug - Input Results: [
  {
    ruleId: 'WCAG-001',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-002',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-003',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-004',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0763
  },
  {
    ruleId: 'WCAG-005',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-006',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-007',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-008',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0534
  },
  {
    ruleId: 'WCAG-009',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-010',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-011',
    status: 'passed',
    score: 96,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-012',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-013',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-014',
    status: 'passed',
    score: 81,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-015',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-016',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-017',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-018',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-019',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '3.0',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-020',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-021',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '3.0',
    weight: 0.0229
  },
  {
    ruleId: 'WCAG-022',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.05
  },
  {
    ruleId: 'WCAG-023',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.03
  },
  {
    ruleId: 'WCAG-044',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-045',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-046',
    status: 'passed',
    score: 99,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-037',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-039',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-040',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-041',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-042',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-043',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-050',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-051',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-052',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-053',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-054',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-055',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-056',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-058',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-059',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-060',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-061',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-062',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-063',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-064',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-065',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-066',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-057',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-047',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-048',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-049',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-024',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-025',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-026',
    status: 'passed',
    score: 86,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-027',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0916
  },
  {
    ruleId: 'WCAG-028',
    status: 'passed',
    score: 80,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-029',
    status: 'passed',
    score: 90,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-030',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0534
  },
  {
    ruleId: 'WCAG-031',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-032',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-033',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-034',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-035',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-036',
    status: 'passed',
    score: 85,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-038',
    status: 'passed',
    score: 90,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0535
  }
]
📊 Rule WCAG-001: score=0/100 (0.0%), weight=0.008
📊 Rule WCAG-002: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-003: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-004: score=100/100 (100.0%), weight=0.010
📊 Rule WCAG-005: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-006: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-007: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-008: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-009: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-010: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-011: score=96/100 (96.0%), weight=0.004
📊 Rule WCAG-012: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-013: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-014: score=81/100 (81.0%), weight=0.006
📊 Rule WCAG-015: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-016: score=0/100 (0.0%), weight=0.003
📊 Rule WCAG-017: score=0/100 (0.0%), weight=0.001
📊 Rule WCAG-018: score=0/100 (0.0%), weight=0.001
📊 Rule WCAG-019: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-020: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-021: score=75/100 (75.0%), weight=0.001
📊 Rule WCAG-022: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-023: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-044: score=75/100 (75.0%), weight=0.012
📊 Rule WCAG-045: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-046: score=99/100 (99.0%), weight=0.008
📊 Rule WCAG-037: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-039: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-040: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-041: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-042: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-043: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-050: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-051: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-052: score=75/100 (75.0%), weight=0.008
📊 Rule WCAG-053: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-054: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-055: score=0/100 (0.0%), weight=0.008
📊 Rule WCAG-056: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-058: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-059: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-060: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-061: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-062: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-063: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-064: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-065: score=75/100 (75.0%), weight=0.002
📊 Rule WCAG-066: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-057: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-047: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-048: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-049: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-024: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-025: score=75/100 (75.0%), weight=0.009
📊 Rule WCAG-026: score=86/100 (86.0%), weight=0.011
📊 Rule WCAG-027: score=0/100 (0.0%), weight=0.016
📊 Rule WCAG-028: score=80/100 (80.0%), weight=0.011
📊 Rule WCAG-029: score=90/100 (90.0%), weight=0.011
📊 Rule WCAG-030: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-031: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-032: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-033: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-034: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-035: score=100/100 (100.0%), weight=0.009
📊 Rule WCAG-036: score=85/100 (85.0%), weight=0.009
📊 Rule WCAG-038: score=90/100 (90.0%), weight=0.007
🎯 WCAG Final Score Calculation:
      - Total Weighted Score: 15.77
      - Total Weight: 0.436
      - Final Score: 36%
      - Rules Processed: 66/66
      - Passed Rules: 25
      - Failed Rules: 41
2025-07-12T07:49:06.185Z [INFO] - 💾 Saving WCAG scan result: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.187Z [INFO] - 📊 Scan summary: {"scanId":"52f3c859-aeff-4274-8d32-62129992f841","totalRules":66,"overallScore":36,"levelAchieved":"None","riskLevel":"Critical"}
2025-07-12T07:49:06.188Z [INFO] - 💾 Saving WCAG scan result: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.298Z [INFO] - ✅ WCAG scan result saved: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.298Z [INFO] - ✅ WCAG scan result saved successfully: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.300Z [INFO] - 📝 Storing 1 manual review items for scan 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.306Z [INFO] - ✅ Stored 1 manual review items for scan 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.307Z [INFO] - ✅ Stored 1 manual review items for scan 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.307Z [INFO] - 📝 Updating WCAG scan status: 52f3c859-aeff-4274-8d32-62129992f841 -> completed
2025-07-12T07:49:06.312Z [INFO] - ✅ WCAG scan status updated: 52f3c859-aeff-4274-8d32-62129992f841 -> completed
2025-07-12T07:49:06.312Z [INFO] - 📈 Scan 52f3c859-aeff-4274-8d32-62129992f841: 100% (66/66)
2025-07-12T07:49:06.313Z [INFO] - ✅ Comprehensive WCAG scan completed: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.314Z [INFO] - 📊 Overall Score: 36% | Level: None | Risk: Critical
2025-07-12T07:49:06.316Z [INFO] - 📈 New performance baseline established - {"performanceScore":100,"averageCheckDuration":179.06060606060606,"memoryPeakMB":478}
2025-07-12T07:49:06.317Z [INFO] - 📊 Performance report generated for scan: 52f3c859-aeff-4274-8d32-62129992f841 - {"duration":22279,"checksExecuted":66,"successRate":100,"memoryPeak":478,"performanceScore":100}
2025-07-12T07:49:06.318Z [INFO] - 📊 Performance report for scan 52f3c859-aeff-4274-8d32-62129992f841: - {"duration":22279,"performanceScore":100,"memoryPeak":478,"recommendations":["Low browser pool efficiency - consider increasing pool size"]}
2025-07-12T07:49:06.431Z [DEBUG] - ✅ Released page back to pool for scan: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:06.432Z [DEBUG] - 📊 Unregistered active scan: 52f3c859-aeff-4274-8d32-62129992f841 (total: 0)
2025-07-12T07:49:06.434Z [INFO] - ✅ [14418698-1780-4e6f-b5c6-9a84c4295ed0] WCAG scan completed successfully: 52f3c859-aeff-4274-8d32-62129992f841
2025-07-12T07:49:09.067Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T07:49:09.068Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":0,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2