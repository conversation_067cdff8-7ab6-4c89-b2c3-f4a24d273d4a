'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { listScans } from '@/lib/api';
import { Scan as SharedScan } from '@/backend-types/index';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, PlusCircle } from 'lucide-react';

interface ProcessedScan extends SharedScan {
  _displayableStandards: string;
  _formattedCreatedAt: string;
}

const formatDateForList = (dateString?: string) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleDateString();
};

const ScansPage = () => {
  const { authenticated, loading: authLoading } = useAuth();
  const [scans, setScans] = useState<ProcessedScan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (authLoading) return;

    if (!authenticated) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    listScans()
      .then((data: SharedScan[]) => {
        const processedScans = data.map((scan) => ({
          ...scan,
          _displayableStandards: scan.standards_scanned
            ? scan.standards_scanned.map((s) => String(s).trim().toUpperCase()).join(', ')
            : 'N/A',
          _formattedCreatedAt: formatDateForList(scan.created_at),
        }));
        setScans(processedScans);
      })
      .catch((err) => {
        setError(err.response?.data?.error || err.message || 'Failed to load scans.');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [authenticated, authLoading]);

  if (authLoading || (isLoading && authenticated)) {
    return (
      <div
        className="flex items-center justify-center min-h-[calc(100vh-8rem)]"
        role="status"
        aria-live="polite"
      >
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <span className="ml-4 text-lg">Loading scans...</span>
      </div>
    );
  }

  if (!authenticated) {
    return (
      <div
        className="flex flex-col items-center justify-center min-h-[calc(100vh-8rem)]"
        role="status"
        aria-live="polite"
      >
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>Please log in to view your compliance scans.</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/auth/login">Go to Login</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="flex flex-col items-center justify-center min-h-[calc(100vh-8rem)]"
        role="alert"
        aria-live="assertive"
      >
        <Card className="w-full max-w-md text-center bg-destructive/10 border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Scans</CardTitle>
            <CardDescription className="text-destructive-foreground">{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="destructive" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Your Scans</h1>
        <div className="flex gap-2">
          <Button variant="secondary" asChild>
            <Link href="/dashboard/scan/test-hipaa">🧪 Test Enhanced HIPAA</Link>
          </Button>
          <Button asChild>
            <Link href="/dashboard/scan/new">
              <PlusCircle className="mr-2 h-5 w-5" />
              New Scan
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Scan History</CardTitle>
          <CardDescription>A list of your submitted compliance scans.</CardDescription>
        </CardHeader>
        <CardContent>
          {scans.length === 0 ? (
            <div className="text-center py-10">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  vectorEffect="non-scaling-stroke"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-semibold text-foreground">No scans found</h3>
              <p className="mt-4 text-muted-foreground" role="status">
                You haven&apos;t submitted any scans yet.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/dashboard/scan/new">
                    <PlusCircle className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                    Submit Your First Scan
                  </Link>
                </Button>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableCaption>A list of your recent compliance scans.</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40%]">URL</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Standards</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {scans.map((scan) => (
                    <TableRow key={scan.id}>
                      <TableCell className="font-medium truncate max-w-xs" title={scan.url}>
                        {scan.url}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            scan.status === 'failed'
                              ? 'destructive'
                              : scan.status === 'in_progress'
                                ? 'secondary'
                                : 'outline'
                          }
                          className={
                            scan.status === 'completed' ? 'bg-success text-success-foreground' : ''
                          }
                        >
                          {scan.status.charAt(0).toUpperCase() + scan.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>{scan._displayableStandards}</TableCell>
                      <TableCell>{scan._formattedCreatedAt}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/dashboard/scans/${scan.id}`}>View Details</Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ScansPage;
