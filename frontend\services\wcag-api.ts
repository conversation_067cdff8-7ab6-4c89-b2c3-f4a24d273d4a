/**
 * WCAG API Service
 * Frontend service for WCAG API communication
 */

import axios, { AxiosResponse } from 'axios';
import {
  WcagScanConfig,
  WcagScanResult,
  WcagScanFormData,
  ScanProgressInfo,
  QueueStatusInfo,
} from '../types/wcag';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  requestId: string;
  processingTime: number;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
    context?: string;
  };
  requestId: string;
  processingTime: number;
}

export interface ScanListResponse {
  scans: Array<{
    scanId: string;
    targetUrl: string;
    status: string;
    overallScore?: number;
    levelAchieved?: string;
    riskLevel?: string;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number; // Count only, no scoring
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface WcagDashboardData {
  overview: {
    totalScans: number;
    completedScans: number;
    runningScans: number;
    failedScans: number;
    averageScore: number;
    complianceRate: number;
  };
  metrics: {
    riskDistribution: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
    levelDistribution: {
      AAA: number;
      AA: number;
      A: number;
      FAIL: number;
    };
    trendsData: Array<{
      date: string;
      score: number;
      level: string;
    }>;
  };
  recentScans: Array<{
    scanId: string;
    targetUrl: string;
    status: string;
    overallScore?: number;
    levelAchieved?: string;
    riskLevel?: string;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: string;
    action: string;
    targetUrl: string;
    timestamp: string;
    score?: number;
    level?: string;
  }>;
}

class WcagApiService {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL =
      process.env.NEXT_PUBLIC_BACKEND_API_URL ||
      process.env.NEXT_PUBLIC_API_BASE_URL ||
      'http://localhost:3001/api/v1';
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = null;
    delete axios.defaults.headers.common['Authorization'];
  }

  /**
   * Get authentication token from Keycloak
   */
  private async getAuthToken(): Promise<string> {
    try {
      // Try to get token from Keycloak instance
      if (typeof window !== 'undefined') {
        const { getKeycloakInstance } = await import('@/lib/keycloak');
        const keycloak = getKeycloakInstance();

        if (keycloak && keycloak.authenticated && keycloak.token) {
          // Update token if it's about to expire
          await keycloak.updateToken(30);
          return keycloak.token;
        }
      }

      // Fallback to stored token for development
      return this.authToken || localStorage.getItem('authToken') || '';
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn('Failed to get auth token:', error);
      return this.authToken || localStorage.getItem('authToken') || '';
    }
  }

  /**
   * Get headers with authentication
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await this.getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Start a new WCAG scan
   */
  async startScan(formData: WcagScanFormData): Promise<WcagScanResult> {
    try {
      const scanConfig: WcagScanConfig = {
        targetUrl: formData.targetUrl,
        scanOptions: {
          enableContrastAnalysis: formData.enableContrastAnalysis,
          enableKeyboardTesting: formData.enableKeyboardTesting,
          enableFocusAnalysis: formData.enableFocusAnalysis,
          enableSemanticValidation: formData.enableSemanticValidation,
          enableManualReview: formData.enableManualReview,
          wcagVersion: formData.wcagVersion,
          level: formData.level,
          maxPages: formData.maxPages,
          timeout: formData.timeout,
          // Enhanced Phase 1-3 Options

          enableSmartCache: formData.enableSmartCache,
          enablePerformanceMonitoring: formData.enablePerformanceMonitoring,
          enableCMSDetection: formData.enableCMSDetection,
          enableEcommerceAnalysis: formData.enableEcommerceAnalysis,
          enableFrameworkDetection: formData.enableFrameworkDetection,
          enableMediaAnalysis: formData.enableMediaAnalysis,
          enableVPSOptimization: formData.enableVPSOptimization,
          // Advanced Options (from orphaned component)
          includeHiddenElements: formData.includeHiddenElements,
          enableScreenshots: formData.enableScreenshots,
          customRules: formData.customRules,
          excludeRules: formData.excludeRules,
          // Authentication Options (from orphaned component)
          requiresAuth: formData.requiresAuth,
          authType: formData.authType,
          authCredentials: formData.authCredentials,
          customHeaders: formData.customHeaders,
        },
        userId: '', // Will be set by backend from auth token
        requestId: '', // Will be generated by backend
      };

      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<WcagScanResult>> = await axios.post(
        `${this.baseURL}/compliance/wcag/scan`,
        scanConfig,
        { headers },
      );

      if (!response.data.success) {
        throw new Error('Scan request failed');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to start WCAG scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get dashboard data with metrics and recent scans
   */
  async getDashboardData(): Promise<WcagDashboardData> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<WcagDashboardData>> = await axios.get(
        `${this.baseURL}/compliance/wcag/dashboard`,
        { headers },
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch dashboard data');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch WCAG dashboard data:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get list of user's scans
   */
  async getScans(
    params: {
      page?: number;
      limit?: number;
      status?: string;
      sortBy?: string;
      sortOrder?: string;
    } = {},
  ): Promise<ScanListResponse> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<ScanListResponse>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans`,
        {
          params,
          headers,
        },
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch scans');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch WCAG scans:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get detailed scan results
   */
  async getScanDetails(scanId: string): Promise<WcagScanResult> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<WcagScanResult>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans/${scanId}`,
        { headers },
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch scan details');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch scan details:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Delete a scan
   */
  async deleteScan(scanId: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<{ message: string }>> = await axios.delete(
        `${this.baseURL}/compliance/wcag/scans/${scanId}`,
        { headers },
      );

      if (!response.data.success) {
        throw new Error('Failed to delete scan');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to delete scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Export scan results
   */
  async exportScan(
    scanId: string,
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence?: boolean;
      includeRecommendations?: boolean;
      includeManualReviewItems?: boolean; // Include manual review tracking
    } = {},
  ): Promise<Blob> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.post(
        `${this.baseURL}/compliance/wcag/export`,
        {
          scanId,
          format,
          ...options,
        },
        {
          responseType: 'blob',
          headers,
        },
      );

      return response.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to export scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Download exported file
   */
  async downloadExport(
    scanId: string,
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence?: boolean;
      includeRecommendations?: boolean;
      includeManualReviewItems?: boolean;
    } = {},
  ): Promise<void> {
    try {
      const blob = await this.exportScan(scanId, format, options);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Set filename based on format
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `wcag-${format === 'pdf' ? 'report' : 'data'}-${scanId.slice(0, 8)}-${timestamp}.${format}`;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to download export:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get scan progress (for real-time updates)
   */
  async getScanProgress(scanId: string): Promise<ScanProgressInfo | null> {
    try {
      // This would typically use WebSocket or Server-Sent Events
      // For now, implement as polling endpoint
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<ScanProgressInfo | null>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans/${scanId}/progress`,
        { headers },
      );

      if (!response.data.success) {
        return null;
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch scan progress:', error);
      return null;
    }
  }

  /**
   * Get queue status
   */
  async getQueueStatus(): Promise<QueueStatusInfo> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<QueueStatusInfo>> = await axios.get(
        `${this.baseURL}/compliance/wcag/queue/status`,
        { headers },
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch queue status');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch queue status:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Cancel a scan
   */
  async cancelScan(scanId: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<{ message: string }>> = await axios.post(
        `${this.baseURL}/compliance/wcag/scans/${scanId}/cancel`,
        {},
        { headers },
      );

      if (!response.data.success) {
        throw new Error('Failed to cancel scan');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to cancel scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Check API health
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/compliance/wcag/health`);
      return response.data.success;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Update manual review status for a specific check
   */
  async updateManualReview(
    scanId: string,
    ruleId: string,
    selector: string,
    assessment: string,
    notes?: string,
    reviewerName?: string,
  ): Promise<{ success: boolean; updatedScore?: number; message?: string }> {
    try {
      const headers = await this.getAuthHeaders();
      const response: AxiosResponse<ApiResponse<{ updatedScore?: number; message?: string }>> =
        await axios.put(
          `${this.baseURL}/compliance/wcag/scan/${scanId}/manual-review`,
          {
            ruleId,
            selector,
            assessment,
            notes: notes || '',
            reviewerName: reviewerName || 'Accessibility Reviewer',
          },
          { headers },
        );

      if (!response.data.success) {
        throw new Error('Failed to update manual review');
      }

      return {
        success: true,
        updatedScore: response.data.data?.updatedScore,
        message: response.data.data?.message,
      };
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to update WCAG manual review:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Handle API errors consistently
   */
  private handleApiError(
    error: unknown /* eslint-disable-line @typescript-eslint/no-explicit-any */,
  ): Error {
    if (axios.isAxiosError(error)) {
      if (error.response?.data?.error) {
        const apiError = error.response.data as ApiError;
        return new Error(apiError.error.message || 'API request failed');
      }

      if (error.response?.status === 401) {
        return new Error('Authentication required - please log in');
      }

      if (error.response?.status === 403) {
        return new Error('Insufficient permissions for WCAG scanning');
      }

      if (error.response?.status === 429) {
        return new Error('Rate limit exceeded - please wait before making more requests');
      }

      return new Error(error.message || 'Network error occurred');
    }

    return error instanceof Error ? error : new Error('Unknown error occurred');
  }
}

// Export singleton instance
export const wcagApiService = new WcagApiService();
export default wcagApiService;
