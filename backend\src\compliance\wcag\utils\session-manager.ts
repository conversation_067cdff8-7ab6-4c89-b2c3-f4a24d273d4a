import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

/**
 * Session Manager for coordinating page lifecycle and preventing conflicts
 */
export class SessionManager {
  private static instance: SessionManager;
  private activeSessions: Map<string, { page: Page; lastActivity: number; checkCount: number }> = new Map();
  private sessionLocks: Map<string, Promise<void>> = new Map();

  private constructor() {}

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Register a page session for tracking
   */
  public registerSession(page: Page, scanId: string): void {
    const sessionKey = this.getSessionKey(page, scanId);
    this.activeSessions.set(sessionKey, {
      page,
      lastActivity: Date.now(),
      checkCount: 0
    });
    logger.debug(`📝 Registered session: ${sessionKey}`);
  }

  /**
   * Update session activity
   */
  public updateActivity(page: Page, scanId: string): void {
    const sessionKey = this.getSessionKey(page, scanId);
    const session = this.activeSessions.get(sessionKey);
    if (session) {
      session.lastActivity = Date.now();
      session.checkCount++;
      logger.debug(`🔄 Updated session activity: ${sessionKey} (checks: ${session.checkCount})`);
    }
  }

  /**
   * Check if a page session is safe to use (not being cleaned up)
   */
  public async isSessionSafe(page: Page, scanId: string): Promise<boolean> {
    try {
      if (page.isClosed()) {
        return false;
      }

      const sessionKey = this.getSessionKey(page, scanId);
      const session = this.activeSessions.get(sessionKey);
      
      if (!session) {
        return false;
      }

      // Check if page is responsive
      await page.evaluate(() => document.readyState);
      return true;
    } catch (error) {
      logger.warn(`⚠️ Session safety check failed:`, { error: (error as Error).message });
      return false;
    }
  }

  /**
   * Wait for any ongoing operations on a session to complete
   */
  public async waitForSessionReady(page: Page, scanId: string): Promise<void> {
    const sessionKey = this.getSessionKey(page, scanId);
    const existingLock = this.sessionLocks.get(sessionKey);
    
    if (existingLock) {
      logger.debug(`⏳ Waiting for session to be ready: ${sessionKey}`);
      await existingLock;
    }
  }

  /**
   * Lock a session for exclusive access
   */
  public async lockSession<T>(page: Page, scanId: string, operation: () => Promise<T>): Promise<T> {
    const sessionKey = this.getSessionKey(page, scanId);
    
    // Wait for any existing lock
    await this.waitForSessionReady(page, scanId);
    
    // Create new lock
    const lockPromise = this.executeWithLock(page, scanId, operation);
    this.sessionLocks.set(sessionKey, lockPromise.then(() => {}));
    
    try {
      const result = await lockPromise;
      return result;
    } finally {
      this.sessionLocks.delete(sessionKey);
    }
  }

  /**
   * Execute operation with session lock
   */
  private async executeWithLock<T>(page: Page, scanId: string, operation: () => Promise<T>): Promise<T> {
    const sessionKey = this.getSessionKey(page, scanId);
    
    try {
      // Verify session is still safe before operation
      const isSafe = await this.isSessionSafe(page, scanId);
      if (!isSafe) {
        throw new Error(`Session ${sessionKey} is not safe for operation`);
      }

      this.updateActivity(page, scanId);
      const result = await operation();
      
      return result;
    } catch (error) {
      logger.error(`❌ Session operation failed for ${sessionKey}:`, { error: (error as Error).message });
      throw error;
    }
  }

  /**
   * Clean up a specific session
   */
  public cleanupSession(page: Page, scanId: string): void {
    const sessionKey = this.getSessionKey(page, scanId);
    this.activeSessions.delete(sessionKey);
    this.sessionLocks.delete(sessionKey);
    logger.debug(`🧹 Cleaned up session: ${sessionKey}`);
  }

  /**
   * Clean up sessions for a specific page URL
   */
  public cleanupPageState(pageUrl: string): void {
    const keysToDelete: string[] = [];

    for (const sessionKey of this.activeSessions.keys()) {
      if (sessionKey.includes(pageUrl)) {
        keysToDelete.push(sessionKey);
      }
    }

    for (const key of keysToDelete) {
      this.activeSessions.delete(key);
      this.sessionLocks.delete(key);
    }

    if (keysToDelete.length > 0) {
      logger.debug(`🧹 Cleaned up ${keysToDelete.length} sessions for page: ${pageUrl}`);
    }
  }

  /**
   * Clean up all sessions
   */
  public cleanupAllSessions(): void {
    this.activeSessions.clear();
    this.sessionLocks.clear();
    logger.debug('🧹 Cleaned up all sessions');
  }

  /**
   * Get session statistics
   */
  public getSessionStats(): { activeSessions: number; lockedSessions: number } {
    return {
      activeSessions: this.activeSessions.size,
      lockedSessions: this.sessionLocks.size
    };
  }

  /**
   * Generate session key
   */
  private getSessionKey(page: Page, scanId: string): string {
    try {
      const url = page.url();
      return `${scanId}:${url}`;
    } catch (error) {
      return `${scanId}:unknown`;
    }
  }

  /**
   * Clean up stale sessions (older than 10 minutes)
   */
  public cleanupStaleSessions(): void {
    const now = Date.now();
    const staleThreshold = 10 * 60 * 1000; // 10 minutes

    for (const [sessionKey, session] of this.activeSessions.entries()) {
      if (now - session.lastActivity > staleThreshold) {
        logger.debug(`🧹 Cleaning up stale session: ${sessionKey}`);
        this.activeSessions.delete(sessionKey);
        this.sessionLocks.delete(sessionKey);
      }
    }
  }
}
