'use client';

/**
 * WCAG Scan Details Page
 * Page for viewing detailed results of a specific WCAG scan
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Download, AlertTriangle, FileText, ClipboardCheck } from 'lucide-react';
import WcagScanOverview from '@/components/wcag/WcagScanOverview';
import { WcagManualReviewDashboard } from '@/components/wcag/WcagManualReviewDashboard';
import { HeroFixButton } from '@/components/wcag/FixIssuesButton';
import { EnhancedFailureEvidenceDisplay } from '@/components/wcag/EnhancedFailureEvidenceDisplay';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

/**
 * WCAG Scan Details Content Component
 */
const WcagScanDetailsContent: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const scanId = params.scanId as string;
  const state = useWcagState();
  const actions = useWcagActions();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  const loadScanDetails = useCallback(async () => {
    try {
      setLoading(true);
      await actions.fetchScanDetails(scanId);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error loading scan details:', error);
    } finally {
      setLoading(false);
    }
  }, [actions.fetchScanDetails, scanId]);

  // Load scan details
  useEffect(() => {
    if (mounted && scanId) {
      loadScanDetails();
    }
  }, [mounted, scanId, loadScanDetails]);

  const handleBack = () => {
    router.push('/dashboard/wcag/history');
  };

  const handleExport = async (format: 'pdf' | 'json' | 'csv') => {
    try {
      await actions.exportScan(scanId, format);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error exporting scan:', error);
    }
  };

  const handleExportClick = () => {
    // Default to PDF export, or could open a dialog to choose format
    handleExport('pdf');
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Loading Scan Details...
            </h1>
          </div>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!state.currentScan) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Scan Not Found</h1>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            The requested scan could not be found. It may have been deleted or you may not have
            permission to view it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Scan Details</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">{state.currentScan.url}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => handleExport('pdf')}
            disabled={state.loading.exporting}
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button
            variant="outline"
            onClick={() => handleExport('json')}
            disabled={state.loading.exporting}
          >
            <Download className="h-4 w-4 mr-2" />
            Export JSON
          </Button>
          <Button
            variant="outline"
            onClick={() => handleExport('csv')}
            disabled={state.loading.exporting}
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Scan Results Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="detailed-issues" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Detailed Issues
            {state.currentScan.summary?.failedAutomatedChecks > 0 && (
              <span className="ml-1 bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                {state.currentScan.summary.failedAutomatedChecks}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="manual-review" className="flex items-center gap-2">
            <ClipboardCheck className="h-4 w-4" />
            Manual Review
            {state.currentScan.summary?.manualReviewItems > 0 && (
              <span className="ml-1 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                {state.currentScan.summary.manualReviewItems}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Technical Details
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <WcagScanOverview
            scanResult={state.currentScan}
            onExport={handleExportClick}
            onManualReview={() => {
              // Switch to manual review tab
              const manualReviewTab = document.querySelector(
                '[value="manual-review"]',
              ) as HTMLElement;
              if (manualReviewTab) {
                manualReviewTab.click();
              }
            }}
            loading={state.loading.exporting}
          />
        </TabsContent>

        <TabsContent value="detailed-issues">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Detailed Failure Analysis</h2>
                <p className="text-gray-600 mt-1">
                  Comprehensive evidence and remediation guidance for all failed accessibility checks
                </p>
              </div>
              <Button
                variant="outline"
                onClick={handleExportClick}
                disabled={state.loading.exporting}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export Report
              </Button>
            </div>

            <EnhancedFailureEvidenceDisplay
              checks={state.currentScan.checks?.map(check => ({
                ...check,
                // Add fallback evidence if missing
                evidence: check.evidence && check.evidence.length > 0 ? check.evidence : [
                  {
                    type: 'error' as const,
                    description: `${check.ruleId} - Evidence Missing`,
                    value: 'This check failed but detailed evidence is not available. This may be due to a database issue or the scan being performed before evidence collection was implemented.',
                    severity: 'error' as const,
                    message: 'Evidence data is missing from the database. Please re-run the scan to generate detailed evidence.',
                  },
                  {
                    type: 'info' as const,
                    description: 'Fallback Information',
                    value: `Status: ${check.status}, Score: ${check.score}/${check.maxScore}`,
                    severity: 'info' as const,
                    message: 'This is fallback evidence generated when detailed evidence is not available.',
                  }
                ]
              })) || []}
              showFixExamples={true}
              showElementCounts={true}
              showPerformanceMetrics={false}
              groupByCategory={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="manual-review">
          <WcagManualReviewDashboard
            scanResult={state.currentScan}
            onReviewUpdate={() => {
              // Refresh scan details to get updated scores
              loadScanDetails();
            }}
          />
        </TabsContent>

        <TabsContent value="details">
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Technical Details</h2>
              <p className="text-gray-600 mt-1">
                Scan configuration, metadata, and technical information
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Scan Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>Scan Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Scan ID:</span>
                    <span className="font-mono text-sm">{state.currentScan.scanId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Started:</span>
                    <span>{new Date(state.currentScan.scanTimestamp).toLocaleString()}</span>
                  </div>
                  {state.currentScan.completionTimestamp && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Completed:</span>
                      <span>
                        {new Date(state.currentScan.completionTimestamp).toLocaleString()}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">WCAG Version:</span>
                    <span>{state.currentScan.wcagVersion}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Compliance Level:</span>
                    <span>{state.currentScan.complianceLevel}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Status:</span>
                    <span className="capitalize">{state.currentScan.status}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Scan Options */}
            <Card>
              <CardHeader>
                <CardTitle>Scan Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Contrast Checks:</span>
                    <span>{state.currentScan.scanOptions?.contrast ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Keyboard Checks:</span>
                    <span>{state.currentScan.scanOptions?.keyboard ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Focus Checks:</span>
                    <span>{state.currentScan.scanOptions?.focus ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Semantic Checks:</span>
                    <span>{state.currentScan.scanOptions?.semantic ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Page Limit:</span>
                    <span>{state.currentScan.scanOptions?.pageLimit || 'No limit'}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Technical Details */}
          {state.currentScan.metadata && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Technical Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">
                      User Agent:
                    </span>
                    <span className="font-mono text-xs break-all">
                      {state.currentScan.metadata.userAgent}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">
                      Viewport:
                    </span>
                    <span>
                      {state.currentScan.metadata.viewport?.width} ×{' '}
                      {state.currentScan.metadata.viewport?.height}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">
                      Environment:
                    </span>
                    <span className="capitalize">{state.currentScan.metadata.environment}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">
                      Scanner Version:
                    </span>
                    <span>{state.currentScan.metadata.version}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 block text-sm">
                      Request ID:
                    </span>
                    <span className="font-mono text-xs">
                      {state.currentScan.metadata.requestId}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

/**
 * Main WCAG Scan Details Page with Provider
 */
export default function WcagScanDetailsPage() {
  return (
    <WcagProvider>
      <WcagScanDetailsContent />
    </WcagProvider>
  );
}
