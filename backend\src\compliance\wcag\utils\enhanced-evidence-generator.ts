/**
 * Enhanced Evidence Generator for WCAG Checks
 * Generates detailed, actionable evidence with fix examples and element counts
 */

import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagFixExample } from '../types-enhanced';
import logger from '../../../utils/logger';

export interface EnhancedEvidenceConfig {
  includeCodeExamples?: boolean;
  includeFixExamples?: boolean;
  includeElementCounts?: boolean;
  includeAffectedSelectors?: boolean;
  maxElementsToShow?: number;
  generateScreenshots?: boolean;
}

export interface ElementIssue {
  selector: string;
  element: string;
  issues: string[];
  severity: 'info' | 'warning' | 'error' | 'critical';
  fixSuggestion: string;
  codeExample?: string;
}

export class EnhancedEvidenceGenerator {
  private static readonly DEFAULT_CONFIG: EnhancedEvidenceConfig = {
    includeCodeExamples: true,
    includeFixExamples: true,
    includeElementCounts: true,
    includeAffectedSelectors: true,
    maxElementsToShow: 10,
    generateScreenshots: false,
  };

  /**
   * Generate enhanced evidence for accessibility issues
   */
  static generateEnhancedEvidence(
    ruleId: string,
    ruleName: string,
    issues: ElementIssue[],
    config: Partial<EnhancedEvidenceConfig> = {},
  ): WcagEvidenceEnhanced[] {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const evidence: WcagEvidenceEnhanced[] = [];

    if (issues.length === 0) {
      // Generate passing evidence
      evidence.push({
        type: 'info',
        description: `${ruleName}: No accessibility issues found`,
        value: 'All elements meet accessibility requirements',
        severity: 'info',
        elementCount: 0,
      });
      return evidence;
    }

    // Group issues by type and severity
    const groupedIssues = this.groupIssuesByType(issues);

    for (const [issueType, issueGroup] of Object.entries(groupedIssues)) {
      const affectedSelectors = issueGroup.map(issue => issue.selector);
      const elementCount = issueGroup.length;
      const severity = this.determineSeverity(issueGroup);

      // Generate main evidence item
      const mainEvidence: WcagEvidenceEnhanced = {
        type: 'error',
        description: this.generateIssueDescription(issueType, elementCount),
        value: this.generateIssueValue(issueGroup),
        severity,
        elementCount,
        affectedSelectors: finalConfig.includeAffectedSelectors ? affectedSelectors : undefined,
      };

      // Add fix example if requested
      if (finalConfig.includeFixExamples) {
        mainEvidence.fixExample = this.generateFixExample(ruleId, issueType, issueGroup[0]);
      }

      evidence.push(mainEvidence);

      // Add individual element evidence if there are multiple issues
      if (elementCount > 1 && finalConfig.includeCodeExamples) {
        const elementsToShow = issueGroup.slice(0, finalConfig.maxElementsToShow || 10);
        
        for (const issue of elementsToShow) {
          evidence.push({
            type: 'code',
            description: `${issue.selector}: ${issue.issues.join(', ')}`,
            value: issue.element,
            selector: issue.selector,
            severity: issue.severity,
            elementCount: 1,
            affectedSelectors: [issue.selector],
            fixExample: finalConfig.includeFixExamples 
              ? this.generateSpecificFixExample(ruleId, issue)
              : undefined,
          });
        }

        // Add "and X more" indicator if there are more elements
        if (finalConfig.maxElementsToShow && issueGroup.length > finalConfig.maxElementsToShow) {
          const remainingCount = issueGroup.length - finalConfig.maxElementsToShow;
          evidence.push({
            type: 'info',
            description: `... and ${remainingCount} more element${remainingCount !== 1 ? 's' : ''} with similar issues`,
            value: `Total affected elements: ${issueGroup.length}`,
            severity: 'info',
            elementCount: remainingCount,
          });
        }
      }
    }

    return evidence;
  }

  /**
   * Group issues by type for better organization
   */
  private static groupIssuesByType(issues: ElementIssue[]): Record<string, ElementIssue[]> {
    const grouped: Record<string, ElementIssue[]> = {};

    for (const issue of issues) {
      const issueType = this.categorizeIssue(issue);
      if (!grouped[issueType]) {
        grouped[issueType] = [];
      }
      grouped[issueType].push(issue);
    }

    return grouped;
  }

  /**
   * Categorize issue based on its characteristics
   */
  private static categorizeIssue(issue: ElementIssue): string {
    const issueText = issue.issues.join(' ').toLowerCase();
    
    if (issueText.includes('alt') || issueText.includes('alternative')) {
      return 'missing_alt_text';
    }
    if (issueText.includes('contrast')) {
      return 'low_contrast';
    }
    if (issueText.includes('keyboard') || issueText.includes('focus')) {
      return 'keyboard_accessibility';
    }
    if (issueText.includes('aria') || issueText.includes('label')) {
      return 'aria_labeling';
    }
    if (issueText.includes('heading') || issueText.includes('structure')) {
      return 'content_structure';
    }
    
    return 'general_accessibility';
  }

  /**
   * Generate descriptive issue description
   */
  private static generateIssueDescription(issueType: string, elementCount: number): string {
    const descriptions: Record<string, string> = {
      missing_alt_text: `Images missing alternative text`,
      low_contrast: `Elements with insufficient color contrast`,
      keyboard_accessibility: `Elements with keyboard accessibility issues`,
      aria_labeling: `Elements missing proper ARIA labels`,
      content_structure: `Content structure and heading issues`,
      general_accessibility: `General accessibility issues`,
    };

    const baseDescription = descriptions[issueType] || 'Accessibility issues found';
    return `${baseDescription} (${elementCount} element${elementCount !== 1 ? 's' : ''})`;
  }

  /**
   * Generate issue value summary
   */
  private static generateIssueValue(issues: ElementIssue[]): string {
    if (issues.length === 1) {
      return issues[0].element;
    }

    const elementTypes = [...new Set(issues.map(issue => {
      const match = issue.element.match(/<(\w+)/);
      return match ? match[1] : 'element';
    }))];

    return `${issues.length} elements affected: ${elementTypes.join(', ')}`;
  }

  /**
   * Determine overall severity for a group of issues
   */
  private static determineSeverity(issues: ElementIssue[]): 'info' | 'warning' | 'error' | 'critical' {
    const severities = issues.map(issue => issue.severity);
    
    if (severities.includes('critical')) return 'critical';
    if (severities.includes('error')) return 'error';
    if (severities.includes('warning')) return 'warning';
    return 'info';
  }

  /**
   * Generate fix example for an issue type
   */
  private static generateFixExample(ruleId: string, issueType: string, sampleIssue: ElementIssue): WcagFixExample {
    const fixTemplates = this.getFixTemplates();
    const template = fixTemplates[`${ruleId}_${issueType}`] || fixTemplates[ruleId] || fixTemplates.default;

    return {
      before: sampleIssue.element,
      after: this.generateFixedElement(sampleIssue),
      description: template.description || sampleIssue.fixSuggestion,
      codeExample: template.codeExample,
      resources: template.resources || [],
    };
  }

  /**
   * Generate specific fix example for individual element
   */
  private static generateSpecificFixExample(ruleId: string, issue: ElementIssue): WcagFixExample {
    return {
      before: issue.element,
      after: this.generateFixedElement(issue),
      description: issue.fixSuggestion,
      resources: this.getResourcesForRule(ruleId),
    };
  }

  /**
   * Generate fixed version of element
   */
  private static generateFixedElement(issue: ElementIssue): string {
    let fixed = issue.element;

    // Apply common fixes based on issue type
    if (issue.issues.some(i => i.includes('alt'))) {
      // Add alt attribute
      if (!fixed.includes('alt=')) {
        fixed = fixed.replace('>', ' alt="Descriptive alternative text">');
      }
    }

    if (issue.issues.some(i => i.includes('aria-label'))) {
      // Add aria-label
      if (!fixed.includes('aria-label=')) {
        fixed = fixed.replace('>', ' aria-label="Descriptive label">');
      }
    }

    if (issue.issues.some(i => i.includes('tabindex'))) {
      // Fix tabindex
      fixed = fixed.replace(/tabindex="-?\d+"/, 'tabindex="0"');
    }

    return fixed;
  }

  /**
   * Get fix templates for different rule types
   */
  private static getFixTemplates(): Record<string, { description: string; codeExample?: string; resources: string[] }> {
    return {
      'WCAG-001_missing_alt_text': {
        description: 'Add descriptive alternative text that conveys the same information as the image',
        codeExample: `// Before
<img src="chart.png">

// After
<img src="chart.png" alt="Sales increased 25% from Q1 to Q2 2024">`,
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html',
          'https://www.w3.org/WAI/tutorials/images/',
        ],
      },
      'WCAG-004_low_contrast': {
        description: 'Increase color contrast to meet WCAG AA standards (4.5:1 for normal text, 3:1 for large text)',
        codeExample: `/* Before */
.text { color: #999; background: #fff; } /* 2.8:1 ratio */

/* After */
.text { color: #666; background: #fff; } /* 5.7:1 ratio */`,
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html',
          'https://webaim.org/resources/contrastchecker/',
        ],
      },
      'WCAG-005_keyboard_accessibility': {
        description: 'Ensure all interactive elements are keyboard accessible',
        codeExample: `// Before
<div onclick="handleClick()">Click me</div>

// After
<button onclick="handleClick()" onkeydown="handleKeydown(event)">Click me</button>`,
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html',
          'https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/',
        ],
      },
      default: {
        description: 'Review and fix the accessibility issue according to WCAG guidelines',
        resources: ['https://www.w3.org/WAI/WCAG21/'],
      },
    };
  }

  /**
   * Get resources for specific WCAG rule
   */
  private static getResourcesForRule(ruleId: string): string[] {
    const resources: Record<string, string[]> = {
      'WCAG-001': [
        'https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html',
        'https://www.w3.org/WAI/tutorials/images/',
      ],
      'WCAG-002': [
        'https://www.w3.org/WAI/WCAG21/Understanding/captions-prerecorded.html',
        'https://www.w3.org/WAI/media/av/',
      ],
      'WCAG-003': [
        'https://www.w3.org/WAI/WCAG21/Understanding/info-and-relationships.html',
        'https://www.w3.org/WAI/tutorials/page-structure/',
      ],
      'WCAG-004': [
        'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html',
        'https://webaim.org/resources/contrastchecker/',
      ],
      'WCAG-005': [
        'https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html',
        'https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/',
      ],
    };

    return resources[ruleId] || ['https://www.w3.org/WAI/WCAG21/'];
  }

  /**
   * Generate element HTML from page evaluation
   */
  static async extractElementHTML(page: any, selector: string): Promise<string> {
    try {
      const html = await page.evaluate((sel: string) => {
        const element = document.querySelector(sel);
        return element ? element.outerHTML : '';
      }, selector);

      return html || `<element selector="${selector}">`;
    } catch (error) {
      logger.warn(`Failed to extract HTML for selector ${selector}:`, {
        error: error instanceof Error ? error.message : String(error)
      });
      return `<element selector="${selector}">`;
    }
  }

  /**
   * Generate comprehensive issue analysis for an element
   */
  static analyzeElementIssues(
    element: any,
    selector: string,
    ruleId: string,
    checkType: string
  ): ElementIssue | null {
    const issues: string[] = [];
    let severity: 'info' | 'warning' | 'error' | 'critical' = 'info';
    let fixSuggestion = '';

    // Analyze based on rule type
    switch (ruleId) {
      case 'WCAG-001':
        return this.analyzeNonTextContentIssues(element, selector);
      case 'WCAG-004':
        return this.analyzeContrastIssues(element, selector);
      case 'WCAG-005':
        return this.analyzeKeyboardIssues(element, selector);
      default:
        return this.analyzeGenericIssues(element, selector, checkType);
    }
  }

  /**
   * Analyze non-text content issues
   */
  private static analyzeNonTextContentIssues(element: any, selector: string): ElementIssue | null {
    const issues: string[] = [];
    let severity: 'info' | 'warning' | 'error' | 'critical' = 'info';

    if (element.tagName === 'IMG') {
      if (!element.alt && element.alt !== '') {
        issues.push('Missing alt attribute');
        severity = 'error';
      } else if (element.alt === '') {
        // Empty alt is okay for decorative images
        return null;
      } else if (element.alt && element.alt.length < 3) {
        issues.push('Alt text too short');
        severity = 'warning';
      }
    }

    if (issues.length === 0) return null;

    return {
      selector,
      element: element.outerHTML || `<${element.tagName.toLowerCase()}>`,
      issues,
      severity,
      fixSuggestion: 'Add descriptive alternative text that conveys the purpose and content of the image',
    };
  }

  /**
   * Analyze contrast issues
   */
  private static analyzeContrastIssues(element: any, selector: string): ElementIssue | null {
    // This would typically involve color analysis
    // For now, return a placeholder structure
    return {
      selector,
      element: element.outerHTML || `<${element.tagName.toLowerCase()}>`,
      issues: ['Insufficient color contrast'],
      severity: 'error',
      fixSuggestion: 'Increase color contrast to meet WCAG AA standards (4.5:1 for normal text)',
    };
  }

  /**
   * Analyze keyboard accessibility issues
   */
  private static analyzeKeyboardIssues(element: any, selector: string): ElementIssue | null {
    const issues: string[] = [];
    let severity: 'info' | 'warning' | 'error' | 'critical' = 'info';

    if (element.onclick && !element.onkeydown) {
      issues.push('Click handler without keyboard equivalent');
      severity = 'error';
    }

    if (element.tabIndex < 0 && element.onclick) {
      issues.push('Interactive element not keyboard focusable');
      severity = 'error';
    }

    if (issues.length === 0) return null;

    return {
      selector,
      element: element.outerHTML || `<${element.tagName.toLowerCase()}>`,
      issues,
      severity,
      fixSuggestion: 'Ensure all interactive elements are keyboard accessible',
    };
  }

  /**
   * Analyze generic accessibility issues
   */
  private static analyzeGenericIssues(element: any, selector: string, checkType: string): ElementIssue | null {
    return {
      selector,
      element: element.outerHTML || `<${element.tagName.toLowerCase()}>`,
      issues: [`${checkType} issue detected`],
      severity: 'warning',
      fixSuggestion: 'Review element for accessibility compliance',
    };
  }
}
