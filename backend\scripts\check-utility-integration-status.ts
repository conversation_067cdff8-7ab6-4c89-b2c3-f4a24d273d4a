/**
 * Check Utility Integration Status
 * Analyzes which WCAG checks have utility integration configured
 */

import UtilityIntegrationManager, { CHECK_UTILITY_PRIORITY } from '../src/compliance/wcag/utils/utility-integration-manager';

function checkUtilityIntegrationStatus(): void {
  console.log('🔧 WCAG Utility Integration Status Analysis');
  console.log('=' .repeat(60));

  const manager = UtilityIntegrationManager.getInstance();
  const stats = manager.getIntegrationStatistics();

  console.log('\n📊 INTEGRATION STATISTICS:');
  console.log(`  Total WCAG Checks: ${stats.totalChecks}`);
  console.log(`  Configured Checks: ${stats.configuredChecks} (${((stats.configuredChecks / stats.totalChecks) * 100).toFixed(1)}%)`);
  console.log(`  Default Config Checks: ${stats.defaultChecks} (${((stats.defaultChecks / stats.totalChecks) * 100).toFixed(1)}%)`);

  console.log('\n🎯 BY PRIORITY:');
  console.log(`  High Priority: ${stats.byPriority.high} checks`);
  console.log(`  Medium Priority: ${stats.byPriority.medium} checks`);
  console.log(`  Low Priority: ${stats.byPriority.low} checks`);

  console.log('\n🔄 BY STRATEGY:');
  Object.entries(stats.byStrategy).forEach(([strategy, count]) => {
    console.log(`  ${strategy}: ${count} checks`);
  });

  console.log('\n📋 CONFIGURED CHECKS:');
  const configuredRuleIds = Object.keys(CHECK_UTILITY_PRIORITY).filter(key => key !== 'default').sort();
  
  configuredRuleIds.forEach((ruleId, index) => {
    const config = CHECK_UTILITY_PRIORITY[ruleId];
    const utilities = [];
    
    if (config.enableSemanticValidation) utilities.push('Semantic');
    if (config.enablePatternValidation) utilities.push('Pattern');
    if (config.enableContentQualityAnalysis) utilities.push('Content');
    if (config.enableFrameworkOptimization) utilities.push('Framework');
    if (config.enableComponentLibraryDetection) utilities.push('Component');
    if (config.enableCMSDetection) utilities.push('CMS');
    
    console.log(`  ${index + 1}. ${ruleId} - Strategy: ${config.integrationStrategy || 'supplement'} - Utilities: [${utilities.join(', ')}]`);
  });

  // Identify missing checks (those using default config)
  const allPossibleRuleIds = [];
  for (let i = 1; i <= 69; i++) {
    const ruleId = `WCAG-${i.toString().padStart(3, '0')}`;
    allPossibleRuleIds.push(ruleId);
  }

  const missingRuleIds = allPossibleRuleIds.filter(ruleId => !configuredRuleIds.includes(ruleId));

  if (missingRuleIds.length > 0) {
    console.log('\n❌ CHECKS NEEDING UTILITY INTEGRATION:');
    missingRuleIds.forEach((ruleId, index) => {
      console.log(`  ${index + 1}. ${ruleId} - Using default configuration`);
    });

    console.log('\n🎯 PRIORITY MIGRATION RECOMMENDATIONS:');
    console.log('  High Priority (Content/Semantic):');
    const contentChecks = missingRuleIds.filter(id => {
      const num = parseInt(id.split('-')[1]);
      return num <= 20 || (num >= 24 && num <= 40); // Content-related ranges
    });
    contentChecks.slice(0, 5).forEach(ruleId => {
      console.log(`    - ${ruleId}: Add semantic + content quality analysis`);
    });

    console.log('  Medium Priority (Interactive):');
    const interactiveChecks = missingRuleIds.filter(id => {
      const num = parseInt(id.split('-')[1]);
      return (num >= 5 && num <= 15) || (num >= 40 && num <= 50); // Interactive ranges
    });
    interactiveChecks.slice(0, 5).forEach(ruleId => {
      console.log(`    - ${ruleId}: Add pattern + framework optimization`);
    });

    console.log('  Low Priority (Specialized):');
    const specializedChecks = missingRuleIds.filter(id => {
      const num = parseInt(id.split('-')[1]);
      return num >= 50; // Specialized checks
    });
    specializedChecks.slice(0, 5).forEach(ruleId => {
      console.log(`    - ${ruleId}: Add basic pattern validation`);
    });
  }

  console.log('\n✅ Analysis complete!');
  console.log(`\n📈 COMPLETION STATUS: ${stats.configuredChecks}/${stats.totalChecks} checks have utility integration (${((stats.configuredChecks / stats.totalChecks) * 100).toFixed(1)}%)`);
}

// Run the analysis
checkUtilityIntegrationStatus();
