/**
 * Color Analysis Utilities
 * Provides 100% automated color contrast analysis
 */

// Import color using require for compatibility
const Color = require('color');
// import { ContrastAnalysisResult } from '../types';

export interface ColorInfo {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  luminance: number;
}

export interface ContrastResult {
  ratio: number;
  level: 'AA' | 'AAA' | 'FAIL';
  isLargeText: boolean;
  passes: boolean;
  recommendation?: string;
}

export class ColorAnalyzer {
  /**
   * Calculate contrast ratio between two colors
   */
  static calculateContrastRatio(foreground: string, background: string): number {
    try {
      const fgColor = Color(foreground);
      const bgColor = Color(background);

      const fgLuminance = fgColor.luminosity();
      const bgLuminance = bgColor.luminosity();

      const lighter = Math.max(fgLuminance, bgLuminance);
      const darker = Math.min(fgLuminance, bgLuminance);

      return (lighter + 0.05) / (darker + 0.05);
    } catch (error) {
      console.error('Error calculating contrast ratio:', error);
      return 0;
    }
  }

  /**
   * Analyze color contrast for accessibility compliance
   */
  static analyzeContrast(
    foreground: string,
    background: string,
    isLargeText: boolean = false,
  ): ContrastResult {
    const ratio = this.calculateContrastRatio(foreground, background);

    // WCAG contrast requirements
    const aaThreshold = isLargeText ? 3.0 : 4.5;
    const aaaThreshold = isLargeText ? 4.5 : 7.0;

    let level: 'AA' | 'AAA' | 'FAIL';
    let passes: boolean;
    let recommendation: string | undefined;

    if (ratio >= aaaThreshold) {
      level = 'AAA';
      passes = true;
    } else if (ratio >= aaThreshold) {
      level = 'AA';
      passes = true;
    } else {
      level = 'FAIL';
      passes = false;
      recommendation = this.generateContrastRecommendation(ratio, aaThreshold, isLargeText);
    }

    return {
      ratio: Math.round(ratio * 100) / 100,
      level,
      isLargeText,
      passes,
      recommendation,
    };
  }

  /**
   * Extract color information from CSS color value
   */
  static extractColorInfo(colorValue: string): ColorInfo {
    try {
      const color = Color(colorValue);

      const rgbObj = color.rgb().object() as { r: number; g: number; b: number };
      const hslObj = color.hsl().object() as { h: number; s: number; l: number };

      return {
        hex: color.hex(),
        rgb: { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b },
        hsl: { h: hslObj.h, s: hslObj.s, l: hslObj.l },
        luminance: color.luminosity(),
      };
    } catch (error) {
      console.error('Error extracting color info:', error);
      // Return default black color
      return {
        hex: '#000000',
        rgb: { r: 0, g: 0, b: 0 },
        hsl: { h: 0, s: 0, l: 0 },
        luminance: 0,
      };
    }
  }

  /**
   * Determine if text is considered large (18pt+ or 14pt+ bold)
   */
  static isLargeText(fontSize: string, fontWeight: string): boolean {
    const size = parseFloat(fontSize);
    const weight = parseInt(fontWeight) || 400;

    // Convert various units to pixels (approximate)
    let sizeInPx = size;
    if (fontSize.includes('pt')) {
      sizeInPx = size * 1.33; // pt to px conversion
    } else if (fontSize.includes('em')) {
      sizeInPx = size * 16; // em to px (assuming 16px base)
    } else if (fontSize.includes('rem')) {
      sizeInPx = size * 16; // rem to px (assuming 16px base)
    }

    // Large text criteria: 18pt+ (24px+) or 14pt+ (18.7px+) bold
    return sizeInPx >= 24 || (sizeInPx >= 18.7 && weight >= 700);
  }

  /**
   * Generate contrast improvement recommendation
   */
  private static generateContrastRecommendation(
    currentRatio: number,
    targetRatio: number,
    isLargeText: boolean,
  ): string {
    const improvement = targetRatio / currentRatio;
    const textType = isLargeText ? 'large text' : 'normal text';

    if (improvement <= 1.2) {
      return `Slightly adjust colors to improve contrast for ${textType}. Current ratio: ${currentRatio.toFixed(2)}, target: ${targetRatio}`;
    } else if (improvement <= 2.0) {
      return `Moderate color adjustment needed for ${textType}. Consider darkening text or lightening background.`;
    } else {
      return `Significant color change required for ${textType}. Consider using high-contrast color combinations.`;
    }
  }

  /**
   * Analyze gradient backgrounds for contrast
   */
  static analyzeGradientContrast(
    foregroundColor: string,
    gradientStops: string[],
  ): ContrastResult[] {
    return gradientStops.map((stopColor) => this.analyzeContrast(foregroundColor, stopColor));
  }

  /**
   * Find optimal color for contrast improvement
   */
  static suggestOptimalColor(
    baseColor: string,
    targetContrast: number,
    adjustBackground: boolean = true,
  ): string {
    try {
      const base = Color(baseColor);
      const baseLuminance = base.luminosity();

      // Calculate required luminance for target contrast
      let targetLuminance: number;

      if (adjustBackground) {
        // Adjust background to meet contrast with foreground
        if (baseLuminance > 0.5) {
          // Dark background needed
          targetLuminance = (baseLuminance + 0.05) / targetContrast - 0.05;
        } else {
          // Light background needed
          targetLuminance = (baseLuminance + 0.05) * targetContrast - 0.05;
        }
      } else {
        // Adjust foreground to meet contrast with background
        if (baseLuminance > 0.5) {
          // Dark foreground needed
          targetLuminance = (baseLuminance + 0.05) / targetContrast - 0.05;
        } else {
          // Light foreground needed
          targetLuminance = (baseLuminance + 0.05) * targetContrast - 0.05;
        }
      }

      // Clamp luminance to valid range
      targetLuminance = Math.max(0, Math.min(1, targetLuminance));

      // Convert luminance back to color
      const lightness = Math.sqrt(targetLuminance) * 100;
      return base.lightness(lightness).hex();
    } catch (error) {
      console.error('Error suggesting optimal color:', error);
      return adjustBackground ? '#ffffff' : '#000000';
    }
  }
}

/**
 * Color contrast validation patterns
 */
export const COLOR_PATTERNS = {
  // CSS color value patterns
  HEX: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  RGB: /^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/,
  RGBA: /^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)$/,
  HSL: /^hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)$/,
  HSLA: /^hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([\d.]+)\s*\)$/,

  // Named colors (subset of CSS named colors)
  NAMED_COLORS: [
    'black',
    'white',
    'red',
    'green',
    'blue',
    'yellow',
    'cyan',
    'magenta',
    'gray',
    'grey',
    'darkgray',
    'darkgrey',
    'lightgray',
    'lightgrey',
    'transparent',
  ],
};

/**
 * WCAG contrast thresholds
 */
export const CONTRAST_THRESHOLDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
} as const;
