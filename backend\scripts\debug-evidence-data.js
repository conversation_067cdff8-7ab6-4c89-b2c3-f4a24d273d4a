const { Pool } = require('pg');
require('dotenv').config({ path: '../.env' });

async function debugEvidenceData() {
  const client = new Pool({
    host: process.env.POSTGRES_HOST || 'localhost',
    port: process.env.POSTGRES_PORT || 5432,
    database: process.env.POSTGRES_DB || 'complychecker_dev',
    user: process.env.POSTGRES_USER || 'complyuser',
    password: process.env.POSTGRES_PASSWORD || 'complypassword',
  });

  try {
    console.log('🔍 Debugging WCAG Evidence Data...\n');

    // Check if we have any scans
    const scanCountResult = await client.query('SELECT COUNT(*) as count FROM wcag_scans');
    console.log(`📊 Total scans: ${scanCountResult.rows[0].count}`);

    if (scanCountResult.rows[0].count > 0) {
      // Get the most recent scan
      const recentScanResult = await client.query(`
        SELECT id, target_url, scan_status, created_at 
        FROM wcag_scans 
        ORDER BY created_at DESC 
        LIMIT 1
      `);
      
      const recentScan = recentScanResult.rows[0];
      console.log(`\n🔍 Most recent scan: ${recentScan.id}`);
      console.log(`   URL: ${recentScan.target_url}`);
      console.log(`   Status: ${recentScan.scan_status}`);
      console.log(`   Created: ${recentScan.created_at}`);

      // Get automated results for this scan
      const resultsQuery = await client.query(`
        SELECT rule_id, rule_name, status, details, 
               total_element_count, failed_element_count, affected_selectors, 
               fix_examples, evidence_metadata
        FROM wcag_automated_results 
        WHERE scan_id = $1
        ORDER BY rule_id
        LIMIT 10
      `, [recentScan.id]);

      console.log(`\n📊 Automated results: ${resultsQuery.rows.length}`);

      for (const result of resultsQuery.rows) {
        console.log(`\n🔍 Rule: ${result.rule_id} (${result.rule_name})`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Total elements: ${result.total_element_count}`);
        console.log(`   Failed elements: ${result.failed_element_count}`);
        console.log(`   Has affected selectors: ${!!result.affected_selectors}`);
        console.log(`   Has fix examples: ${!!result.fix_examples}`);
        console.log(`   Has evidence metadata: ${!!result.evidence_metadata}`);
        
        if (result.details) {
          try {
            const details = typeof result.details === 'string' ? JSON.parse(result.details) : result.details;
            console.log(`   Details type: ${typeof details}`);
            console.log(`   Has evidence: ${!!(details.evidence)}`);
            console.log(`   Evidence items: ${details.evidence ? details.evidence.length : 0}`);
            
            if (details.evidence && details.evidence.length > 0) {
              console.log(`   First evidence type: ${details.evidence[0].type}`);
              console.log(`   First evidence desc: ${details.evidence[0].description}`);
              console.log(`   First evidence value: ${details.evidence[0].value}`);
            }
          } catch (e) {
            console.log(`   Details parse error: ${e.message}`);
            console.log(`   Raw details: ${JSON.stringify(result.details).substring(0, 200)}...`);
          }
        } else {
          console.log(`   No details field`);
        }
      }

      // Check if we have any results with evidence
      const evidenceCountQuery = await client.query(`
        SELECT COUNT(*) as count
        FROM wcag_automated_results 
        WHERE scan_id = $1 
        AND details IS NOT NULL 
        AND details::text LIKE '%evidence%'
      `, [recentScan.id]);

      console.log(`\n📈 Results with evidence: ${evidenceCountQuery.rows[0].count}`);

      // Check the structure of the details field
      const detailsStructureQuery = await client.query(`
        SELECT rule_id, 
               CASE 
                 WHEN details IS NULL THEN 'NULL'
                 WHEN details::text = '{}' THEN 'EMPTY_OBJECT'
                 WHEN details::text LIKE '%evidence%' THEN 'HAS_EVIDENCE'
                 ELSE 'OTHER'
               END as details_type,
               LENGTH(details::text) as details_length
        FROM wcag_automated_results 
        WHERE scan_id = $1
        ORDER BY rule_id
      `, [recentScan.id]);

      console.log(`\n📋 Details field analysis:`);
      for (const row of detailsStructureQuery.rows) {
        console.log(`   ${row.rule_id}: ${row.details_type} (${row.details_length} chars)`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.end();
  }
}

debugEvidenceData();
