/**
 * Populate Sample Evidence Script
 * Adds sample evidence data to existing WCAG scans for testing
 */

import db from '../src/lib/db';
import logger from '../src/utils/logger';

async function populateSampleEvidence() {
  try {
    logger.info('🧪 Populating sample evidence data...');

    // Get existing scans
    const scans = await db('wcag_scans')
      .select('id', 'target_url')
      .limit(5);

    if (scans.length === 0) {
      logger.info('📋 No scans found in database');
      return;
    }

    logger.info(`📋 Found ${scans.length} scans to update`);

    for (const scan of scans) {
      logger.info(`🔄 Updating scan: ${scan.id}`);

      // Get automated results for this scan
      const results = await db('wcag_automated_results')
        .where('scan_id', scan.id)
        .select('*');

      logger.info(`📊 Found ${results.length} automated results`);

      // Update each result with sample evidence
      for (const result of results) {
        const sampleEvidence = [
          {
            type: 'code',
            description: `Missing alt attribute on image element`,
            value: `<img src="/example.jpg" class="hero-image">`,
            selector: 'img.hero-image',
          },
          {
            type: 'text',
            description: `Element fails accessibility check`,
            value: `This element does not meet WCAG ${result.conformance_level} standards for ${result.rule_name}`,
            selector: result.rule_id === 'WCAG-001' ? 'img[src*="example"]' : 'div.content',
          }
        ];

        const sampleDetails = {
          totalChecks: 1,
          passedChecks: result.status === 'passed' ? 1 : 0,
          evidence: sampleEvidence,
          issues: result.status === 'failed' ? [`${result.rule_name} violation detected`] : [],
          recommendations: [`Fix ${result.rule_name} issues by following WCAG guidelines`],
          manualReviewItems: []
        };

        // Update the result with sample evidence
        await db('wcag_automated_results')
          .where('id', result.id)
          .update({
            details: JSON.stringify(sampleDetails),
            total_element_count: 2,
            failed_element_count: result.status === 'failed' ? 1 : 0,
            affected_selectors: JSON.stringify(['img.hero-image', 'div.content']),
            fix_examples: JSON.stringify([
              {
                before: '<img src="/example.jpg" class="hero-image">',
                after: '<img src="/example.jpg" class="hero-image" alt="Hero image showing our main product">',
                description: 'Add descriptive alt text to images',
                codeExample: 'alt="Descriptive text about the image content"',
                resources: ['https://www.w3.org/WAI/tutorials/images/']
              }
            ]),
            evidence_metadata: JSON.stringify({
              scanDuration: 1500,
              elementsAnalyzed: 10,
              checkSpecificData: {
                imageType: 'content',
                hasAriaLabel: false,
                automationLevel: 'high'
              }
            }),
            scan_duration_ms: 1500,
            elements_analyzed: 10,
            updated_at: new Date()
          });

        logger.info(`✅ Updated result: ${result.rule_id} (${result.rule_name})`);
      }
    }

    logger.info('✅ Sample evidence population completed!');

  } catch (error) {
    logger.error('❌ Error populating sample evidence:', error);
    throw error;
  }
}

// Run the script
populateSampleEvidence()
  .then(() => {
    logger.info('🏁 Sample evidence population completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('💥 Sample evidence population failed:', error);
    process.exit(1);
  });
