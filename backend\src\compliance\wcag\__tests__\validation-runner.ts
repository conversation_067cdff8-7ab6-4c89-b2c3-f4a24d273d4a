/**
 * Comprehensive WCAG Validation Runner
 * Direct validation of all fixes without Jest dependencies
 */

import puppeteer from 'puppeteer';
import { Page } from 'puppeteer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { EnhancedColorAnalyzer } from '../utils/enhanced-color-analyzer';
import { AccessibleAuthenticationCheck } from '../checks/accessible-authentication';
import SmartCache from '../utils/smart-cache';
import logger from '../../../utils/logger';

interface ValidationResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class WCAGValidationRunner {
  private browser: any;
  private page: Page | null = null;
  private results: ValidationResult[] = [];

  async initialize(): Promise<void> {
    console.log('🚀 Initializing WCAG Validation Runner...');
    
    this.browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
    });
    
    this.page = await this.browser.newPage();
    
    // Set up test page
    await this.page.setContent(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <title>WCAG Validation Test Page</title>
        <style>
          .test-element { color: #333; background: #fff; padding: 10px; }
          .focus-element { outline: 2px solid blue; }
          .hidden { display: none; }
        </style>
      </head>
      <body>
        <header>
          <h1>WCAG Validation Test</h1>
          <nav>
            <ul>
              <li><a href="#section1">Section 1</a></li>
              <li><a href="#section2">Section 2</a></li>
            </ul>
          </nav>
        </header>
        
        <main>
          <section id="section1">
            <h2>Authentication Form</h2>
            <form id="login-form">
              <label for="username">Username:</label>
              <input type="text" id="username" name="username" class="test-element" required>
              
              <label for="password">Password:</label>
              <input type="password" id="password" name="password" class="test-element" required>
              
              <div class="social-login">
                <button type="button" data-auth-alternative="true">Login with Google</button>
              </div>
              
              <button type="submit" class="focus-element">Submit</button>
            </form>
          </section>
          
          <section id="section2">
            <h2>Interactive Elements</h2>
            <div class="test-element" tabindex="0">Focusable div</div>
            <button class="test-element focus-element">Test Button</button>
            <a href="#" class="test-element">Test Link</a>
            
            <div class="hidden">Hidden content</div>
          </section>
        </main>
      </body>
      </html>
    `);
    
    console.log('✅ Test page loaded successfully');
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }

  private addResult(testName: string, passed: boolean, error?: string, details?: any): void {
    this.results.push({ testName, passed, error, details });
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}${error ? ` - ${error}` : ''}`);
  }

  async validatePatternDetection(): Promise<void> {
    console.log('\n🔍 Testing Pattern Detection Fixes...');
    
    try {
      const patternLibrary = AccessibilityPatternLibrary.getInstance();
      
      // Test 1: Function injection
      const injectionResult = await patternLibrary.injectPatternDetectionFunctions(this.page!);
      this.addResult('Pattern Detection Function Injection', injectionResult);
      
      // Test 2: Valid selector array handling
      const validElements = await this.page!.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        if (!detection) return null;
        return detection.findPatternElements(['input[type="text"]', 'input[type="password"]']);
      });
      
      this.addResult(
        'Valid Selector Array Handling',
        Array.isArray(validElements) && validElements.length > 0,
        undefined,
        { elementCount: validElements?.length }
      );
      
      // Test 3: Invalid input handling (the main fix)
      const invalidInputResult = await this.page!.evaluate(() => {
        const detection = (window as any).accessibilityPatternDetection;
        if (!detection) return null;
        
        try {
          // Test with non-array input
          const result1 = detection.findPatternElements('not-an-array');
          const result2 = detection.findPatternElements(null);
          const result3 = detection.findPatternElements(undefined);
          
          return {
            nonArrayResult: Array.isArray(result1) && result1.length === 0,
            nullResult: Array.isArray(result2) && result2.length === 0,
            undefinedResult: Array.isArray(result3) && result3.length === 0,
          };
        } catch (error) {
          return { error: error.message };
        }
      });
      
      const invalidInputPassed = invalidInputResult && 
        invalidInputResult.nonArrayResult && 
        invalidInputResult.nullResult && 
        invalidInputResult.undefinedResult &&
        !invalidInputResult.error;
      
      this.addResult(
        'Invalid Input Handling (Main Fix)',
        invalidInputPassed,
        invalidInputResult?.error,
        invalidInputResult
      );
      
      // Test 4: Pattern analysis integration
      const pattern = {
        name: 'Form Input Pattern',
        selectors: ['input[type="text"]', 'input[type="password"]'],
        description: 'Test pattern for form inputs',
      };
      
      const analysisResult = await patternLibrary.analyzePattern(this.page!, pattern);
      this.addResult(
        'Pattern Analysis Integration',
        analysisResult && Array.isArray(analysisResult.elements),
        undefined,
        { elementCount: analysisResult?.elements?.length }
      );
      
    } catch (error) {
      this.addResult('Pattern Detection Validation', false, error.message);
    }
  }

  async validateColorAnalyzer(): Promise<void> {
    console.log('\n🎨 Testing Color Analyzer Fixes...');
    
    try {
      const colorAnalyzer = EnhancedColorAnalyzer.getInstance();
      
      // Test the className.split fix
      const colorAnalysisResult = await this.page!.evaluate(() => {
        // Create test elements with various className scenarios
        const testElements = [
          { className: 'test-element focus-element', tagName: 'DIV' },
          { className: '', tagName: 'SPAN' },
          { className: null, tagName: 'P' },
          { className: undefined, tagName: 'A' },
          { className: 'single-class', tagName: 'BUTTON' },
        ];
        
        const results = [];
        
        testElements.forEach((elementData, index) => {
          try {
            // Create actual DOM element
            const element = document.createElement(elementData.tagName);
            element.id = `test-element-${index}`;
            if (elementData.className !== null && elementData.className !== undefined) {
              element.className = elementData.className;
            }
            document.body.appendChild(element);
            
            // Test the getElementSelector function that was fixed
            const detection = (window as any).accessibilityPatternDetection;
            if (detection && detection.getElementSelector) {
              const selector = detection.getElementSelector(element);
              results.push({
                elementIndex: index,
                className: elementData.className,
                selector: selector,
                success: typeof selector === 'string' && selector.length > 0,
              });
            }
            
            // Clean up
            document.body.removeChild(element);
          } catch (error) {
            results.push({
              elementIndex: index,
              className: elementData.className,
              error: error.message,
              success: false,
            });
          }
        });
        
        return results;
      });
      
      const allSuccessful = colorAnalysisResult.every(result => result.success);
      this.addResult(
        'Color Analyzer className.split Fix',
        allSuccessful,
        undefined,
        colorAnalysisResult
      );
      
      // Test focus appearance analysis
      const focusAnalysis = await colorAnalyzer.analyzeFocusAppearance(this.page!);
      this.addResult(
        'Focus Appearance Analysis',
        focusAnalysis && Array.isArray(focusAnalysis.focusableElements),
        undefined,
        { elementCount: focusAnalysis?.focusableElements?.length }
      );
      
    } catch (error) {
      this.addResult('Color Analyzer Validation', false, error.message);
    }
  }

  async validateAuthenticationChecks(): Promise<void> {
    console.log('\n🔐 Testing Authentication Check Fixes...');
    
    try {
      const authCheck = new AccessibleAuthenticationCheck();
      
      // Test the fixed authentication check
      const config = {
        targetUrl: 'test://localhost',
        scanId: 'validation-test',
        enableManualReview: false,
        enableManualTracking: false,
        maxManualItems: 0,
        page: this.page!,
      };
      
      const result = await authCheck.performCheck(config);
      
      this.addResult(
        'Authentication Check Execution',
        result && typeof result.score === 'number',
        undefined,
        { score: result?.score, status: result?.status }
      );
      
      // Test alternative detection (the fixed method)
      const alternativeDetection = await this.page!.evaluate(() => {
        const form = document.querySelector('#login-form');
        if (!form) return false;
        
        const alternatives = form.querySelectorAll('[data-auth-alternative]');
        return alternatives.length > 0;
      });
      
      this.addResult(
        'Authentication Alternative Detection',
        alternativeDetection,
        undefined,
        { hasAlternatives: alternativeDetection }
      );
      
    } catch (error) {
      this.addResult('Authentication Check Validation', false, error.message);
    }
  }

  async validateCacheSystem(): Promise<void> {
    console.log('\n💾 Testing Cache System Improvements...');
    
    try {
      const cache = SmartCache.getInstance();
      
      // Test cache operations
      const testKey = 'validation-test-key';
      const testData = { test: 'data', timestamp: Date.now() };
      
      // Store data
      await cache.cacheRuleResult('TEST-001', 'test-content-hash', 'test-config-hash', testData);
      
      // Retrieve data
      const retrievedData = await cache.getRuleResult('TEST-001', 'test-content-hash', 'test-config-hash');
      
      this.addResult(
        'Cache Store and Retrieve',
        retrievedData !== null && retrievedData.test === testData.test,
        undefined,
        { stored: testData, retrieved: retrievedData }
      );
      
      // Test cache statistics
      const stats = cache.getStats();
      this.addResult(
        'Cache Statistics',
        stats && typeof stats.hits === 'number' && typeof stats.misses === 'number',
        undefined,
        stats
      );
      
    } catch (error) {
      this.addResult('Cache System Validation', false, error.message);
    }
  }

  async runAllValidations(): Promise<ValidationResult[]> {
    try {
      await this.initialize();
      
      await this.validatePatternDetection();
      await this.validateColorAnalyzer();
      await this.validateAuthenticationChecks();
      await this.validateCacheSystem();
      
      return this.results;
    } finally {
      await this.cleanup();
    }
  }

  printSummary(): void {
    console.log('\n📊 VALIDATION SUMMARY');
    console.log('='.repeat(50));
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${percentage}%`);
    
    if (passed === total) {
      console.log('\n🎉 ALL VALIDATIONS PASSED! Fixes are working perfectly.');
    } else {
      console.log('\n⚠️  Some validations failed. Review the details above.');
      
      const failed = this.results.filter(r => !r.passed);
      console.log('\nFailed Tests:');
      failed.forEach(result => {
        console.log(`❌ ${result.testName}: ${result.error || 'Unknown error'}`);
      });
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const runner = new WCAGValidationRunner();
  
  runner.runAllValidations()
    .then(() => {
      runner.printSummary();
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Validation runner failed:', error);
      process.exit(1);
    });
}

export { WCAGValidationRunner, ValidationResult };
