/**
 * Enhanced Keyboard Navigation Tester
 * Comprehensive keyboard testing for complex interactions, custom controls, and focus management
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface KeyboardTestResult {
  element: string;
  selector: string;
  isKeyboardAccessible: boolean;
  supportedKeys: string[];
  focusManagement: {
    canReceiveFocus: boolean;
    hasVisibleFocus: boolean;
    focusOrder: number;
    trapsFocus: boolean;
  };
  ariaSupport: {
    hasAriaLabel: boolean;
    hasAriaRole: boolean;
    hasAriaStates: boolean;
    ariaCompliant: boolean;
  };
  interactions: {
    activation: boolean; // Enter/Space
    navigation: boolean; // Arrow keys
    escape: boolean; // Escape key
    custom: string[]; // Custom key bindings
  };
  issues: string[];
  recommendations: string[];
  score: number; // 0-100
}

export interface KeyboardNavigationReport {
  totalElements: number;
  accessibleElements: number;
  inaccessibleElements: number;
  overallScore: number;
  focusOrder: string[];
  focusTraps: string[];
  keyboardShortcuts: { key: string; action: string; element: string }[];
  criticalIssues: string[];
  recommendations: string[];
  testResults: KeyboardTestResult[];
}

export interface KeyboardTestConfig {
  testInteractiveElements: boolean;
  testCustomControls: boolean;
  testFocusManagement: boolean;
  testKeyboardShortcuts: boolean;
  testModalDialogs: boolean;
  testDropdowns: boolean;
  testCarousels: boolean;
  testTabs: boolean;
  includeHiddenElements: boolean;
  timeout: number;
}

/**
 * Enhanced keyboard navigation tester
 */
export class KeyboardNavigationTester {
  private static instance: KeyboardNavigationTester;

  private constructor() {}

  static getInstance(): KeyboardNavigationTester {
    if (!KeyboardNavigationTester.instance) {
      KeyboardNavigationTester.instance = new KeyboardNavigationTester();
    }
    return KeyboardNavigationTester.instance;
  }

  /**
   * Test keyboard navigation for the entire page
   */
  async testKeyboardNavigation(
    page: Page,
    config: Partial<KeyboardTestConfig> = {},
  ): Promise<KeyboardNavigationReport> {
    const fullConfig: KeyboardTestConfig = {
      testInteractiveElements: config.testInteractiveElements ?? true,
      testCustomControls: config.testCustomControls ?? true,
      testFocusManagement: config.testFocusManagement ?? true,
      testKeyboardShortcuts: config.testKeyboardShortcuts ?? true,
      testModalDialogs: config.testModalDialogs ?? true,
      testDropdowns: config.testDropdowns ?? true,
      testCarousels: config.testCarousels ?? true,
      testTabs: config.testTabs ?? true,
      includeHiddenElements: config.includeHiddenElements ?? false,
      timeout: config.timeout ?? 30000,
    };

    logger.debug('⌨️ Starting comprehensive keyboard navigation testing');

    // Inject keyboard testing functions
    await this.injectKeyboardTestingFunctions(page);

    // Get all interactive elements
    const interactiveElements = await this.getInteractiveElements(page, fullConfig);

    // Test each element
    const testResults: KeyboardTestResult[] = [];
    for (const element of interactiveElements) {
      try {
        const result = await this.testElement(page, element, fullConfig);
        testResults.push(result);
      } catch (error) {
        logger.warn(`Error testing element ${element.selector}`, { error });
      }
    }

    // Test focus order
    const focusOrder = await this.testFocusOrder(page);

    // Test focus traps
    const focusTraps = await this.testFocusTraps(page);

    // Test keyboard shortcuts
    const keyboardShortcuts = await this.testKeyboardShortcuts(page);

    // Generate report
    const report = this.generateReport(testResults, focusOrder, focusTraps, keyboardShortcuts);

    logger.info(`✅ Keyboard navigation testing completed`, {
      totalElements: report.totalElements,
      accessibleElements: report.accessibleElements,
      overallScore: report.overallScore,
      criticalIssues: report.criticalIssues.length,
    });

    return report;
  }

  /**
   * Inject keyboard testing functions into the page
   */
  private async injectKeyboardTestingFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as unknown as { keyboardTesting: Record<string, unknown> }).keyboardTesting = {
        /**
         * Get element selector
         */
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          let current = element;

          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();

            if (current.className) {
              const classes = current.className.split(' ').filter((c) => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }

            path.unshift(selector);
            current = current.parentElement!;

            if (path.length > 4) break;
          }

          return path.join(' > ');
        },

        /**
         * Check if element is interactive
         */
        isInteractiveElement(element: HTMLElement): boolean {
          const interactiveTags = ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'];
          const interactiveRoles = [
            'button',
            'link',
            'menuitem',
            'tab',
            'checkbox',
            'radio',
            'slider',
          ];

          // Check tag name
          if (interactiveTags.includes(element.tagName)) {
            return true;
          }

          // Check role
          const role = element.getAttribute('role');
          if (role && interactiveRoles.includes(role)) {
            return true;
          }

          // Check if has click handlers
          if (element.onclick || element.getAttribute('onclick')) {
            return true;
          }

          // Check tabindex
          const tabindex = element.getAttribute('tabindex');
          if (tabindex !== null && tabindex !== '-1') {
            return true;
          }

          return false;
        },

        /**
         * Test if element can receive focus
         */
        canReceiveFocus(element: HTMLElement): boolean {
          try {
            const originalFocus = document.activeElement;
            element.focus();
            const canFocus = document.activeElement === element;

            // Restore original focus
            if (originalFocus && originalFocus instanceof HTMLElement) {
              originalFocus.focus();
            }

            return canFocus;
          } catch (error) {
            return false;
          }
        },

        /**
         * Check if element has visible focus indicator
         */
        hasVisibleFocus(element: HTMLElement): boolean {
          element.focus();

          const computedStyle = window.getComputedStyle(element, ':focus');

          // Check for focus outline
          const hasOutline =
            computedStyle.outline !== 'none' &&
            computedStyle.outline !== '0px' &&
            computedStyle.outline !== '';

          // Check for box shadow (common focus indicator)
          const hasBoxShadow = computedStyle.boxShadow !== 'none';

          // Check for background color change
          const normalStyle = window.getComputedStyle(element);
          const backgroundChanged = computedStyle.backgroundColor !== normalStyle.backgroundColor;

          // Check for border changes
          const borderChanged = computedStyle.border !== normalStyle.border;

          return hasOutline || hasBoxShadow || backgroundChanged || borderChanged;
        },

        /**
         * Test keyboard interactions
         */
        async testKeyboardInteractions(element: HTMLElement): Promise<{
          activation: boolean;
          navigation: boolean;
          escape: boolean;
          custom: string[];
        }> {
          const interactions = {
            activation: false,
            navigation: false,
            escape: false,
            custom: [] as string[],
          };

          element.focus();

          // Test activation (Enter/Space)
          const activationKeys = ['Enter', 'Space'];
          for (const key of activationKeys) {
            try {
              const event = new KeyboardEvent('keydown', { key, bubbles: true });
              element.dispatchEvent(event);

              // Check if event was handled (preventDefault called or action occurred)
              if (event.defaultPrevented) {
                interactions.activation = true;
                break;
              }
            } catch (error) {
              // Ignore errors
            }
          }

          // Test navigation (Arrow keys)
          const navigationKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
          for (const key of navigationKeys) {
            try {
              const event = new KeyboardEvent('keydown', { key, bubbles: true });
              element.dispatchEvent(event);

              if (event.defaultPrevented) {
                interactions.navigation = true;
                interactions.custom.push(key);
              }
            } catch (error) {
              // Ignore errors
            }
          }

          // Test escape
          try {
            const event = new KeyboardEvent('keydown', { key: 'Escape', bubbles: true });
            element.dispatchEvent(event);

            if (event.defaultPrevented) {
              interactions.escape = true;
            }
          } catch (error) {
            // Ignore errors
          }

          // Test common custom keys
          const customKeys = ['Tab', 'Home', 'End', 'PageUp', 'PageDown'];
          for (const key of customKeys) {
            try {
              const event = new KeyboardEvent('keydown', { key, bubbles: true });
              element.dispatchEvent(event);

              if (event.defaultPrevented) {
                interactions.custom.push(key);
              }
            } catch (error) {
              // Ignore errors
            }
          }

          return interactions;
        },

        /**
         * Analyze ARIA support
         */
        analyzeAriaSupport(element: HTMLElement): {
          hasAriaLabel: boolean;
          hasAriaRole: boolean;
          hasAriaStates: boolean;
          ariaCompliant: boolean;
        } {
          const hasAriaLabel = !!(
            element.getAttribute('aria-label') ||
            element.getAttribute('aria-labelledby') ||
            element.getAttribute('aria-describedby')
          );

          const hasAriaRole = !!element.getAttribute('role');

          const ariaStateAttributes = [
            'aria-expanded',
            'aria-selected',
            'aria-checked',
            'aria-pressed',
            'aria-hidden',
            'aria-disabled',
            'aria-current',
            'aria-live',
          ];

          const hasAriaStates = ariaStateAttributes.some((attr) => element.hasAttribute(attr));

          // Basic ARIA compliance check
          const role = element.getAttribute('role');
          let ariaCompliant = true;

          if (role) {
            // Check role-specific requirements
            switch (role) {
              case 'button':
                ariaCompliant = hasAriaLabel || element.textContent?.trim() !== '';
                break;
              case 'checkbox':
              case 'radio':
                ariaCompliant = hasAriaLabel && element.hasAttribute('aria-checked');
                break;
              case 'tab':
                ariaCompliant = hasAriaLabel && element.hasAttribute('aria-selected');
                break;
              case 'menuitem':
                ariaCompliant = hasAriaLabel || element.textContent?.trim() !== '';
                break;
            }
          }

          return {
            hasAriaLabel,
            hasAriaRole,
            hasAriaStates,
            ariaCompliant,
          };
        },

        /**
         * Get focus order
         */
        getFocusOrder(): string[] {
          const focusableElements = document.querySelectorAll(
            'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])',
          );

          const elementsWithTabIndex = Array.from(focusableElements)
            .map((el) => ({
              element: el as HTMLElement,
              tabIndex: parseInt(el.getAttribute('tabindex') || '0'),
              selector: (
                window as unknown as {
                  keyboardTesting: { getElementSelector: (el: HTMLElement) => string };
                }
              ).keyboardTesting.getElementSelector(el as HTMLElement),
            }))
            .sort((a, b) => {
              if (a.tabIndex === b.tabIndex) {
                // Same tabindex, use document order
                return (
                  Array.from(focusableElements).indexOf(a.element) -
                  Array.from(focusableElements).indexOf(b.element)
                );
              }
              return a.tabIndex - b.tabIndex;
            });

          return elementsWithTabIndex.map((item) => item.selector);
        },

        /**
         * Test for focus traps
         */
        testFocusTraps(): string[] {
          const focusTraps: string[] = [];

          // Look for modal dialogs and other focus trap containers
          const potentialTraps = document.querySelectorAll(
            '[role="dialog"], [role="alertdialog"], .modal, .popup, .overlay',
          );

          potentialTraps.forEach((trap) => {
            const trapElement = trap as HTMLElement;

            // Check if it contains focusable elements
            const focusableInTrap = trapElement.querySelectorAll(
              'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])',
            );

            if (focusableInTrap.length > 0) {
              // Check if it's currently visible
              const style = window.getComputedStyle(trapElement);
              if (style.display !== 'none' && style.visibility !== 'hidden') {
                focusTraps.push(
                  (
                    window as unknown as {
                      keyboardTesting: { getElementSelector: (el: HTMLElement) => string };
                    }
                  ).keyboardTesting.getElementSelector(trapElement),
                );
              }
            }
          });

          return focusTraps;
        },

        /**
         * Detect keyboard shortcuts
         */
        detectKeyboardShortcuts(): { key: string; action: string; element: string }[] {
          const shortcuts: { key: string; action: string; element: string }[] = [];

          // Look for elements with accesskey attribute
          const elementsWithAccessKey = document.querySelectorAll('[accesskey]');
          elementsWithAccessKey.forEach((el) => {
            const element = el as HTMLElement;
            const accessKey = element.getAttribute('accesskey');
            if (accessKey) {
              shortcuts.push({
                key: `Alt+${accessKey}`,
                action: 'activate',
                element: (
                  window as unknown as {
                    keyboardTesting: { getElementSelector: (el: HTMLElement) => string };
                  }
                ).keyboardTesting.getElementSelector(element),
              });
            }
          });

          // Look for common keyboard shortcut patterns in event listeners
          // This is a simplified detection - in practice, you'd need more sophisticated analysis
          const elementsWithKeyHandlers = document.querySelectorAll(
            '[onkeydown], [onkeyup], [onkeypress]',
          );
          elementsWithKeyHandlers.forEach((el) => {
            const element = el as HTMLElement;
            shortcuts.push({
              key: 'various',
              action: 'custom',
              element: (
                window as unknown as {
                  keyboardTesting: { getElementSelector: (el: HTMLElement) => string };
                }
              ).keyboardTesting.getElementSelector(element),
            });
          });

          return shortcuts;
        },
      };
    });
  }

  /**
   * Get interactive elements from the page
   */
  private async getInteractiveElements(
    page: Page,
    config: KeyboardTestConfig,
  ): Promise<
    {
      selector: string;
      element: {
        tagName: string;
        type: string | null;
        role: string | null;
        tabIndex: string | null;
      };
    }[]
  > {
    return await page.evaluate((includeHidden) => {
      const elements: {
        selector: string;
        element: {
          tagName: string;
          type: string | null;
          role: string | null;
          tabIndex: string | null;
        };
      }[] = [];
      const keyboardTesting = (window as unknown as Record<string, unknown>).keyboardTesting as {
        isInteractiveElement: (element: HTMLElement) => boolean;
        getElementSelector: (element: HTMLElement) => string;
      };

      // Get all potentially interactive elements
      const allElements = document.querySelectorAll('*');

      allElements.forEach((el) => {
        const element = el as HTMLElement;

        // Skip hidden elements unless configured to include them
        if (!includeHidden) {
          const style = window.getComputedStyle(element);
          if (style.display === 'none' || style.visibility === 'hidden') {
            return;
          }
        }

        if (keyboardTesting.isInteractiveElement(element)) {
          elements.push({
            selector: keyboardTesting.getElementSelector(element),
            element: {
              tagName: element.tagName,
              type: element.getAttribute('type'),
              role: element.getAttribute('role'),
              tabIndex: element.getAttribute('tabindex'),
            },
          });
        }
      });

      return elements;
    }, config.includeHiddenElements);
  }

  /**
   * Test individual element
   */
  private async testElement(
    page: Page,
    elementInfo: {
      selector: string;
      element: {
        tagName: string;
        type: string | null;
        role: string | null;
        tabIndex: string | null;
      };
    },
    config: KeyboardTestConfig,
  ): Promise<KeyboardTestResult> {
    return await page.evaluate(
      async (selector, _testConfig) => {
        const keyboardTesting = (window as unknown as Record<string, unknown>).keyboardTesting as {
          canReceiveFocus: (element: HTMLElement) => boolean;
          hasVisibleFocus: (element: HTMLElement) => boolean;
          testKeyboardInteractions: (element: HTMLElement) => Promise<{
            activation: boolean;
            navigation: boolean;
            escape: boolean;
            custom: string[];
          }>;
          analyzeAriaSupport: (element: HTMLElement) => {
            hasAriaLabel: boolean;
            hasAriaRole: boolean;
            hasAriaStates: boolean;
            ariaCompliant: boolean;
          };
        };
        const element = document.querySelector(selector) as HTMLElement;

        if (!element) {
          throw new Error(`Element not found: ${selector}`);
        }

        const issues: string[] = [];
        const recommendations: string[] = [];

        // Test focus management
        const canReceiveFocus = keyboardTesting.canReceiveFocus(element);
        const hasVisibleFocus = canReceiveFocus ? keyboardTesting.hasVisibleFocus(element) : false;

        if (!canReceiveFocus) {
          issues.push('Element cannot receive keyboard focus');
          recommendations.push('Add tabindex="0" or make element naturally focusable');
        }

        if (canReceiveFocus && !hasVisibleFocus) {
          issues.push('Element lacks visible focus indicator');
          recommendations.push('Add CSS focus styles (outline, box-shadow, etc.)');
        }

        // Test keyboard interactions
        const interactions = await keyboardTesting.testKeyboardInteractions(element);

        if (!interactions.activation && ['BUTTON', 'A'].includes(element.tagName)) {
          issues.push('Element does not respond to Enter/Space keys');
          recommendations.push('Add keyboard event handlers for activation');
        }

        // Test ARIA support
        const ariaSupport = keyboardTesting.analyzeAriaSupport(element);

        if (!ariaSupport.ariaCompliant) {
          issues.push('Element has incomplete ARIA implementation');
          recommendations.push('Review and complete ARIA attributes for accessibility');
        }

        // Calculate score
        let score = 100;
        score -= issues.length * 15; // Deduct 15 points per issue
        if (canReceiveFocus) score += 20;
        if (hasVisibleFocus) score += 20;
        if (interactions.activation) score += 15;
        if (ariaSupport.ariaCompliant) score += 15;

        score = Math.max(0, Math.min(100, score));

        return {
          element: element.tagName.toLowerCase(),
          selector,
          isKeyboardAccessible: issues.length === 0,
          supportedKeys: [
            ...(interactions.activation ? ['Enter', 'Space'] : []),
            ...interactions.custom,
          ],
          focusManagement: {
            canReceiveFocus,
            hasVisibleFocus,
            focusOrder: 0, // Will be set later
            trapsFocus: false, // Will be determined separately
          },
          ariaSupport,
          interactions,
          issues,
          recommendations,
          score,
        };
      },
      elementInfo.selector,
      config,
    );
  }

  /**
   * Test focus order
   */
  private async testFocusOrder(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const keyboardTesting = (window as unknown as Record<string, unknown>).keyboardTesting as {
        getFocusOrder: () => string[];
      };
      return keyboardTesting.getFocusOrder();
    });
  }

  /**
   * Test focus traps
   */
  private async testFocusTraps(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const keyboardTesting = (window as unknown as Record<string, unknown>).keyboardTesting as {
        testFocusTraps: () => string[];
      };
      return keyboardTesting.testFocusTraps();
    });
  }

  /**
   * Test keyboard shortcuts
   */
  private async testKeyboardShortcuts(
    page: Page,
  ): Promise<{ key: string; action: string; element: string }[]> {
    return await page.evaluate(() => {
      const keyboardTesting = (window as unknown as Record<string, unknown>).keyboardTesting as {
        detectKeyboardShortcuts: () => { key: string; action: string; element: string }[];
      };
      return keyboardTesting.detectKeyboardShortcuts();
    });
  }

  /**
   * Generate comprehensive report
   */
  private generateReport(
    testResults: KeyboardTestResult[],
    focusOrder: string[],
    focusTraps: string[],
    keyboardShortcuts: { key: string; action: string; element: string }[],
  ): KeyboardNavigationReport {
    const totalElements = testResults.length;
    const accessibleElements = testResults.filter((r) => r.isKeyboardAccessible).length;
    const inaccessibleElements = totalElements - accessibleElements;

    const overallScore =
      totalElements > 0
        ? Math.round(testResults.reduce((sum, r) => sum + r.score, 0) / totalElements)
        : 100;

    // Collect critical issues
    const criticalIssues: string[] = [];
    testResults.forEach((result) => {
      if (result.score < 50) {
        criticalIssues.push(`Critical keyboard accessibility issues in ${result.selector}`);
      }
      if (!result.focusManagement.canReceiveFocus && result.element !== 'div') {
        criticalIssues.push(`Interactive element cannot receive focus: ${result.selector}`);
      }
    });

    // Generate recommendations
    const recommendations: string[] = [];
    if (inaccessibleElements > 0) {
      recommendations.push(
        `${inaccessibleElements} elements need keyboard accessibility improvements`,
      );
    }
    if (focusOrder.length > 20) {
      recommendations.push('Consider simplifying focus order for better usability');
    }
    if (focusTraps.length === 0) {
      recommendations.push('Consider implementing focus traps for modal dialogs');
    }

    // Update focus order in test results
    testResults.forEach((result) => {
      const index = focusOrder.indexOf(result.selector);
      result.focusManagement.focusOrder = index >= 0 ? index : -1;
      result.focusManagement.trapsFocus = focusTraps.includes(result.selector);
    });

    return {
      totalElements,
      accessibleElements,
      inaccessibleElements,
      overallScore,
      focusOrder,
      focusTraps,
      keyboardShortcuts,
      criticalIssues,
      recommendations,
      testResults,
    };
  }
}

export default KeyboardNavigationTester;
