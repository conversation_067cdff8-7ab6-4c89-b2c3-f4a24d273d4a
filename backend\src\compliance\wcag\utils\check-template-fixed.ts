/**
 * WCAG Check Template System - Fixed Version
 * Provides consistent structure for all WCAG checks
 */

import { Page } from 'puppeteer';
import {
  WcagCheckResult,
  WcagEvidence,
  WcagCategory,
  WcagVersion,
  WcagLevel,
} from '../types';
import {
  SCORING_CONFIG,
  ScoringConfig,
  PENALTY_TIERS,
  CATEGORY_THRESHOLDS,
  LEVEL_THRESHOLDS,
  CONFIDENCE_ADJUSTMENTS,
  EnhancedScoringResult,
  ScoringStatus,
} from '../constants';
import { UnifiedDOMExtractor, PageStructure } from './unified-dom-extractor';
import logger from '../../../utils/logger';

// Define WcagCheckTemplateResult interface first to avoid forward reference issues
export interface WcagCheckTemplateResult {
  score: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
  confidence?: number; // ✅ Detection confidence (0.0 to 1.0) for scoring adjustments
}

export interface CheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
  page?: Page;
  pageStructure?: PageStructure; // ✅ Unified DOM structure for all checks
}

export interface EnhancedCheckConfig extends CheckConfig {
  retryAttempts: number;
  enableJavaScript: boolean;
  enableImages: boolean;
  followRedirects: boolean;
}

export type CheckFunction<T extends CheckConfig> = (
  page: Page,
  config: T,
) => Promise<WcagCheckTemplateResult>;

export class CheckTemplate {
  private domExtractor: UnifiedDOMExtractor;

  constructor() {
    this.domExtractor = UnifiedDOMExtractor.getInstance();
  }

  /**
   * ✅ ENHANCED WCAG COMPLIANCE CALCULATION: Graduated Penalty System
   * Implements partial credit, confidence weighting, and category-specific thresholds
   */
  private calculateWcagCompliance(
    result: WcagCheckTemplateResult,
    category?: WcagCategory,
    level?: WcagLevel,
    confidence?: number,
    config: ScoringConfig = {
      passThreshold: SCORING_CONFIG.DEFAULT_PASS_THRESHOLD,
      strictMode: SCORING_CONFIG.STRICT_MODE,
      enableGradualScoring: SCORING_CONFIG.ENABLE_GRADUAL_SCORING,
      enableThresholdLogging: SCORING_CONFIG.ENABLE_THRESHOLD_LOGGING,
      enableGraduatedPenalties: SCORING_CONFIG.ENABLE_GRADUATED_PENALTIES,
      enableConfidenceWeighting: SCORING_CONFIG.ENABLE_CONFIDENCE_WEIGHTING,
      enableCategoryThresholds: SCORING_CONFIG.ENABLE_CATEGORY_THRESHOLDS,
    }
  ): EnhancedScoringResult {
    const originalScore = result.score;
    const maxScore = result.maxScore;
    const scorePercentage = maxScore > 0 ? Math.round((originalScore / maxScore) * 100) : 0;

    // ✅ STEP 1: Determine threshold (category-specific or default)
    let threshold = config.passThreshold;
    if (config.enableCategoryThresholds && category) {
      threshold = CATEGORY_THRESHOLDS[category] || config.passThreshold;
    }

    // ✅ STEP 2: Apply confidence weighting if enabled
    let adjustedScore = originalScore;
    let adjustedPercentage = scorePercentage;
    let confidenceAdjustment = 1.0;

    if (config.enableConfidenceWeighting && confidence !== undefined) {
      const adjustment = CONFIDENCE_ADJUSTMENTS.find((adj) => confidence >= adj.min);
      if (adjustment) {
        confidenceAdjustment = adjustment.multiplier;
        adjustedScore = Math.round(originalScore * confidenceAdjustment);
        adjustedPercentage = maxScore > 0 ? Math.round((adjustedScore / maxScore) * 100) : 0;
      }
    }

    // ✅ STEP 3: Apply graduated penalty system or legacy scoring
    let finalScore: number;
    let status: ScoringStatus;
    let penaltyTier: (typeof PENALTY_TIERS)[number] | undefined;

    if (config.strictMode) {
      // Legacy strict mode: binary scoring
      const isPassed = adjustedPercentage >= threshold;
      finalScore = isPassed ? adjustedScore : 0;
      status = isPassed ? 'passed' : 'failed';
    } else if (config.enableGraduatedPenalties) {
      // ✅ NEW: Graduated penalty system with partial credit
      penaltyTier = PENALTY_TIERS.find((tier) => adjustedPercentage >= tier.min);

      if (penaltyTier) {
        finalScore = Math.round(adjustedScore * penaltyTier.multiplier);
        status = penaltyTier.status;
      } else {
        // Below minimum threshold - complete failure
        finalScore = 0;
        status = 'failed';
      }
    } else {
      // ✅ ENHANCED: Gradual scoring with threshold
      if (adjustedPercentage >= threshold) {
        finalScore = adjustedScore;
        status = 'passed';
      } else if (adjustedPercentage >= 50) {
        // Partial credit for scores above 50%
        finalScore = Math.round(adjustedScore * 0.5);
        status = 'partial';
      } else {
        finalScore = 0;
        status = 'failed';
      }
    }

    return {
      score: finalScore,
      originalScore,
      adjustedScore,
      status,
      threshold,
      penaltyTier,
      confidenceAdjustment,
      details: this.generateScoringDetails(
        scorePercentage,
        adjustedPercentage,
        threshold,
        status,
        penaltyTier,
        confidenceAdjustment,
        confidence
      ),
    };
  }

  /**
   * Generate detailed scoring explanation
   */
  private generateScoringDetails(
    originalPercentage: number,
    adjustedPercentage: number,
    threshold: number,
    status: ScoringStatus,
    penaltyTier?: (typeof PENALTY_TIERS)[number],
    confidenceAdjustment?: number,
    confidence?: number
  ): string {
    let details = `Original: ${originalPercentage}%`;

    if (confidenceAdjustment && confidenceAdjustment !== 1.0) {
      details += ` → Confidence-adjusted: ${adjustedPercentage}% (confidence: ${confidence?.toFixed(2)}, multiplier: ${confidenceAdjustment})`;
    }

    details += ` → Threshold: ${threshold}%`;

    if (penaltyTier) {
      details += ` → Penalty tier: ${penaltyTier.min}%+ (${penaltyTier.multiplier}x)`;
    }

    details += ` → ${status.toUpperCase()}`;

    return details;
  }

  /**
   * Execute a WCAG check with consistent error handling and logging
   */
  async executeCheck<T extends CheckConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    config: T,
    checkFunction: CheckFunction<T>,
    requiresBrowser: boolean = true,
    requiresManualReview: boolean = false,
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();

    try {
      logger.info(`🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName}`);

      if (requiresManualReview) {
        throw new Error('Manual review checks should not use fully automated template');
      }

      try {
        if (requiresBrowser && !config.page) {
          throw new Error('Browser instance required - page not provided in config');
        }

        // ✅ USE UNIFIED DOM EXTRACTOR: Extract page structure once for all checks
        if (!config.pageStructure && config.page) {
          logger.debug(`🔍 [${config.scanId}] Extracting page structure for ${ruleId}`);
          config.pageStructure = await this.domExtractor.extractPageStructure(config.page, config.targetUrl);
        }

        // Execute the check function
        const result = await checkFunction(config.page!, config);

        // ✅ ENHANCED SCORING: Apply graduated penalty system
        const scoringResult = this.calculateWcagCompliance(
          result,
          category as WcagCategory,
          level as WcagLevel,
          result.confidence
        );

        const { score: finalScore, status, threshold, penaltyTier, confidenceAdjustment, details } = scoringResult;

        const executionTime = Date.now() - startTime;
        const originalScore = result.score;
        const adjustedScore = Math.round(originalScore * (confidenceAdjustment || 1.0));

        logger.info(
          `✅ [${config.scanId}] ${ruleId} completed: ${status} (${finalScore}/${result.maxScore}) - ${details}`
        );

        return {
          ruleId,
          ruleName,
          category: category as WcagCategory,
          wcagVersion: this.getVersionFromRuleId(ruleId),
          successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
          level: level as WcagLevel,
          status: status as 'passed' | 'failed', // Convert to legacy status for compatibility
          score: finalScore, // ✅ Enhanced score with graduated penalties
          maxScore: result.maxScore,
          weight,
          automated: true,
          evidence: result.evidence,
          recommendations: result.recommendations,
          executionTime,
          // ✅ Enhanced scoring metadata for analysis and debugging
          originalScore: originalScore, // Original score before any adjustments
          adjustedScore: adjustedScore, // Score after confidence weighting
          thresholdApplied: threshold, // Actual threshold used (may be category-specific)
          scoringDetails: details, // Full scoring explanation
          penaltyTier: penaltyTier?.multiplier, // Penalty multiplier applied
          confidenceAdjustment: scoringResult.confidenceAdjustment, // Confidence adjustment factor
          enhancedStatus: status, // Full status including 'partial'
        };
      } finally {
        // Browser cleanup will be handled by orchestrator
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`❌ [${config.scanId}] Error in ${ruleId}`, {
        error: {
          message: errorMessage,
          stack: errorStack,
          name: error instanceof Error ? error.name : 'UnknownError',
        },
        ruleId,
        ruleName,
        executionTime,
      });

      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [
          {
            type: 'text',
            description: 'Technical error during check execution',
            value: errorMessage,
            severity: 'error',
          },
        ],
        recommendations: [
          'Check failed due to technical error - manual review recommended',
          `Error details: ${errorMessage}`,
          'Check browser console and server logs for more information',
        ],
        executionTime,
        errorMessage,
      };
    }
  }

  /**
   * Get WCAG version from rule ID
   */
  protected getVersionFromRuleId(ruleId: string): WcagVersion {
    // Default to 2.2 for now - could be enhanced to parse from rule ID
    return '2.2';
  }

  /**
   * Get success criterion from rule ID
   */
  protected getSuccessCriterionFromRuleId(ruleId: string): string {
    // Extract from rule ID or use a mapping
    const match = ruleId.match(/WCAG-(\d+)/);
    return match ? `${match[1].charAt(0)}.${match[1].charAt(1)}.${match[1].charAt(2)}` : '1.1.1';
  }
}
