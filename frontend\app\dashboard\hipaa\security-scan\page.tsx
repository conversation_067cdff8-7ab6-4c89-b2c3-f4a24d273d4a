'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Check } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  Shield,
  AlertCircle,
  Play,
  Settings,
  Lock,
  Globe,
  Search,
  ArrowLeft,
  Info,
} from 'lucide-react';

interface SecurityScanConfig {
  targetUrl: string;
  maxPages: number;
  scanDepth: number;
  timeout: number;
  enableVulnerabilityScanning: boolean;
  enableSSLAnalysis: boolean;
  enableContentAnalysis: boolean;
  enablePortScanning: boolean;
  enableHeaderAnalysis: boolean;
  userAgent: string;
}

export default function HipaaSecurityScanPage() {
  const router = useRouter();
  const [scanConfig, setScanConfig] = useState<SecurityScanConfig>({
    targetUrl: '',
    maxPages: 15, // Best default setting
    scanDepth: 2, // Best default setting
    timeout: 1800000, // 30 minutes - best default for comprehensive scanning
    enableVulnerabilityScanning: true,
    enableSSLAnalysis: true,
    enableContentAnalysis: true,
    enablePortScanning: false, // Keep disabled by default for performance
    enableHeaderAnalysis: true,
    userAgent: 'HIPAA-Security-Scanner/1.0',
  });

  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scanProgress, setScanProgress] = useState(0);

  const handleBackToDashboard = () => {
    router.push('/dashboard/hipaa');
  };

  const handleStartScan = async () => {
    if (!scanConfig.targetUrl.trim()) {
      setError('Please enter a valid URL to scan');
      return;
    }

    setIsScanning(true);
    setError(null);
    setScanProgress(0);

    try {
      // Simulate scan progress
      const progressInterval = setInterval(() => {
        setScanProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 1500);

      const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3001/api/v1';
      const response = await fetch(`${apiUrl}/hipaa-security/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scanConfig),
      });

      clearInterval(progressInterval);
      setScanProgress(100);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Redirect to the dedicated scan results page
        router.push(`/dashboard/hipaa/security/${result.data.scanId}`);
      } else {
        throw new Error(result.error || 'Scan failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsScanning(false);
      setScanProgress(0);
    }
  };

  const updateConfig = (key: keyof SecurityScanConfig, value: string | number | boolean) => {
    setScanConfig((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: '#F5F5F5', color: '#333333' }}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToDashboard}
              style={{
                borderColor: '#0055A4',
                color: '#0055A4',
                backgroundColor: 'white',
              }}
              className="hover:bg-blue-50 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to HIPAA Dashboard
            </Button>
            <div className="flex items-center gap-3">
              <Lock className="h-8 w-8" style={{ color: '#0055A4' }} />
              <div>
                <h1 className="text-3xl font-bold" style={{ color: '#333333' }}>
                  HIPAA Security Scan
                </h1>
                <p style={{ color: '#666666' }}>Comprehensive security vulnerability assessment</p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className="border-red-200" style={{ backgroundColor: '#FEF2F2' }}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription style={{ color: '#DC2626' }}>{error}</AlertDescription>
          </Alert>
        )}

        {/* Scan Configuration */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Configuration */}
          <div className="lg:col-span-2 space-y-6">
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardHeader>
                <CardTitle style={{ color: '#333333' }}>Scan Configuration</CardTitle>
                <CardDescription style={{ color: '#666666' }}>
                  Configure your HIPAA security vulnerability scan
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="targetUrl">Target URL *</Label>
                  <Input
                    id="targetUrl"
                    type="url"
                    placeholder="https://example.com"
                    value={scanConfig.targetUrl}
                    onChange={(e) => updateConfig('targetUrl', e.target.value)}
                    disabled={isScanning}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxPages">Max Pages</Label>
                    <Input
                      id="maxPages"
                      type="number"
                      value={scanConfig.maxPages}
                      onChange={(e) => updateConfig('maxPages', parseInt(e.target.value))}
                      disabled={isScanning}
                      min="1"
                      max="100"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="scanDepth">Scan Depth</Label>
                    <Input
                      id="scanDepth"
                      type="number"
                      value={scanConfig.scanDepth}
                      onChange={(e) => updateConfig('scanDepth', parseInt(e.target.value))}
                      disabled={isScanning}
                      min="1"
                      max="5"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Timeout (seconds)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      value={scanConfig.timeout / 1000}
                      onChange={(e) => updateConfig('timeout', parseInt(e.target.value) * 1000)}
                      disabled={isScanning}
                      min="30"
                      max="600"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="userAgent">User Agent</Label>
                  <Input
                    id="userAgent"
                    value={scanConfig.userAgent}
                    onChange={(e) => updateConfig('userAgent', e.target.value)}
                    disabled={isScanning}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Security Analysis Types */}
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardHeader>
                <CardTitle style={{ color: '#333333' }}>Security Analysis Types</CardTitle>
                <CardDescription style={{ color: '#666666' }}>
                  All essential security analysis types are automatically enabled for comprehensive
                  scanning
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div
                    className="flex items-center justify-center w-5 h-5 rounded-full"
                    style={{ backgroundColor: '#22C55E' }}
                  >
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Shield className="h-4 w-4" style={{ color: '#EF4444' }} />
                      Vulnerability Scanning
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Scan for known security vulnerabilities and weaknesses
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div
                    className="flex items-center justify-center w-5 h-5 rounded-full"
                    style={{ backgroundColor: '#22C55E' }}
                  >
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Lock className="h-4 w-4" style={{ color: '#22C55E' }} />
                      SSL/TLS Analysis
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Analyze SSL/TLS configuration and certificate validity
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div
                    className="flex items-center justify-center w-5 h-5 rounded-full"
                    style={{ backgroundColor: '#22C55E' }}
                  >
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Search className="h-4 w-4" style={{ color: '#F59E0B' }} />
                      Content Analysis
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Analyze page content for security issues and data exposure
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div
                    className="flex items-center justify-center w-5 h-5 rounded-full"
                    style={{ backgroundColor: '#22C55E' }}
                  >
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Globe className="h-4 w-4" style={{ color: '#3B82F6' }} />
                      Security Headers Analysis
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Check for proper security headers implementation
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div
                    className="flex items-center justify-center w-5 h-5 rounded-full"
                    style={{ backgroundColor: '#9CA3AF' }}
                  >
                    <Settings className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Settings className="h-4 w-4" style={{ color: '#8B5CF6' }} />
                      Port Scanning
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Disabled by default for performance (can be enabled if needed)
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Start Scan */}
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardContent className="pt-6">
                <Button
                  onClick={handleStartScan}
                  disabled={isScanning || !scanConfig.targetUrl.trim()}
                  className="w-full hover:bg-blue-700 transition-colors"
                  style={{
                    backgroundColor: '#0055A4',
                    color: 'white',
                    border: 'none',
                  }}
                >
                  {isScanning ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Scanning... {scanProgress}%
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Start Security Scan
                    </>
                  )}
                </Button>

                {isScanning && (
                  <div className="mt-4">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 rounded-full transition-all duration-300"
                        style={{
                          backgroundColor: '#0055A4',
                          width: `${scanProgress}%`,
                        }}
                      ></div>
                    </div>
                    <p className="text-sm mt-2 text-center" style={{ color: '#666666' }}>
                      Analyzing security vulnerabilities...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Scan Summary */}
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardHeader>
                <CardTitle style={{ color: '#333333' }}>Scan Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>
                    Max Pages:
                  </span>
                  <Badge variant="outline" style={{ borderColor: '#0055A4', color: '#0055A4' }}>
                    {scanConfig.maxPages}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>
                    Scan Depth:
                  </span>
                  <Badge variant="outline" style={{ borderColor: '#0055A4', color: '#0055A4' }}>
                    {scanConfig.scanDepth}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>
                    Timeout:
                  </span>
                  <Badge variant="outline" style={{ borderColor: '#0055A4', color: '#0055A4' }}>
                    {Math.round(scanConfig.timeout / 60000)}min
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>
                    Analysis Types:
                  </span>
                  <Badge variant="outline" style={{ borderColor: '#22C55E', color: '#22C55E' }}>
                    {
                      [
                        scanConfig.enableVulnerabilityScanning && 'Vuln',
                        scanConfig.enableSSLAnalysis && 'SSL',
                        scanConfig.enableContentAnalysis && 'Content',
                        scanConfig.enableHeaderAnalysis && 'Headers',
                        scanConfig.enablePortScanning && 'Ports',
                      ].filter(Boolean).length
                    }{' '}
                    Enabled
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Info Card */}
            <Card style={{ backgroundColor: '#FEF3F2', color: '#333333' }}>
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 mt-0.5" style={{ color: '#EF4444' }} />
                  <div>
                    <h4 className="font-semibold mb-2" style={{ color: '#333333' }}>
                      About Security Scans
                    </h4>
                    <p className="text-sm" style={{ color: '#666666' }}>
                      Our security scanner uses industry-standard tools like Nuclei to identify
                      vulnerabilities, SSL issues, and security misconfigurations that could impact
                      HIPAA compliance.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
