/**
 * HTML Language Check Tests
 * Tests for WCAG-024: Language of Page check
 */

import { HtmlLangCheck } from '../html-lang';
import { CheckConfig } from '../../utils/check-template';
import { Page } from 'puppeteer';

// Mock Puppeteer page
const mockPage = {
  evaluate: jest.fn(),
} as unknown as Page;

const mockConfig: CheckConfig = {
  targetUrl: 'https://example.com',
  page: mockPage,
  requestId: 'test-request',
  userId: 'test-user',
  scanOptions: {},
};

describe('HtmlLangCheck', () => {
  let htmlLangCheck: HtmlLangCheck;

  beforeEach(() => {
    htmlLangCheck = new HtmlLangCheck();
    jest.clearAllMocks();
  });

  describe('performCheck', () => {
    it('should return enhanced check result structure', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en">',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result).toMatchObject({
        ruleId: 'WCAG-024',
        ruleName: 'Language of Page',
        category: 'understandable',
        level: 'A',
        elementCounts: {
          total: 1,
          failed: 0,
          passed: 1,
        },
        performance: {
          scanDuration: expect.any(Number),
          elementsAnalyzed: 1,
        },
        checkMetadata: {
          version: '1.0.0',
          algorithm: 'html-lang-detection',
          confidence: 1.0,
        },
      });
    });
  });

  describe('executeHtmlLangCheck', () => {
    it('should pass when html has valid lang attribute', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en">',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.score).toBe(100);
      expect(result.maxScore).toBe(100);
      expect(result.issues).toHaveLength(0);
      expect(result.evidence).toHaveLength(1);
      expect(result.evidence[0]).toMatchObject({
        type: 'info',
        description: 'HTML element has valid lang attribute',
        severity: 'info',
        elementCount: 1,
        affectedSelectors: ['html'],
      });
    });

    it('should fail when html is missing lang attribute', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: false,
        langValue: null,
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html>',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('HTML document missing lang attribute');
      expect(result.evidence).toHaveLength(1);
      expect(result.evidence[0]).toMatchObject({
        type: 'code',
        description: 'HTML element missing lang attribute',
        severity: 'error',
        elementCount: 1,
        fixExample: {
          before: '<html>',
          after: '<html lang="en">',
          description: 'Add lang attribute to html element',
        },
      });
    });

    it('should fail when html has invalid lang code', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: 'invalid-code-123',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="invalid-code-123">',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('Invalid language code: invalid-code-123');
      expect(result.evidence).toHaveLength(1);
      expect(result.evidence[0]).toMatchObject({
        type: 'code',
        description: 'Invalid language code format',
        severity: 'error',
        fixExample: {
          before: '<html lang="invalid-code-123">',
          after: '<html lang="en">',
          description: 'Use valid ISO language code',
        },
      });
    });

    it('should pass with valid complex lang codes', async () => {
      const validCodes = ['en-US', 'fr-CA', 'zh-CN', 'es-MX'];

      for (const code of validCodes) {
        (mockPage.evaluate as jest.Mock).mockResolvedValue({
          hasLang: true,
          langValue: code,
          hasXmlLang: false,
          xmlLangValue: null,
          htmlOuterHTML: `<html lang="${code}">`,
        });

        const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

        expect(result.score).toBe(100);
        expect(result.issues).toHaveLength(0);
      }
    });

    it('should include xml:lang information in metadata', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: true,
        xmlLangValue: 'en',
        htmlOuterHTML: '<html lang="en" xml:lang="en">',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.evidence[0].metadata?.checkSpecificData).toMatchObject({
        hasXmlLang: true,
        xmlLangValue: 'en',
      });
    });

    it('should provide appropriate recommendations', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: false,
        langValue: null,
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html>',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.recommendations).toContain(
        'Add lang attribute to html element: <html lang="en">',
      );
      expect(result.recommendations).toContain('Use a valid ISO 639-1 language code');
      expect(result.recommendations).toContain('Consider the primary language of your content');
    });

    it('should suggest xml:lang for XHTML compatibility when missing', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en">',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.recommendations).toContain(
        'Consider adding xml:lang attribute for XHTML compatibility',
      );
    });

    it('should include performance metadata', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en">',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.evidence[0].metadata).toMatchObject({
        scanDuration: expect.any(Number),
        elementsAnalyzed: 1,
      });
    });

    it('should handle empty lang attribute as missing', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: true,
        langValue: '',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="">',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('HTML document missing lang attribute');
    });

    it('should include fix example resources', async () => {
      (mockPage.evaluate as jest.Mock).mockResolvedValue({
        hasLang: false,
        langValue: null,
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html>',
      });

      const result = await (htmlLangCheck as any).executeHtmlLangCheck(mockPage, mockConfig);

      expect(result.evidence[0].fixExample?.resources).toContain(
        'https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html',
      );
      expect(result.evidence[0].fixExample?.resources).toContain(
        'https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang',
      );
    });
  });

  describe('getCommonLanguageCodes', () => {
    it('should return array of common language codes', () => {
      const codes = (htmlLangCheck as any).getCommonLanguageCodes();

      expect(Array.isArray(codes)).toBe(true);
      expect(codes).toContain('en');
      expect(codes).toContain('en-US');
      expect(codes).toContain('fr');
      expect(codes).toContain('es');
      expect(codes).toContain('de');
      expect(codes.length).toBeGreaterThan(20);
    });
  });
});
