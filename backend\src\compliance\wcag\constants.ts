/**
 * WCAG Constants and Configuration
 * STRICT SEPARATION: Automated vs Manual rules
 * Following HIPAA/GDPR patterns with single automated scoring
 */

import { WcagRuleConfig, WcagCategory, WcagVersion, WcagScanOptions } from './types';

// AUTOMATED WCAG Rules - ONLY these contribute to main score
export const WCAG_AUTOMATED_RULES: WcagRuleConfig[] = [
  // WCAG 2.1 Automated Rules (high automation level only)
  {
    ruleId: 'WCAG-001',
    ruleName: 'Non-text Content',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.1.1',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'All non-text content has text alternatives',
    checkFunction: 'NonTextContentCheck',
  },
  {
    ruleId: 'WCAG-002',
    ruleName: 'Captions (Prerecorded)',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.2.2',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Captions provided for prerecorded audio content',
    checkFunction: 'CaptionsCheck',
  },
  {
    ruleId: 'WCAG-003',
    ruleName: 'Info and Relationships',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.3.1',
    level: 'A',
    weight: 0.0687,
    automated: true,
    description:
      'Information and relationships conveyed through presentation can be programmatically determined',
    checkFunction: 'InfoRelationshipsCheck',
  },
  {
    ruleId: 'WCAG-004',
    ruleName: 'Contrast (Minimum)',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.3',
    level: 'AA',
    weight: 0.0763,
    automated: true,
    description: 'Text has sufficient contrast ratio',
    checkFunction: 'ContrastMinimumCheck',
  },
  {
    ruleId: 'WCAG-005',
    ruleName: 'Keyboard',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.1.1',
    level: 'A',
    weight: 0.0687,
    automated: true,
    description: 'All functionality is available from a keyboard',
    checkFunction: 'KeyboardCheck',
  },
  {
    ruleId: 'WCAG-006',
    ruleName: 'Focus Order',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.3',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Focusable components receive focus in logical order',
    checkFunction: 'FocusOrderCheck',
  },
  {
    ruleId: 'WCAG-007',
    ruleName: 'Focus Visible',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.7',
    level: 'AA',
    weight: 0.0687,
    automated: true,
    description: 'Keyboard focus indicator is visible',
    checkFunction: 'FocusVisibleCheck',
  },
  {
    ruleId: 'WCAG-008',
    ruleName: 'Error Identification',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.3.1',
    level: 'A',
    weight: 0.0534,
    automated: true,
    description: 'Input errors are identified and described to user',
    checkFunction: 'ErrorIdentificationCheck',
  },
  {
    ruleId: 'WCAG-009',
    ruleName: 'Name, Role, Value',
    category: 'robust',
    wcagVersion: '2.1',
    successCriterion: '4.1.2',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'UI components have accessible name, role, and value',
    checkFunction: 'NameRoleValueCheck',
  },

  // WCAG 2.2 New Rules (7 rules)
  {
    ruleId: 'WCAG-010',
    ruleName: 'Focus Not Obscured (Minimum)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.11',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Focused element is not fully hidden by author content',
    checkFunction: 'FocusNotObscuredMinCheck',
  },
  {
    ruleId: 'WCAG-011',
    ruleName: 'Focus Not Obscured (Enhanced)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.12',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Focused element is never hidden by author content',
    checkFunction: 'FocusNotObscuredEnhCheck',
  },
  {
    ruleId: 'WCAG-012',
    ruleName: 'Focus Appearance',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.13',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Focus indicator meets size and contrast requirements',
    checkFunction: 'FocusAppearanceCheck',
  },
  {
    ruleId: 'WCAG-013',
    ruleName: 'Dragging Movements',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.5.7',
    level: 'AA',
    weight: 0.0382,
    automated: true,
    description: 'Dragging movements have single pointer alternative',
    checkFunction: 'DraggingMovementsCheck',
  },
  {
    ruleId: 'WCAG-014',
    ruleName: 'Target Size (Minimum)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.5.8',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Target size is at least 24×24 CSS pixels',
    checkFunction: 'TargetSizeCheck',
  },
  {
    ruleId: 'WCAG-015',
    ruleName: 'Consistent Help',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.2.6',
    level: 'A',
    weight: 0.0305,
    automated: true,
    description: 'Help mechanisms appear in consistent order',
    checkFunction: 'ConsistentHelpCheck',
  },
  {
    ruleId: 'WCAG-016',
    ruleName: 'Redundant Entry',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.3.7',
    level: 'A',
    weight: 0.0382,
    automated: true,
    description: 'Information previously entered is auto-populated',
    checkFunction: 'RedundantEntryCheck',
  },

  // WCAG 3.0 Draft Rules (5 rules)
  {
    ruleId: 'WCAG-017',
    ruleName: 'Image Alternatives',
    category: 'perceivable',
    wcagVersion: '3.0',
    successCriterion: '2.1',
    level: 'A',
    weight: 0.0305,
    automated: true,
    description: 'Enhanced image alternative requirements',
    checkFunction: 'ImageAlternativesCheck',
  },
  {
    ruleId: 'WCAG-018',
    ruleName: 'Text and Wording',
    category: 'understandable',
    wcagVersion: '3.0',
    successCriterion: '2.2',
    level: 'AA',
    weight: 0.0305,
    automated: true,
    description: 'Content uses plain language',
    checkFunction: 'TextWordingCheck',
  },
  {
    ruleId: 'WCAG-019',
    ruleName: 'Keyboard Focus',
    category: 'operable',
    wcagVersion: '3.0',
    successCriterion: '2.4',
    level: 'A',
    weight: 0.0382,
    automated: true,
    description: 'Enhanced keyboard focus requirements',
    checkFunction: 'KeyboardFocusCheck',
  },
  {
    ruleId: 'WCAG-020',
    ruleName: 'Motor',
    category: 'operable',
    wcagVersion: '3.0',
    successCriterion: '2.5',
    level: 'AA',
    weight: 0.0305,
    automated: true,
    description: 'Motor accessibility requirements',
    checkFunction: 'MotorCheck',
  },
  {
    ruleId: 'WCAG-021',
    ruleName: 'Pronunciation & Meaning',
    category: 'understandable',
    wcagVersion: '3.0',
    successCriterion: '3.1',
    level: 'AAA',
    weight: 0.0229,
    automated: true,
    description: 'Unusual words are defined',
    checkFunction: 'PronunciationMeaningCheck',
  },
  {
    ruleId: 'WCAG-022',
    ruleName: 'Accessible Authentication (Minimum)',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.3.8',
    level: 'AA',
    weight: 0.05,
    automated: true,
    description: 'Authentication does not rely solely on cognitive function tests',
    checkFunction: 'AccessibleAuthenticationCheck',
  },
  {
    ruleId: 'WCAG-023',
    ruleName: 'Accessible Authentication (Enhanced)',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.3.9',
    level: 'AAA',
    weight: 0.03,
    automated: true,
    description: 'Enhanced authentication accessibility requirements',
    checkFunction: 'AccessibleAuthenticationEnhancedCheck',
  },
  {
    ruleId: 'WCAG-044',
    ruleName: 'Timing Adjustable',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.2.1',
    level: 'A',
    weight: 0.0687,
    automated: true,
    description: 'Time limits can be turned off, adjusted, or extended',
    checkFunction: 'TimingAdjustableCheck',
  },
  {
    ruleId: 'WCAG-045',
    ruleName: 'Pause, Stop, Hide',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.2.2',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Moving, blinking, or auto-updating content can be paused, stopped, or hidden',
    checkFunction: 'PauseStopHideCheck',
  },
  {
    ruleId: 'WCAG-046',
    ruleName: 'Three Flashes or Below Threshold',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.3.1',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Content does not flash more than three times per second',
    checkFunction: 'ThreeFlashesCheck',
  },
  {
    ruleId: 'WCAG-037',
    ruleName: 'Resize Text',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.4',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Text can be resized up to 200% without loss of content or functionality',
    checkFunction: 'ResizeTextCheck',
  },
  {
    ruleId: 'WCAG-039',
    ruleName: 'Images of Text',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.5',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Images of text are only used when essential',
    checkFunction: 'ImagesOfTextCheck',
  },
  {
    ruleId: 'WCAG-040',
    ruleName: 'Reflow',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.10',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Content reflows without horizontal scrolling at 320px width',
    checkFunction: 'ReflowCheck',
  },
  {
    ruleId: 'WCAG-041',
    ruleName: 'Non-text Contrast',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.11',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Non-text elements have sufficient contrast (3:1 minimum)',
    checkFunction: 'NonTextContrastCheck',
  },
  {
    ruleId: 'WCAG-042',
    ruleName: 'Text Spacing',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.12',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Content adapts to increased text spacing without loss of functionality',
    checkFunction: 'TextSpacingCheck',
  },
  {
    ruleId: 'WCAG-043',
    ruleName: 'Content on Hover or Focus',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.13',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Content that appears on hover or focus is dismissible, hoverable, and persistent',
    checkFunction: 'ContentOnHoverFocusCheck',
  },
  {
    ruleId: 'WCAG-050',
    ruleName: 'Audio Control',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.2',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Audio that plays automatically for more than 3 seconds has controls',
    checkFunction: 'AudioControlCheck',
  },
  {
    ruleId: 'WCAG-051',
    ruleName: 'Keyboard Accessible',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.1.1',
    level: 'A',
    weight: 0.0687,
    automated: true,
    description: 'All functionality is available from a keyboard',
    checkFunction: 'KeyboardAccessibleCheck',
  },
  {
    ruleId: 'WCAG-052',
    ruleName: 'Character Key Shortcuts',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.1.4',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Character key shortcuts can be turned off or remapped',
    checkFunction: 'CharacterKeyShortcutsCheck',
  },
  {
    ruleId: 'WCAG-053',
    ruleName: 'Pointer Gestures',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.5.1',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Multipoint or path-based gestures have single-pointer alternatives',
    checkFunction: 'PointerGesturesCheck',
  },
  {
    ruleId: 'WCAG-054',
    ruleName: 'Pointer Cancellation',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.5.2',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Functions triggered by pointer events can be cancelled',
    checkFunction: 'PointerCancellationCheck',
  },
  {
    ruleId: 'WCAG-055',
    ruleName: 'Label in Name',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.5.3',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Visible text is included in accessible names',
    checkFunction: 'LabelInNameCheck',
  },
  {
    ruleId: 'WCAG-056',
    ruleName: 'Motion Actuation',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.5.4',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Motion actuation can be disabled and alternative input methods are available',
    checkFunction: 'MotionActuationCheck',
  },
  {
    ruleId: 'WCAG-058',
    ruleName: 'Target Size Enhanced',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.5.5',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Target size is at least 44 by 44 CSS pixels',
    checkFunction: 'TargetSizeEnhancedCheck',
  },
  {
    ruleId: 'WCAG-059',
    ruleName: 'Concurrent Input Mechanisms',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.5.6',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Content does not restrict use of input modalities',
    checkFunction: 'ConcurrentInputMechanismsCheck',
  },
  {
    ruleId: 'WCAG-060',
    ruleName: 'Unusual Words',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.1.3',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Unusual words and jargon have definitions available',
    checkFunction: 'UnusualWordsCheck',
  },
  {
    ruleId: 'WCAG-061',
    ruleName: 'Abbreviations',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.1.4',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Expanded form of abbreviations is available',
    checkFunction: 'AbbreviationsCheck',
  },
  {
    ruleId: 'WCAG-062',
    ruleName: 'Reading Level',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.1.5',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description:
      'Text content can be read without requiring reading ability more advanced than lower secondary education',
    checkFunction: 'ReadingLevelCheck',
  },
  {
    ruleId: 'WCAG-063',
    ruleName: 'Pronunciation',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.1.6',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description:
      'Pronunciation information is available for words where meaning is ambiguous without knowing pronunciation',
    checkFunction: 'PronunciationCheck',
  },
  {
    ruleId: 'WCAG-064',
    ruleName: 'Change on Request',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.2.5',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description:
      'Context changes are initiated only by user request or a mechanism is available to turn off such changes',
    checkFunction: 'ContextChangesCheck',
  },
  {
    ruleId: 'WCAG-065',
    ruleName: 'Help',
    category: 'robust',
    wcagVersion: '2.1',
    successCriterion: '3.3.5',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Context-sensitive help is available',
    checkFunction: 'HelpCheck',
  },
  {
    ruleId: 'WCAG-066',
    ruleName: 'Error Prevention Enhanced',
    category: 'robust',
    wcagVersion: '2.1',
    successCriterion: '3.3.6',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'For all user input, one of the following is true: reversible, checked, confirmed',
    checkFunction: 'ErrorPreventionEnhancedCheck',
  },
  {
    ruleId: 'WCAG-057',
    ruleName: 'Status Messages',
    category: 'robust',
    wcagVersion: '2.1',
    successCriterion: '4.1.3',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Status messages can be programmatically determined through role or properties',
    checkFunction: 'StatusMessagesCheck',
  },
  {
    ruleId: 'WCAG-047',
    ruleName: 'Skip Links',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.1',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Skip links or other bypass mechanisms are provided',
    checkFunction: 'SkipLinksCheck',
  },
  {
    ruleId: 'WCAG-048',
    ruleName: 'Enhanced Focus Management',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.3',
    level: 'A',
    weight: 0.0535,
    automated: true,
    description: 'Enhanced focus order validation with comprehensive focus management',
    checkFunction: 'EnhancedFocusManagementCheck',
  },
  {
    ruleId: 'WCAG-049',
    ruleName: 'Link Context',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.4',
    level: 'A',
    weight: 0.0535,
    automated: true,
    description: 'Enhanced link purpose validation with context analysis',
    checkFunction: 'LinkContextCheck',
  },

  // ✅ NEWLY REGISTERED CHECKS (WCAG-024 through WCAG-038)
  // These check files exist but were not registered in constants
  {
    ruleId: 'WCAG-024',
    ruleName: 'Language of Page',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.1.1',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Page language is identified using lang attribute',
    checkFunction: 'HtmlLangCheck',
  },
  {
    ruleId: 'WCAG-025',
    ruleName: 'Page Content Landmarks',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.3.6',
    level: 'AA',
    weight: 0.0687,
    automated: true,
    description: 'Content is contained within appropriate landmark elements',
    checkFunction: 'LandmarksCheck',
  },
  {
    ruleId: 'WCAG-026',
    ruleName: 'Link Purpose (In Context)',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.4',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Links clearly describe their purpose',
    checkFunction: 'LinkPurposeCheck',
  },
  {
    ruleId: 'WCAG-027',
    ruleName: 'No Keyboard Trap',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.1.2',
    level: 'A',
    weight: 0.0916,
    automated: true,
    description: 'Keyboard focus is not trapped in any component',
    checkFunction: 'KeyboardTrapCheck',
  },
  {
    ruleId: 'WCAG-028',
    ruleName: 'Bypass Blocks',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.1',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Skip links or other bypass mechanisms are provided',
    checkFunction: 'BypassBlocksCheck',
  },
  {
    ruleId: 'WCAG-029',
    ruleName: 'Page Titled',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.2',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Web pages have titles that describe topic or purpose',
    checkFunction: 'PageTitledCheck',
  },
  {
    ruleId: 'WCAG-030',
    ruleName: 'Labels or Instructions',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.3.2',
    level: 'A',
    weight: 0.0534,
    automated: true,
    description: 'Labels or instructions are provided when content requires user input',
    checkFunction: 'LabelsInstructionsCheck',
  },
  {
    ruleId: 'WCAG-031',
    ruleName: 'Error Suggestion',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.3.3',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Error suggestions are provided when input errors are detected',
    checkFunction: 'ErrorSuggestionCheck',
  },
  {
    ruleId: 'WCAG-032',
    ruleName: 'Error Prevention',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.3.4',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Error prevention mechanisms are provided for important data',
    checkFunction: 'ErrorPreventionCheck',
  },
  {
    ruleId: 'WCAG-033',
    ruleName: 'Audio-only and Video-only',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.2.1',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Alternatives provided for audio-only and video-only content',
    checkFunction: 'AudioVideoOnlyCheck',
  },
  {
    ruleId: 'WCAG-034',
    ruleName: 'Audio Description',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.2.3',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Audio description provided for prerecorded video content',
    checkFunction: 'AudioDescriptionCheck',
  },
  {
    ruleId: 'WCAG-035',
    ruleName: 'Multiple Ways',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.5',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Multiple ways are available to locate web pages',
    checkFunction: 'MultipleWaysCheck',
  },
  {
    ruleId: 'WCAG-036',
    ruleName: 'Headings and Labels',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.6',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Headings and labels describe topic or purpose',
    checkFunction: 'HeadingsLabelsCheck',
  },
  {
    ruleId: 'WCAG-038',
    ruleName: 'Language of Parts',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.1.2',
    level: 'AA',
    weight: 0.0535,
    automated: true,
    description: 'Language of each passage or phrase is programmatically determined',
    checkFunction: 'LanguagePartsCheck',
  },
];

// MANUAL REVIEW RULES - tracked separately, NO scoring contribution
export const WCAG_MANUAL_RULES = [
  {
    ruleId: 'WCAG-MANUAL-001',
    ruleName: 'Content Quality Review',
    category: 'understandable',
    description: 'Manual review of content clarity and comprehension',
    priority: 'medium',
    estimatedTime: 15, // minutes
  },
  {
    ruleId: 'WCAG-MANUAL-002',
    ruleName: 'User Experience Flow',
    category: 'operable',
    description: 'Manual testing of complete user workflows',
    priority: 'high',
    estimatedTime: 30,
  },
  {
    ruleId: 'WCAG-MANUAL-003',
    ruleName: 'Context-Specific Accessibility',
    category: 'perceivable',
    description: 'Manual review of context-dependent accessibility features',
    priority: 'medium',
    estimatedTime: 20,
  },
] as const;

// AUTOMATED SCORING ONLY - Single score system
export const AUTOMATED_CATEGORY_WEIGHTS: Record<WcagCategory, number> = {
  perceivable: 0.25,
  operable: 0.35,
  understandable: 0.25,
  robust: 0.15,
};

// AUTOMATED VERSION WEIGHTS - Single score system
export const AUTOMATED_VERSION_WEIGHTS: Record<WcagVersion, number> = {
  '2.1': 0.5,
  '2.2': 0.35,
  '3.0': 0.15,
};

// AUTOMATED LEVEL REQUIREMENTS - Single score system
export const AUTOMATED_LEVEL_REQUIREMENTS = {
  A: 0.8, // 80% of automated Level A rules must pass
  AA: 0.75, // 75% of automated Level A + AA rules must pass
  AAA: 0.7, // 70% of all automated rules must pass
};

// Aliases for Part 7 compatibility
export const WCAG_RULES = WCAG_AUTOMATED_RULES;
export const CATEGORY_WEIGHTS = AUTOMATED_CATEGORY_WEIGHTS;
export const VERSION_WEIGHTS = AUTOMATED_VERSION_WEIGHTS;
export const LEVEL_REQUIREMENTS = AUTOMATED_LEVEL_REQUIREMENTS;

// MANUAL REVIEW PRIORITIES - separate tracking
export const MANUAL_REVIEW_PRIORITIES = {
  high: 1.0,
  medium: 0.7,
  low: 0.4,
} as const;

// Risk Level Thresholds
export const RISK_THRESHOLDS = {
  critical: 40, // 0-40 score
  high: 60, // 41-60 score
  medium: 80, // 61-80 score
  low: 100, // 81-100 score
};

// Automation Levels by Rule
export const AUTOMATION_LEVELS: Record<string, number> = {
  'WCAG-001': 0.95, // Non-text Content
  'WCAG-002': 0.8, // Captions
  'WCAG-003': 0.9, // Info and Relationships
  'WCAG-004': 1.0, // Contrast (Minimum)
  'WCAG-005': 0.85, // Keyboard
  'WCAG-006': 0.75, // Focus Order
  'WCAG-007': 1.0, // Focus Visible
  'WCAG-008': 0.9, // Error Identification
  'WCAG-009': 0.9, // Name, Role, Value
  'WCAG-010': 1.0, // Focus Not Obscured (Minimum)
  'WCAG-011': 1.0, // Focus Not Obscured (Enhanced)
  'WCAG-012': 1.0, // Focus Appearance
  'WCAG-013': 0.7, // Dragging Movements
  'WCAG-014': 1.0, // Target Size
  'WCAG-015': 0.8, // Consistent Help
  'WCAG-016': 0.85, // Redundant Entry
  'WCAG-017': 0.95, // Image Alternatives (3.0)
  'WCAG-018': 0.75, // Text and Wording (3.0)
  'WCAG-019': 0.9, // Keyboard Focus (3.0)
  'WCAG-020': 0.8, // Motor (3.0)
  'WCAG-021': 0.6, // Pronunciation & Meaning (3.0)
  'WCAG-022': 0.5, // Accessible Authentication (Minimum)
  'WCAG-023': 0.4, // Accessible Authentication (Enhanced)
  'WCAG-044': 0.85, // Timing Adjustable
  'WCAG-045': 0.8, // Pause, Stop, Hide
  'WCAG-046': 0.75, // Three Flashes

  'WCAG-037': 0.75, // Resize Text
  'WCAG-039': 0.7, // Images of Text
  'WCAG-040': 0.8, // Reflow
  'WCAG-041': 0.75, // Non-text Contrast
  'WCAG-042': 0.85, // Text Spacing
  'WCAG-043': 0.75, // Content on Hover/Focus
  'WCAG-050': 0.8, // Audio Control
  'WCAG-051': 0.85, // Keyboard Accessible
  'WCAG-052': 0.7, // Character Key Shortcuts
  'WCAG-053': 0.75, // Pointer Gestures
  'WCAG-054': 0.8, // Pointer Cancellation
  'WCAG-055': 0.85, // Label in Name
  'WCAG-056': 0.75, // Motion Actuation
  'WCAG-058': 0.7, // Target Size Enhanced
  'WCAG-059': 0.65, // Concurrent Input Mechanisms
  'WCAG-060': 0.6, // Unusual Words
  'WCAG-061': 0.75, // Abbreviations
  'WCAG-062': 0.55, // Reading Level
  'WCAG-063': 0.5, // Pronunciation
  'WCAG-064': 0.7, // Context Changes
  'WCAG-065': 0.8, // Help
  'WCAG-066': 0.85, // Error Prevention Enhanced
  'WCAG-057': 0.8, // Status Messages

  // ✅ NEWLY REGISTERED AUTOMATION LEVELS
  'WCAG-024': 1.0, // HTML Language Check (fully automated)
  'WCAG-025': 0.95, // Page Content Landmarks
  'WCAG-026': 0.9, // Link Purpose (In Context)
  'WCAG-027': 0.85, // No Keyboard Trap
  'WCAG-028': 0.9, // Bypass Blocks
  'WCAG-029': 1.0, // Page Titled (fully automated)
  'WCAG-030': 0.8, // Labels or Instructions
  'WCAG-031': 0.75, // Error Suggestion
  'WCAG-032': 0.8, // Error Prevention
  'WCAG-033': 0.7, // Audio-only and Video-only
  'WCAG-034': 0.7, // Audio Description
  'WCAG-035': 0.85, // Multiple Ways
  'WCAG-036': 0.9, // Headings and Labels
  'WCAG-038': 0.85, // Language of Parts
  'WCAG-047': 0.9, // Skip Links
  'WCAG-048': 0.85, // Enhanced Focus Management
  'WCAG-049': 0.8, // Link Context
};

// Default Scan Configuration
export const DEFAULT_SCAN_CONFIG: Required<WcagScanOptions> = {
  enableContrastAnalysis: true,
  enableKeyboardTesting: true,
  enableFocusAnalysis: true,
  enableSemanticValidation: true,
  enableManualReview: true,   // ✅ Enable manual review by default
  wcagVersion: 'all',         // ✅ Keep 'all' to run all 66 checks
  level: 'AAA',               // ✅ Change to 'AAA' to include all compliance levels
  maxPages: 5,
  timeout: 30000,

  // ✅ ENHANCED FEATURES: Default values for enhanced options
  includeElementCounts: true,
  generateFixExamples: true,
  enablePerformanceMetrics: true,
  enhancedReporting: true,
  cacheStrategy: 'memory',
};

// ✅ ENHANCED SCORING CONFIGURATION: Graduated Penalty System
export const SCORING_CONFIG = {
  DEFAULT_PASS_THRESHOLD: 75,        // 75% threshold for passing
  STRICT_MODE: false,                // Allow threshold-based scoring
  ENABLE_GRADUAL_SCORING: true,      // Show actual scores when passed
  LEGACY_BINARY_MODE: false,         // For backward compatibility
  ENABLE_THRESHOLD_LOGGING: true,    // Log threshold decisions
  ENABLE_GRADUATED_PENALTIES: true,  // Enable graduated penalty system
  ENABLE_CONFIDENCE_WEIGHTING: true, // Enable confidence-based adjustments
  ENABLE_CATEGORY_THRESHOLDS: true,  // Enable category-specific thresholds
} as const;

// ✅ GRADUATED PENALTY TIERS: Partial Credit System
export const PENALTY_TIERS = [
  { min: 75, multiplier: 1.0, status: 'passed' as const },     // Full credit
  { min: 60, multiplier: 0.8, status: 'partial' as const },    // 80% credit
  { min: 45, multiplier: 0.6, status: 'partial' as const },    // 60% credit
  { min: 30, multiplier: 0.4, status: 'partial' as const },    // 40% credit
  { min: 0,  multiplier: 0.0, status: 'failed' as const }      // No credit
] as const;

// ✅ CATEGORY-SPECIFIC THRESHOLDS: Different standards by category
export const CATEGORY_THRESHOLDS = {
  'perceivable': 75,     // Standard threshold
  'operable': 80,        // Higher threshold (critical for usability)
  'understandable': 70,  // Lower threshold (subjective content)
  'robust': 85          // Higher threshold (technical compliance)
} as const;

// ✅ LEVEL-SPECIFIC THRESHOLDS: Different standards by WCAG level
export const LEVEL_THRESHOLDS = {
  'A': 70,    // Level A: 70% threshold (basic accessibility)
  'AA': 75,   // Level AA: 75% threshold (standard)
  'AAA': 85   // Level AAA: 85% threshold (enhanced)
} as const;

// ✅ CONFIDENCE ADJUSTMENT FACTORS: Adjust scores based on detection confidence
export const CONFIDENCE_ADJUSTMENTS = [
  { min: 0.9, multiplier: 1.0 },    // High confidence: no adjustment
  { min: 0.8, multiplier: 0.95 },   // Good confidence: 5% reduction
  { min: 0.7, multiplier: 0.9 },    // Fair confidence: 10% reduction
  { min: 0.6, multiplier: 0.85 },   // Low confidence: 15% reduction
  { min: 0.0, multiplier: 0.8 }     // Very low confidence: 20% reduction
] as const;

export interface ScoringConfig {
  passThreshold: number;
  strictMode: boolean;
  enableGradualScoring: boolean;
  enableThresholdLogging?: boolean;
  enableGraduatedPenalties?: boolean;
  enableConfidenceWeighting?: boolean;
  enableCategoryThresholds?: boolean;
}

export type ScoringStatus = 'passed' | 'partial' | 'failed';

export interface EnhancedScoringResult {
  status: ScoringStatus;
  score: number;
  originalScore: number;
  adjustedScore: number;
  confidence?: number;
  threshold: number;
  penaltyTier?: typeof PENALTY_TIERS[number];
  confidenceAdjustment?: number;
  details: string;
}

// Error Codes
export const WCAG_ERROR_CODES = {
  INVALID_URL: 'WCAG_INVALID_URL',
  SCAN_TIMEOUT: 'WCAG_SCAN_TIMEOUT',
  NETWORK_ERROR: 'WCAG_NETWORK_ERROR',
  BROWSER_ERROR: 'WCAG_BROWSER_ERROR',
  VALIDATION_ERROR: 'WCAG_VALIDATION_ERROR',
  DATABASE_ERROR: 'WCAG_DATABASE_ERROR',
  AUTHENTICATION_ERROR: 'WCAG_AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR: 'WCAG_RATE_LIMIT_ERROR',
} as const;

// Success Messages
export const WCAG_SUCCESS_MESSAGES = {
  SCAN_STARTED: 'WCAG scan initiated successfully',
  SCAN_COMPLETED: 'WCAG scan completed successfully',
  RESULTS_RETRIEVED: 'WCAG scan results retrieved successfully',
  EXPORT_GENERATED: 'WCAG report exported successfully',
} as const;
