-- Create sample WCAG scan data for testing
-- This script creates sample scans for the mock user

-- Insert sample WCAG scans
INSERT INTO wcag_scans (
  id,
  user_id,
  target_url,
  scan_status,
  scan_timestamp,
  completion_timestamp,
  overall_score,
  level_achieved,
  risk_level,
  perceivable_score,
  operable_score,
  understandable_score,
  robust_score,
  wcag21_score,
  wcag22_score,
  wcag30_score,
  total_automated_checks,
  passed_automated_checks,
  failed_automated_checks,
  manual_review_items,
  scan_options
) VALUES 
(
  'sample-wcag-001',
  '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  'https://example.com',
  'completed',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '1 day' + INTERVAL '5 minutes',
  85.5,
  'AA',
  'low',
  90,
  85,
  80,
  85,
  85,
  85,
  85,
  25,
  20,
  5,
  3,
  '{"maxPages": 5, "enableContrastAnalysis": true, "enableKeyboardTesting": true, "enableFocusAnalysis": true, "enableSemanticValidation": true, "wcagVersion": "all", "level": "AA"}'::jsonb
),
(
  'sample-wcag-002',
  '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  'https://test.example.com',
  'completed',
  NOW() - INTERVAL '2 days',
  NOW() - INTERVAL '2 days' + INTERVAL '7 minutes',
  72.3,
  'A',
  'medium',
  75,
  70,
  68,
  76,
  72,
  73,
  71,
  25,
  18,
  7,
  5,
  '{"maxPages": 3, "enableContrastAnalysis": true, "enableKeyboardTesting": true, "enableFocusAnalysis": false, "enableSemanticValidation": true, "wcagVersion": "2.1", "level": "AA"}'::jsonb
),
(
  'sample-wcag-003',
  '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  'https://demo.example.com',
  'completed',
  NOW() - INTERVAL '3 days',
  NOW() - INTERVAL '3 days' + INTERVAL '4 minutes',
  92.1,
  'AAA',
  'low',
  95,
  90,
  88,
  95,
  92,
  93,
  91,
  30,
  28,
  2,
  1,
  '{"maxPages": 10, "enableContrastAnalysis": true, "enableKeyboardTesting": true, "enableFocusAnalysis": true, "enableSemanticValidation": true, "wcagVersion": "all", "level": "AAA"}'::jsonb
),
(
  'sample-wcag-004',
  '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  'https://blog.example.com',
  'running',
  NOW() - INTERVAL '1 hour',
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  20,
  12,
  3,
  0,
  '{"maxPages": 5, "enableContrastAnalysis": true, "enableKeyboardTesting": true, "enableFocusAnalysis": true, "enableSemanticValidation": true, "wcagVersion": "2.2", "level": "AA"}'::jsonb
),
(
  'sample-wcag-005',
  '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  'https://shop.example.com',
  'failed',
  NOW() - INTERVAL '6 hours',
  NOW() - INTERVAL '6 hours' + INTERVAL '2 minutes',
  NULL,
  'FAIL',
  'high',
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  NULL,
  15,
  5,
  10,
  0,
  '{"maxPages": 8, "enableContrastAnalysis": true, "enableKeyboardTesting": true, "enableFocusAnalysis": true, "enableSemanticValidation": true, "wcagVersion": "all", "level": "AA"}'::jsonb
);

-- Insert some sample automated results for the completed scans
INSERT INTO wcag_automated_results (
  id,
  scan_id,
  rule_id,
  rule_name,
  category,
  wcag_version,
  success_criterion,
  level,
  status,
  score,
  max_score,
  weight,
  evidence,
  recommendations,
  execution_time
) VALUES 
(
  'result-001',
  'sample-wcag-001',
  'WCAG-001',
  'Image Alternative Text',
  'perceivable',
  '2.1',
  '1.1.1',
  'A',
  'passed',
  100,
  100,
  1.0,
  '[{"type": "text", "description": "All images have appropriate alt text", "value": "15 images checked", "severity": "info"}]'::jsonb,
  '["Continue providing descriptive alt text for all images"]'::jsonb,
  150
),
(
  'result-002',
  'sample-wcag-001',
  'WCAG-007',
  'Focus Visible',
  'operable',
  '2.1',
  '2.4.7',
  'AA',
  'failed',
  70,
  100,
  1.0,
  '[{"type": "text", "description": "Some elements lack visible focus indicators", "value": "3 elements failed", "severity": "error"}]'::jsonb,
  '["Add visible focus indicators to all interactive elements", "Ensure focus indicators have sufficient contrast"]'::jsonb,
  200
);
