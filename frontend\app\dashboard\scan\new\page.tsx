'use client';

import Link from 'next/link'; // Import Link
import React, { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox'; // Added Checkbox import
import { Loader2 } from 'lucide-react'; // Added Loader2 import
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useAuth } from '@/context/AuthContext';
import { COMPLIANCE_STANDARDS, MESSAGES, TEXT, ComplianceStandard } from '@/lib/constants'; // Removed TIMEOUTS as it's not used in the current version
import { submitScan } from '@/lib/api';

/**
 * Page component for submitting a new compliance scan.
 * Allows users to input a URL, select standards, and initiate a scan.
 * @returns {JSX.Element} The new scan page.
 */
export default function NewScanPage() {
  const [url, setUrl] = useState('');
  const [selectedStandards, setSelectedStandards] = useState<string[]>([]); // Added state for standards
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null); // Holds error messages for the form
  const [successMessage, setSuccessMessage] = useState<string | null>(null); // Holds success messages
  const { authenticated } = useAuth();

  /**
   * Handles changes to the selection of compliance standards.
   * @param {string} standardId The ID of the standard being changed.
   * @param {boolean | 'indeterminate'} checked The new checked state of the standard.
   */
  const handleStandardChange = (standardId: string, checked: boolean | 'indeterminate') => {
    setSelectedStandards((prev) =>
      checked === true ? [...prev, standardId] : prev.filter((s) => s !== standardId),
    );
  };

  /**
   * Handles the form submission for a new scan.
   * Validates inputs, calls the API, and manages UI state (loading, errors, success).
   * @param {React.FormEvent<HTMLFormElement>} event The form submission event.
   */
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!authenticated) {
      setError(MESSAGES.ERROR_NOT_LOGGED_IN);
      return;
    }

    if (!url.trim()) {
      setError(MESSAGES.ERROR_INVALID_URL_EMPTY);
      return;
    }

    try {
      new URL(url);
    } catch (_) {
      setError(MESSAGES.ERROR_INVALID_URL_FORMAT);
      return;
    }

    if (selectedStandards.length === 0) {
      setError(MESSAGES.ERROR_NO_STANDARD_SELECTED);
      return;
    }

    setIsLoading(true);
    try {
      // Fixed: submitScan expects a single object parameter, not two separate parameters
      const response = await submitScan({ url, standards: selectedStandards });
      // Assuming submitScan returns the scan object with an id
      setSuccessMessage(
        `Scan submitted successfully! Scan ID: ${response.id}. You will be notified upon completion.`, // Dynamic message, kept as is
      );
      setUrl(''); // Clear URL input
      setSelectedStandards([]); // Clear selected standards
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || MESSAGES.ERROR_SUBMISSION_FAILED); // Use constant for fallback
      } else {
        setError(MESSAGES.ERROR_SUBMISSION_FAILED); // Use constant for generic error
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-muted/40 p-4">
      <Card className="w-full max-w-lg">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-2xl">Submit New Scan</CardTitle>
                <CardDescription>
                  Enter the URL of the website you want to scan and select the compliance standards.
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Link href="/dashboard/hipaa/security-scan" passHref>
                  <Button variant="secondary" size="sm">
                    🔥 Live HIPAA Security Scanner
                  </Button>
                </Link>
                <Link href="/dashboard/scan/test-hipaa" passHref>
                  <Button variant="outline" size="sm">
                    Test Enhanced HIPAA
                  </Button>
                </Link>
                <Link href="/dashboard" passHref>
                  <Button variant="outline" size="sm">
                    Home
                  </Button>
                </Link>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="url">Website URL</Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-3">
              <Label>Compliance Standards</Label>
              <div className="grid grid-cols-2 gap-4">
                {COMPLIANCE_STANDARDS.map((standard: ComplianceStandard) => (
                  <div key={standard.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={standard.id}
                      checked={selectedStandards.includes(standard.id)}
                      onCheckedChange={(checked) => handleStandardChange(standard.id, checked)}
                      disabled={isLoading}
                    />
                    <Label htmlFor={standard.id} className="font-normal">
                      {standard.label}
                    </Label>
                  </div>
                ))}
                {/* To add more standards, update the COMPLIANCE_STANDARDS array in constants.ts */}
              </div>
            </div>

            {error && (
              <p className="text-sm font-medium text-destructive" role="alert">
                {error}
              </p>
            )}
            {successMessage && (
              <p className="text-sm font-medium text-success-foreground" role="status">
                {successMessage}
              </p>
            )}
          </CardContent>
          <CardFooter className="flex flex-col gap-3">
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {TEXT.SUBMITTING_BUTTON}
                </>
              ) : (
                TEXT.SUBMIT_SCAN_BUTTON
              )}
            </Button>

            {/* Link to Simple HIPAA Test */}
            <div className="text-center">
              <Link href="/dashboard/scan/simple-hipaa">
                <Button variant="outline" size="sm">
                  Test Simple HIPAA Analysis
                </Button>
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
