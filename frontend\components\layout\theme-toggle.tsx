'use client';

import * as React from 'react';
import { <PERSON>, Sun, Monitor } from 'lucide-react';
import { useTheme } from 'next-themes';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { THEME_OPTIONS, getThemeDisplayName, type ThemeName } from '@/lib/theme-config';

/**
 * Enhanced ThemeToggle component with clear theme naming system.
 * Supports Standard (light with design system), Dark (WCAG AA compliant), and System themes.
 * @returns {JSX.Element} The ThemeToggle component.
 */
export function ThemeToggle(): JSX.Element {
  const { setTheme, theme } = useTheme();

  // Get current theme display name
  const currentThemeDisplay = getThemeDisplayName((theme as ThemeName) || 'standard');

  // Get appropriate icon for current theme
  const getThemeIcon = (themeName: string) => {
    switch (themeName) {
      case 'standard':
        return <Sun className="h-[1.2rem] w-[1.2rem]" />;
      case 'dark':
        return <Moon className="h-[1.2rem] w-[1.2rem]" />;
      case 'system':
        return <Monitor className="h-[1.2rem] w-[1.2rem]" />;
      default:
        return <Sun className="h-[1.2rem] w-[1.2rem]" />;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          style={{
            borderColor: 'var(--color-primary, #0055A4)',
            color: 'var(--color-primary, #0055A4)',
            backgroundColor: 'var(--color-background-secondary, white)',
          }}
          className="hover:bg-blue-50 transition-colors dark:bg-gray-800 dark:border-gray-600 dark:text-blue-400 dark:hover:bg-gray-700"
          aria-label={`Current theme: ${currentThemeDisplay}. Click to change theme`}
          title={`Current theme: ${currentThemeDisplay}`}
        >
          {getThemeIcon(theme || 'standard')}
          <span className="sr-only">Toggle theme - Current: {currentThemeDisplay}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        style={{
          backgroundColor: 'var(--color-background-secondary, white)',
          borderColor: 'var(--color-border, #E5E5E5)',
          color: 'var(--color-text-primary, #333333)',
        }}
        className="dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100 min-w-[200px]"
      >
        {THEME_OPTIONS.map((option) => {
          const isSelected = theme === option.value;
          const IconComponent =
            option.value === 'standard' ? Sun : option.value === 'dark' ? Moon : Monitor;

          return (
            <DropdownMenuItem
              key={option.value}
              onClick={() => setTheme(option.value)}
              className="hover:bg-blue-50 dark:hover:bg-gray-700 focus:bg-blue-50 dark:focus:bg-gray-700 cursor-pointer"
              style={{
                color: isSelected ? 'var(--color-primary, #0055A4)' : undefined,
                fontWeight: isSelected ? '600' : '400',
              }}
            >
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center">
                  <IconComponent className="h-4 w-4 mr-3" />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{option.label}</span>
                    <span className="text-xs opacity-70">{option.description}</span>
                  </div>
                </div>
                {isSelected && <span className="ml-2 text-xs font-bold">✓</span>}
              </div>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
