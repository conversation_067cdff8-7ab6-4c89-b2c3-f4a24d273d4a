2025-07-12T06:37:40.113Z [INFO] - 🔓 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Development mode: Mock authentication applied
2025-07-12T06:37:40.116Z [INFO] - 🚀 [e624d466-9a68-41c8-88e9-ae6d76f60af0] WCAG scan request received
2025-07-12T06:37:40.117Z [INFO] - 📋 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Request headers: - {"content-type":"application/json","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36","authorization":"Bearer [REDACTED]"}
2025-07-12T06:37:40.119Z [INFO] - 👤 [e624d466-9a68-41c8-88e9-ae6d76f60af0] User context: - {"userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","email":"<EMAIL>","permissions":["wcag:scan","wcag:view","wcag:export"]}
2025-07-12T06:37:40.120Z [INFO] - 🎯 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Target URL: https://tigerconnect.com/
2025-07-12T06:37:40.121Z [INFO] - ⚙️ [e624d466-9a68-41c8-88e9-ae6d76f60af0] Scan options: - {"enableContrastAnalysis":true,"enableKeyboardTesting":true,"enableFocusAnalysis":true,"enableSemanticValidation":true,"enableManualReview":true,"wcagVersion":"all","level":"AAA","maxPages":5,"timeout":60000}
2025-07-12T06:37:40.123Z [INFO] - ✅ [e624d466-9a68-41c8-88e9-ae6d76f60af0] Target URL validation passed
2025-07-12T06:37:40.124Z [INFO] - 🔧 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Scan configuration created: - {"targetUrl":"https://tigerconnect.com/","userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","level":"AAA","timeout":60000}
2025-07-12T06:37:40.125Z [INFO] - 🚀 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Starting comprehensive WCAG scan with orchestrator
2025-07-12T06:37:40.127Z [INFO] - 📝 Creating WCAG scan record...
2025-07-12T06:37:40.129Z [INFO] - 🆔 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Scan initiated with ID: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.130Z [INFO] - 📊 [e624d466-9a68-41c8-88e9-ae6d76f60af0] Scan started successfully: - {"scanId":"8b80d206-4a60-40bd-9825-753e25410d09","status":"pending","targetUrl":"https://tigerconnect.com/"}
2025-07-12T06:37:40.131Z [INFO] - ✅ [e624d466-9a68-41c8-88e9-ae6d76f60af0] WCAG scan initiated successfully in 15ms
2025-07-12T06:37:40.146Z [INFO] - ✅ WCAG scan record created with ID: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.146Z [INFO] - ✅ WCAG scan record created with ID: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.147Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> pending
2025-07-12T06:37:40.162Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> pending
2025-07-12T06:37:40.162Z [INFO] - 🚀 Initialized scan 8b80d206-4a60-40bd-9825-753e25410d09 with 66 rules
2025-07-12T06:37:40.163Z [DEBUG] - 📊 Started performance monitoring for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.164Z [DEBUG] - 📊 Started performance monitoring for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.167Z [DEBUG] - 📊 Started performance monitoring for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.168Z [DEBUG] - 🔗 Integrated monitoring started for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:40.171Z [DEBUG] - 📊 Registered active scan: 8b80d206-4a60-40bd-9825-753e25410d09 (total: 1)
2025-07-12T06:37:40.171Z [INFO] - 🎛️ Starting Performance Automation System...
2025-07-12T06:37:40.174Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T06:37:40.175Z [INFO] - 📊 Real-Time Monitoring Dashboard started
2025-07-12T06:37:40.177Z [INFO] - ✅ Dashboard monitoring started
2025-07-12T06:37:40.180Z [INFO] - 🔌 Dashboard WebSocket Service started on port 8081
2025-07-12T06:37:40.183Z [INFO] - ✅ WebSocket service started
2025-07-12T06:37:40.186Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-12T06:37:40.187Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-12T06:37:40.188Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-12T06:37:40.189Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-12T06:37:40.190Z [INFO] - 🔮 Predictive Performance Analytics started
2025-07-12T06:37:40.191Z [INFO] - ✅ Predictive analytics started
2025-07-12T06:37:40.192Z [INFO] - 🎛️ Performance Automation System fully operational
2025-07-12T06:37:40.193Z [INFO] - 🎛️ Started Performance Automation Controller for scan monitoring
2025-07-12T06:37:40.198Z [DEBUG] - 🔍 Getting page for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:42.134Z [WARN] - ⚠️ High memory usage detected: 452MB
2025-07-12T06:37:42.134Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-12T06:37:42.138Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-12T06:37:42.140Z [INFO] - ✅ Proactive cleanup completed
2025-07-12T06:37:42.492Z [INFO] - ✅ Created new browser: browser-1752302262492-9o2glsv8i
2025-07-12T06:37:43.891Z [DEBUG] - 🆕 Created new page for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:43.891Z [DEBUG] - ✅ URL validation passed for: https://tigerconnect.com/
2025-07-12T06:37:43.894Z [DEBUG] - 🔍 Checking connectivity for: https://tigerconnect.com/
2025-07-12T06:37:45.188Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T06:37:46.437Z [DEBUG] - ✅ Connectivity check passed for: https://tigerconnect.com/ (status: 200)
2025-07-12T06:37:46.438Z [INFO] - 🔗 Navigating to: https://tigerconnect.com/ (timeout: 60000ms)
2025-07-12T06:37:50.194Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T06:37:53.980Z [DEBUG] - 📝 Registered session: 8b80d206-4a60-40bd-9825-753e25410d09:https://tigerconnect.com/
2025-07-12T06:37:53.980Z [DEBUG] - 📝 Registered session for scan: 8b80d206-4a60-40bd-9825-753e25410d09
2025-07-12T06:37:53.982Z [INFO] - ✅ Navigation completed
2025-07-12T06:37:53.984Z [INFO] - 🚀 Starting Phase 2 analysis: Website type detection and optimization
2025-07-12T06:37:53.986Z [DEBUG] - 🔍 Starting CMS platform detection
2025-07-12T06:37:53.990Z [DEBUG] - 🛒 Starting e-commerce accessibility analysis
2025-07-12T06:37:53.991Z [DEBUG] - 🎬 Starting media accessibility analysis
2025-07-12T06:37:53.993Z [DEBUG] - ⚛️ Starting framework-specific accessibility analysis
2025-07-12T06:37:54.014Z [WARN] - ⚠️ Phase 2 analysis encountered errors, continuing with standard analysis - {"error":"CMS detection failed - no results returned"}
2025-07-12T06:37:54.016Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.063Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.063Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 0% (0/66)
2025-07-12T06:37:54.066Z [INFO] - 🔍 Executing 66 WCAG checks...
2025-07-12T06:37:54.067Z [INFO] - 🔥 Pre-warming cache for WCAG scan...
2025-07-12T06:37:54.070Z [INFO] - 🔥 Pre-warming cache for WCAG scan: https://tigerconnect.com/
2025-07-12T06:37:54.071Z [INFO] - 🔥 Warming up cache with 1 URLs
2025-07-12T06:37:54.086Z [DEBUG] - 💾 Cache saved to file: bebee4be960508b81bf52717657b8895.json (key: https://tigerconnect.com/:ce50...)
2025-07-12T06:37:54.086Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:ce50a093 (62 bytes)
2025-07-12T06:37:54.097Z [DEBUG] - 💾 Cache saved to file: 95f19ad0cce33ee263801de200e8a74a.json (key: https://tigerconnect.com/:a43c...)
2025-07-12T06:37:54.098Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a43c1b0a (61 bytes)
2025-07-12T06:37:54.102Z [DEBUG] - 💾 Cache saved to file: 534c19df0f92f221836ddb58a770c182.json (key: https://tigerconnect.com/:0cc1...)
2025-07-12T06:37:54.103Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:0cc175b9 (57 bytes)
2025-07-12T06:37:54.110Z [DEBUG] - 💾 Cache saved to file: e5d81c1831723c1a497c41a53662ed26.json (key: https://tigerconnect.com/:b798...)
2025-07-12T06:37:54.112Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:b798abe6 (59 bytes)
2025-07-12T06:37:54.118Z [DEBUG] - 💾 Cache saved to file: 663792f6de7c4bd08e95ed6047b0752f.json (key: https://tigerconnect.com/:3fcd...)
2025-07-12T06:37:54.119Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3fcdb73d (60 bytes)
2025-07-12T06:37:54.126Z [DEBUG] - 💾 Cache saved to file: 0a43342a413174a1d2603306ccf44deb.json (key: https://tigerconnect.com/:d72e...)
2025-07-12T06:37:54.127Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d72e5ee9 (59 bytes)
2025-07-12T06:37:54.130Z [DEBUG] - 💾 Cache saved to file: 35c8746de252dfe5065a332b43442ab3.json (key: https://tigerconnect.com/:fad5...)
2025-07-12T06:37:54.130Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:fad58de7 (60 bytes)
2025-07-12T06:37:54.134Z [DEBUG] - 💾 Cache saved to file: f1f8620c79b22a3449d8feaceb632cfc.json (key: https://tigerconnect.com/:099f...)
2025-07-12T06:37:54.135Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:099fb995 (62 bytes)
2025-07-12T06:37:54.139Z [DEBUG] - 💾 Cache saved to file: 1a8ca4ce0660cb82f14b79daae26c351.json (key: https://tigerconnect.com/:251d...)
2025-07-12T06:37:54.140Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:251d1646 (62 bytes)
2025-07-12T06:37:54.144Z [DEBUG] - 💾 Cache saved to file: a5521ee36e71b2c9433c715f93408670.json (key: https://tigerconnect.com/:3f38...)
2025-07-12T06:37:54.145Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3f3852a9 (73 bytes)
2025-07-12T06:37:54.148Z [DEBUG] - 💾 Cache saved to file: 712629e01944fb3e69bd1e0f8c42ff5a.json (key: https://tigerconnect.com/:14e1...)
2025-07-12T06:37:54.151Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:14e1a17f (71 bytes)
2025-07-12T06:37:54.155Z [DEBUG] - 💾 Cache saved to file: bd8bd7fbd915dbba54e7f054fc53f744.json (key: https://tigerconnect.com/:2e71...)
2025-07-12T06:37:54.156Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:2e716448 (68 bytes)
2025-07-12T06:37:54.160Z [DEBUG] - 💾 Cache saved to file: ecec730c5f49a267feabefa9cd405473.json (key: https://tigerconnect.com/:cacf...)
2025-07-12T06:37:54.161Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:cacf55f1 (61 bytes)
2025-07-12T06:37:54.166Z [DEBUG] - 💾 Cache saved to file: 28d51e63f13fde14df4973c59c70fc6c.json (key: https://tigerconnect.com/:af04...)
2025-07-12T06:37:54.169Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:af04abc2 (66 bytes)
2025-07-12T06:37:54.175Z [DEBUG] - 💾 Cache saved to file: afcddacf2eb740820a22ed45014963ec.json (key: https://tigerconnect.com/:346b...)
2025-07-12T06:37:54.176Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:346b81a3 (58 bytes)
2025-07-12T06:37:54.179Z [DEBUG] - 💾 Cache saved to file: e5f6dfa7d9f997dcad6a7e1fdca8e5b5.json (key: https://tigerconnect.com/:490b...)
2025-07-12T06:37:54.181Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:490b2834 (58 bytes)
2025-07-12T06:37:54.187Z [DEBUG] - 💾 Cache saved to file: e1fadcb2de37bd42402f1408934ee8fa.json (key: https://tigerconnect.com/:6f20...)
2025-07-12T06:37:54.188Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:6f207f8b (58 bytes)
2025-07-12T06:37:54.192Z [DEBUG] - 💾 Cache saved to file: 4b977d1289dde40c75e5b724b0e5ddab.json (key: https://tigerconnect.com/:ce1b...)
2025-07-12T06:37:54.192Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:ce1b1e8c (58 bytes)
2025-07-12T06:37:54.197Z [DEBUG] - 💾 Cache saved to file: 8dd69209d8df0c5749198f87713c0c77.json (key: https://tigerconnect.com/:7723...)
2025-07-12T06:37:54.197Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:77230e94 (58 bytes)
2025-07-12T06:37:54.201Z [DEBUG] - 💾 Cache saved to file: bf790dbb37199cfaa73dde1b45b28211.json (key: https://tigerconnect.com/:d854...)
2025-07-12T06:37:54.201Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d8544ba1 (58 bytes)
2025-07-12T06:37:54.205Z [DEBUG] - 💾 Cache saved to file: b98a00e39bad8aaebc681168fb4179bd.json (key: https://tigerconnect.com/:73d5...)
2025-07-12T06:37:54.205Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:73d5342e (63 bytes)
2025-07-12T06:37:54.210Z [DEBUG] - 💾 Cache saved to file: b123149d68166737fedff453406f1877.json (key: https://tigerconnect.com/:92a2...)
2025-07-12T06:37:54.212Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:92a2b5cb (63 bytes)
2025-07-12T06:37:54.218Z [DEBUG] - 💾 Cache saved to file: 7b4bdc44c78075314d0a544b17c078b1.json (key: https://tigerconnect.com/:421b...)
2025-07-12T06:37:54.218Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:421b47ff (61 bytes)
2025-07-12T06:37:54.221Z [DEBUG] - 💾 Cache saved to file: c3a1426057f2930eac9f74423b62d8f0.json (key: https://tigerconnect.com/:a5ca...)
2025-07-12T06:37:54.221Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a5ca0b58 (61 bytes)
2025-07-12T06:37:54.228Z [DEBUG] - 💾 Cache saved to file: c4cf726155115372e8af6959ac180908.json (key: https://tigerconnect.com/:a598...)
2025-07-12T06:37:54.229Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a598e4f2 (62 bytes)
2025-07-12T06:37:54.232Z [DEBUG] - 💾 Cache saved to file: 5c4d62c175a69c1e9d3911b51ec38373.json (key: https://tigerconnect.com/:a8cf...)
2025-07-12T06:37:54.233Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a8cfde63 (62 bytes)
2025-07-12T06:37:54.235Z [DEBUG] - 💾 Cache saved to file: bd000830ab45a8d7cdac2f207437912b.json (key: https://tigerconnect.com/:2696...)
2025-07-12T06:37:54.236Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:269605d4 (61 bytes)
2025-07-12T06:37:54.240Z [DEBUG] - 💾 Cache saved to file: 89160a289dabf8718c30977330411c01.json (key: https://tigerconnect.com/:d304...)
2025-07-12T06:37:54.241Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d304ba20 (61 bytes)
2025-07-12T06:37:54.246Z [DEBUG] - 💾 Cache saved to file: da1cd83794fb9244cb67a6f4e4bf629c.json (key: https://tigerconnect.com/:6396...)
2025-07-12T06:37:54.246Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:639612e6 (64 bytes)
2025-07-12T06:37:54.250Z [DEBUG] - 💾 Cache saved to file: ef36f0c11a1d6cd780326394e9ce2933.json (key: https://tigerconnect.com/:3d4d...)
2025-07-12T06:37:54.250Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3d4dcd6f (62 bytes)
2025-07-12T06:37:54.253Z [DEBUG] - 💾 Cache saved to file: 38cdf473277f920fa85ca5f9f4f2289d.json (key: https://tigerconnect.com/:9993...)
2025-07-12T06:37:54.255Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:99938282 (62 bytes)
2025-07-12T06:37:54.260Z [DEBUG] - 💾 Cache saved to file: 36a1e272427f69e6d72075e2e281f974.json (key: https://tigerconnect.com/:6394...)
2025-07-12T06:37:54.260Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:6394d816 (64 bytes)
2025-07-12T06:37:54.263Z [DEBUG] - 💾 Cache saved to file: 855230df3153f47bb32a65b46f01fe64.json (key: https://tigerconnect.com/:aab9...)
2025-07-12T06:37:54.263Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:aab9e1de (61 bytes)
2025-07-12T06:37:54.269Z [DEBUG] - 💾 Cache saved to file: 96925a000e150d4777fee5b1efdb0276.json (key: https://tigerconnect.com/:1fdc...)
2025-07-12T06:37:54.270Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:1fdc0f89 (58 bytes)
2025-07-12T06:37:54.273Z [DEBUG] - 💾 Cache saved to file: 14ad258288e72d4a6b61f0140e6d7d49.json (key: https://tigerconnect.com/:6267...)
2025-07-12T06:37:54.274Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:626726e6 (58 bytes)
2025-07-12T06:37:54.277Z [DEBUG] - 💾 Cache saved to file: 02ae13ea624f7e894ae36e7f362be6a7.json (key: https://tigerconnect.com/:7a7d...)
2025-07-12T06:37:54.278Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:7a7dc1cd (63 bytes)
2025-07-12T06:37:54.281Z [DEBUG] - 💾 Cache saved to file: ad72b5a662be888865387d8391c79d1b.json (key: https://tigerconnect.com/:imag...)
2025-07-12T06:37:54.282Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:image-analysis (82 bytes)
2025-07-12T06:37:54.292Z [DEBUG] - 💾 Cache saved to file: 8cab73c21d5b2e5d804f9a01f886d1fa.json (key: https://tigerconnect.com/:link...)
2025-07-12T06:37:54.293Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:link-analysis (81 bytes)
2025-07-12T06:37:54.296Z [DEBUG] - 💾 Cache saved to file: b5ef386e77eb6386a09f2f179305d2f3.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:37:54.297Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis (81 bytes)
2025-07-12T06:37:54.303Z [DEBUG] - 💾 Cache saved to file: 20ab0109264dc706ee7cc1921f847428.json (key: https://tigerconnect.com/:head...)
2025-07-12T06:37:54.303Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:heading-analysis (84 bytes)
2025-07-12T06:37:54.306Z [DEBUG] - 💾 Cache saved to file: 088fc800f61cab6c26bb949d47942450.json (key: https://tigerconnect.com/:colo...)
2025-07-12T06:37:54.306Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:color-contrast (82 bytes)
2025-07-12T06:37:54.310Z [DEBUG] - 💾 Cache saved to file: e16102238ff5e7451282d7e044253334.json (key: https://tigerconnect.com/:focu...)
2025-07-12T06:37:54.311Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:focus-management (84 bytes)
2025-07-12T06:37:54.316Z [DEBUG] - 💾 Cache saved to file: de769a9b5f84a8f90831e7232e232037.json (key: https://tigerconnect.com/:keyb...)
2025-07-12T06:37:54.317Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:keyboard-navigation (87 bytes)
2025-07-12T06:37:54.320Z [DEBUG] - 💾 Cache saved to file: 463c8f0eb64ca4ba49b84deb80487cb0.json (key: https://tigerconnect.com/:aria...)
2025-07-12T06:37:54.320Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:aria-validation (83 bytes)
2025-07-12T06:37:54.321Z [DEBUG] - 🔥 Warmed up cache for: https://tigerconnect.com/
2025-07-12T06:37:54.322Z [INFO] - 🔥 Cache warming completed for 1 URLs
2025-07-12T06:37:54.324Z [DEBUG] - 🔥 Pre-warming rule-specific caches for: https://tigerconnect.com/
2025-07-12T06:37:54.333Z [DEBUG] - 💾 Cache saved to file: 0c680842e4e43e2b60ed5369f3762039.json (key: WCAG-001:053b13d2:7e800487...)
2025-07-12T06:37:54.333Z [DEBUG] - 💾 Cached: rule:WCAG-001:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.337Z [DEBUG] - 💾 Cache saved to file: a75e35dd209843948b36c84329d22a7d.json (key: WCAG-002:053b13d2:7e800487...)
2025-07-12T06:37:54.338Z [DEBUG] - 💾 Cached: rule:WCAG-002:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.341Z [DEBUG] - 💾 Cache saved to file: 9cb4dc32d5df423f385c05f9b3176ed5.json (key: WCAG-003:053b13d2:7e800487...)
2025-07-12T06:37:54.342Z [DEBUG] - 💾 Cached: rule:WCAG-003:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.349Z [DEBUG] - 💾 Cache saved to file: 6a3ba030ed4f6bdd1430c84c076923c4.json (key: WCAG-004:053b13d2:7e800487...)
2025-07-12T06:37:54.349Z [DEBUG] - 💾 Cached: rule:WCAG-004:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.352Z [DEBUG] - 💾 Cache saved to file: 982de888cf5260e212204a91473b1660.json (key: WCAG-005:053b13d2:7e800487...)
2025-07-12T06:37:54.352Z [DEBUG] - 💾 Cached: rule:WCAG-005:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.355Z [DEBUG] - 💾 Cache saved to file: e2c4ec65380b1d83c294ae7167544945.json (key: WCAG-006:053b13d2:7e800487...)
2025-07-12T06:37:54.356Z [DEBUG] - 💾 Cached: rule:WCAG-006:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.361Z [DEBUG] - 💾 Cache saved to file: 44542e0fa17221195424bce0cb661872.json (key: WCAG-007:053b13d2:7e800487...)
2025-07-12T06:37:54.361Z [DEBUG] - 💾 Cached: rule:WCAG-007:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.365Z [DEBUG] - 💾 Cache saved to file: 088a0703edaba86c5235f3435f5dd24a.json (key: WCAG-008:053b13d2:7e800487...)
2025-07-12T06:37:54.365Z [DEBUG] - 💾 Cached: rule:WCAG-008:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.367Z [DEBUG] - 💾 Cache saved to file: b0e2c61f5c13d8949e43d1312f7b01b6.json (key: WCAG-009:053b13d2:7e800487...)
2025-07-12T06:37:54.368Z [DEBUG] - 💾 Cached: rule:WCAG-009:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.371Z [DEBUG] - 💾 Cache saved to file: 00004cdfca760e473a4e6b26f7b6b994.json (key: WCAG-010:053b13d2:7e800487...)
2025-07-12T06:37:54.371Z [DEBUG] - 💾 Cached: rule:WCAG-010:053b13d2:7e800487 (61 bytes)
2025-07-12T06:37:54.376Z [DEBUG] - 💾 Cache saved to file: 7fcabdbedc224ce24e39fafab2ef7405.json (key: https://tigerconnect.com/:sema...)
2025-07-12T06:37:54.377Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:semantic-validation (112 bytes)
2025-07-12T06:37:54.382Z [DEBUG] - 💾 Cache saved to file: 94a1fb81d4021b5adcf4359192244915.json (key: https://tigerconnect.com/:cms-...)
2025-07-12T06:37:54.382Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:cms-analysis (105 bytes)
2025-07-12T06:37:54.386Z [DEBUG] - 💾 Cache saved to file: 4b801483243e34625c13b8bedfdbbda8.json (key: https://tigerconnect.com/:enha...)
2025-07-12T06:37:54.387Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:enhanced-contrast (110 bytes)
2025-07-12T06:37:54.394Z [DEBUG] - 💾 Cache saved to file: b5ef386e77eb6386a09f2f179305d2f3.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:37:54.394Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis (106 bytes)
2025-07-12T06:37:54.397Z [DEBUG] - 💾 Cache saved to file: ad72b5a662be888865387d8391c79d1b.json (key: https://tigerconnect.com/:imag...)
2025-07-12T06:37:54.397Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:image-analysis (107 bytes)
2025-07-12T06:37:54.400Z [DEBUG] - 💾 Cache saved to file: 8cab73c21d5b2e5d804f9a01f886d1fa.json (key: https://tigerconnect.com/:link...)
2025-07-12T06:37:54.400Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:link-analysis (106 bytes)
2025-07-12T06:37:54.404Z [DEBUG] - 💾 Cache saved to file: 20ab0109264dc706ee7cc1921f847428.json (key: https://tigerconnect.com/:head...)
2025-07-12T06:37:54.405Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:heading-analysis (109 bytes)
2025-07-12T06:37:54.409Z [DEBUG] - 💾 Cache saved to file: 088fc800f61cab6c26bb949d47942450.json (key: https://tigerconnect.com/:colo...)
2025-07-12T06:37:54.410Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:color-contrast (107 bytes)
2025-07-12T06:37:54.413Z [DEBUG] - 💾 Cache saved to file: e16102238ff5e7451282d7e044253334.json (key: https://tigerconnect.com/:focu...)
2025-07-12T06:37:54.414Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:focus-management (109 bytes)
2025-07-12T06:37:54.417Z [DEBUG] - 💾 Cache saved to file: de769a9b5f84a8f90831e7232e232037.json (key: https://tigerconnect.com/:keyb...)
2025-07-12T06:37:54.420Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:keyboard-navigation (112 bytes)
2025-07-12T06:37:54.425Z [DEBUG] - 💾 Cache saved to file: 463c8f0eb64ca4ba49b84deb80487cb0.json (key: https://tigerconnect.com/:aria...)
2025-07-12T06:37:54.427Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:aria-validation (108 bytes)
2025-07-12T06:37:54.428Z [DEBUG] - ✅ Rule-specific cache warming completed for 10 rules and 11 analysis types
2025-07-12T06:37:54.429Z [INFO] - ✅ Cache pre-warming completed for: https://tigerconnect.com/
2025-07-12T06:37:54.431Z [INFO] - ✅ Cache pre-warming completed in 364ms
2025-07-12T06:37:54.433Z [INFO] - 📋 Extracting unified page structure for all checks...
2025-07-12T06:37:54.466Z [DEBUG] - 📁 Cache file valid: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (185min remaining)
2025-07-12T06:37:54.467Z [DEBUG] - 📁 Cache loaded from file: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (key: dom-structure:https://tigercon...)
2025-07-12T06:37:54.469Z [DEBUG] - 📁 Restored from file cache: dom:dom-structure:https://tigerconnect.com/:053b13d2...
2025-07-12T06:37:54.478Z [DEBUG] - ✅ Cache hit: dom:dom-structure:https://tigerconnect.com/:053b13d2... (accessed 2 times, age: 3282s)
2025-07-12T06:37:54.483Z [DEBUG] - 📋 Using cached page structure (persistent)
2025-07-12T06:37:54.486Z [INFO] - 📋 Page structure extracted successfully: 761 elements found
2025-07-12T06:37:54.487Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.538Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.538Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 0% (0/66)
2025-07-12T06:37:54.541Z [INFO] - 🔍 Executing rule: Non-text Content (WCAG-001)
2025-07-12T06:37:54.544Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-001: Non-text Content
2025-07-12T06:37:54.547Z [DEBUG] - 📁 Cache file valid: b1c2386f72a4e403f0a9afccef14ee51.json (185min remaining)
2025-07-12T06:37:54.547Z [DEBUG] - 📁 Cache loaded from file: b1c2386f72a4e403f0a9afccef14ee51.json (key: WCAG-001:053b13d2:add92319...)
2025-07-12T06:37:54.548Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-001:053b13d2:add92319...
2025-07-12T06:37:54.549Z [DEBUG] - ✅ Cache hit: rule:WCAG-001:053b13d2:add92319... (accessed 2 times, age: 3281s)
2025-07-12T06:37:54.550Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-001
2025-07-12T06:37:54.553Z [DEBUG] - 📁 Cache file valid: 6bccbd724edc1ba02226d2db65245661.json (185min remaining)
2025-07-12T06:37:54.554Z [DEBUG] - 📁 Cache loaded from file: 6bccbd724edc1ba02226d2db65245661.json (key: WCAG-001:WCAG-001...)
2025-07-12T06:37:54.555Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-001:WCAG-001...
2025-07-12T06:37:54.557Z [DEBUG] - ✅ Cache hit: rule:WCAG-001:WCAG-001... (accessed 2 times, age: 3281s)
2025-07-12T06:37:54.558Z [DEBUG] - 📋 Using cached evidence for WCAG-001
2025-07-12T06:37:54.560Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-001 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:54.561Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:54.563Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:54.565Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:54.567Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:54.572Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:54.588Z [DEBUG] - 📁 Cache file valid: 80e3a546bf63e5f250cbac2e49f14df2.json (185min remaining)
2025-07-12T06:37:54.590Z [DEBUG] - 📁 Cache loaded from file: 80e3a546bf63e5f250cbac2e49f14df2.json (key: https://tigerconnect.com/:sema...)
2025-07-12T06:37:54.592Z [DEBUG] - 📁 Restored from file cache: site:https://tigerconnect.com/:semantic-validation-{"va...
2025-07-12T06:37:54.593Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 2 times, age: 3282s)
2025-07-12T06:37:54.595Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:54.599Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:54.602Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:54.605Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:54.607Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:54.747Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:54.781Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:54.858Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:37:54.859Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-001 - {"utilitiesUsed":["content-quality","semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8999999999999999,"executionTime":299}
2025-07-12T06:37:54.863Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:54.864Z [DEBUG] - 🔧 Utility performance recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:54.865Z [DEBUG] - 🔧 Utility analysis completed for WCAG-001: - {"utilitiesUsed":5,"errors":0,"executionTime":321}
2025-07-12T06:37:54.866Z [DEBUG] - ⏱️ Check WCAG-001 completed in 325ms (success: true)
2025-07-12T06:37:54.867Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.876Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.876Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 2% (1/66)
2025-07-12T06:37:54.877Z [INFO] - ✅ Rule WCAG-001 completed: failed (0/100)
2025-07-12T06:37:54.878Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.891Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:54.891Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 2% (1/66)
2025-07-12T06:37:54.893Z [INFO] - 🔍 Executing rule: Captions (Prerecorded) (WCAG-002)
2025-07-12T06:37:54.894Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-002: Captions
2025-07-12T06:37:54.897Z [DEBUG] - 📁 Cache file valid: b5fceffd89ea9b0940a3056d55e299b1.json (185min remaining)
2025-07-12T06:37:54.897Z [DEBUG] - 📁 Cache loaded from file: b5fceffd89ea9b0940a3056d55e299b1.json (key: WCAG-002:053b13d2:add92319...)
2025-07-12T06:37:54.898Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-002:053b13d2:add92319...
2025-07-12T06:37:54.899Z [DEBUG] - ✅ Cache hit: rule:WCAG-002:053b13d2:add92319... (accessed 2 times, age: 3281s)
2025-07-12T06:37:54.903Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-002
2025-07-12T06:37:54.907Z [DEBUG] - 📁 Cache file valid: adad9a0534e9436bc170dbd5c01b3603.json (185min remaining)
2025-07-12T06:37:54.907Z [DEBUG] - 📁 Cache loaded from file: adad9a0534e9436bc170dbd5c01b3603.json (key: WCAG-002:WCAG-002...)
2025-07-12T06:37:54.909Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-002:WCAG-002...
2025-07-12T06:37:54.910Z [DEBUG] - ✅ Cache hit: rule:WCAG-002:WCAG-002... (accessed 2 times, age: 3281s)
2025-07-12T06:37:54.911Z [DEBUG] - 📋 Using cached evidence for WCAG-002
2025-07-12T06:37:54.912Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-002 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"enhance","maxExecutionTime":8000}}
2025-07-12T06:37:54.913Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:54.914Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 3 times, age: 3282s)
2025-07-12T06:37:54.917Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:54.920Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:37:54.921Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 2 times, age: 1s)
2025-07-12T06:37:54.923Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:54.923Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:37:54.925Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:54.927Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:54.928Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:54.933Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:54.967Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:54.995Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:54.998Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-002 - {"utilitiesUsed":["cms-detection","content-quality","semantic-validation"],"confidence":0.8,"accuracy":0.35,"executionTime":86}
2025-07-12T06:37:54.998Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-002: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:54.999Z [DEBUG] - 🔧 Utility performance recorded for WCAG-002: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:55.000Z [DEBUG] - 🔧 Utility analysis completed for WCAG-002: - {"utilitiesUsed":3,"errors":0,"executionTime":105}
2025-07-12T06:37:55.001Z [DEBUG] - ⏱️ Check WCAG-002 completed in 108ms (success: true)
2025-07-12T06:37:55.002Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.012Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.013Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 3% (2/66)
2025-07-12T06:37:55.014Z [INFO] - ✅ Rule WCAG-002 completed: failed (0/100)
2025-07-12T06:37:55.015Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.021Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.022Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 3% (2/66)
2025-07-12T06:37:55.023Z [INFO] - 🔍 Executing rule: Info and Relationships (WCAG-003)
2025-07-12T06:37:55.027Z [INFO] - 🌈 Wide Gamut Color Analyzer initialized - {"p3Analysis":true,"rec2020Analysis":true,"dynamicMonitoring":true}
2025-07-12T06:37:55.028Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-003: Info and Relationships
2025-07-12T06:37:55.031Z [DEBUG] - 📁 Cache file valid: 6b51c453280c02d19238652f55728191.json (185min remaining)
2025-07-12T06:37:55.031Z [DEBUG] - 📁 Cache loaded from file: 6b51c453280c02d19238652f55728191.json (key: WCAG-003:053b13d2:add92319...)
2025-07-12T06:37:55.032Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-003:053b13d2:add92319...
2025-07-12T06:37:55.033Z [DEBUG] - ✅ Cache hit: rule:WCAG-003:053b13d2:add92319... (accessed 2 times, age: 3280s)
2025-07-12T06:37:55.034Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-003
2025-07-12T06:37:55.038Z [DEBUG] - 📁 Cache file valid: a75e90580e5662e79e418867b28e1836.json (185min remaining)
2025-07-12T06:37:55.039Z [DEBUG] - 📁 Cache loaded from file: a75e90580e5662e79e418867b28e1836.json (key: WCAG-003:WCAG-003...)
2025-07-12T06:37:55.040Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-003:WCAG-003...
2025-07-12T06:37:55.042Z [DEBUG] - ✅ Cache hit: rule:WCAG-003:WCAG-003... (accessed 2 times, age: 3280s)
2025-07-12T06:37:55.043Z [DEBUG] - 📋 Using cached evidence for WCAG-003
2025-07-12T06:37:55.044Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-003 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:55.045Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:55.046Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 4 times, age: 3282s)
2025-07-12T06:37:55.047Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:55.049Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:55.050Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:55.055Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:55.055Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:55.058Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:55.059Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:55.060Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:55.091Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:55.113Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:55.130Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:37:55.130Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-003 - {"utilitiesUsed":["content-quality","semantic-validation","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.7,"executionTime":86}
2025-07-12T06:37:55.131Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:55.133Z [DEBUG] - 🔧 Utility performance recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:55.134Z [DEBUG] - 🔧 Utility analysis completed for WCAG-003: - {"utilitiesUsed":4,"errors":0,"executionTime":108}
2025-07-12T06:37:55.135Z [DEBUG] - ⏱️ Check WCAG-003 completed in 112ms (success: true)
2025-07-12T06:37:55.135Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.141Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.143Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 5% (3/66)
2025-07-12T06:37:55.144Z [INFO] - ✅ Rule WCAG-003 completed: failed (0/100)
2025-07-12T06:37:55.145Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.149Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.149Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 5% (3/66)
2025-07-12T06:37:55.150Z [INFO] - 🔍 Executing rule: Contrast (Minimum) (WCAG-004)
2025-07-12T06:37:55.151Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-004: Contrast (Minimum)
2025-07-12T06:37:55.155Z [DEBUG] - 📁 Cache file valid: 79931a2e7b38e3a273482da1d4531a95.json (185min remaining)
2025-07-12T06:37:55.156Z [DEBUG] - 📁 Cache loaded from file: 79931a2e7b38e3a273482da1d4531a95.json (key: WCAG-004:053b13d2:add92319...)
2025-07-12T06:37:55.157Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-004:053b13d2:add92319...
2025-07-12T06:37:55.159Z [DEBUG] - ✅ Cache hit: rule:WCAG-004:053b13d2:add92319... (accessed 2 times, age: 3279s)
2025-07-12T06:37:55.160Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-004
2025-07-12T06:37:55.163Z [DEBUG] - 📁 Cache file valid: d2ad183430c8e3077869fa832c90b2ca.json (185min remaining)
2025-07-12T06:37:55.163Z [DEBUG] - 📁 Cache loaded from file: d2ad183430c8e3077869fa832c90b2ca.json (key: WCAG-004:WCAG-004...)
2025-07-12T06:37:55.164Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-004:WCAG-004...
2025-07-12T06:37:55.165Z [DEBUG] - ✅ Cache hit: rule:WCAG-004:WCAG-004... (accessed 2 times, age: 3279s)
2025-07-12T06:37:55.165Z [DEBUG] - 📋 Using cached evidence for WCAG-004
2025-07-12T06:37:55.166Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-004 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:55.167Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:55.168Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:55.170Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:55.200Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T06:37:55.200Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T06:37:55.426Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:55.427Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-004 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":261}
2025-07-12T06:37:55.429Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:55.431Z [DEBUG] - 🔧 Utility performance recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:55.432Z [DEBUG] - 🔧 Utility analysis completed for WCAG-004: - {"utilitiesUsed":3,"errors":0,"executionTime":279}
2025-07-12T06:37:55.433Z [DEBUG] - ⏱️ Check WCAG-004 completed in 283ms (success: true)
2025-07-12T06:37:55.433Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.440Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.441Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 6% (4/66)
2025-07-12T06:37:55.442Z [INFO] - ✅ Rule WCAG-004 completed: passed (100/100)
2025-07-12T06:37:55.443Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.448Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.448Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 6% (4/66)
2025-07-12T06:37:55.449Z [INFO] - 🔍 Executing rule: Keyboard (WCAG-005)
2025-07-12T06:37:55.450Z [INFO] - 🎯 Advanced Focus Tracker initialized - {"customIndicators":true,"flowAnalysis":true,"accessibilityTree":true}
2025-07-12T06:37:55.451Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-005: Keyboard
2025-07-12T06:37:55.456Z [DEBUG] - 📁 Cache file valid: a6ecce5051f9339949683209a1de4a1a.json (185min remaining)
2025-07-12T06:37:55.457Z [DEBUG] - 📁 Cache loaded from file: a6ecce5051f9339949683209a1de4a1a.json (key: WCAG-005:053b13d2:add92319...)
2025-07-12T06:37:55.458Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-005:053b13d2:add92319...
2025-07-12T06:37:55.459Z [DEBUG] - ✅ Cache hit: rule:WCAG-005:053b13d2:add92319... (accessed 2 times, age: 3279s)
2025-07-12T06:37:55.460Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-005
2025-07-12T06:37:55.461Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-005 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","maxExecutionTime":6000}}
2025-07-12T06:37:55.462Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:55.463Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 5 times, age: 3282s)
2025-07-12T06:37:55.464Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:55.465Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:55.467Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:55.473Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:55.474Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:55.476Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:55.477Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:55.478Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:55.490Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:55.539Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:37:55.540Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-005 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":78}
2025-07-12T06:37:55.542Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:55.543Z [DEBUG] - 🔧 Utility performance recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:55.544Z [DEBUG] - 🔧 Utility analysis completed for WCAG-005: - {"utilitiesUsed":4,"errors":0,"executionTime":93}
2025-07-12T06:37:55.545Z [DEBUG] - ⏱️ Check WCAG-005 completed in 96ms (success: true)
2025-07-12T06:37:55.546Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.552Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.552Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 8% (5/66)
2025-07-12T06:37:55.553Z [INFO] - ✅ Rule WCAG-005 completed: failed (0/100)
2025-07-12T06:37:55.554Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.561Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.561Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 8% (5/66)
2025-07-12T06:37:55.562Z [INFO] - 🔍 Executing rule: Focus Order (WCAG-006)
2025-07-12T06:37:55.563Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-006: Focus Order
2025-07-12T06:37:55.566Z [DEBUG] - 📁 Cache file valid: c1c224c4d926d4c3a0d059b8492adcc4.json (185min remaining)
2025-07-12T06:37:55.567Z [DEBUG] - 📁 Cache loaded from file: c1c224c4d926d4c3a0d059b8492adcc4.json (key: WCAG-006:053b13d2:add92319...)
2025-07-12T06:37:55.568Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-006:053b13d2:add92319...
2025-07-12T06:37:55.569Z [DEBUG] - ✅ Cache hit: rule:WCAG-006:053b13d2:add92319... (accessed 2 times, age: 3277s)
2025-07-12T06:37:55.570Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-006
2025-07-12T06:37:55.576Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-006 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:55.577Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:55.579Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:55.581Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:55.850Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:55.850Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-006 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":274}
2025-07-12T06:37:55.853Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:55.855Z [DEBUG] - 🔧 Utility performance recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:55.856Z [DEBUG] - 🔧 Utility analysis completed for WCAG-006: - {"utilitiesUsed":3,"errors":0,"executionTime":291}
2025-07-12T06:37:55.857Z [DEBUG] - ⏱️ Check WCAG-006 completed in 295ms (success: true)
2025-07-12T06:37:55.858Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.863Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.863Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 9% (6/66)
2025-07-12T06:37:55.864Z [INFO] - ✅ Rule WCAG-006 completed: failed (0/100)
2025-07-12T06:37:55.865Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.869Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:55.870Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 9% (6/66)
2025-07-12T06:37:55.871Z [INFO] - 🔍 Executing rule: Focus Visible (WCAG-007)
2025-07-12T06:37:55.872Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-007: Focus Visible
2025-07-12T06:37:55.878Z [DEBUG] - 📁 Cache file valid: 1f4c31fe7cc78e443a634f63156f8b6a.json (185min remaining)
2025-07-12T06:37:55.879Z [DEBUG] - 📁 Cache loaded from file: 1f4c31fe7cc78e443a634f63156f8b6a.json (key: WCAG-007:053b13d2:add92319...)
2025-07-12T06:37:55.879Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-007:053b13d2:add92319...
2025-07-12T06:37:55.880Z [DEBUG] - ✅ Cache hit: rule:WCAG-007:053b13d2:add92319... (accessed 2 times, age: 3271s)
2025-07-12T06:37:55.881Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-007
2025-07-12T06:37:55.884Z [DEBUG] - 📁 Cache file valid: 92e29ae80832f8cf81c9ef789399e501.json (185min remaining)
2025-07-12T06:37:55.885Z [DEBUG] - 📁 Cache loaded from file: 92e29ae80832f8cf81c9ef789399e501.json (key: WCAG-007:WCAG-007...)
2025-07-12T06:37:55.886Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-007:WCAG-007...
2025-07-12T06:37:55.890Z [DEBUG] - ✅ Cache hit: rule:WCAG-007:WCAG-007... (accessed 2 times, age: 3271s)
2025-07-12T06:37:55.892Z [DEBUG] - 📋 Using cached evidence for WCAG-007
2025-07-12T06:37:55.893Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-007 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:37:55.895Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:55.897Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:55.898Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:56.169Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:56.170Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-007 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":277}
2025-07-12T06:37:56.172Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:56.174Z [DEBUG] - 🔧 Utility performance recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:56.176Z [DEBUG] - 🔧 Utility analysis completed for WCAG-007: - {"utilitiesUsed":3,"errors":0,"executionTime":301}
2025-07-12T06:37:56.177Z [DEBUG] - ⏱️ Check WCAG-007 completed in 306ms (success: true)
2025-07-12T06:37:56.178Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.186Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.186Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 11% (7/66)
2025-07-12T06:37:56.187Z [INFO] - ✅ Rule WCAG-007 completed: failed (0/100)
2025-07-12T06:37:56.187Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.194Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.194Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 11% (7/66)
2025-07-12T06:37:56.195Z [INFO] - 🔍 Executing rule: Error Identification (WCAG-008)
2025-07-12T06:37:56.197Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-008: Error Identification
2025-07-12T06:37:56.199Z [DEBUG] - 📁 Cache file valid: 06edb7a0699869f2657c51a560ea933c.json (186min remaining)
2025-07-12T06:37:56.199Z [DEBUG] - 📁 Cache loaded from file: 06edb7a0699869f2657c51a560ea933c.json (key: WCAG-008:053b13d2:add92319...)
2025-07-12T06:37:56.200Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-008:053b13d2:add92319...
2025-07-12T06:37:56.201Z [DEBUG] - ✅ Cache hit: rule:WCAG-008:053b13d2:add92319... (accessed 2 times, age: 3266s)
2025-07-12T06:37:56.202Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-008
2025-07-12T06:37:56.204Z [DEBUG] - 📁 Cache file valid: e98e5525ae11e3cecb7834f06806f786.json (186min remaining)
2025-07-12T06:37:56.207Z [DEBUG] - 📁 Cache loaded from file: e98e5525ae11e3cecb7834f06806f786.json (key: WCAG-008:WCAG-008...)
2025-07-12T06:37:56.208Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-008:WCAG-008...
2025-07-12T06:37:56.209Z [DEBUG] - ✅ Cache hit: rule:WCAG-008:WCAG-008... (accessed 2 times, age: 3266s)
2025-07-12T06:37:56.210Z [DEBUG] - 📋 Using cached evidence for WCAG-008
2025-07-12T06:37:56.211Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-008 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:56.212Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:56.213Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 6 times, age: 3283s)
2025-07-12T06:37:56.214Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:56.216Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:56.218Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:56.219Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:56.225Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:56.229Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:56.230Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:56.240Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:56.282Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:37:56.283Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-008 - {"utilitiesUsed":["semantic-validation","component-library","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.55,"executionTime":72}
2025-07-12T06:37:56.285Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:56.287Z [DEBUG] - 🔧 Utility performance recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:56.288Z [DEBUG] - 🔧 Utility analysis completed for WCAG-008: - {"utilitiesUsed":3,"errors":0,"executionTime":90}
2025-07-12T06:37:56.289Z [DEBUG] - ⏱️ Check WCAG-008 completed in 94ms (success: true)
2025-07-12T06:37:56.290Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.296Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.296Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 12% (8/66)
2025-07-12T06:37:56.297Z [INFO] - ✅ Rule WCAG-008 completed: failed (0/100)
2025-07-12T06:37:56.297Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.302Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.302Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 12% (8/66)
2025-07-12T06:37:56.303Z [INFO] - 🔍 Executing rule: Name, Role, Value (WCAG-009)
2025-07-12T06:37:56.304Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-009: Name, Role, Value
2025-07-12T06:37:56.315Z [DEBUG] - 📁 Cache file valid: 6411962e15bff5d711f1d34d86b5a178.json (186min remaining)
2025-07-12T06:37:56.316Z [DEBUG] - 📁 Cache loaded from file: 6411962e15bff5d711f1d34d86b5a178.json (key: WCAG-009:053b13d2:add92319...)
2025-07-12T06:37:56.317Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-009:053b13d2:add92319...
2025-07-12T06:37:56.318Z [DEBUG] - ✅ Cache hit: rule:WCAG-009:053b13d2:add92319... (accessed 2 times, age: 3266s)
2025-07-12T06:37:56.319Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-009
2025-07-12T06:37:56.324Z [DEBUG] - 📁 Cache file valid: a3bf5ff732223bebe7ee44260f121f3e.json (186min remaining)
2025-07-12T06:37:56.325Z [DEBUG] - 📁 Cache loaded from file: a3bf5ff732223bebe7ee44260f121f3e.json (key: WCAG-009:WCAG-009...)
2025-07-12T06:37:56.326Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-009:WCAG-009...
2025-07-12T06:37:56.327Z [DEBUG] - ✅ Cache hit: rule:WCAG-009:WCAG-009... (accessed 2 times, age: 3266s)
2025-07-12T06:37:56.328Z [DEBUG] - 📋 Using cached evidence for WCAG-009
2025-07-12T06:37:56.329Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-009 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:56.330Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:56.330Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 7 times, age: 3283s)
2025-07-12T06:37:56.331Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:56.333Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:56.335Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:56.340Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:56.340Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:56.342Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:56.343Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:56.344Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:56.358Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:56.409Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:37:56.409Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-009 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":80}
2025-07-12T06:37:56.411Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:56.413Z [DEBUG] - 🔧 Utility performance recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:56.414Z [DEBUG] - 🔧 Utility analysis completed for WCAG-009: - {"utilitiesUsed":4,"errors":0,"executionTime":108}
2025-07-12T06:37:56.415Z [DEBUG] - ⏱️ Check WCAG-009 completed in 112ms (success: true)
2025-07-12T06:37:56.416Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.421Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.421Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 14% (9/66)
2025-07-12T06:37:56.422Z [INFO] - ✅ Rule WCAG-009 completed: failed (0/100)
2025-07-12T06:37:56.423Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.430Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.430Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 14% (9/66)
2025-07-12T06:37:56.431Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Minimum) (WCAG-010)
2025-07-12T06:37:56.432Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-010: Focus Not Obscured (Minimum)
2025-07-12T06:37:56.442Z [DEBUG] - 📁 Cache file valid: d60e2dd8f7e1b2b08da70c1edb0ebaf5.json (186min remaining)
2025-07-12T06:37:56.442Z [DEBUG] - 📁 Cache loaded from file: d60e2dd8f7e1b2b08da70c1edb0ebaf5.json (key: WCAG-010:053b13d2:add92319...)
2025-07-12T06:37:56.443Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-010:053b13d2:add92319...
2025-07-12T06:37:56.444Z [DEBUG] - ✅ Cache hit: rule:WCAG-010:053b13d2:add92319... (accessed 2 times, age: 3261s)
2025-07-12T06:37:56.444Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-010
2025-07-12T06:37:56.448Z [DEBUG] - 📁 Cache file valid: b58e4c6bbef16c78192296d9cc3e9207.json (186min remaining)
2025-07-12T06:37:56.450Z [DEBUG] - 📁 Cache loaded from file: b58e4c6bbef16c78192296d9cc3e9207.json (key: WCAG-010:WCAG-010...)
2025-07-12T06:37:56.451Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-010:WCAG-010...
2025-07-12T06:37:56.452Z [DEBUG] - ✅ Cache hit: rule:WCAG-010:WCAG-010... (accessed 2 times, age: 3261s)
2025-07-12T06:37:56.453Z [DEBUG] - 📋 Using cached evidence for WCAG-010
2025-07-12T06:37:56.454Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:56.455Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:56.457Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:56.458Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:56.733Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:56.733Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":279}
2025-07-12T06:37:56.736Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:56.737Z [DEBUG] - 🔧 Utility performance recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:56.739Z [DEBUG] - 🔧 Utility analysis completed for WCAG-010: - {"utilitiesUsed":3,"errors":0,"executionTime":304}
2025-07-12T06:37:56.740Z [DEBUG] - ⏱️ Check WCAG-010 completed in 308ms (success: true)
2025-07-12T06:37:56.741Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.783Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.783Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 15% (10/66)
2025-07-12T06:37:56.785Z [INFO] - ✅ Rule WCAG-010 completed: failed (0/100)
2025-07-12T06:37:56.788Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.792Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:56.793Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 15% (10/66)
2025-07-12T06:37:56.794Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Enhanced) (WCAG-011)
2025-07-12T06:37:56.795Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-011: Focus Not Obscured (Enhanced)
2025-07-12T06:37:56.799Z [DEBUG] - 📁 Cache file valid: 03cd0bc1a4ee73aa53c096bc5d54750a.json (186min remaining)
2025-07-12T06:37:56.800Z [DEBUG] - 📁 Cache loaded from file: 03cd0bc1a4ee73aa53c096bc5d54750a.json (key: WCAG-011:053b13d2:add92319...)
2025-07-12T06:37:56.801Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-011:053b13d2:add92319...
2025-07-12T06:37:56.803Z [DEBUG] - ✅ Cache hit: rule:WCAG-011:053b13d2:add92319... (accessed 2 times, age: 3257s)
2025-07-12T06:37:56.804Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-011
2025-07-12T06:37:56.807Z [DEBUG] - 📁 Cache file valid: 1903ba2567cc5b56b159dd13b2eeb39d.json (186min remaining)
2025-07-12T06:37:56.807Z [DEBUG] - 📁 Cache loaded from file: 1903ba2567cc5b56b159dd13b2eeb39d.json (key: WCAG-011:WCAG-011...)
2025-07-12T06:37:56.808Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-011:WCAG-011...
2025-07-12T06:37:56.809Z [DEBUG] - ✅ Cache hit: rule:WCAG-011:WCAG-011... (accessed 2 times, age: 3257s)
2025-07-12T06:37:56.809Z [DEBUG] - 📋 Using cached evidence for WCAG-011
2025-07-12T06:37:56.810Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:56.811Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:56.816Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:56.818Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:57.082Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:57.082Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":272}
2025-07-12T06:37:57.085Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:57.086Z [DEBUG] - 🔧 Utility performance recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:57.087Z [DEBUG] - 🔧 Utility analysis completed for WCAG-011: - {"utilitiesUsed":3,"errors":0,"executionTime":291}
2025-07-12T06:37:57.088Z [DEBUG] - ⏱️ Check WCAG-011 completed in 294ms (success: true)
2025-07-12T06:37:57.090Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.094Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.094Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 17% (11/66)
2025-07-12T06:37:57.095Z [INFO] - ✅ Rule WCAG-011 completed: passed (96/100)
2025-07-12T06:37:57.095Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.110Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.110Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 17% (11/66)
2025-07-12T06:37:57.111Z [INFO] - 🔍 Executing rule: Focus Appearance (WCAG-012)
2025-07-12T06:37:57.112Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-012: Focus Appearance
2025-07-12T06:37:57.116Z [DEBUG] - 📁 Cache file valid: 72aedcf6766a997165d0b6bb2a48954e.json (186min remaining)
2025-07-12T06:37:57.116Z [DEBUG] - 📁 Cache loaded from file: 72aedcf6766a997165d0b6bb2a48954e.json (key: WCAG-012:053b13d2:add92319...)
2025-07-12T06:37:57.117Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-012:053b13d2:add92319...
2025-07-12T06:37:57.117Z [DEBUG] - ✅ Cache hit: rule:WCAG-012:053b13d2:add92319... (accessed 2 times, age: 3248s)
2025-07-12T06:37:57.118Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-012
2025-07-12T06:37:57.122Z [DEBUG] - 📁 Cache file valid: c6ed0a7d6ff63e2693b81632a40525f4.json (186min remaining)
2025-07-12T06:37:57.123Z [DEBUG] - 📁 Cache loaded from file: c6ed0a7d6ff63e2693b81632a40525f4.json (key: WCAG-012:WCAG-012...)
2025-07-12T06:37:57.125Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-012:WCAG-012...
2025-07-12T06:37:57.126Z [DEBUG] - ✅ Cache hit: rule:WCAG-012:WCAG-012... (accessed 2 times, age: 3248s)
2025-07-12T06:37:57.127Z [DEBUG] - 📋 Using cached evidence for WCAG-012
2025-07-12T06:37:57.127Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:57.128Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:57.130Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:57.131Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:57.363Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:57.363Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":236}
2025-07-12T06:37:57.365Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:57.367Z [DEBUG] - 🔧 Utility performance recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:57.368Z [DEBUG] - 🔧 Utility analysis completed for WCAG-012: - {"utilitiesUsed":3,"errors":0,"executionTime":254}
2025-07-12T06:37:57.369Z [DEBUG] - ⏱️ Check WCAG-012 completed in 258ms (success: true)
2025-07-12T06:37:57.370Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.380Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.380Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 18% (12/66)
2025-07-12T06:37:57.381Z [INFO] - ✅ Rule WCAG-012 completed: failed (0/100)
2025-07-12T06:37:57.383Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.395Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.395Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 18% (12/66)
2025-07-12T06:37:57.396Z [INFO] - 🔍 Executing rule: Dragging Movements (WCAG-013)
2025-07-12T06:37:57.398Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-013: Dragging Movements
2025-07-12T06:37:57.400Z [DEBUG] - 📁 Cache file valid: d7c811b39f1be98996f445fb6f9bd998.json (186min remaining)
2025-07-12T06:37:57.401Z [DEBUG] - 📁 Cache loaded from file: d7c811b39f1be98996f445fb6f9bd998.json (key: WCAG-013:053b13d2:add92319...)
2025-07-12T06:37:57.401Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-013:053b13d2:add92319...
2025-07-12T06:37:57.402Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:053b13d2:add92319... (accessed 2 times, age: 3248s)
2025-07-12T06:37:57.403Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-013
2025-07-12T06:37:57.407Z [DEBUG] - 📁 Cache file valid: 27e7e292431047de714c2100e736b7bd.json (186min remaining)
2025-07-12T06:37:57.409Z [DEBUG] - 📁 Cache loaded from file: 27e7e292431047de714c2100e736b7bd.json (key: WCAG-013:WCAG-013...)
2025-07-12T06:37:57.410Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-013:WCAG-013...
2025-07-12T06:37:57.412Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:WCAG-013... (accessed 2 times, age: 3248s)
2025-07-12T06:37:57.412Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-12T06:37:57.413Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:57.414Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:57.415Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:57.616Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:57.617Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":203}
2025-07-12T06:37:57.619Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:57.621Z [DEBUG] - 🔧 Utility performance recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:57.623Z [DEBUG] - 🔧 Utility analysis completed for WCAG-013: - {"utilitiesUsed":2,"errors":0,"executionTime":223}
2025-07-12T06:37:57.624Z [DEBUG] - ⏱️ Check WCAG-013 completed in 227ms (success: true)
2025-07-12T06:37:57.624Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.746Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.746Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 20% (13/66)
2025-07-12T06:37:57.748Z [INFO] - ✅ Rule WCAG-013 completed: failed (0/100)
2025-07-12T06:37:57.750Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.933Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:57.933Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 20% (13/66)
2025-07-12T06:37:57.937Z [INFO] - 🔍 Executing rule: Target Size (Minimum) (WCAG-014)
2025-07-12T06:37:57.939Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-014: Target Size
2025-07-12T06:37:58.121Z [DEBUG] - 📁 Cache file valid: 8b42333cd82cdfc12b4027f127683b1b.json (186min remaining)
2025-07-12T06:37:58.122Z [DEBUG] - 📁 Cache loaded from file: 8b42333cd82cdfc12b4027f127683b1b.json (key: WCAG-014:053b13d2:add92319...)
2025-07-12T06:37:58.124Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-014:053b13d2:add92319...
2025-07-12T06:37:58.125Z [DEBUG] - ✅ Cache hit: rule:WCAG-014:053b13d2:add92319... (accessed 2 times, age: 3247s)
2025-07-12T06:37:58.126Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-014
2025-07-12T06:37:58.218Z [DEBUG] - 📁 Cache file valid: 45019f21152e7ac5a9a16de79e14d653.json (186min remaining)
2025-07-12T06:37:58.218Z [DEBUG] - 📁 Cache loaded from file: 45019f21152e7ac5a9a16de79e14d653.json (key: WCAG-014:WCAG-014...)
2025-07-12T06:37:58.220Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-014:WCAG-014...
2025-07-12T06:37:58.222Z [DEBUG] - ✅ Cache hit: rule:WCAG-014:WCAG-014... (accessed 2 times, age: 3247s)
2025-07-12T06:37:58.223Z [DEBUG] - 📋 Using cached evidence for WCAG-014
2025-07-12T06:37:58.224Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:37:58.224Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:58.226Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:58.459Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:58.459Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":235}
2025-07-12T06:37:58.461Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:58.463Z [DEBUG] - 🔧 Utility performance recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:58.463Z [DEBUG] - 🔧 Utility analysis completed for WCAG-014: - {"utilitiesUsed":2,"errors":0,"executionTime":524}
2025-07-12T06:37:58.464Z [DEBUG] - ⏱️ Check WCAG-014 completed in 527ms (success: true)
2025-07-12T06:37:58.465Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.469Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.474Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 21% (14/66)
2025-07-12T06:37:58.475Z [INFO] - ✅ Rule WCAG-014 completed: passed (81/100)
2025-07-12T06:37:58.476Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.480Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.480Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 21% (14/66)
2025-07-12T06:37:58.481Z [INFO] - 🔍 Executing rule: Consistent Help (WCAG-015)
2025-07-12T06:37:58.482Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-015: Consistent Help
2025-07-12T06:37:58.484Z [DEBUG] - 📁 Cache file valid: 697e44ab0d03e917e7e24943c6f491ae.json (186min remaining)
2025-07-12T06:37:58.487Z [DEBUG] - 📁 Cache loaded from file: 697e44ab0d03e917e7e24943c6f491ae.json (key: WCAG-015:053b13d2:add92319...)
2025-07-12T06:37:58.487Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-015:053b13d2:add92319...
2025-07-12T06:37:58.490Z [DEBUG] - ✅ Cache hit: rule:WCAG-015:053b13d2:add92319... (accessed 2 times, age: 3247s)
2025-07-12T06:37:58.491Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-015
2025-07-12T06:37:58.494Z [DEBUG] - 📁 Cache file valid: 8b605d087733e8f3be3611d94b7ebdae.json (186min remaining)
2025-07-12T06:37:58.494Z [DEBUG] - 📁 Cache loaded from file: 8b605d087733e8f3be3611d94b7ebdae.json (key: WCAG-035:WCAG-035...)
2025-07-12T06:37:58.495Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-035:WCAG-035...
2025-07-12T06:37:58.496Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:WCAG-035... (accessed 2 times, age: 3247s)
2025-07-12T06:37:58.497Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-12T06:37:58.498Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-12T06:37:58.499Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:58.501Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:58.551Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:58.736Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:58.736Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":238}
2025-07-12T06:37:58.739Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:58.740Z [DEBUG] - 🔧 Utility performance recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:58.741Z [DEBUG] - 🔧 Utility analysis completed for WCAG-015: - {"utilitiesUsed":2,"errors":0,"executionTime":258}
2025-07-12T06:37:58.742Z [DEBUG] - ⏱️ Check WCAG-015 completed in 261ms (success: true)
2025-07-12T06:37:58.743Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.752Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.752Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 23% (15/66)
2025-07-12T06:37:58.753Z [INFO] - ✅ Rule WCAG-015 completed: passed (100/100)
2025-07-12T06:37:58.756Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.760Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:58.760Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 23% (15/66)
2025-07-12T06:37:58.761Z [INFO] - 🔍 Executing rule: Redundant Entry (WCAG-016)
2025-07-12T06:37:58.762Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-016: Redundant Entry
2025-07-12T06:37:58.764Z [DEBUG] - 📁 Cache file valid: fa41840651a51956c64ee2b37855c8da.json (186min remaining)
2025-07-12T06:37:58.764Z [DEBUG] - 📁 Cache loaded from file: fa41840651a51956c64ee2b37855c8da.json (key: WCAG-016:053b13d2:add92319...)
2025-07-12T06:37:58.765Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-016:053b13d2:add92319...
2025-07-12T06:37:58.768Z [DEBUG] - ✅ Cache hit: rule:WCAG-016:053b13d2:add92319... (accessed 2 times, age: 3245s)
2025-07-12T06:37:58.770Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-016
2025-07-12T06:37:58.773Z [DEBUG] - 📁 Cache file valid: 00ec64e924e2b9439f3c7ff096f625cd.json (186min remaining)
2025-07-12T06:37:58.773Z [DEBUG] - 📁 Cache loaded from file: 00ec64e924e2b9439f3c7ff096f625cd.json (key: WCAG-016:WCAG-016...)
2025-07-12T06:37:58.774Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-016:WCAG-016...
2025-07-12T06:37:58.775Z [DEBUG] - ✅ Cache hit: rule:WCAG-016:WCAG-016... (accessed 2 times, age: 3245s)
2025-07-12T06:37:58.775Z [DEBUG] - 📋 Using cached evidence for WCAG-016
2025-07-12T06:37:58.776Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:58.777Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:58.778Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:58.989Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:58.989Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":213}
2025-07-12T06:37:58.991Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:58.993Z [DEBUG] - 🔧 Utility performance recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:58.993Z [DEBUG] - 🔧 Utility analysis completed for WCAG-016: - {"utilitiesUsed":2,"errors":0,"executionTime":230}
2025-07-12T06:37:58.994Z [DEBUG] - ⏱️ Check WCAG-016 completed in 233ms (success: true)
2025-07-12T06:37:58.995Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.001Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.001Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 24% (16/66)
2025-07-12T06:37:59.002Z [INFO] - ✅ Rule WCAG-016 completed: failed (0/100)
2025-07-12T06:37:59.004Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.009Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.009Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 24% (16/66)
2025-07-12T06:37:59.010Z [INFO] - 🔍 Executing rule: Image Alternatives (WCAG-017)
2025-07-12T06:37:59.011Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-017: Image Alternatives 3.0
2025-07-12T06:37:59.014Z [DEBUG] - 📁 Cache file valid: 2bd69c6d0cff0a882c80dbccc0ddbd2e.json (186min remaining)
2025-07-12T06:37:59.014Z [DEBUG] - 📁 Cache loaded from file: 2bd69c6d0cff0a882c80dbccc0ddbd2e.json (key: WCAG-017:053b13d2:add92319...)
2025-07-12T06:37:59.015Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-017:053b13d2:add92319...
2025-07-12T06:37:59.016Z [DEBUG] - ✅ Cache hit: rule:WCAG-017:053b13d2:add92319... (accessed 2 times, age: 3245s)
2025-07-12T06:37:59.020Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-017
2025-07-12T06:37:59.025Z [DEBUG] - 📁 Cache file valid: 5d32c0d91398f2b5e88f9e138f971a46.json (186min remaining)
2025-07-12T06:37:59.026Z [DEBUG] - 📁 Cache loaded from file: 5d32c0d91398f2b5e88f9e138f971a46.json (key: WCAG-017:WCAG-017...)
2025-07-12T06:37:59.027Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-017:WCAG-017...
2025-07-12T06:37:59.027Z [DEBUG] - ✅ Cache hit: rule:WCAG-017:WCAG-017... (accessed 2 times, age: 3245s)
2025-07-12T06:37:59.028Z [DEBUG] - 📋 Using cached evidence for WCAG-017
2025-07-12T06:37:59.029Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-017 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:59.029Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:59.030Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 8 times, age: 3286s)
2025-07-12T06:37:59.031Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:59.035Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:59.037Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:59.037Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:59.039Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:59.040Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:59.040Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:59.065Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:59.085Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:59.092Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:37:59.093Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-017 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":64}
2025-07-12T06:37:59.094Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:59.096Z [DEBUG] - 🔧 Utility performance recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:59.098Z [DEBUG] - 🔧 Utility analysis completed for WCAG-017: - {"utilitiesUsed":3,"errors":0,"executionTime":83}
2025-07-12T06:37:59.099Z [DEBUG] - ⏱️ Check WCAG-017 completed in 89ms (success: true)
2025-07-12T06:37:59.100Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.106Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.106Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 26% (17/66)
2025-07-12T06:37:59.107Z [INFO] - ✅ Rule WCAG-017 completed: failed (0/100)
2025-07-12T06:37:59.107Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.114Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.114Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 26% (17/66)
2025-07-12T06:37:59.115Z [INFO] - 🔍 Executing rule: Text and Wording (WCAG-018)
2025-07-12T06:37:59.117Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-018: Text and Wording
2025-07-12T06:37:59.119Z [DEBUG] - 📁 Cache file valid: e7fe6692ea6f27ac095ad2f9449b6b9c.json (186min remaining)
2025-07-12T06:37:59.120Z [DEBUG] - 📁 Cache loaded from file: e7fe6692ea6f27ac095ad2f9449b6b9c.json (key: WCAG-018:053b13d2:add92319...)
2025-07-12T06:37:59.121Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-018:053b13d2:add92319...
2025-07-12T06:37:59.122Z [DEBUG] - ✅ Cache hit: rule:WCAG-018:053b13d2:add92319... (accessed 2 times, age: 3245s)
2025-07-12T06:37:59.122Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-018
2025-07-12T06:37:59.124Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-018 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"enableCMSDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:59.127Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:37:59.128Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 9 times, age: 3286s)
2025-07-12T06:37:59.129Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:59.130Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:37:59.131Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 3 times, age: 5s)
2025-07-12T06:37:59.132Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:37:59.133Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:37:59.133Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:37:59.135Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:37:59.136Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:37:59.137Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:37:59.162Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:37:59.182Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:59.184Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-018 - {"utilitiesUsed":["cms-detection","content-quality","semantic-validation"],"confidence":0.8,"accuracy":0.35,"executionTime":60}
2025-07-12T06:37:59.185Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-018: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:59.187Z [DEBUG] - 🔧 Utility performance recorded for WCAG-018: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:59.187Z [DEBUG] - 🔧 Utility analysis completed for WCAG-018: - {"utilitiesUsed":3,"errors":0,"executionTime":70}
2025-07-12T06:37:59.188Z [DEBUG] - ⏱️ Check WCAG-018 completed in 73ms (success: true)
2025-07-12T06:37:59.189Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.193Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.194Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 27% (18/66)
2025-07-12T06:37:59.194Z [INFO] - ✅ Rule WCAG-018 completed: failed (0/100)
2025-07-12T06:37:59.195Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.199Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.200Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 27% (18/66)
2025-07-12T06:37:59.201Z [INFO] - 🔍 Executing rule: Keyboard Focus (WCAG-019)
2025-07-12T06:37:59.202Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-019: Keyboard Focus 3.0
2025-07-12T06:37:59.205Z [DEBUG] - 📁 Cache file valid: fe1748cdfb619e21f2bb4a61da77c615.json (186min remaining)
2025-07-12T06:37:59.205Z [DEBUG] - 📁 Cache loaded from file: fe1748cdfb619e21f2bb4a61da77c615.json (key: WCAG-019:053b13d2:add92319...)
2025-07-12T06:37:59.206Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-019:053b13d2:add92319...
2025-07-12T06:37:59.207Z [DEBUG] - ✅ Cache hit: rule:WCAG-019:053b13d2:add92319... (accessed 2 times, age: 3244s)
2025-07-12T06:37:59.207Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-019
2025-07-12T06:37:59.211Z [DEBUG] - 📁 Cache file valid: bc9fe1ac640ba8a7c64083f4dce51eff.json (186min remaining)
2025-07-12T06:37:59.213Z [DEBUG] - 📁 Cache loaded from file: bc9fe1ac640ba8a7c64083f4dce51eff.json (key: WCAG-019:WCAG-019...)
2025-07-12T06:37:59.214Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-019:WCAG-019...
2025-07-12T06:37:59.215Z [DEBUG] - ✅ Cache hit: rule:WCAG-019:WCAG-019... (accessed 2 times, age: 3244s)
2025-07-12T06:37:59.216Z [DEBUG] - 📋 Using cached evidence for WCAG-019
2025-07-12T06:37:59.217Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-019 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:59.218Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:59.220Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:59.222Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:59.438Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:59.439Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-019 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":221}
2025-07-12T06:37:59.441Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:59.442Z [DEBUG] - 🔧 Utility performance recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:59.443Z [DEBUG] - 🔧 Utility analysis completed for WCAG-019: - {"utilitiesUsed":3,"errors":0,"executionTime":239}
2025-07-12T06:37:59.444Z [DEBUG] - ⏱️ Check WCAG-019 completed in 243ms (success: true)
2025-07-12T06:37:59.445Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.450Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.450Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 29% (19/66)
2025-07-12T06:37:59.451Z [INFO] - ✅ Rule WCAG-019 completed: failed (0/100)
2025-07-12T06:37:59.452Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.457Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.457Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 29% (19/66)
2025-07-12T06:37:59.458Z [INFO] - 🔍 Executing rule: Motor (WCAG-020)
2025-07-12T06:37:59.459Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-058: Motor
2025-07-12T06:37:59.463Z [DEBUG] - 📁 Cache file valid: 4c83a99c9da2c4342974e94427876acd.json (186min remaining)
2025-07-12T06:37:59.464Z [DEBUG] - 📁 Cache loaded from file: 4c83a99c9da2c4342974e94427876acd.json (key: WCAG-058:053b13d2:add92319...)
2025-07-12T06:37:59.464Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-058:053b13d2:add92319...
2025-07-12T06:37:59.465Z [DEBUG] - ✅ Cache hit: rule:WCAG-058:053b13d2:add92319... (accessed 2 times, age: 3243s)
2025-07-12T06:37:59.466Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-058
2025-07-12T06:37:59.469Z [DEBUG] - 📁 Cache file valid: e75c3291ea913f99e367de4c994bd504.json (186min remaining)
2025-07-12T06:37:59.471Z [DEBUG] - 📁 Cache loaded from file: e75c3291ea913f99e367de4c994bd504.json (key: WCAG-058:WCAG-058...)
2025-07-12T06:37:59.472Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-058:WCAG-058...
2025-07-12T06:37:59.473Z [DEBUG] - ✅ Cache hit: rule:WCAG-058:WCAG-058... (accessed 2 times, age: 3243s)
2025-07-12T06:37:59.474Z [DEBUG] - 📋 Using cached evidence for WCAG-058
2025-07-12T06:37:59.475Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-020 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:37:59.475Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:59.477Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:59.478Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:37:59.684Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:37:59.684Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-020 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":209}
2025-07-12T06:37:59.686Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:59.688Z [DEBUG] - 🔧 Utility performance recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:59.689Z [DEBUG] - 🔧 Utility analysis completed for WCAG-020: - {"utilitiesUsed":3,"errors":0,"executionTime":228}
2025-07-12T06:37:59.690Z [DEBUG] - ⏱️ Check WCAG-020 completed in 232ms (success: true)
2025-07-12T06:37:59.690Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.695Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.696Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 30% (20/66)
2025-07-12T06:37:59.697Z [INFO] - ✅ Rule WCAG-020 completed: failed (0/100)
2025-07-12T06:37:59.698Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.702Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.703Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 30% (20/66)
2025-07-12T06:37:59.704Z [INFO] - 🔍 Executing rule: Pronunciation & Meaning (WCAG-021)
2025-07-12T06:37:59.705Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-065: Pronunciation & Meaning
2025-07-12T06:37:59.707Z [DEBUG] - 📁 Cache file valid: 7bdf34e25514cb352be82815f91affad.json (186min remaining)
2025-07-12T06:37:59.707Z [DEBUG] - 📁 Cache loaded from file: 7bdf34e25514cb352be82815f91affad.json (key: WCAG-065:053b13d2:add92319...)
2025-07-12T06:37:59.708Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-065:053b13d2:add92319...
2025-07-12T06:37:59.712Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:053b13d2:add92319... (accessed 2 times, age: 3242s)
2025-07-12T06:37:59.713Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-065
2025-07-12T06:37:59.715Z [DEBUG] - 📁 Cache file valid: ee411a3562e329e251a37ada2a1f5641.json (186min remaining)
2025-07-12T06:37:59.715Z [DEBUG] - 📁 Cache loaded from file: ee411a3562e329e251a37ada2a1f5641.json (key: WCAG-065:WCAG-065...)
2025-07-12T06:37:59.716Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-065:WCAG-065...
2025-07-12T06:37:59.717Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:WCAG-065... (accessed 2 times, age: 3242s)
2025-07-12T06:37:59.718Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-12T06:37:59.718Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-021 - {"config":{"enableFrameworkOptimization":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-12T06:37:59.719Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:37:59.721Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:59.764Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:37:59.775Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-021 - {"utilitiesUsed":["content-quality","framework-optimization"],"confidence":0.7,"accuracy":0.35,"executionTime":57}
2025-07-12T06:37:59.775Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:37:59.776Z [DEBUG] - 🔧 Utility performance recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:37:59.777Z [DEBUG] - 🔧 Utility analysis completed for WCAG-021: - {"utilitiesUsed":2,"errors":0,"executionTime":71}
2025-07-12T06:37:59.778Z [DEBUG] - ⏱️ Check WCAG-021 completed in 74ms (success: true)
2025-07-12T06:37:59.778Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.784Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.786Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 32% (21/66)
2025-07-12T06:37:59.789Z [INFO] - ✅ Rule WCAG-021 completed: passed (75/100)
2025-07-12T06:37:59.790Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.794Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:37:59.794Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 32% (21/66)
2025-07-12T06:37:59.795Z [INFO] - 🔍 Executing rule: Accessible Authentication (Minimum) (WCAG-022)
2025-07-12T06:37:59.796Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-022: Accessible Authentication (Minimum)
2025-07-12T06:37:59.799Z [DEBUG] - 📁 Cache file valid: bc5bd398704b1dbd65c4a3ece6d5f341.json (186min remaining)
2025-07-12T06:37:59.800Z [DEBUG] - 📁 Cache loaded from file: bc5bd398704b1dbd65c4a3ece6d5f341.json (key: WCAG-022:053b13d2:add92319...)
2025-07-12T06:37:59.801Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-022:053b13d2:add92319...
2025-07-12T06:37:59.803Z [DEBUG] - ✅ Cache hit: rule:WCAG-022:053b13d2:add92319... (accessed 2 times, age: 3242s)
2025-07-12T06:37:59.804Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-022
2025-07-12T06:37:59.807Z [DEBUG] - 📁 Cache file valid: 07a72359a8282ab6dba353d773a225d3.json (186min remaining)
2025-07-12T06:37:59.807Z [DEBUG] - 📁 Cache loaded from file: 07a72359a8282ab6dba353d773a225d3.json (key: WCAG-022:WCAG-022...)
2025-07-12T06:37:59.808Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-022:WCAG-022...
2025-07-12T06:37:59.809Z [DEBUG] - ✅ Cache hit: rule:WCAG-022:WCAG-022... (accessed 2 times, age: 3242s)
2025-07-12T06:37:59.810Z [DEBUG] - 📋 Using cached evidence for WCAG-022
2025-07-12T06:37:59.810Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:37:59.811Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:37:59.813Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:37:59.816Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:00.029Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:00.030Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":220}
2025-07-12T06:38:00.032Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:00.034Z [DEBUG] - 🔧 Utility performance recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:00.034Z [DEBUG] - 🔧 Utility analysis completed for WCAG-022: - {"utilitiesUsed":3,"errors":0,"executionTime":236}
2025-07-12T06:38:00.035Z [DEBUG] - ⏱️ Check WCAG-022 completed in 240ms (success: true)
2025-07-12T06:38:00.037Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.047Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.049Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 33% (22/66)
2025-07-12T06:38:00.050Z [INFO] - ✅ Rule WCAG-022 completed: failed (0/100)
2025-07-12T06:38:00.051Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.057Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.057Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 33% (22/66)
2025-07-12T06:38:00.058Z [INFO] - 🔍 Executing rule: Accessible Authentication (Enhanced) (WCAG-023)
🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting WCAG-037: Accessible Authentication (Enhanced) (40% automated)
✅ [8b80d206-4a60-40bd-9825-753e25410d09] Completed WCAG-037 in 20ms - Status: passed (100/100, Manual items: 1)
2025-07-12T06:38:00.083Z [DEBUG] - 📁 Cache file valid: e8778cf0e75a1f0e38b3c8636943fd56.json (186min remaining)
2025-07-12T06:38:00.083Z [DEBUG] - 📁 Cache loaded from file: e8778cf0e75a1f0e38b3c8636943fd56.json (key: WCAG-037:WCAG-037...)
2025-07-12T06:38:00.084Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-037:WCAG-037...
2025-07-12T06:38:00.085Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:WCAG-037... (accessed 2 times, age: 3242s)
2025-07-12T06:38:00.085Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-12T06:38:00.086Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-023 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:00.087Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:00.089Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:00.090Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:00.200Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T06:38:00.200Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T06:38:00.297Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:00.297Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-023 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":211}
2025-07-12T06:38:00.299Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:00.301Z [DEBUG] - 🔧 Utility performance recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:00.302Z [DEBUG] - 🔧 Utility analysis completed for WCAG-023: - {"utilitiesUsed":3,"errors":0,"executionTime":241}
2025-07-12T06:38:00.302Z [DEBUG] - ⏱️ Check WCAG-023 completed in 244ms (success: true)
2025-07-12T06:38:00.303Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.309Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.309Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 35% (23/66)
2025-07-12T06:38:00.310Z [INFO] - ✅ Rule WCAG-023 completed: passed (100/100)
2025-07-12T06:38:00.311Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.315Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.315Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 35% (23/66)
2025-07-12T06:38:00.316Z [INFO] - 🔍 Executing rule: Timing Adjustable (WCAG-044)
2025-07-12T06:38:00.317Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-044: Timing Adjustable
2025-07-12T06:38:00.321Z [DEBUG] - 📁 Cache file valid: 98e045dbc9aa729028791ef5084c3d73.json (186min remaining)
2025-07-12T06:38:00.323Z [DEBUG] - 📁 Cache loaded from file: 98e045dbc9aa729028791ef5084c3d73.json (key: WCAG-044:053b13d2:add92319...)
2025-07-12T06:38:00.324Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-044:053b13d2:add92319...
2025-07-12T06:38:00.325Z [DEBUG] - ✅ Cache hit: rule:WCAG-044:053b13d2:add92319... (accessed 2 times, age: 3241s)
2025-07-12T06:38:00.325Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-044
2025-07-12T06:38:00.328Z [DEBUG] - 📁 Cache file valid: d1e2f48c659bd8e8b7ae07436b74c326.json (186min remaining)
2025-07-12T06:38:00.328Z [DEBUG] - 📁 Cache loaded from file: d1e2f48c659bd8e8b7ae07436b74c326.json (key: WCAG-044:WCAG-044...)
2025-07-12T06:38:00.329Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-044:WCAG-044...
2025-07-12T06:38:00.330Z [DEBUG] - ✅ Cache hit: rule:WCAG-044:WCAG-044... (accessed 2 times, age: 3241s)
2025-07-12T06:38:00.331Z [DEBUG] - 📋 Using cached evidence for WCAG-044
2025-07-12T06:38:00.331Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-044 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:00.332Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:00.334Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:00.567Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:00.567Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-044 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":236}
2025-07-12T06:38:00.571Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:00.572Z [DEBUG] - 🔧 Utility performance recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:00.573Z [DEBUG] - 🔧 Utility analysis completed for WCAG-044: - {"utilitiesUsed":2,"errors":0,"executionTime":255}
2025-07-12T06:38:00.574Z [DEBUG] - ⏱️ Check WCAG-044 completed in 257ms (success: true)
2025-07-12T06:38:00.574Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.580Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.580Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 36% (24/66)
2025-07-12T06:38:00.581Z [INFO] - ✅ Rule WCAG-044 completed: passed (75/100)
2025-07-12T06:38:00.581Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.586Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.589Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 36% (24/66)
2025-07-12T06:38:00.590Z [INFO] - 🔍 Executing rule: Pause, Stop, Hide (WCAG-045)
2025-07-12T06:38:00.591Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-045: Pause, Stop, Hide
2025-07-12T06:38:00.594Z [DEBUG] - 📁 Cache file valid: e107a6a933c5dce72c582a57daf2513c.json (186min remaining)
2025-07-12T06:38:00.594Z [DEBUG] - 📁 Cache loaded from file: e107a6a933c5dce72c582a57daf2513c.json (key: WCAG-045:053b13d2:add92319...)
2025-07-12T06:38:00.595Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-045:053b13d2:add92319...
2025-07-12T06:38:00.596Z [DEBUG] - ✅ Cache hit: rule:WCAG-045:053b13d2:add92319... (accessed 2 times, age: 3241s)
2025-07-12T06:38:00.596Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-045
2025-07-12T06:38:00.599Z [DEBUG] - 📁 Cache file valid: 8073e0345c1bb47130e00ead626dd137.json (186min remaining)
2025-07-12T06:38:00.601Z [DEBUG] - 📁 Cache loaded from file: 8073e0345c1bb47130e00ead626dd137.json (key: WCAG-045:WCAG-045...)
2025-07-12T06:38:00.601Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-045:WCAG-045...
2025-07-12T06:38:00.604Z [DEBUG] - ✅ Cache hit: rule:WCAG-045:WCAG-045... (accessed 2 times, age: 3241s)
2025-07-12T06:38:00.605Z [DEBUG] - 📋 Using cached evidence for WCAG-045
2025-07-12T06:38:00.606Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-045 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:00.606Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:00.608Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:00.786Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:00.786Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-045 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":181}
2025-07-12T06:38:00.788Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:00.790Z [DEBUG] - 🔧 Utility performance recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:00.791Z [DEBUG] - 🔧 Utility analysis completed for WCAG-045: - {"utilitiesUsed":2,"errors":0,"executionTime":198}
2025-07-12T06:38:00.792Z [DEBUG] - ⏱️ Check WCAG-045 completed in 202ms (success: true)
2025-07-12T06:38:00.793Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.798Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.798Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 38% (25/66)
2025-07-12T06:38:00.799Z [INFO] - ✅ Rule WCAG-045 completed: failed (0/100)
2025-07-12T06:38:00.800Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.805Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:00.807Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 38% (25/66)
2025-07-12T06:38:00.808Z [INFO] - 🔍 Executing rule: Three Flashes or Below Threshold (WCAG-046)
2025-07-12T06:38:00.809Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-046: Three Flashes or Below Threshold
2025-07-12T06:38:00.812Z [DEBUG] - 📁 Cache file valid: a98794df9865343e56915dbbe43cd6a7.json (186min remaining)
2025-07-12T06:38:00.812Z [DEBUG] - 📁 Cache loaded from file: a98794df9865343e56915dbbe43cd6a7.json (key: WCAG-046:053b13d2:add92319...)
2025-07-12T06:38:00.813Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-046:053b13d2:add92319...
2025-07-12T06:38:00.814Z [DEBUG] - ✅ Cache hit: rule:WCAG-046:053b13d2:add92319... (accessed 2 times, age: 3240s)
2025-07-12T06:38:00.814Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-046
2025-07-12T06:38:00.817Z [DEBUG] - 📁 Cache file valid: 0effabcb96214c0f882ee2a31620da53.json (186min remaining)
2025-07-12T06:38:00.817Z [DEBUG] - 📁 Cache loaded from file: 0effabcb96214c0f882ee2a31620da53.json (key: WCAG-046:WCAG-046...)
2025-07-12T06:38:00.818Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-046:WCAG-046...
2025-07-12T06:38:00.823Z [DEBUG] - ✅ Cache hit: rule:WCAG-046:WCAG-046... (accessed 2 times, age: 3240s)
2025-07-12T06:38:00.825Z [DEBUG] - 📋 Using cached evidence for WCAG-046
2025-07-12T06:38:00.825Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-046 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:00.826Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:00.827Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:01.014Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:01.014Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-046 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":189}
2025-07-12T06:38:01.016Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:01.018Z [DEBUG] - 🔧 Utility performance recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:01.020Z [DEBUG] - 🔧 Utility analysis completed for WCAG-046: - {"utilitiesUsed":2,"errors":0,"executionTime":208}
2025-07-12T06:38:01.020Z [DEBUG] - ⏱️ Check WCAG-046 completed in 212ms (success: true)
2025-07-12T06:38:01.021Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.028Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.029Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 39% (26/66)
2025-07-12T06:38:01.030Z [INFO] - ✅ Rule WCAG-046 completed: passed (99/100)
2025-07-12T06:38:01.031Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.037Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.037Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 39% (26/66)
2025-07-12T06:38:01.038Z [INFO] - 🔍 Executing rule: Resize Text (WCAG-037)
2025-07-12T06:38:01.039Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-037: Resize Text
2025-07-12T06:38:01.045Z [DEBUG] - 📁 Cache file valid: 8b199614ebef71562728e34c7c5cbe56.json (186min remaining)
2025-07-12T06:38:01.045Z [DEBUG] - 📁 Cache loaded from file: 8b199614ebef71562728e34c7c5cbe56.json (key: WCAG-037:053b13d2:add92319...)
2025-07-12T06:38:01.046Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-037:053b13d2:add92319...
2025-07-12T06:38:01.048Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:053b13d2:add92319... (accessed 2 times, age: 3231s)
2025-07-12T06:38:01.049Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-037
2025-07-12T06:38:01.050Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:WCAG-037... (accessed 3 times, age: 3243s)
2025-07-12T06:38:01.050Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-12T06:38:01.051Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-037 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:01.053Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:01.054Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:01.055Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:01.571Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:01.572Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-037 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":521}
2025-07-12T06:38:01.574Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:01.575Z [DEBUG] - 🔧 Utility performance recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:01.576Z [DEBUG] - 🔧 Utility analysis completed for WCAG-037: - {"utilitiesUsed":3,"errors":0,"executionTime":536}
2025-07-12T06:38:01.577Z [DEBUG] - ⏱️ Check WCAG-037 completed in 539ms (success: true)
2025-07-12T06:38:01.578Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.584Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.587Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 41% (27/66)
2025-07-12T06:38:01.588Z [INFO] - ✅ Rule WCAG-037 completed: failed (0/100)
2025-07-12T06:38:01.589Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.593Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.593Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 41% (27/66)
2025-07-12T06:38:01.594Z [INFO] - 🔍 Executing rule: Images of Text (WCAG-039)
2025-07-12T06:38:01.595Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-039: Images of Text
2025-07-12T06:38:01.599Z [DEBUG] - 📁 Cache file valid: 0b9377f001d8de85e74a9756ae50f61f.json (186min remaining)
2025-07-12T06:38:01.600Z [DEBUG] - 📁 Cache loaded from file: 0b9377f001d8de85e74a9756ae50f61f.json (key: WCAG-039:053b13d2:add92319...)
2025-07-12T06:38:01.601Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-039:053b13d2:add92319...
2025-07-12T06:38:01.604Z [DEBUG] - ✅ Cache hit: rule:WCAG-039:053b13d2:add92319... (accessed 2 times, age: 3231s)
2025-07-12T06:38:01.605Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-039
2025-07-12T06:38:01.607Z [DEBUG] - 📁 Cache file valid: 5b4d8451893f90896a28cbbb74a4e6ea.json (186min remaining)
2025-07-12T06:38:01.608Z [DEBUG] - 📁 Cache loaded from file: 5b4d8451893f90896a28cbbb74a4e6ea.json (key: WCAG-039:WCAG-039...)
2025-07-12T06:38:01.608Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-039:WCAG-039...
2025-07-12T06:38:01.609Z [DEBUG] - ✅ Cache hit: rule:WCAG-039:WCAG-039... (accessed 2 times, age: 3231s)
2025-07-12T06:38:01.610Z [DEBUG] - 📋 Using cached evidence for WCAG-039
2025-07-12T06:38:01.611Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-039 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:01.611Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:01.613Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:01.654Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:01.815Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:01.815Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-039 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":204}
2025-07-12T06:38:01.817Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:01.819Z [DEBUG] - 🔧 Utility performance recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:01.819Z [DEBUG] - 🔧 Utility analysis completed for WCAG-039: - {"utilitiesUsed":2,"errors":0,"executionTime":223}
2025-07-12T06:38:01.820Z [DEBUG] - ⏱️ Check WCAG-039 completed in 226ms (success: true)
2025-07-12T06:38:01.821Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.827Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.828Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 42% (28/66)
2025-07-12T06:38:01.829Z [INFO] - ✅ Rule WCAG-039 completed: failed (0/100)
2025-07-12T06:38:01.830Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.835Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:01.835Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 42% (28/66)
2025-07-12T06:38:01.836Z [INFO] - 🔍 Executing rule: Reflow (WCAG-040)
2025-07-12T06:38:01.837Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-040: Reflow
2025-07-12T06:38:01.842Z [DEBUG] - 📁 Cache file valid: a90e5993c16b32f6ab78b892a8127444.json (186min remaining)
2025-07-12T06:38:01.843Z [DEBUG] - 📁 Cache loaded from file: a90e5993c16b32f6ab78b892a8127444.json (key: WCAG-040:053b13d2:add92319...)
2025-07-12T06:38:01.843Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-040:053b13d2:add92319...
2025-07-12T06:38:01.845Z [DEBUG] - ✅ Cache hit: rule:WCAG-040:053b13d2:add92319... (accessed 2 times, age: 3226s)
2025-07-12T06:38:01.846Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-040
2025-07-12T06:38:01.849Z [DEBUG] - 📁 Cache file valid: c1d7238a0d406c4e4d2b9057a9b92850.json (186min remaining)
2025-07-12T06:38:01.850Z [DEBUG] - 📁 Cache loaded from file: c1d7238a0d406c4e4d2b9057a9b92850.json (key: WCAG-041:WCAG-041...)
2025-07-12T06:38:01.850Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-041:WCAG-041...
2025-07-12T06:38:01.852Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:WCAG-041... (accessed 2 times, age: 3226s)
2025-07-12T06:38:01.853Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-12T06:38:01.853Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-040 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:01.854Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:01.859Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:02.044Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:02.044Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-040 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":191}
2025-07-12T06:38:02.046Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:02.048Z [DEBUG] - 🔧 Utility performance recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:02.049Z [DEBUG] - 🔧 Utility analysis completed for WCAG-040: - {"utilitiesUsed":2,"errors":0,"executionTime":210}
2025-07-12T06:38:02.050Z [DEBUG] - ⏱️ Check WCAG-040 completed in 214ms (success: true)
2025-07-12T06:38:02.051Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.056Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.056Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 44% (29/66)
2025-07-12T06:38:02.057Z [INFO] - ✅ Rule WCAG-040 completed: failed (0/100)
2025-07-12T06:38:02.057Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.063Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.064Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 44% (29/66)
2025-07-12T06:38:02.065Z [INFO] - 🔍 Executing rule: Non-text Contrast (WCAG-041)
2025-07-12T06:38:02.066Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-041: Non-text Contrast
2025-07-12T06:38:02.070Z [DEBUG] - 📁 Cache file valid: 51c48e5d1d70bfe79193b22e30987da9.json (186min remaining)
2025-07-12T06:38:02.070Z [DEBUG] - 📁 Cache loaded from file: 51c48e5d1d70bfe79193b22e30987da9.json (key: WCAG-041:053b13d2:add92319...)
2025-07-12T06:38:02.071Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-041:053b13d2:add92319...
2025-07-12T06:38:02.072Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:053b13d2:add92319... (accessed 2 times, age: 3219s)
2025-07-12T06:38:02.073Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-041
2025-07-12T06:38:02.074Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:WCAG-041... (accessed 3 times, age: 3226s)
2025-07-12T06:38:02.074Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-12T06:38:02.075Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-041 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:02.077Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:02.081Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:02.280Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:02.280Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-041 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":205}
2025-07-12T06:38:02.282Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:02.284Z [DEBUG] - 🔧 Utility performance recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:02.285Z [DEBUG] - 🔧 Utility analysis completed for WCAG-041: - {"utilitiesUsed":2,"errors":0,"executionTime":217}
2025-07-12T06:38:02.286Z [DEBUG] - ⏱️ Check WCAG-041 completed in 221ms (success: true)
2025-07-12T06:38:02.287Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.291Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.292Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 45% (30/66)
2025-07-12T06:38:02.292Z [INFO] - ✅ Rule WCAG-041 completed: failed (0/100)
2025-07-12T06:38:02.293Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.298Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.300Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 45% (30/66)
2025-07-12T06:38:02.302Z [INFO] - 🔍 Executing rule: Text Spacing (WCAG-042)
2025-07-12T06:38:02.303Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-042: Text Spacing
2025-07-12T06:38:02.322Z [DEBUG] - 📁 Cache file valid: a6023bea83e9245d90991fcca6d694c6.json (186min remaining)
2025-07-12T06:38:02.322Z [DEBUG] - 📁 Cache loaded from file: a6023bea83e9245d90991fcca6d694c6.json (key: WCAG-042:053b13d2:add92319...)
2025-07-12T06:38:02.323Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-042:053b13d2:add92319...
2025-07-12T06:38:02.324Z [DEBUG] - ✅ Cache hit: rule:WCAG-042:053b13d2:add92319... (accessed 2 times, age: 3218s)
2025-07-12T06:38:02.324Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-042
2025-07-12T06:38:02.337Z [DEBUG] - 📁 Cache file valid: 153cca37beb6d4b5378b7047a7af55f3.json (186min remaining)
2025-07-12T06:38:02.337Z [DEBUG] - 📁 Cache loaded from file: 153cca37beb6d4b5378b7047a7af55f3.json (key: WCAG-042:WCAG-042...)
2025-07-12T06:38:02.338Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-042:WCAG-042...
2025-07-12T06:38:02.339Z [DEBUG] - ✅ Cache hit: rule:WCAG-042:WCAG-042... (accessed 2 times, age: 3218s)
2025-07-12T06:38:02.339Z [DEBUG] - 📋 Using cached evidence for WCAG-042
2025-07-12T06:38:02.340Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-042 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:02.341Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:02.342Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:02.345Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:02.552Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:02.552Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-042 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":212}
2025-07-12T06:38:02.554Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:02.555Z [DEBUG] - 🔧 Utility performance recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:02.557Z [DEBUG] - 🔧 Utility analysis completed for WCAG-042: - {"utilitiesUsed":3,"errors":0,"executionTime":252}
2025-07-12T06:38:02.558Z [DEBUG] - ⏱️ Check WCAG-042 completed in 256ms (success: true)
2025-07-12T06:38:02.559Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.566Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.566Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 47% (31/66)
2025-07-12T06:38:02.567Z [INFO] - ✅ Rule WCAG-042 completed: failed (0/100)
2025-07-12T06:38:02.568Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.576Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.577Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 47% (31/66)
2025-07-12T06:38:02.578Z [INFO] - 🔍 Executing rule: Content on Hover or Focus (WCAG-043)
2025-07-12T06:38:02.580Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-043: Content on Hover or Focus
2025-07-12T06:38:02.584Z [DEBUG] - 📁 Cache file valid: 5214d88911d20698ac817297c9833426.json (186min remaining)
2025-07-12T06:38:02.585Z [DEBUG] - 📁 Cache loaded from file: 5214d88911d20698ac817297c9833426.json (key: WCAG-043:053b13d2:add92319...)
2025-07-12T06:38:02.587Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-043:053b13d2:add92319...
2025-07-12T06:38:02.588Z [DEBUG] - ✅ Cache hit: rule:WCAG-043:053b13d2:add92319... (accessed 2 times, age: 3216s)
2025-07-12T06:38:02.588Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-043
2025-07-12T06:38:02.593Z [DEBUG] - 📁 Cache file valid: 1b5418cf3c968be17db0f146897c9c17.json (186min remaining)
2025-07-12T06:38:02.593Z [DEBUG] - 📁 Cache loaded from file: 1b5418cf3c968be17db0f146897c9c17.json (key: WCAG-043:WCAG-043...)
2025-07-12T06:38:02.595Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-043:WCAG-043...
2025-07-12T06:38:02.596Z [DEBUG] - ✅ Cache hit: rule:WCAG-043:WCAG-043... (accessed 2 times, age: 3216s)
2025-07-12T06:38:02.597Z [DEBUG] - 📋 Using cached evidence for WCAG-043
2025-07-12T06:38:02.597Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-043 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:02.598Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:02.600Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:02.604Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:02.812Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:02.812Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-043 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":215}
2025-07-12T06:38:02.814Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:02.816Z [DEBUG] - 🔧 Utility performance recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:02.817Z [DEBUG] - 🔧 Utility analysis completed for WCAG-043: - {"utilitiesUsed":3,"errors":0,"executionTime":236}
2025-07-12T06:38:02.818Z [DEBUG] - ⏱️ Check WCAG-043 completed in 240ms (success: true)
2025-07-12T06:38:02.818Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.823Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.823Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 48% (32/66)
2025-07-12T06:38:02.824Z [INFO] - ✅ Rule WCAG-043 completed: failed (0/100)
2025-07-12T06:38:02.825Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.835Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:02.835Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 48% (32/66)
2025-07-12T06:38:02.836Z [INFO] - 🔍 Executing rule: Audio Control (WCAG-050)
2025-07-12T06:38:02.837Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-050: Audio Control
2025-07-12T06:38:02.839Z [DEBUG] - 📁 Cache file valid: 03a937c64b250f0e5f97bd540ecbe76c.json (186min remaining)
2025-07-12T06:38:02.839Z [DEBUG] - 📁 Cache loaded from file: 03a937c64b250f0e5f97bd540ecbe76c.json (key: WCAG-050:053b13d2:add92319...)
2025-07-12T06:38:02.841Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-050:053b13d2:add92319...
2025-07-12T06:38:02.843Z [DEBUG] - ✅ Cache hit: rule:WCAG-050:053b13d2:add92319... (accessed 2 times, age: 3216s)
2025-07-12T06:38:02.844Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-050
2025-07-12T06:38:02.854Z [DEBUG] - 📁 Cache file valid: 22c5971f253d1728b987c254eb8f0974.json (186min remaining)
2025-07-12T06:38:02.855Z [DEBUG] - 📁 Cache loaded from file: 22c5971f253d1728b987c254eb8f0974.json (key: WCAG-050:WCAG-050...)
2025-07-12T06:38:02.856Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-050:WCAG-050...
2025-07-12T06:38:02.858Z [DEBUG] - ✅ Cache hit: rule:WCAG-050:WCAG-050... (accessed 2 times, age: 3216s)
2025-07-12T06:38:02.860Z [DEBUG] - 📋 Using cached evidence for WCAG-050
2025-07-12T06:38:02.860Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-050 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:02.861Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:02.862Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:03.051Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:03.051Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-050 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":191}
2025-07-12T06:38:03.053Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:03.055Z [DEBUG] - 🔧 Utility performance recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:03.056Z [DEBUG] - 🔧 Utility analysis completed for WCAG-050: - {"utilitiesUsed":2,"errors":0,"executionTime":217}
2025-07-12T06:38:03.057Z [DEBUG] - ⏱️ Check WCAG-050 completed in 221ms (success: true)
2025-07-12T06:38:03.058Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.064Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.064Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 50% (33/66)
2025-07-12T06:38:03.065Z [INFO] - ✅ Rule WCAG-050 completed: passed (100/100)
2025-07-12T06:38:03.069Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.074Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.074Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 50% (33/66)
2025-07-12T06:38:03.075Z [INFO] - 🔍 Executing rule: Keyboard Accessible (WCAG-051)
2025-07-12T06:38:03.077Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-051: Keyboard Accessible
2025-07-12T06:38:03.080Z [DEBUG] - 📁 Cache file valid: 33a17a36ed1c6d42ea750fc0ef49cd03.json (186min remaining)
2025-07-12T06:38:03.080Z [DEBUG] - 📁 Cache loaded from file: 33a17a36ed1c6d42ea750fc0ef49cd03.json (key: WCAG-051:053b13d2:add92319...)
2025-07-12T06:38:03.081Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-051:053b13d2:add92319...
2025-07-12T06:38:03.082Z [DEBUG] - ✅ Cache hit: rule:WCAG-051:053b13d2:add92319... (accessed 2 times, age: 3214s)
2025-07-12T06:38:03.082Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-051
2025-07-12T06:38:03.086Z [DEBUG] - 📁 Cache file valid: 0782c8c437935165aea40991dcb4dc4a.json (186min remaining)
2025-07-12T06:38:03.086Z [DEBUG] - 📁 Cache loaded from file: 0782c8c437935165aea40991dcb4dc4a.json (key: WCAG-051:WCAG-051...)
2025-07-12T06:38:03.087Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-051:WCAG-051...
2025-07-12T06:38:03.088Z [DEBUG] - ✅ Cache hit: rule:WCAG-051:WCAG-051... (accessed 2 times, age: 3214s)
2025-07-12T06:38:03.088Z [DEBUG] - 📋 Using cached evidence for WCAG-051
2025-07-12T06:38:03.089Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-051 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:03.090Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:03.092Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:03.272Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:03.272Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-051 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":183}
2025-07-12T06:38:03.274Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:03.276Z [DEBUG] - 🔧 Utility performance recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:03.277Z [DEBUG] - 🔧 Utility analysis completed for WCAG-051: - {"utilitiesUsed":2,"errors":0,"executionTime":199}
2025-07-12T06:38:03.278Z [DEBUG] - ⏱️ Check WCAG-051 completed in 203ms (success: true)
2025-07-12T06:38:03.279Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.581Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.582Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 52% (34/66)
2025-07-12T06:38:03.585Z [INFO] - ✅ Rule WCAG-051 completed: failed (0/100)
2025-07-12T06:38:03.586Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.591Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.591Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 52% (34/66)
2025-07-12T06:38:03.592Z [INFO] - 🔍 Executing rule: Character Key Shortcuts (WCAG-052)
2025-07-12T06:38:03.593Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-052: Character Key Shortcuts
2025-07-12T06:38:03.597Z [DEBUG] - 📁 Cache file valid: 6cfd95a26006ae76535b7fc128712b8a.json (186min remaining)
2025-07-12T06:38:03.597Z [DEBUG] - 📁 Cache loaded from file: 6cfd95a26006ae76535b7fc128712b8a.json (key: WCAG-052:053b13d2:add92319...)
2025-07-12T06:38:03.598Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-052:053b13d2:add92319...
2025-07-12T06:38:03.602Z [DEBUG] - ✅ Cache hit: rule:WCAG-052:053b13d2:add92319... (accessed 2 times, age: 3213s)
2025-07-12T06:38:03.603Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-052
2025-07-12T06:38:03.606Z [DEBUG] - 📁 Cache file valid: c49e76409c1749df890ade80ef803b4f.json (186min remaining)
2025-07-12T06:38:03.606Z [DEBUG] - 📁 Cache loaded from file: c49e76409c1749df890ade80ef803b4f.json (key: WCAG-052:WCAG-052...)
2025-07-12T06:38:03.607Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-052:WCAG-052...
2025-07-12T06:38:03.608Z [DEBUG] - ✅ Cache hit: rule:WCAG-052:WCAG-052... (accessed 2 times, age: 3213s)
2025-07-12T06:38:03.610Z [DEBUG] - 📋 Using cached evidence for WCAG-052
2025-07-12T06:38:03.610Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-052 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:03.611Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:03.613Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:03.863Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:03.864Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-052 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":254}
2025-07-12T06:38:03.866Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:03.868Z [DEBUG] - 🔧 Utility performance recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:03.868Z [DEBUG] - 🔧 Utility analysis completed for WCAG-052: - {"utilitiesUsed":2,"errors":0,"executionTime":273}
2025-07-12T06:38:03.869Z [DEBUG] - ⏱️ Check WCAG-052 completed in 277ms (success: true)
2025-07-12T06:38:03.870Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.876Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.876Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 53% (35/66)
2025-07-12T06:38:03.877Z [INFO] - ✅ Rule WCAG-052 completed: passed (75/100)
2025-07-12T06:38:03.878Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.881Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:03.882Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 53% (35/66)
2025-07-12T06:38:03.883Z [INFO] - 🔍 Executing rule: Pointer Gestures (WCAG-053)
2025-07-12T06:38:03.884Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-053: Pointer Gestures
2025-07-12T06:38:03.890Z [DEBUG] - 📁 Cache file valid: 18f9e9e7dfa6a0644a8fbb82748d1d26.json (186min remaining)
2025-07-12T06:38:03.890Z [DEBUG] - 📁 Cache loaded from file: 18f9e9e7dfa6a0644a8fbb82748d1d26.json (key: WCAG-053:053b13d2:add92319...)
2025-07-12T06:38:03.891Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-053:053b13d2:add92319...
2025-07-12T06:38:03.892Z [DEBUG] - ✅ Cache hit: rule:WCAG-053:053b13d2:add92319... (accessed 2 times, age: 3213s)
2025-07-12T06:38:03.893Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-053
2025-07-12T06:38:03.896Z [DEBUG] - 📁 Cache file valid: 12cde2393923c93e264e54e16b815352.json (186min remaining)
2025-07-12T06:38:03.896Z [DEBUG] - 📁 Cache loaded from file: 12cde2393923c93e264e54e16b815352.json (key: WCAG-049:WCAG-049...)
2025-07-12T06:38:03.897Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-049:WCAG-049...
2025-07-12T06:38:03.898Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:WCAG-049... (accessed 2 times, age: 3213s)
2025-07-12T06:38:03.902Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-12T06:38:03.903Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-053 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:03.903Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:03.905Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:04.129Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:04.129Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-053 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":226}
2025-07-12T06:38:04.131Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:04.134Z [DEBUG] - 🔧 Utility performance recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:04.136Z [DEBUG] - 🔧 Utility analysis completed for WCAG-053: - {"utilitiesUsed":2,"errors":0,"executionTime":248}
2025-07-12T06:38:04.136Z [DEBUG] - ⏱️ Check WCAG-053 completed in 253ms (success: true)
2025-07-12T06:38:04.137Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.142Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.142Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 55% (36/66)
2025-07-12T06:38:04.143Z [INFO] - ✅ Rule WCAG-053 completed: passed (100/100)
2025-07-12T06:38:04.144Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.150Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.150Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 55% (36/66)
2025-07-12T06:38:04.151Z [INFO] - 🔍 Executing rule: Pointer Cancellation (WCAG-054)
2025-07-12T06:38:04.153Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-054: Pointer Cancellation
2025-07-12T06:38:04.155Z [DEBUG] - 📁 Cache file valid: c98dc2b02e1b577a00e483906e8ca192.json (186min remaining)
2025-07-12T06:38:04.155Z [DEBUG] - 📁 Cache loaded from file: c98dc2b02e1b577a00e483906e8ca192.json (key: WCAG-054:053b13d2:add92319...)
2025-07-12T06:38:04.156Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-054:053b13d2:add92319...
2025-07-12T06:38:04.157Z [DEBUG] - ✅ Cache hit: rule:WCAG-054:053b13d2:add92319... (accessed 2 times, age: 3212s)
2025-07-12T06:38:04.158Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-054
2025-07-12T06:38:04.161Z [DEBUG] - 📁 Cache file valid: d282ada86df9f29b48342af5f0aa8881.json (186min remaining)
2025-07-12T06:38:04.163Z [DEBUG] - 📁 Cache loaded from file: d282ada86df9f29b48342af5f0aa8881.json (key: WCAG-054:WCAG-054...)
2025-07-12T06:38:04.164Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-054:WCAG-054...
2025-07-12T06:38:04.166Z [DEBUG] - ✅ Cache hit: rule:WCAG-054:WCAG-054... (accessed 2 times, age: 3212s)
2025-07-12T06:38:04.167Z [DEBUG] - 📋 Using cached evidence for WCAG-054
2025-07-12T06:38:04.168Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-054 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:04.169Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:04.170Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:04.391Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:04.391Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-054 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":223}
2025-07-12T06:38:04.393Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:04.395Z [DEBUG] - 🔧 Utility performance recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:04.396Z [DEBUG] - 🔧 Utility analysis completed for WCAG-054: - {"utilitiesUsed":2,"errors":0,"executionTime":242}
2025-07-12T06:38:04.397Z [DEBUG] - ⏱️ Check WCAG-054 completed in 246ms (success: true)
2025-07-12T06:38:04.398Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.406Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.406Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 56% (37/66)
2025-07-12T06:38:04.407Z [INFO] - ✅ Rule WCAG-054 completed: passed (100/100)
2025-07-12T06:38:04.408Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.412Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.413Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 56% (37/66)
2025-07-12T06:38:04.414Z [INFO] - 🔍 Executing rule: Label in Name (WCAG-055)
2025-07-12T06:38:04.416Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-055: Label in Name
2025-07-12T06:38:04.418Z [DEBUG] - 📁 Cache file valid: a2f4f89f62b43b45ced080eefd45fdef.json (187min remaining)
2025-07-12T06:38:04.419Z [DEBUG] - 📁 Cache loaded from file: a2f4f89f62b43b45ced080eefd45fdef.json (key: WCAG-055:053b13d2:add92319...)
2025-07-12T06:38:04.420Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-055:053b13d2:add92319...
2025-07-12T06:38:04.420Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:053b13d2:add92319... (accessed 2 times, age: 3207s)
2025-07-12T06:38:04.421Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-055
2025-07-12T06:38:04.424Z [DEBUG] - 📁 Cache file valid: 5da1af541ddaa467b915848dbb0b3030.json (187min remaining)
2025-07-12T06:38:04.425Z [DEBUG] - 📁 Cache loaded from file: 5da1af541ddaa467b915848dbb0b3030.json (key: WCAG-055:WCAG-055...)
2025-07-12T06:38:04.426Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-055:WCAG-055...
2025-07-12T06:38:04.429Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:WCAG-055... (accessed 2 times, age: 3207s)
2025-07-12T06:38:04.431Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-12T06:38:04.432Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-055 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:04.433Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:04.434Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 10 times, age: 3291s)
2025-07-12T06:38:04.435Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:04.437Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:04.437Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:04.440Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:04.444Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:04.446Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:04.458Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:04.478Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:38:04.479Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-055 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":47}
2025-07-12T06:38:04.480Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:04.484Z [DEBUG] - 🔧 Utility performance recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:04.486Z [DEBUG] - 🔧 Utility analysis completed for WCAG-055: - {"utilitiesUsed":2,"errors":0,"executionTime":66}
2025-07-12T06:38:04.489Z [DEBUG] - ⏱️ Check WCAG-055 completed in 75ms (success: true)
2025-07-12T06:38:04.492Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.496Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.496Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 58% (38/66)
2025-07-12T06:38:04.497Z [INFO] - ✅ Rule WCAG-055 completed: failed (0/100)
2025-07-12T06:38:04.498Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.503Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.504Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 58% (38/66)
2025-07-12T06:38:04.505Z [INFO] - 🔍 Executing rule: Motion Actuation (WCAG-056)
2025-07-12T06:38:04.506Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-056: Motion Actuation
2025-07-12T06:38:04.508Z [DEBUG] - 📁 Cache file valid: 4871a48558d3f2cd5e11bf922bb8c891.json (187min remaining)
2025-07-12T06:38:04.509Z [DEBUG] - 📁 Cache loaded from file: 4871a48558d3f2cd5e11bf922bb8c891.json (key: WCAG-056:053b13d2:add92319...)
2025-07-12T06:38:04.510Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-056:053b13d2:add92319...
2025-07-12T06:38:04.511Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:053b13d2:add92319... (accessed 2 times, age: 3207s)
2025-07-12T06:38:04.512Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-056
2025-07-12T06:38:04.520Z [DEBUG] - 📁 Cache file valid: 8ff67000f1a92dc4301713b6e619f281.json (187min remaining)
2025-07-12T06:38:04.520Z [DEBUG] - 📁 Cache loaded from file: 8ff67000f1a92dc4301713b6e619f281.json (key: WCAG-056:WCAG-056...)
2025-07-12T06:38:04.521Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-056:WCAG-056...
2025-07-12T06:38:04.522Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:WCAG-056... (accessed 2 times, age: 3207s)
2025-07-12T06:38:04.524Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-12T06:38:04.525Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-056 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:04.526Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:04.527Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:04.799Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:04.799Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-056 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":275}
2025-07-12T06:38:04.802Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:04.804Z [DEBUG] - 🔧 Utility performance recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:04.804Z [DEBUG] - 🔧 Utility analysis completed for WCAG-056: - {"utilitiesUsed":2,"errors":0,"executionTime":296}
2025-07-12T06:38:04.805Z [DEBUG] - ⏱️ Check WCAG-056 completed in 300ms (success: true)
2025-07-12T06:38:04.806Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.812Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.812Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 59% (39/66)
2025-07-12T06:38:04.814Z [INFO] - ✅ Rule WCAG-056 completed: passed (100/100)
2025-07-12T06:38:04.815Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.821Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:04.821Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 59% (39/66)
2025-07-12T06:38:04.821Z [INFO] - 🔍 Executing rule: Target Size Enhanced (WCAG-058)
2025-07-12T06:38:04.823Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting WCAG-058: Target Size Enhanced
2025-07-12T06:38:04.829Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-12T06:38:05.206Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T06:38:05.206Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T06:38:05.753Z [WARN] - ⚠️ [8b80d206-4a60-40bd-9825-753e25410d09] WCAG-058 failed: 0.0% (threshold: 75%) - FAILED
2025-07-12T06:38:05.753Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Completed WCAG-058 in 930ms - Status: failed (0/100)
2025-07-12T06:38:05.755Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:WCAG-055... (accessed 3 times, age: 3209s)
2025-07-12T06:38:05.757Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-12T06:38:05.758Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-058 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:05.759Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:05.761Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:05.961Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:05.961Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-058 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":203}
2025-07-12T06:38:05.963Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:05.965Z [DEBUG] - 🔧 Utility performance recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:05.966Z [DEBUG] - 🔧 Utility analysis completed for WCAG-058: - {"utilitiesUsed":2,"errors":0,"executionTime":1142}
2025-07-12T06:38:05.967Z [DEBUG] - ⏱️ Check WCAG-058 completed in 1146ms (success: true)
2025-07-12T06:38:05.968Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:05.975Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:05.975Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 61% (40/66)
2025-07-12T06:38:05.976Z [INFO] - ✅ Rule WCAG-058 completed: failed (0/100)
2025-07-12T06:38:05.977Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:05.982Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:05.982Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 61% (40/66)
2025-07-12T06:38:05.983Z [INFO] - 🔍 Executing rule: Concurrent Input Mechanisms (WCAG-059)
2025-07-12T06:38:05.984Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-059: Concurrent Input Mechanisms
2025-07-12T06:38:05.987Z [DEBUG] - 📁 Cache file valid: de67f57cc2635b0704bae41ccf557edd.json (187min remaining)
2025-07-12T06:38:05.988Z [DEBUG] - 📁 Cache loaded from file: de67f57cc2635b0704bae41ccf557edd.json (key: WCAG-059:053b13d2:add92319...)
2025-07-12T06:38:05.989Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-059:053b13d2:add92319...
2025-07-12T06:38:05.990Z [DEBUG] - ✅ Cache hit: rule:WCAG-059:053b13d2:add92319... (accessed 2 times, age: 3205s)
2025-07-12T06:38:05.991Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-059
2025-07-12T06:38:05.994Z [DEBUG] - 📁 Cache file valid: c40334838e5426b6fddc1e74ce9760a1.json (187min remaining)
2025-07-12T06:38:05.994Z [DEBUG] - 📁 Cache loaded from file: c40334838e5426b6fddc1e74ce9760a1.json (key: WCAG-059:WCAG-059...)
2025-07-12T06:38:05.995Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-059:WCAG-059...
2025-07-12T06:38:05.996Z [DEBUG] - ✅ Cache hit: rule:WCAG-059:WCAG-059... (accessed 2 times, age: 3205s)
2025-07-12T06:38:05.997Z [DEBUG] - 📋 Using cached evidence for WCAG-059
2025-07-12T06:38:05.998Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-059 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:05.999Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:06.002Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:06.045Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-059 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.45,"executionTime":47}
2025-07-12T06:38:06.046Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.048Z [DEBUG] - 🔧 Utility performance recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.050Z [DEBUG] - 🔧 Utility analysis completed for WCAG-059: - {"utilitiesUsed":2,"errors":0,"executionTime":63}
2025-07-12T06:38:06.051Z [DEBUG] - ⏱️ Check WCAG-059 completed in 68ms (success: true)
2025-07-12T06:38:06.052Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.059Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.059Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 62% (41/66)
2025-07-12T06:38:06.060Z [INFO] - ✅ Rule WCAG-059 completed: failed (0/100)
2025-07-12T06:38:06.061Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.066Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.067Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 62% (41/66)
2025-07-12T06:38:06.068Z [INFO] - 🔍 Executing rule: Unusual Words (WCAG-060)
2025-07-12T06:38:06.069Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-060: Unusual Words
2025-07-12T06:38:06.072Z [DEBUG] - 📁 Cache file valid: f33dbf5b167f7819cd8f5243d29a7d8d.json (187min remaining)
2025-07-12T06:38:06.072Z [DEBUG] - 📁 Cache loaded from file: f33dbf5b167f7819cd8f5243d29a7d8d.json (key: WCAG-060:053b13d2:add92319...)
2025-07-12T06:38:06.073Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-060:053b13d2:add92319...
2025-07-12T06:38:06.074Z [DEBUG] - ✅ Cache hit: rule:WCAG-060:053b13d2:add92319... (accessed 2 times, age: 3205s)
2025-07-12T06:38:06.075Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-060
2025-07-12T06:38:06.078Z [DEBUG] - 📁 Cache file valid: 6e0c6281873e8247f3f140619a454c39.json (187min remaining)
2025-07-12T06:38:06.078Z [DEBUG] - 📁 Cache loaded from file: 6e0c6281873e8247f3f140619a454c39.json (key: WCAG-060:WCAG-060...)
2025-07-12T06:38:06.079Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-060:WCAG-060...
2025-07-12T06:38:06.083Z [DEBUG] - ✅ Cache hit: rule:WCAG-060:WCAG-060... (accessed 2 times, age: 3205s)
2025-07-12T06:38:06.084Z [DEBUG] - 📋 Using cached evidence for WCAG-060
2025-07-12T06:38:06.085Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-060 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:06.086Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:06.087Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 11 times, age: 3293s)
2025-07-12T06:38:06.088Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:06.089Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:06.089Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:06.091Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:06.092Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:06.093Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:06.123Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:06.142Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:06.144Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-060 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":59}
2025-07-12T06:38:06.145Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.146Z [DEBUG] - 🔧 Utility performance recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.146Z [DEBUG] - 🔧 Utility analysis completed for WCAG-060: - {"utilitiesUsed":2,"errors":0,"executionTime":77}
2025-07-12T06:38:06.147Z [DEBUG] - ⏱️ Check WCAG-060 completed in 79ms (success: true)
2025-07-12T06:38:06.149Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.157Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.157Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 64% (42/66)
2025-07-12T06:38:06.158Z [INFO] - ✅ Rule WCAG-060 completed: failed (0/100)
2025-07-12T06:38:06.160Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.164Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.165Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 64% (42/66)
2025-07-12T06:38:06.166Z [INFO] - 🔍 Executing rule: Abbreviations (WCAG-061)
2025-07-12T06:38:06.168Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-061: Abbreviations
2025-07-12T06:38:06.171Z [DEBUG] - 📁 Cache file valid: 79b5cd7a70a41bc232e7bb4b6db82dfa.json (187min remaining)
2025-07-12T06:38:06.171Z [DEBUG] - 📁 Cache loaded from file: 79b5cd7a70a41bc232e7bb4b6db82dfa.json (key: WCAG-061:053b13d2:add92319...)
2025-07-12T06:38:06.172Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-061:053b13d2:add92319...
2025-07-12T06:38:06.173Z [DEBUG] - ✅ Cache hit: rule:WCAG-061:053b13d2:add92319... (accessed 2 times, age: 3193s)
2025-07-12T06:38:06.173Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-061
2025-07-12T06:38:06.176Z [DEBUG] - 📁 Cache file valid: 444acc81fee9b5e62cda4ea16fdaf9dc.json (187min remaining)
2025-07-12T06:38:06.176Z [DEBUG] - 📁 Cache loaded from file: 444acc81fee9b5e62cda4ea16fdaf9dc.json (key: WCAG-061:WCAG-061...)
2025-07-12T06:38:06.177Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-061:WCAG-061...
2025-07-12T06:38:06.178Z [DEBUG] - ✅ Cache hit: rule:WCAG-061:WCAG-061... (accessed 2 times, age: 3193s)
2025-07-12T06:38:06.179Z [DEBUG] - 📋 Using cached evidence for WCAG-061
2025-07-12T06:38:06.179Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-061 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:06.180Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:06.181Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 12 times, age: 3293s)
2025-07-12T06:38:06.185Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:06.187Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:06.187Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:06.188Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:06.189Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:06.190Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:06.213Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:06.235Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:06.237Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-061 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":58}
2025-07-12T06:38:06.237Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.238Z [DEBUG] - 🔧 Utility performance recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.239Z [DEBUG] - 🔧 Utility analysis completed for WCAG-061: - {"utilitiesUsed":2,"errors":0,"executionTime":71}
2025-07-12T06:38:06.240Z [DEBUG] - ⏱️ Check WCAG-061 completed in 74ms (success: true)
2025-07-12T06:38:06.241Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.246Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.246Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 65% (43/66)
2025-07-12T06:38:06.247Z [INFO] - ✅ Rule WCAG-061 completed: failed (0/100)
2025-07-12T06:38:06.250Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.254Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.254Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 65% (43/66)
2025-07-12T06:38:06.255Z [INFO] - 🔍 Executing rule: Reading Level (WCAG-062)
2025-07-12T06:38:06.256Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-062: Reading Level
2025-07-12T06:38:06.259Z [DEBUG] - 📁 Cache file valid: 42dc7144c5be663ce950fd291e4d98d7.json (187min remaining)
2025-07-12T06:38:06.260Z [DEBUG] - 📁 Cache loaded from file: 42dc7144c5be663ce950fd291e4d98d7.json (key: WCAG-062:053b13d2:add92319...)
2025-07-12T06:38:06.260Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-062:053b13d2:add92319...
2025-07-12T06:38:06.262Z [DEBUG] - ✅ Cache hit: rule:WCAG-062:053b13d2:add92319... (accessed 2 times, age: 3193s)
2025-07-12T06:38:06.263Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-062
2025-07-12T06:38:06.266Z [DEBUG] - 📁 Cache file valid: 55656632f66aff78067600822a1d12fd.json (187min remaining)
2025-07-12T06:38:06.266Z [DEBUG] - 📁 Cache loaded from file: 55656632f66aff78067600822a1d12fd.json (key: WCAG-062:WCAG-062...)
2025-07-12T06:38:06.267Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-062:WCAG-062...
2025-07-12T06:38:06.267Z [DEBUG] - ✅ Cache hit: rule:WCAG-062:WCAG-062... (accessed 2 times, age: 3193s)
2025-07-12T06:38:06.268Z [DEBUG] - 📋 Using cached evidence for WCAG-062
2025-07-12T06:38:06.269Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-062 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:06.269Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:06.270Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 13 times, age: 3293s)
2025-07-12T06:38:06.273Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:06.275Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:06.275Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:06.277Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:06.278Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:06.278Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:06.302Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:06.322Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:06.324Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-062 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":55}
2025-07-12T06:38:06.324Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.325Z [DEBUG] - 🔧 Utility performance recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.326Z [DEBUG] - 🔧 Utility analysis completed for WCAG-062: - {"utilitiesUsed":2,"errors":0,"executionTime":69}
2025-07-12T06:38:06.328Z [DEBUG] - ⏱️ Check WCAG-062 completed in 73ms (success: true)
2025-07-12T06:38:06.330Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.338Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.338Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 67% (44/66)
2025-07-12T06:38:06.339Z [INFO] - ✅ Rule WCAG-062 completed: failed (0/100)
2025-07-12T06:38:06.339Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.345Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.345Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 67% (44/66)
2025-07-12T06:38:06.346Z [INFO] - 🔍 Executing rule: Pronunciation (WCAG-063)
2025-07-12T06:38:06.348Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-063: Pronunciation
2025-07-12T06:38:06.351Z [DEBUG] - 📁 Cache file valid: 0c14c6b55c802116f5cf12801e8a61cb.json (190min remaining)
2025-07-12T06:38:06.351Z [DEBUG] - 📁 Cache loaded from file: 0c14c6b55c802116f5cf12801e8a61cb.json (key: WCAG-063:053b13d2:add92319...)
2025-07-12T06:38:06.352Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-063:053b13d2:add92319...
2025-07-12T06:38:06.353Z [DEBUG] - ✅ Cache hit: rule:WCAG-063:053b13d2:add92319... (accessed 2 times, age: 3020s)
2025-07-12T06:38:06.353Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-063
2025-07-12T06:38:06.356Z [DEBUG] - 📁 Cache file valid: 39507b86200ef80a538f4fcd9d085cfb.json (190min remaining)
2025-07-12T06:38:06.358Z [DEBUG] - 📁 Cache loaded from file: 39507b86200ef80a538f4fcd9d085cfb.json (key: WCAG-063:WCAG-063...)
2025-07-12T06:38:06.359Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-063:WCAG-063...
2025-07-12T06:38:06.360Z [DEBUG] - ✅ Cache hit: rule:WCAG-063:WCAG-063... (accessed 2 times, age: 3020s)
2025-07-12T06:38:06.361Z [DEBUG] - 📋 Using cached evidence for WCAG-063
2025-07-12T06:38:06.362Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-063 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:06.363Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:06.364Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 14 times, age: 3293s)
2025-07-12T06:38:06.364Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:06.366Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:06.366Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:06.368Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:06.369Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:06.369Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:06.396Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:06.415Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:06.417Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-063 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":55}
2025-07-12T06:38:06.419Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.420Z [DEBUG] - 🔧 Utility performance recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.422Z [DEBUG] - 🔧 Utility analysis completed for WCAG-063: - {"utilitiesUsed":2,"errors":0,"executionTime":73}
2025-07-12T06:38:06.423Z [DEBUG] - ⏱️ Check WCAG-063 completed in 77ms (success: true)
2025-07-12T06:38:06.424Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.430Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.430Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 68% (45/66)
2025-07-12T06:38:06.432Z [INFO] - ✅ Rule WCAG-063 completed: failed (0/100)
2025-07-12T06:38:06.433Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.441Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.442Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 68% (45/66)
2025-07-12T06:38:06.442Z [INFO] - 🔍 Executing rule: Change on Request (WCAG-064)
2025-07-12T06:38:06.444Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-064: Change on Request
2025-07-12T06:38:06.446Z [DEBUG] - 📁 Cache file valid: 5a40d51c15a824f433f94fee6332db5e.json (190min remaining)
2025-07-12T06:38:06.447Z [DEBUG] - 📁 Cache loaded from file: 5a40d51c15a824f433f94fee6332db5e.json (key: WCAG-064:053b13d2:add92319...)
2025-07-12T06:38:06.450Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-064:053b13d2:add92319...
2025-07-12T06:38:06.451Z [DEBUG] - ✅ Cache hit: rule:WCAG-064:053b13d2:add92319... (accessed 2 times, age: 3019s)
2025-07-12T06:38:06.452Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-064
2025-07-12T06:38:06.454Z [DEBUG] - 📁 Cache file valid: bd402f34a3490413e5e9d577c7f15e8f.json (190min remaining)
2025-07-12T06:38:06.454Z [DEBUG] - 📁 Cache loaded from file: bd402f34a3490413e5e9d577c7f15e8f.json (key: WCAG-064:WCAG-064...)
2025-07-12T06:38:06.455Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-064:WCAG-064...
2025-07-12T06:38:06.456Z [DEBUG] - ✅ Cache hit: rule:WCAG-064:WCAG-064... (accessed 2 times, age: 3019s)
2025-07-12T06:38:06.457Z [DEBUG] - 📋 Using cached evidence for WCAG-064
2025-07-12T06:38:06.457Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-064 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:06.458Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:06.460Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:06.646Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:06.646Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-064 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":189}
2025-07-12T06:38:06.648Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.649Z [DEBUG] - 🔧 Utility performance recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.651Z [DEBUG] - 🔧 Utility analysis completed for WCAG-064: - {"utilitiesUsed":2,"errors":0,"executionTime":206}
2025-07-12T06:38:06.652Z [DEBUG] - ⏱️ Check WCAG-064 completed in 210ms (success: true)
2025-07-12T06:38:06.653Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.659Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.659Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 70% (46/66)
2025-07-12T06:38:06.660Z [INFO] - ✅ Rule WCAG-064 completed: failed (0/100)
2025-07-12T06:38:06.661Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.666Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.667Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 70% (46/66)
2025-07-12T06:38:06.668Z [INFO] - 🔍 Executing rule: Help (WCAG-065)
2025-07-12T06:38:06.669Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-065: Help
2025-07-12T06:38:06.670Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:053b13d2:add92319... (accessed 3 times, age: 3249s)
2025-07-12T06:38:06.671Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-065
2025-07-12T06:38:06.671Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:WCAG-065... (accessed 3 times, age: 3249s)
2025-07-12T06:38:06.672Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-12T06:38:06.673Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-065 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:06.674Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:06.675Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:38:06.675Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 4 times, age: 12s)
2025-07-12T06:38:06.677Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:38:06.715Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:06.715Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-065 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":42}
2025-07-12T06:38:06.717Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-065: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.719Z [DEBUG] - 🔧 Utility performance recorded for WCAG-065: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.720Z [DEBUG] - 🔧 Utility analysis completed for WCAG-065: - {"utilitiesUsed":2,"errors":0,"executionTime":49}
2025-07-12T06:38:06.721Z [DEBUG] - ⏱️ Check WCAG-065 completed in 53ms (success: true)
2025-07-12T06:38:06.722Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.727Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.727Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 71% (47/66)
2025-07-12T06:38:06.728Z [INFO] - ✅ Rule WCAG-065 completed: passed (75/100)
2025-07-12T06:38:06.729Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.735Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.735Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 71% (47/66)
2025-07-12T06:38:06.736Z [INFO] - 🔍 Executing rule: Error Prevention Enhanced (WCAG-066)
2025-07-12T06:38:06.737Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-066: Error Prevention Enhanced
2025-07-12T06:38:06.742Z [DEBUG] - 📁 Cache file valid: 42a6a4719458cd8c2297ef4ce76e8acd.json (190min remaining)
2025-07-12T06:38:06.743Z [DEBUG] - 📁 Cache loaded from file: 42a6a4719458cd8c2297ef4ce76e8acd.json (key: WCAG-066:053b13d2:add92319...)
2025-07-12T06:38:06.744Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-066:053b13d2:add92319...
2025-07-12T06:38:06.745Z [DEBUG] - ✅ Cache hit: rule:WCAG-066:053b13d2:add92319... (accessed 2 times, age: 3019s)
2025-07-12T06:38:06.746Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-066
2025-07-12T06:38:06.749Z [DEBUG] - 📁 Cache file valid: d9087b62feeb8ebe1c97494a35155993.json (190min remaining)
2025-07-12T06:38:06.749Z [DEBUG] - 📁 Cache loaded from file: d9087b62feeb8ebe1c97494a35155993.json (key: WCAG-066:WCAG-066...)
2025-07-12T06:38:06.751Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-066:WCAG-066...
2025-07-12T06:38:06.752Z [DEBUG] - ✅ Cache hit: rule:WCAG-066:WCAG-066... (accessed 2 times, age: 3019s)
2025-07-12T06:38:06.752Z [DEBUG] - 📋 Using cached evidence for WCAG-066
2025-07-12T06:38:06.755Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-066 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:06.756Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:06.759Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:06.968Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:06.969Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-066 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":214}
2025-07-12T06:38:06.971Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:06.973Z [DEBUG] - 🔧 Utility performance recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:06.974Z [DEBUG] - 🔧 Utility analysis completed for WCAG-066: - {"utilitiesUsed":2,"errors":0,"executionTime":235}
2025-07-12T06:38:06.974Z [DEBUG] - ⏱️ Check WCAG-066 completed in 238ms (success: true)
2025-07-12T06:38:06.975Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.984Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.984Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 73% (48/66)
2025-07-12T06:38:06.985Z [INFO] - ✅ Rule WCAG-066 completed: failed (0/100)
2025-07-12T06:38:06.986Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.991Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:06.991Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 73% (48/66)
2025-07-12T06:38:06.992Z [INFO] - 🔍 Executing rule: Status Messages (WCAG-057)
2025-07-12T06:38:06.993Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-057: Status Messages
2025-07-12T06:38:06.997Z [DEBUG] - 📁 Cache file valid: 45d26f3295717a92f8bd1aec0a3c8557.json (190min remaining)
2025-07-12T06:38:06.998Z [DEBUG] - 📁 Cache loaded from file: 45d26f3295717a92f8bd1aec0a3c8557.json (key: WCAG-057:053b13d2:add92319...)
2025-07-12T06:38:06.999Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-057:053b13d2:add92319...
2025-07-12T06:38:07.000Z [DEBUG] - ✅ Cache hit: rule:WCAG-057:053b13d2:add92319... (accessed 2 times, age: 3018s)
2025-07-12T06:38:07.001Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-057
2025-07-12T06:38:07.004Z [DEBUG] - 📁 Cache file valid: d5679298ae7d049fc06accf18537cdf9.json (190min remaining)
2025-07-12T06:38:07.004Z [DEBUG] - 📁 Cache loaded from file: d5679298ae7d049fc06accf18537cdf9.json (key: WCAG-057:WCAG-057...)
2025-07-12T06:38:07.005Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-057:WCAG-057...
2025-07-12T06:38:07.006Z [DEBUG] - ✅ Cache hit: rule:WCAG-057:WCAG-057... (accessed 2 times, age: 3018s)
2025-07-12T06:38:07.007Z [DEBUG] - 📋 Using cached evidence for WCAG-057
2025-07-12T06:38:07.008Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-057 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:07.009Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:07.010Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 15 times, age: 3294s)
2025-07-12T06:38:07.014Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:07.016Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:07.018Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:07.018Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:07.020Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:07.021Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:07.022Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:07.038Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:07.062Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:38:07.063Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-057 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":55}
2025-07-12T06:38:07.066Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:07.068Z [DEBUG] - 🔧 Utility performance recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:07.071Z [DEBUG] - 🔧 Utility analysis completed for WCAG-057: - {"utilitiesUsed":3,"errors":0,"executionTime":74}
2025-07-12T06:38:07.072Z [DEBUG] - ⏱️ Check WCAG-057 completed in 80ms (success: true)
2025-07-12T06:38:07.073Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.084Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.084Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 74% (49/66)
2025-07-12T06:38:07.085Z [INFO] - ✅ Rule WCAG-057 completed: passed (100/100)
2025-07-12T06:38:07.086Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.092Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.093Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 74% (49/66)
2025-07-12T06:38:07.093Z [INFO] - 🔍 Executing rule: Skip Links (WCAG-047)
2025-07-12T06:38:07.096Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-047: Skip Links
2025-07-12T06:38:07.099Z [DEBUG] - 📁 Cache file valid: 5003fdcdca0d68384074f375ad9bbced.json (190min remaining)
2025-07-12T06:38:07.099Z [DEBUG] - 📁 Cache loaded from file: 5003fdcdca0d68384074f375ad9bbced.json (key: WCAG-047:053b13d2:add92319...)
2025-07-12T06:38:07.100Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-047:053b13d2:add92319...
2025-07-12T06:38:07.101Z [DEBUG] - ✅ Cache hit: rule:WCAG-047:053b13d2:add92319... (accessed 2 times, age: 3016s)
2025-07-12T06:38:07.101Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-047
2025-07-12T06:38:07.104Z [DEBUG] - 📁 Cache file valid: 306a6611ec109dccc0c3cc13f1967422.json (190min remaining)
2025-07-12T06:38:07.104Z [DEBUG] - 📁 Cache loaded from file: 306a6611ec109dccc0c3cc13f1967422.json (key: WCAG-047:WCAG-047...)
2025-07-12T06:38:07.107Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-047:WCAG-047...
2025-07-12T06:38:07.110Z [DEBUG] - ✅ Cache hit: rule:WCAG-047:WCAG-047... (accessed 2 times, age: 3016s)
2025-07-12T06:38:07.112Z [DEBUG] - 📋 Using cached evidence for WCAG-047
2025-07-12T06:38:07.113Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-047 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T06:38:07.114Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:07.116Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:07.117Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:07.318Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:07.318Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-047 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":205}
2025-07-12T06:38:07.320Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:07.322Z [DEBUG] - 🔧 Utility performance recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:07.323Z [DEBUG] - 🔧 Utility analysis completed for WCAG-047: - {"utilitiesUsed":3,"errors":0,"executionTime":227}
2025-07-12T06:38:07.323Z [DEBUG] - ⏱️ Check WCAG-047 completed in 230ms (success: true)
2025-07-12T06:38:07.324Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.331Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.331Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 76% (50/66)
2025-07-12T06:38:07.335Z [INFO] - ✅ Rule WCAG-047 completed: failed (0/100)
2025-07-12T06:38:07.335Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.342Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.342Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 76% (50/66)
2025-07-12T06:38:07.343Z [INFO] - 🔍 Executing rule: Enhanced Focus Management (WCAG-048)
2025-07-12T06:38:07.344Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-048: Enhanced Focus Management
2025-07-12T06:38:07.346Z [DEBUG] - 📁 Cache file valid: 49b79b6e25ca864115e6cd6beea15c0b.json (190min remaining)
2025-07-12T06:38:07.346Z [DEBUG] - 📁 Cache loaded from file: 49b79b6e25ca864115e6cd6beea15c0b.json (key: WCAG-048:053b13d2:add92319...)
2025-07-12T06:38:07.349Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-048:053b13d2:add92319...
2025-07-12T06:38:07.350Z [DEBUG] - ✅ Cache hit: rule:WCAG-048:053b13d2:add92319... (accessed 2 times, age: 3015s)
2025-07-12T06:38:07.352Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-048
2025-07-12T06:38:07.353Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:WCAG-056... (accessed 3 times, age: 3210s)
2025-07-12T06:38:07.354Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-12T06:38:07.354Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-048 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T06:38:07.355Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:07.357Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:07.358Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:07.552Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:07.553Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-048 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":199}
2025-07-12T06:38:07.554Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:07.557Z [DEBUG] - 🔧 Utility performance recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:07.557Z [DEBUG] - 🔧 Utility analysis completed for WCAG-048: - {"utilitiesUsed":3,"errors":0,"executionTime":211}
2025-07-12T06:38:07.558Z [DEBUG] - ⏱️ Check WCAG-048 completed in 215ms (success: true)
2025-07-12T06:38:07.559Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.571Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.571Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 77% (51/66)
2025-07-12T06:38:07.572Z [INFO] - ✅ Rule WCAG-048 completed: failed (0/100)
2025-07-12T06:38:07.575Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.586Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.586Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 77% (51/66)
2025-07-12T06:38:07.587Z [INFO] - 🔍 Executing rule: Link Context (WCAG-049)
2025-07-12T06:38:07.589Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-049: Link Context
2025-07-12T06:38:07.595Z [DEBUG] - 📁 Cache file valid: 008eb54f4d432fcc3f7258c8e293b10f.json (190min remaining)
2025-07-12T06:38:07.595Z [DEBUG] - 📁 Cache loaded from file: 008eb54f4d432fcc3f7258c8e293b10f.json (key: WCAG-049:053b13d2:add92319...)
2025-07-12T06:38:07.596Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-049:053b13d2:add92319...
2025-07-12T06:38:07.598Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:053b13d2:add92319... (accessed 2 times, age: 3015s)
2025-07-12T06:38:07.599Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-049
2025-07-12T06:38:07.600Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:WCAG-049... (accessed 3 times, age: 3217s)
2025-07-12T06:38:07.600Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-12T06:38:07.601Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-049 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T06:38:07.602Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:07.606Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:07.607Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:07.801Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:07.801Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-049 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":200}
2025-07-12T06:38:07.803Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:07.805Z [DEBUG] - 🔧 Utility performance recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:07.806Z [DEBUG] - 🔧 Utility analysis completed for WCAG-049: - {"utilitiesUsed":3,"errors":0,"executionTime":216}
2025-07-12T06:38:07.807Z [DEBUG] - ⏱️ Check WCAG-049 completed in 220ms (success: true)
2025-07-12T06:38:07.808Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.813Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.813Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 79% (52/66)
2025-07-12T06:38:07.814Z [INFO] - ✅ Rule WCAG-049 completed: failed (0/100)
2025-07-12T06:38:07.814Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.818Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:07.819Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 79% (52/66)
2025-07-12T06:38:07.820Z [INFO] - 🔍 Executing rule: Language of Page (WCAG-024)
2025-07-12T06:38:07.822Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-024: Language of Page
2025-07-12T06:38:07.824Z [DEBUG] - 📁 Cache file valid: 5c1e9d7eec6a203c208331b409edb500.json (190min remaining)
2025-07-12T06:38:07.824Z [DEBUG] - 📁 Cache loaded from file: 5c1e9d7eec6a203c208331b409edb500.json (key: WCAG-024:053b13d2:add92319...)
2025-07-12T06:38:07.825Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-024:053b13d2:add92319...
2025-07-12T06:38:07.826Z [DEBUG] - ✅ Cache hit: rule:WCAG-024:053b13d2:add92319... (accessed 2 times, age: 3014s)
2025-07-12T06:38:07.826Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-024
2025-07-12T06:38:07.831Z [DEBUG] - 📁 Cache file valid: cccac7bc70a2c5813d5114243f43e9fe.json (190min remaining)
2025-07-12T06:38:07.832Z [DEBUG] - 📁 Cache loaded from file: cccac7bc70a2c5813d5114243f43e9fe.json (key: WCAG-024:WCAG-024...)
2025-07-12T06:38:07.833Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-024:WCAG-024...
2025-07-12T06:38:07.833Z [DEBUG] - ✅ Cache hit: rule:WCAG-024:WCAG-024... (accessed 2 times, age: 3014s)
2025-07-12T06:38:07.834Z [DEBUG] - 📋 Using cached evidence for WCAG-024
2025-07-12T06:38:07.835Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-024 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:07.836Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:07.837Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:07.873Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:08.031Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:08.032Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-024 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":197}
2025-07-12T06:38:08.034Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.035Z [DEBUG] - 🔧 Utility performance recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.036Z [DEBUG] - 🔧 Utility analysis completed for WCAG-024: - {"utilitiesUsed":2,"errors":0,"executionTime":213}
2025-07-12T06:38:08.037Z [DEBUG] - ⏱️ Check WCAG-024 completed in 217ms (success: true)
2025-07-12T06:38:08.038Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.043Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.043Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 80% (53/66)
2025-07-12T06:38:08.044Z [INFO] - ✅ Rule WCAG-024 completed: passed (100/100)
2025-07-12T06:38:08.045Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.050Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.052Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 80% (53/66)
2025-07-12T06:38:08.053Z [INFO] - 🔍 Executing rule: Page Content Landmarks (WCAG-025)
2025-07-12T06:38:08.055Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-025: Landmarks
2025-07-12T06:38:08.057Z [DEBUG] - 📁 Cache file valid: 04790cccdd869ffab5f77a1751f4a4f7.json (190min remaining)
2025-07-12T06:38:08.057Z [DEBUG] - 📁 Cache loaded from file: 04790cccdd869ffab5f77a1751f4a4f7.json (key: WCAG-025:053b13d2:add92319...)
2025-07-12T06:38:08.058Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-025:053b13d2:add92319...
2025-07-12T06:38:08.059Z [DEBUG] - ✅ Cache hit: rule:WCAG-025:053b13d2:add92319... (accessed 2 times, age: 3011s)
2025-07-12T06:38:08.060Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-025
2025-07-12T06:38:08.062Z [DEBUG] - 📁 Cache file valid: b7395b0b8421574ba7f2e384a624c76e.json (190min remaining)
2025-07-12T06:38:08.063Z [DEBUG] - 📁 Cache loaded from file: b7395b0b8421574ba7f2e384a624c76e.json (key: WCAG-025:WCAG-025...)
2025-07-12T06:38:08.064Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-025:WCAG-025...
2025-07-12T06:38:08.064Z [DEBUG] - ✅ Cache hit: rule:WCAG-025:WCAG-025... (accessed 2 times, age: 3011s)
2025-07-12T06:38:08.065Z [DEBUG] - 📋 Using cached evidence for WCAG-025
2025-07-12T06:38:08.068Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-025 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:08.069Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:08.070Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 16 times, age: 3295s)
2025-07-12T06:38:08.071Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:08.072Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:08.074Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:08.074Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:08.075Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:08.076Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:08.078Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:08.088Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:08.107Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:38:08.108Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-025 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":40}
2025-07-12T06:38:08.108Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.111Z [DEBUG] - 🔧 Utility performance recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.116Z [DEBUG] - 🔧 Utility analysis completed for WCAG-025: - {"utilitiesUsed":3,"errors":0,"executionTime":55}
2025-07-12T06:38:08.117Z [DEBUG] - ⏱️ Check WCAG-025 completed in 64ms (success: true)
2025-07-12T06:38:08.117Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.122Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.122Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 82% (54/66)
2025-07-12T06:38:08.123Z [INFO] - ✅ Rule WCAG-025 completed: passed (75/100)
2025-07-12T06:38:08.124Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.129Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.129Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 82% (54/66)
2025-07-12T06:38:08.131Z [INFO] - 🔍 Executing rule: Link Purpose (In Context) (WCAG-026)
2025-07-12T06:38:08.133Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-026: Link Purpose
2025-07-12T06:38:08.135Z [DEBUG] - 📁 Cache file valid: e87613a3972eabcdb786083f8cc131bf.json (190min remaining)
2025-07-12T06:38:08.135Z [DEBUG] - 📁 Cache loaded from file: e87613a3972eabcdb786083f8cc131bf.json (key: WCAG-026:053b13d2:add92319...)
2025-07-12T06:38:08.136Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-026:053b13d2:add92319...
2025-07-12T06:38:08.137Z [DEBUG] - ✅ Cache hit: rule:WCAG-026:053b13d2:add92319... (accessed 2 times, age: 3011s)
2025-07-12T06:38:08.137Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-026
2025-07-12T06:38:08.140Z [DEBUG] - 📁 Cache file valid: 4463855cd76edcf65e62e0abbfe2bdaf.json (190min remaining)
2025-07-12T06:38:08.140Z [DEBUG] - 📁 Cache loaded from file: 4463855cd76edcf65e62e0abbfe2bdaf.json (key: WCAG-026:WCAG-026...)
2025-07-12T06:38:08.143Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-026:WCAG-026...
2025-07-12T06:38:08.144Z [DEBUG] - ✅ Cache hit: rule:WCAG-026:WCAG-026... (accessed 2 times, age: 3011s)
2025-07-12T06:38:08.146Z [DEBUG] - 📋 Using cached evidence for WCAG-026
2025-07-12T06:38:08.147Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-026 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:08.148Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:08.149Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 17 times, age: 3295s)
2025-07-12T06:38:08.149Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:08.151Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:08.151Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:08.152Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:08.153Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:08.154Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:08.163Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:08.174Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:38:08.174Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-026 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":27}
2025-07-12T06:38:08.175Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.177Z [DEBUG] - 🔧 Utility performance recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.178Z [DEBUG] - 🔧 Utility analysis completed for WCAG-026: - {"utilitiesUsed":2,"errors":0,"executionTime":44}
2025-07-12T06:38:08.180Z [DEBUG] - ⏱️ Check WCAG-026 completed in 49ms (success: true)
2025-07-12T06:38:08.180Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.184Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.184Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 83% (55/66)
2025-07-12T06:38:08.186Z [INFO] - ✅ Rule WCAG-026 completed: passed (86/100)
2025-07-12T06:38:08.189Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.193Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.193Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 83% (55/66)
2025-07-12T06:38:08.194Z [INFO] - 🔍 Executing rule: No Keyboard Trap (WCAG-027)
2025-07-12T06:38:08.196Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-027: No Keyboard Trap
2025-07-12T06:38:08.199Z [DEBUG] - 📁 Cache file valid: 566fd9979fbf5569e1a137c821bf54f5.json (190min remaining)
2025-07-12T06:38:08.199Z [DEBUG] - 📁 Cache loaded from file: 566fd9979fbf5569e1a137c821bf54f5.json (key: WCAG-027:053b13d2:add92319...)
2025-07-12T06:38:08.201Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-027:053b13d2:add92319...
2025-07-12T06:38:08.203Z [DEBUG] - ✅ Cache hit: rule:WCAG-027:053b13d2:add92319... (accessed 2 times, age: 3009s)
2025-07-12T06:38:08.205Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-027
2025-07-12T06:38:08.208Z [DEBUG] - 📁 Cache file valid: d5fb2d75ab65b657ffac81435872fadc.json (190min remaining)
2025-07-12T06:38:08.208Z [DEBUG] - 📁 Cache loaded from file: d5fb2d75ab65b657ffac81435872fadc.json (key: WCAG-027:WCAG-027...)
2025-07-12T06:38:08.209Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-027:WCAG-027...
2025-07-12T06:38:08.209Z [DEBUG] - ✅ Cache hit: rule:WCAG-027:WCAG-027... (accessed 2 times, age: 3009s)
2025-07-12T06:38:08.210Z [DEBUG] - 📋 Using cached evidence for WCAG-027
2025-07-12T06:38:08.211Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-027 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:08.212Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:08.214Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:08.383Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:08.383Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-027 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":172}
2025-07-12T06:38:08.385Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.387Z [DEBUG] - 🔧 Utility performance recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.388Z [DEBUG] - 🔧 Utility analysis completed for WCAG-027: - {"utilitiesUsed":2,"errors":0,"executionTime":191}
2025-07-12T06:38:08.388Z [DEBUG] - ⏱️ Check WCAG-027 completed in 194ms (success: true)
2025-07-12T06:38:08.389Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.394Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.394Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 85% (56/66)
2025-07-12T06:38:08.395Z [INFO] - ✅ Rule WCAG-027 completed: failed (0/100)
2025-07-12T06:38:08.396Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.402Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.402Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 85% (56/66)
2025-07-12T06:38:08.403Z [INFO] - 🔍 Executing rule: Bypass Blocks (WCAG-028)
2025-07-12T06:38:08.405Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-028: Bypass Blocks
2025-07-12T06:38:08.406Z [DEBUG] - 📁 Cache file valid: 00899b8aae33a74dcbf491eb938cbcbd.json (190min remaining)
2025-07-12T06:38:08.407Z [DEBUG] - 📁 Cache loaded from file: 00899b8aae33a74dcbf491eb938cbcbd.json (key: WCAG-028:053b13d2:add92319...)
2025-07-12T06:38:08.407Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-028:053b13d2:add92319...
2025-07-12T06:38:08.408Z [DEBUG] - ✅ Cache hit: rule:WCAG-028:053b13d2:add92319... (accessed 2 times, age: 3008s)
2025-07-12T06:38:08.409Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-028
2025-07-12T06:38:08.412Z [DEBUG] - 📁 Cache file valid: 0372d08002092230cca2af735c55eddb.json (190min remaining)
2025-07-12T06:38:08.413Z [DEBUG] - 📁 Cache loaded from file: 0372d08002092230cca2af735c55eddb.json (key: WCAG-028:WCAG-028...)
2025-07-12T06:38:08.416Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-028:WCAG-028...
2025-07-12T06:38:08.418Z [DEBUG] - ✅ Cache hit: rule:WCAG-028:WCAG-028... (accessed 2 times, age: 3008s)
2025-07-12T06:38:08.419Z [DEBUG] - 📋 Using cached evidence for WCAG-028
2025-07-12T06:38:08.419Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-028 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:08.420Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:08.421Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 18 times, age: 3295s)
2025-07-12T06:38:08.422Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:08.423Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:08.424Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:08.425Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:08.426Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:08.427Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:08.436Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:08.447Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:38:08.448Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-028 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":29}
2025-07-12T06:38:08.449Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.450Z [DEBUG] - 🔧 Utility performance recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.450Z [DEBUG] - 🔧 Utility analysis completed for WCAG-028: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T06:38:08.451Z [DEBUG] - ⏱️ Check WCAG-028 completed in 48ms (success: true)
2025-07-12T06:38:08.452Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.456Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.456Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 86% (57/66)
2025-07-12T06:38:08.457Z [INFO] - ✅ Rule WCAG-028 completed: passed (80/100)
2025-07-12T06:38:08.457Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.462Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.463Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 86% (57/66)
2025-07-12T06:38:08.464Z [INFO] - 🔍 Executing rule: Page Titled (WCAG-029)
2025-07-12T06:38:08.465Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-029: Page Titled
2025-07-12T06:38:08.467Z [DEBUG] - 📁 Cache file valid: 0622341e9cae4a03c93eb461d777af4f.json (190min remaining)
2025-07-12T06:38:08.468Z [DEBUG] - 📁 Cache loaded from file: 0622341e9cae4a03c93eb461d777af4f.json (key: WCAG-029:053b13d2:add92319...)
2025-07-12T06:38:08.468Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-029:053b13d2:add92319...
2025-07-12T06:38:08.469Z [DEBUG] - ✅ Cache hit: rule:WCAG-029:053b13d2:add92319... (accessed 2 times, age: 3008s)
2025-07-12T06:38:08.470Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-029
2025-07-12T06:38:08.471Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:WCAG-013... (accessed 3 times, age: 3259s)
2025-07-12T06:38:08.471Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-12T06:38:08.472Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-029 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:08.473Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:08.474Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:38:08.477Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 5 times, age: 14s)
2025-07-12T06:38:08.479Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:38:08.512Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:08.512Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-029 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":40}
2025-07-12T06:38:08.514Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-029: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.516Z [DEBUG] - 🔧 Utility performance recorded for WCAG-029: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.517Z [DEBUG] - 🔧 Utility analysis completed for WCAG-029: - {"utilitiesUsed":2,"errors":0,"executionTime":50}
2025-07-12T06:38:08.518Z [DEBUG] - ⏱️ Check WCAG-029 completed in 54ms (success: true)
2025-07-12T06:38:08.518Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.522Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.523Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 88% (58/66)
2025-07-12T06:38:08.523Z [INFO] - ✅ Rule WCAG-029 completed: passed (90/100)
2025-07-12T06:38:08.524Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.528Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.528Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 88% (58/66)
2025-07-12T06:38:08.529Z [INFO] - 🔍 Executing rule: Labels or Instructions (WCAG-030)
2025-07-12T06:38:08.530Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-030: Labels or Instructions
2025-07-12T06:38:08.532Z [DEBUG] - 📁 Cache file valid: 318873c4e04faa8d89a794c60564b6a5.json (190min remaining)
2025-07-12T06:38:08.532Z [DEBUG] - 📁 Cache loaded from file: 318873c4e04faa8d89a794c60564b6a5.json (key: WCAG-030:053b13d2:add92319...)
2025-07-12T06:38:08.533Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-030:053b13d2:add92319...
2025-07-12T06:38:08.534Z [DEBUG] - ✅ Cache hit: rule:WCAG-030:053b13d2:add92319... (accessed 2 times, age: 3003s)
2025-07-12T06:38:08.534Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-030
2025-07-12T06:38:08.538Z [DEBUG] - 📁 Cache file valid: 9f51555c21b6b178e061e7b0de6eaa08.json (190min remaining)
2025-07-12T06:38:08.539Z [DEBUG] - 📁 Cache loaded from file: 9f51555c21b6b178e061e7b0de6eaa08.json (key: WCAG-030:WCAG-030...)
2025-07-12T06:38:08.540Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-030:WCAG-030...
2025-07-12T06:38:08.541Z [DEBUG] - ✅ Cache hit: rule:WCAG-030:WCAG-030... (accessed 2 times, age: 3003s)
2025-07-12T06:38:08.542Z [DEBUG] - 📋 Using cached evidence for WCAG-030
2025-07-12T06:38:08.543Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-030 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:08.543Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:38:08.544Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 19 times, age: 3296s)
2025-07-12T06:38:08.544Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:08.546Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:38:08.547Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:38:08.548Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:38:08.549Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:38:08.550Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:38:08.559Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:38:08.571Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:38:08.571Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-030 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":29}
2025-07-12T06:38:08.572Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.574Z [DEBUG] - 🔧 Utility performance recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.575Z [DEBUG] - 🔧 Utility analysis completed for WCAG-030: - {"utilitiesUsed":2,"errors":0,"executionTime":43}
2025-07-12T06:38:08.575Z [DEBUG] - ⏱️ Check WCAG-030 completed in 46ms (success: true)
2025-07-12T06:38:08.576Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.580Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.582Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 89% (59/66)
2025-07-12T06:38:08.583Z [INFO] - ✅ Rule WCAG-030 completed: failed (0/100)
2025-07-12T06:38:08.584Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.588Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.588Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 89% (59/66)
2025-07-12T06:38:08.589Z [INFO] - 🔍 Executing rule: Error Suggestion (WCAG-031)
2025-07-12T06:38:08.590Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-031: Error Suggestion
2025-07-12T06:38:08.592Z [DEBUG] - 📁 Cache file valid: afd4e8553d2527a61d9bf9674019f478.json (190min remaining)
2025-07-12T06:38:08.592Z [DEBUG] - 📁 Cache loaded from file: afd4e8553d2527a61d9bf9674019f478.json (key: WCAG-031:053b13d2:add92319...)
2025-07-12T06:38:08.593Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-031:053b13d2:add92319...
2025-07-12T06:38:08.594Z [DEBUG] - ✅ Cache hit: rule:WCAG-031:053b13d2:add92319... (accessed 2 times, age: 2997s)
2025-07-12T06:38:08.594Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-031
2025-07-12T06:38:08.598Z [DEBUG] - 📁 Cache file valid: 7f773532f54478ddb2be2ece7257b22c.json (190min remaining)
2025-07-12T06:38:08.599Z [DEBUG] - 📁 Cache loaded from file: 7f773532f54478ddb2be2ece7257b22c.json (key: WCAG-031:WCAG-031...)
2025-07-12T06:38:08.600Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-031:WCAG-031...
2025-07-12T06:38:08.601Z [DEBUG] - ✅ Cache hit: rule:WCAG-031:WCAG-031... (accessed 2 times, age: 2997s)
2025-07-12T06:38:08.602Z [DEBUG] - 📋 Using cached evidence for WCAG-031
2025-07-12T06:38:08.603Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-031 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:08.603Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:08.605Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:38:08.605Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 6 times, age: 14s)
2025-07-12T06:38:08.606Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:38:08.643Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:08.643Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-031 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":40}
2025-07-12T06:38:08.645Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-031: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.647Z [DEBUG] - 🔧 Utility performance recorded for WCAG-031: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.648Z [DEBUG] - 🔧 Utility analysis completed for WCAG-031: - {"utilitiesUsed":2,"errors":0,"executionTime":56}
2025-07-12T06:38:08.649Z [DEBUG] - ⏱️ Check WCAG-031 completed in 60ms (success: true)
2025-07-12T06:38:08.649Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.653Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.653Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 91% (60/66)
2025-07-12T06:38:08.654Z [INFO] - ✅ Rule WCAG-031 completed: failed (0/100)
2025-07-12T06:38:08.655Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.659Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.659Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 91% (60/66)
2025-07-12T06:38:08.660Z [INFO] - 🔍 Executing rule: Error Prevention (WCAG-032)
2025-07-12T06:38:08.663Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-032: Error Prevention
2025-07-12T06:38:08.665Z [DEBUG] - 📁 Cache file valid: 44c080abba45af2ff4a6ac832f660888.json (190min remaining)
2025-07-12T06:38:08.665Z [DEBUG] - 📁 Cache loaded from file: 44c080abba45af2ff4a6ac832f660888.json (key: WCAG-032:053b13d2:add92319...)
2025-07-12T06:38:08.666Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-032:053b13d2:add92319...
2025-07-12T06:38:08.666Z [DEBUG] - ✅ Cache hit: rule:WCAG-032:053b13d2:add92319... (accessed 2 times, age: 2992s)
2025-07-12T06:38:08.667Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-032
2025-07-12T06:38:08.670Z [DEBUG] - 📁 Cache file valid: e401202f7944ff2eec72489ab235f330.json (190min remaining)
2025-07-12T06:38:08.670Z [DEBUG] - 📁 Cache loaded from file: e401202f7944ff2eec72489ab235f330.json (key: WCAG-032:WCAG-032...)
2025-07-12T06:38:08.671Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-032:WCAG-032...
2025-07-12T06:38:08.674Z [DEBUG] - ✅ Cache hit: rule:WCAG-032:WCAG-032... (accessed 2 times, age: 2992s)
2025-07-12T06:38:08.674Z [DEBUG] - 📋 Using cached evidence for WCAG-032
2025-07-12T06:38:08.676Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-032 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:38:08.677Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:08.678Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:38:08.851Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:08.851Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-032 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":175}
2025-07-12T06:38:08.853Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.855Z [DEBUG] - 🔧 Utility performance recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.856Z [DEBUG] - 🔧 Utility analysis completed for WCAG-032: - {"utilitiesUsed":2,"errors":0,"executionTime":193}
2025-07-12T06:38:08.857Z [DEBUG] - ⏱️ Check WCAG-032 completed in 197ms (success: true)
2025-07-12T06:38:08.857Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.861Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.862Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 92% (61/66)
2025-07-12T06:38:08.863Z [INFO] - ✅ Rule WCAG-032 completed: failed (0/100)
2025-07-12T06:38:08.863Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.868Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.871Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 92% (61/66)
2025-07-12T06:38:08.872Z [INFO] - 🔍 Executing rule: Audio-only and Video-only (WCAG-033)
2025-07-12T06:38:08.873Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-033: Audio-only and Video-only
2025-07-12T06:38:08.875Z [DEBUG] - 📁 Cache file valid: 949b30f4e5c95acf0bd0f077c7ba9124.json (190min remaining)
2025-07-12T06:38:08.876Z [DEBUG] - 📁 Cache loaded from file: 949b30f4e5c95acf0bd0f077c7ba9124.json (key: WCAG-033:053b13d2:add92319...)
2025-07-12T06:38:08.876Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-033:053b13d2:add92319...
2025-07-12T06:38:08.877Z [DEBUG] - ✅ Cache hit: rule:WCAG-033:053b13d2:add92319... (accessed 2 times, age: 2991s)
2025-07-12T06:38:08.878Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-033
2025-07-12T06:38:08.881Z [DEBUG] - 📁 Cache file valid: 49c6f131e0988b238b6cd294050269cb.json (190min remaining)
2025-07-12T06:38:08.881Z [DEBUG] - 📁 Cache loaded from file: 49c6f131e0988b238b6cd294050269cb.json (key: WCAG-033:WCAG-033...)
2025-07-12T06:38:08.882Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-033:WCAG-033...
2025-07-12T06:38:08.887Z [DEBUG] - ✅ Cache hit: rule:WCAG-033:WCAG-033... (accessed 2 times, age: 2991s)
2025-07-12T06:38:08.887Z [DEBUG] - 📋 Using cached evidence for WCAG-033
2025-07-12T06:38:08.889Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-033 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:08.890Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:08.891Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:38:08.891Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 7 times, age: 15s)
2025-07-12T06:38:08.893Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:38:08.925Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:08.925Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-033 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":36}
2025-07-12T06:38:08.927Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-033: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:08.930Z [DEBUG] - 🔧 Utility performance recorded for WCAG-033: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:08.931Z [DEBUG] - 🔧 Utility analysis completed for WCAG-033: - {"utilitiesUsed":2,"errors":0,"executionTime":55}
2025-07-12T06:38:08.932Z [DEBUG] - ⏱️ Check WCAG-033 completed in 60ms (success: true)
2025-07-12T06:38:08.932Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.937Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.937Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 94% (62/66)
2025-07-12T06:38:08.938Z [INFO] - ✅ Rule WCAG-033 completed: passed (100/100)
2025-07-12T06:38:08.939Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.945Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:08.946Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 94% (62/66)
2025-07-12T06:38:08.947Z [INFO] - 🔍 Executing rule: Audio Description (WCAG-034)
2025-07-12T06:38:08.948Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-034: Audio Description
2025-07-12T06:38:08.950Z [DEBUG] - 📁 Cache file valid: 475e798d534b8f0ef7bb04c71985d0af.json (190min remaining)
2025-07-12T06:38:08.950Z [DEBUG] - 📁 Cache loaded from file: 475e798d534b8f0ef7bb04c71985d0af.json (key: WCAG-034:053b13d2:add92319...)
2025-07-12T06:38:08.951Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-034:053b13d2:add92319...
2025-07-12T06:38:08.951Z [DEBUG] - ✅ Cache hit: rule:WCAG-034:053b13d2:add92319... (accessed 2 times, age: 2991s)
2025-07-12T06:38:08.952Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-034
2025-07-12T06:38:08.955Z [DEBUG] - 📁 Cache file valid: d66098f699797d33163e6b8427f7847e.json (190min remaining)
2025-07-12T06:38:08.956Z [DEBUG] - 📁 Cache loaded from file: d66098f699797d33163e6b8427f7847e.json (key: WCAG-034:WCAG-034...)
2025-07-12T06:38:08.957Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-034:WCAG-034...
2025-07-12T06:38:08.959Z [DEBUG] - ✅ Cache hit: rule:WCAG-034:WCAG-034... (accessed 2 times, age: 2991s)
2025-07-12T06:38:08.960Z [DEBUG] - 📋 Using cached evidence for WCAG-034
2025-07-12T06:38:08.961Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-034 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:08.961Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:38:08.963Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:38:08.963Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 8 times, age: 15s)
2025-07-12T06:38:08.965Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:38:08.997Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:38:08.997Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-034 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":37}
2025-07-12T06:38:08.999Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-034: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:09.001Z [DEBUG] - 🔧 Utility performance recorded for WCAG-034: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:09.001Z [DEBUG] - 🔧 Utility analysis completed for WCAG-034: - {"utilitiesUsed":2,"errors":0,"executionTime":52}
2025-07-12T06:38:09.002Z [DEBUG] - ⏱️ Check WCAG-034 completed in 55ms (success: true)
2025-07-12T06:38:09.003Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.007Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.007Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 95% (63/66)
2025-07-12T06:38:09.008Z [INFO] - ✅ Rule WCAG-034 completed: passed (100/100)
2025-07-12T06:38:09.009Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.013Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.013Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 95% (63/66)
2025-07-12T06:38:09.014Z [INFO] - 🔍 Executing rule: Multiple Ways (WCAG-035)
2025-07-12T06:38:09.015Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-035: Multiple Ways
2025-07-12T06:38:09.020Z [DEBUG] - 📁 Cache file valid: cc45a01e4bffb1e837dd3935974aa92b.json (190min remaining)
2025-07-12T06:38:09.020Z [DEBUG] - 📁 Cache loaded from file: cc45a01e4bffb1e837dd3935974aa92b.json (key: WCAG-035:053b13d2:add92319...)
2025-07-12T06:38:09.021Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-035:053b13d2:add92319...
2025-07-12T06:38:09.022Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:053b13d2:add92319... (accessed 2 times, age: 2988s)
2025-07-12T06:38:09.023Z [INFO] - 🎯 [8b80d206-4a60-40bd-9825-753e25410d09] Cache hit for WCAG-035
2025-07-12T06:38:09.023Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:WCAG-035... (accessed 3 times, age: 3257s)
2025-07-12T06:38:09.024Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-12T06:38:09.025Z [DEBUG] - 🔧 [8b80d206-4a60-40bd-9825-753e25410d09] Starting utility analysis for WCAG-035 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:38:09.025Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:38:09.027Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:38:09.244Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:38:09.245Z [DEBUG] - ✅ [8b80d206-4a60-40bd-9825-753e25410d09] Utility analysis completed for WCAG-035 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":220}
2025-07-12T06:38:09.250Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:38:09.252Z [DEBUG] - 🔧 Utility performance recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:38:09.254Z [DEBUG] - 🔧 Utility analysis completed for WCAG-035: - {"utilitiesUsed":2,"errors":0,"executionTime":235}
2025-07-12T06:38:09.255Z [DEBUG] - ⏱️ Check WCAG-035 completed in 241ms (success: true)
2025-07-12T06:38:09.258Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.274Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.276Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 97% (64/66)
2025-07-12T06:38:09.283Z [INFO] - ✅ Rule WCAG-035 completed: passed (100/100)
2025-07-12T06:38:09.285Z [INFO] - 📝 Updating WCAG scan status: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.304Z [INFO] - ✅ WCAG scan status updated: 8b80d206-4a60-40bd-9825-753e25410d09 -> running
2025-07-12T06:38:09.304Z [INFO] - 📈 Scan 8b80d206-4a60-40bd-9825-753e25410d09: 97% (64/66)
2025-07-12T06:38:09.309Z [INFO] - 🔍 Executing rule: Headings and Labels (WCAG-036)
2025-07-12T06:38:09.310Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting enhanced WCAG-036: Headings and Labels
2025-07-12T06:38:09.368Z [DEBUG] - 📁 Cache file not found: 154e205869bd5401ba0ec01a183a7960.json (key: WCAG-036:053b13d2:add92319...)
2025-07-12T06:38:09.368Z [DEBUG] - 🔍 Cache miss: rule:WCAG-036:053b13d2:add92319...
2025-07-12T06:38:09.371Z [DEBUG] - 📊 Cache stats: 149 hits, 1 misses, 124 entries
2025-07-12T06:38:09.373Z [DEBUG] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Cache miss for WCAG-036, executing check
2025-07-12T06:38:09.374Z [DEBUG] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Cache key: WCAG-036:053b13d2:add92319
2025-07-12T06:38:09.375Z [INFO] - 🔍 [8b80d206-4a60-40bd-9825-753e25410d09] Starting WCAG-036: Headings and Labels
2025-07-12T06:38:09.377Z [DEBUG] - 📋 Using unified DOM structure: 64 headings found
2025-07-12T06:38:09.377Z [DEBUG] - 📋 Using unified DOM structure: 1 forms found
2025-07-12T06:38:09.379Z [DEBUG] - 📁 Cache file not found: 81fd93f4610e191362e7346e12cd8f1f.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:38:09.379Z [DEBUG] - 🔍 Cache miss: site:https://tigerconnect.com/:form-analysis-{"analyzeL...
2025-07-12T06:38:09.380Z [DEBUG] - 📊 Cache stats: 149 hits, 2 misses, 12 entries
2025-07-12T06:38:09.381Z [DEBUG] - 📝 Starting advanced form accessibility analysis
2025-07-12T06:38:09.387Z [DEBUG] - 🔄 Updated session activity: 8b80d206-4a60-40bd-9825-753e25410d09:https://tigerconnect.com/ (checks: 1)
2025-07-12T06:38:09.414Z [WARN] - ⚠️ Form analysis injection validation attempt 1 failed: - {"error":"Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}","willRetry":true}
2025-07-12T06:38:10.217Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=98.7%, reported=0.0%
2025-07-12T06:38:10.218Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"98.7%","alerts":0}
2025-07-12T06:38:10.434Z [WARN] - ⚠️ Form analysis injection validation attempt 2 failed: - {"error":"Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}","willRetry":true}
2025-07-12T06:38:12.136Z [DEBUG] - 📊 Memory usage: 494MB / 527MB (growth: 84.0MB/min)
2025-07-12T06:38:12.136Z [WARN] - ⚠️ High memory usage detected: 494MB
2025-07-12T06:38:12.138Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-12T06:38:12.140Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-12T06:38:12.141Z [INFO] - ✅ Proactive cleanup completed
2025-07-12T06:38:12.142Z [WARN] - 🔍 Memory leak detected: 84.0MB/min growth
2025-07-12T06:38:12.143Z [WARN] - 🔍 Memory leak investigation: - {"browsers":1,"availablePages":0,"inUsePages":1,"heapUsed":494,"heapTotal":527}
2025-07-12T06:38:12.144Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-12T06:38:12.145Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-12T06:38:12.146Z [INFO] - ✅ Proactive cleanup completed
2025-07-12T06:38:12.453Z [WARN] - ⚠️ Form analysis injection validation attempt 3 failed: - {"error":"Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}","willRetry":false}
2025-07-12T06:38:12.453Z [ERROR] - ❌ Form analysis injection validation failed after all attempts: - {"error":"Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}"}
2025-07-12T06:38:12.456Z [ERROR] - ❌ Form analysis injection process failed: - {"error":"Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}"}
2025-07-12T06:38:12.458Z [ERROR] - ❌ Form analysis injection process failed: - {"error":"Form analysis injection process failed: Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}"}
2025-07-12T06:38:12.459Z [ERROR] - ❌ Session operation failed for 8b80d206-4a60-40bd-9825-753e25410d09:https://tigerconnect.com/: - {"error":"Form analysis injection process failed: Form analysis injection process failed: Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}"}
2025-07-12T06:38:12.463Z [ERROR] - ❌ [8b80d206-4a60-40bd-9825-753e25410d09] Error in WCAG-036 - {"error":{"message":"Form analysis injection process failed: Form analysis injection process failed: Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}","stack":"Error: Form analysis injection process failed: Form analysis injection process failed: Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {\"hasGetElementSelector\":true,\"hasAnalyzeFormElement\":false,\"hasGetFormValidation\":false,\"hasGetFormLabels\":false,\"hasGetFormErrors\":false,\"hasGetFormGrouping\":false,\"hasGetKeyboardAccess\":false}\n    at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:280:13)\n    at async FormAccessibilityAnalyzer.performFormAnalysis (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:200:5)\n    at async D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\form-accessibility-analyzer.ts:185:14\n    at async SessionManager.executeWithLock (D:\\Web projects\\Comply Checker\\backend\\src\\compliance\\wcag\\utils\\session-manager.ts:120:22)","name":"Error"},"ruleId":"WCAG-036","ruleName":"Headings and Labels","executionTime":3085}
❌ Unhandled Rejection at: Promise {
  <rejected> Error: Form analysis injection process failed: Form analysis injection process failed: Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {"hasGetElementSelector":true,"hasAnalyzeFormElement":false,"hasGetFormValidation":false,"hasGetFormLabels":false,"hasGetFormErrors":false,"hasGetFormGrouping":false,"hasGetKeyboardAccess":false}
      at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\form-accessibility-analyzer.ts:280:13)
      at async FormAccessibilityAnalyzer.performFormAnalysis (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\form-accessibility-analyzer.ts:200:5)
      at async D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\form-accessibility-analyzer.ts:185:14
      at async SessionManager.executeWithLock (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\session-manager.ts:120:22)
} reason: Error: Form analysis injection process failed: Form analysis injection process failed: Form analysis injection failed after 3 attempts: Form analysis injection validation failed - missing functions: {"hasGetElementSelector":true,"hasAnalyzeFormElement":false,"hasGetFormValidation":false,"hasGetFormLabels":false,"hasGetFormErrors":false,"hasGetFormGrouping":false,"hasGetKeyboardAccess":false}
    at FormAccessibilityAnalyzer.injectFormAnalysisFunctions (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\form-accessibility-analyzer.ts:280:13)
    at async FormAccessibilityAnalyzer.performFormAnalysis (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\form-accessibility-analyzer.ts:200:5)
    at async D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\form-accessibility-analyzer.ts:185:14
    at async SessionManager.executeWithLock (D:\Web projects\Comply Checker\backend\src\compliance\wcag\utils\session-manager.ts:120:22)

🛑 Received unhandledRejection. Starting graceful shutdown...
🧹 Cleaning up 0 browser instances
✅ Browser pool cleanup completed
✅ Browser pool cleanup completed
✅ HTTP server closed
[nodemon] clean exit - waiting for changes before restart