/**
 * Resource-Aware Manager for VPS Environments
 * Dynamic resource allocation and scaling based on system capacity and load
 */

import * as os from 'os';
import * as process from 'process';
import logger from '../../../utils/logger';

export interface SystemResources {
  cpu: {
    cores: number;
    usage: number; // 0-100
    loadAverage: number[];
    available: number; // 0-100
  };
  memory: {
    totalMB: number;
    usedMB: number;
    freeMB: number;
    usage: number; // 0-100
    available: number; // 0-100
  };
  network: {
    connections: number;
    bandwidth: number; // estimated Mbps
    latency: number; // ms
  };
  storage: {
    totalGB: number;
    usedGB: number;
    freeGB: number;
    usage: number; // 0-100
  };
}

export interface ResourceLimits {
  maxConcurrentScans: number;
  maxBrowserInstances: number;
  maxPagesPerBrowser: number;
  maxCacheSize: number;
  maxMemoryUsage: number;
  scanTimeout: number;
}

export interface VPSProfile {
  tier: 'micro' | 'small' | 'medium' | 'large' | 'xlarge';
  cpu: number;
  memoryGB: number;
  storageGB: number;
  networkMbps: number;
  recommendedLimits: ResourceLimits;
}

export interface ResourceAllocation {
  scanId: string;
  allocatedResources: {
    cpu: number; // percentage
    memory: number; // MB
    priority: 'low' | 'normal' | 'high';
  };
  estimatedDuration: number; // ms
  timestamp: number;
}

/**
 * Advanced resource manager for VPS environments
 */
export class ResourceManager {
  private static instance: ResourceManager;
  private currentAllocations: Map<string, ResourceAllocation> = new Map();
  private resourceHistory: SystemResources[] = [];
  private vpsProfile: VPSProfile;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private lastResourceCheck: number = 0;

  private constructor() {
    this.vpsProfile = this.detectVPSProfile();
    this.startResourceMonitoring();
  }

  static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager();
    }
    return ResourceManager.instance;
  }

  /**
   * Get current system resources
   */
  async getCurrentResources(): Promise<SystemResources> {
    const now = Date.now();

    // Cache resource checks for 5 seconds to avoid excessive system calls
    if (now - this.lastResourceCheck < 5000 && this.resourceHistory.length > 0) {
      return this.resourceHistory[this.resourceHistory.length - 1];
    }

    const cpuUsage = await this.getCPUUsage();
    const memoryInfo = this.getMemoryInfo();
    const networkInfo = await this.getNetworkInfo();
    const storageInfo = await this.getStorageInfo();

    const resources: SystemResources = {
      cpu: {
        cores: os.cpus().length,
        usage: cpuUsage,
        loadAverage: os.loadavg(),
        available: Math.max(0, 100 - cpuUsage),
      },
      memory: {
        ...memoryInfo,
        usage: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100),
        available: Math.round((os.freemem() / os.totalmem()) * 100),
      },
      network: networkInfo,
      storage: storageInfo,
    };

    // Store in history (keep last 60 entries = 5 minutes at 5-second intervals)
    this.resourceHistory.push(resources);
    if (this.resourceHistory.length > 60) {
      this.resourceHistory.shift();
    }

    this.lastResourceCheck = now;
    return resources;
  }

  /**
   * Allocate resources for a scan
   */
  async allocateResources(
    scanId: string,
    priority: 'low' | 'normal' | 'high' = 'normal',
  ): Promise<ResourceAllocation | null> {
    const resources = await this.getCurrentResources();
    const limits = this.calculateDynamicLimits(resources);

    // Check if we have enough resources
    if (this.currentAllocations.size >= limits.maxConcurrentScans) {
      logger.warn(
        `Resource allocation denied: Maximum concurrent scans reached (${limits.maxConcurrentScans})`,
      );
      return null;
    }

    // Calculate resource allocation based on priority and availability
    const allocation = this.calculateResourceAllocation(resources, priority);

    if (!allocation) {
      logger.warn(`Resource allocation denied: Insufficient resources available`);
      return null;
    }

    const resourceAllocation: ResourceAllocation = {
      scanId,
      allocatedResources: allocation,
      estimatedDuration: this.estimateScanDuration(allocation),
      timestamp: Date.now(),
    };

    this.currentAllocations.set(scanId, resourceAllocation);

    logger.info(`Resources allocated for scan ${scanId}`, {
      cpu: allocation.cpu,
      memory: allocation.memory,
      priority: allocation.priority,
      estimatedDuration: resourceAllocation.estimatedDuration,
    });

    return resourceAllocation;
  }

  /**
   * Release resources for a scan
   */
  releaseResources(scanId: string): void {
    const allocation = this.currentAllocations.get(scanId);
    if (allocation) {
      this.currentAllocations.delete(scanId);
      logger.info(`Resources released for scan ${scanId}`, {
        duration: Date.now() - allocation.timestamp,
        cpu: allocation.allocatedResources.cpu,
        memory: allocation.allocatedResources.memory,
      });
    }
  }

  /**
   * Get dynamic resource limits based on current system state
   */
  async getDynamicLimits(): Promise<ResourceLimits> {
    const resources = await this.getCurrentResources();
    return this.calculateDynamicLimits(resources);
  }

  /**
   * Get resource utilization statistics
   */
  getResourceStats(): {
    currentAllocations: number;
    totalCPUAllocated: number;
    totalMemoryAllocated: number;
    averageResourceUsage: SystemResources | null;
  } {
    const totalCPUAllocated = Array.from(this.currentAllocations.values()).reduce(
      (sum, alloc) => sum + alloc.allocatedResources.cpu,
      0,
    );

    const totalMemoryAllocated = Array.from(this.currentAllocations.values()).reduce(
      (sum, alloc) => sum + alloc.allocatedResources.memory,
      0,
    );

    const averageResourceUsage =
      this.resourceHistory.length > 0 ? this.calculateAverageResources() : null;

    return {
      currentAllocations: this.currentAllocations.size,
      totalCPUAllocated,
      totalMemoryAllocated,
      averageResourceUsage,
    };
  }

  /**
   * Detect VPS profile based on system specifications
   */
  private detectVPSProfile(): VPSProfile {
    const totalMemoryGB = Math.round(os.totalmem() / 1024 / 1024 / 1024);
    const cpuCores = os.cpus().length;

    // Define VPS profiles
    const profiles: VPSProfile[] = [
      {
        tier: 'micro',
        cpu: 1,
        memoryGB: 1,
        storageGB: 20,
        networkMbps: 100,
        recommendedLimits: {
          maxConcurrentScans: 1,
          maxBrowserInstances: 1,
          maxPagesPerBrowser: 1,
          maxCacheSize: 25,
          maxMemoryUsage: 800,
          scanTimeout: 180000,
        },
      },
      {
        tier: 'small',
        cpu: 2,
        memoryGB: 2,
        storageGB: 50,
        networkMbps: 200,
        recommendedLimits: {
          maxConcurrentScans: 2,
          maxBrowserInstances: 1,
          maxPagesPerBrowser: 2,
          maxCacheSize: 50,
          maxMemoryUsage: 1600,
          scanTimeout: 150000,
        },
      },
      {
        tier: 'medium',
        cpu: 4,
        memoryGB: 4,
        storageGB: 100,
        networkMbps: 500,
        recommendedLimits: {
          maxConcurrentScans: 4,
          maxBrowserInstances: 2,
          maxPagesPerBrowser: 2,
          maxCacheSize: 100,
          maxMemoryUsage: 3200,
          scanTimeout: 120000,
        },
      },
      {
        tier: 'large',
        cpu: 8,
        memoryGB: 8,
        storageGB: 200,
        networkMbps: 1000,
        recommendedLimits: {
          maxConcurrentScans: 8,
          maxBrowserInstances: 3,
          maxPagesPerBrowser: 3,
          maxCacheSize: 200,
          maxMemoryUsage: 6400,
          scanTimeout: 90000,
        },
      },
      {
        tier: 'xlarge',
        cpu: 16,
        memoryGB: 16,
        storageGB: 500,
        networkMbps: 2000,
        recommendedLimits: {
          maxConcurrentScans: 12,
          maxBrowserInstances: 4,
          maxPagesPerBrowser: 4,
          maxCacheSize: 400,
          maxMemoryUsage: 12800,
          scanTimeout: 60000,
        },
      },
    ];

    // Find best matching profile
    for (const profile of profiles) {
      if (cpuCores <= profile.cpu && totalMemoryGB <= profile.memoryGB) {
        logger.info(`Detected VPS profile: ${profile.tier}`, {
          cpu: cpuCores,
          memory: totalMemoryGB,
          recommendedLimits: profile.recommendedLimits,
        });
        return profile;
      }
    }

    // Default to largest profile if system exceeds all profiles
    const largestProfile = profiles[profiles.length - 1];
    logger.info(`Using largest VPS profile: ${largestProfile.tier} (system exceeds profiles)`, {
      cpu: cpuCores,
      memory: totalMemoryGB,
    });
    return largestProfile;
  }

  /**
   * Calculate dynamic limits based on current resources
   */
  private calculateDynamicLimits(resources: SystemResources): ResourceLimits {
    const baseLimits = this.vpsProfile.recommendedLimits;

    // Adjust limits based on current resource availability
    const cpuMultiplier = Math.max(0.3, resources.cpu.available / 100);
    const memoryMultiplier = Math.max(0.3, resources.memory.available / 100);
    const resourceMultiplier = Math.min(cpuMultiplier, memoryMultiplier);

    return {
      maxConcurrentScans: Math.max(
        1,
        Math.floor(baseLimits.maxConcurrentScans * resourceMultiplier),
      ),
      maxBrowserInstances: Math.max(
        1,
        Math.floor(baseLimits.maxBrowserInstances * resourceMultiplier),
      ),
      maxPagesPerBrowser: baseLimits.maxPagesPerBrowser,
      maxCacheSize: Math.floor(baseLimits.maxCacheSize * memoryMultiplier),
      maxMemoryUsage: Math.floor(baseLimits.maxMemoryUsage * memoryMultiplier),
      scanTimeout: Math.max(60000, baseLimits.scanTimeout),
    };
  }

  /**
   * Calculate resource allocation for a scan
   */
  private calculateResourceAllocation(
    resources: SystemResources,
    priority: 'low' | 'normal' | 'high',
  ): { cpu: number; memory: number; priority: 'low' | 'normal' | 'high' } | null {
    // Base allocation percentages
    const baseAllocations = {
      low: { cpu: 15, memory: 200 },
      normal: { cpu: 25, memory: 400 },
      high: { cpu: 40, memory: 600 },
    };

    const baseAllocation = baseAllocations[priority];

    // Check if we have enough resources
    const currentCPUUsage = Array.from(this.currentAllocations.values()).reduce(
      (sum, alloc) => sum + alloc.allocatedResources.cpu,
      0,
    );

    const currentMemoryUsage = Array.from(this.currentAllocations.values()).reduce(
      (sum, alloc) => sum + alloc.allocatedResources.memory,
      0,
    );

    const availableCPU = Math.max(0, 80 - currentCPUUsage); // Keep 20% CPU free
    const availableMemory = Math.max(0, resources.memory.freeMB - currentMemoryUsage - 500); // Keep 500MB free

    if (baseAllocation.cpu > availableCPU || baseAllocation.memory > availableMemory) {
      return null; // Not enough resources
    }

    return {
      cpu: baseAllocation.cpu,
      memory: baseAllocation.memory,
      priority,
    };
  }

  /**
   * Estimate scan duration based on resource allocation
   */
  private estimateScanDuration(allocation: {
    cpu: number;
    memory: number;
    priority: string;
  }): number {
    // Base duration estimates (in milliseconds)
    const baseDuration = 45000; // 45 seconds

    // Adjust based on resource allocation
    const cpuFactor = Math.max(0.5, allocation.cpu / 25); // Normal allocation is 25%
    const memoryFactor = Math.max(0.5, allocation.memory / 400); // Normal allocation is 400MB
    const resourceFactor = (cpuFactor + memoryFactor) / 2;

    return Math.round(baseDuration / resourceFactor);
  }

  /**
   * Get CPU usage percentage
   */
  private async getCPUUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = Date.now();

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = Date.now();

        const totalTime = (endTime - startTime) * 1000; // Convert to microseconds
        const totalUsage = endUsage.user + endUsage.system;
        const usage = Math.min(100, (totalUsage / totalTime) * 100);

        resolve(usage);
      }, 100);
    });
  }

  /**
   * Get memory information
   */
  private getMemoryInfo() {
    return {
      totalMB: Math.round(os.totalmem() / 1024 / 1024),
      usedMB: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024),
      freeMB: Math.round(os.freemem() / 1024 / 1024),
      usage: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100),
      available: Math.round((os.freemem() / os.totalmem()) * 100),
    };
  }

  /**
   * Get network information (simplified)
   */
  private async getNetworkInfo(): Promise<{
    connections: number;
    bandwidth: number;
    latency: number;
  }> {
    // Simplified network info - in production, this could be enhanced with actual network monitoring
    return {
      connections: this.currentAllocations.size,
      bandwidth: this.vpsProfile.networkMbps,
      latency: 50, // Estimated
    };
  }

  /**
   * Get storage information (simplified)
   */
  private async getStorageInfo(): Promise<{
    totalGB: number;
    usedGB: number;
    freeGB: number;
    usage: number;
  }> {
    // Simplified storage info - in production, this could use actual disk usage
    return {
      totalGB: this.vpsProfile.storageGB,
      usedGB: Math.round(this.vpsProfile.storageGB * 0.3), // Estimated 30% usage
      freeGB: Math.round(this.vpsProfile.storageGB * 0.7),
      usage: 30,
    };
  }

  /**
   * Calculate average resources from history
   */
  private calculateAverageResources(): SystemResources {
    if (this.resourceHistory.length === 0) {
      throw new Error('No resource history available');
    }

    const avgCPU =
      this.resourceHistory.reduce((sum, r) => sum + r.cpu.usage, 0) / this.resourceHistory.length;
    const avgMemory =
      this.resourceHistory.reduce((sum, r) => sum + r.memory.usage, 0) /
      this.resourceHistory.length;

    const latest = this.resourceHistory[this.resourceHistory.length - 1];

    return {
      cpu: {
        ...latest.cpu,
        usage: avgCPU,
        available: 100 - avgCPU,
      },
      memory: {
        ...latest.memory,
        usage: avgMemory,
        available: 100 - avgMemory,
      },
      network: latest.network,
      storage: latest.storage,
    };
  }

  /**
   * Start resource monitoring
   */
  private startResourceMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.getCurrentResources();

        // Clean up old allocations (older than 10 minutes)
        const tenMinutesAgo = Date.now() - 600000;
        for (const [scanId, allocation] of Array.from(this.currentAllocations.entries())) {
          if (allocation.timestamp < tenMinutesAgo) {
            logger.warn(`Cleaning up stale resource allocation for scan ${scanId}`);
            this.currentAllocations.delete(scanId);
          }
        }
      } catch (error) {
        logger.error('Error during resource monitoring', { error });
      }
    }, 30000); // Monitor every 30 seconds
  }

  /**
   * Stop resource monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Get VPS profile information
   */
  getVPSProfile(): VPSProfile {
    return this.vpsProfile;
  }
}

export default ResourceManager;
