import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Add assessment and reviewer columns to wcag_manual_reviews table
  await knex.schema.alterTable('wcag_manual_reviews', (table) => {
    table.enu('review_assessment', ['compliant', 'non-compliant', 'partially-compliant', 'needs-review']);
    table.string('reviewed_by', 255); // User ID who performed the review
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  // Remove the added columns
  await knex.schema.alterTable('wcag_manual_reviews', (table) => {
    table.dropColumn('review_assessment');
    table.dropColumn('reviewed_by');
    table.dropColumn('updated_at');
  });
}
