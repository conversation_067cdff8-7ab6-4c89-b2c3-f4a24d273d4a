'use client';

/**
 * WCAG Scan Page
 * Page for starting new WCAG compliance scans
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, AlertTriangle, CheckCircle, ClipboardCheck } from 'lucide-react';
import WcagScanForm from '@/components/wcag/WcagScanForm';
import WcagScanProgress from '@/components/wcag/WcagScanProgress';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';
import { WcagScanFormData } from '@/types/wcag';

/**
 * WCAG Scan Content Component
 */
const WcagScanContent: React.FC = () => {
  const router = useRouter();
  const state = useWcagState();
  const actions = useWcagActions();
  const [mounted, setMounted] = useState(false);
  const [scanStarted, setScanStarted] = useState(false);

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleBack = () => {
    router.push('/dashboard/wcag');
  };

  const handleManualReview = () => {
    router.push('/dashboard/wcag/manual-review/submit');
  };

  const handleStartScan = async (formData: WcagScanFormData) => {
    try {
      const result = await actions.startScan(formData);
      setScanStarted(true);

      // Start progress polling
      startProgressPolling(result.scanId);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error starting scan:', error);
    }
  };

  const startProgressPolling = (scanId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        // In a real implementation, you would call an API to get progress
        // For now, we'll simulate progress updates
        const currentProgress = state.scanProgressMap ? state.scanProgressMap[scanId] : undefined;
        if (
          !currentProgress ||
          currentProgress.status === 'completed' ||
          currentProgress.status === 'failed'
        ) {
          clearInterval(pollInterval);
          return;
        }

        // Simulate progress update
        const newProgress = Math.min((currentProgress?.progress || 0) + 5, 100);
        actions.updateScanProgress({
          scanId,
          progress: newProgress,
          status: newProgress >= 100 ? 'completed' : 'running',
          currentCheck: `Checking accessibility rule ${Math.floor(newProgress / 10) + 1}`,
          estimatedTimeRemaining: Math.max(0, (100 - newProgress) * 2),
          completedChecks: Math.floor(newProgress / 10),
          totalChecks: 10,
        });

        if (newProgress >= 100) {
          clearInterval(pollInterval);
          setScanStarted(false);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error polling progress:', error);
        clearInterval(pollInterval);
      }
    }, 2000);
  };

  const handleScanComplete = (scanId: string) => {
    setScanStarted(false);
    router.push(`/dashboard/wcag/scan/${scanId}`);
  };

  const handleScanCancel = () => {
    setScanStarted(false);
    actions.clearError();
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Start WCAG Scan</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Configure and start a new accessibility compliance scan
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleManualReview}
            className="flex items-center gap-2"
          >
            <ClipboardCheck className="h-4 w-4" />
            Manual Review
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {scanStarted && !state.error && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>Scan started successfully! Monitor progress below.</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Scan Form */}
        <Card>
          <CardHeader>
            <CardTitle>Scan Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <WcagScanForm
              onSubmit={handleStartScan}
              disabled={state.loading.scanning || scanStarted}
              isLoading={state.loading.scanning}
            />
          </CardContent>
        </Card>

        {/* Scan Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Scan Progress</CardTitle>
          </CardHeader>
          <CardContent>
            {scanStarted || Object.keys(state.scanProgress).length > 0 ? (
              <div className="space-y-4">
                {Object.entries(state.scanProgress)
                  .filter(
                    ([, progress]) =>
                      progress.status === 'running' ||
                      progress.status === 'pending' ||
                      progress.status === 'completed',
                  )
                  .map(([scanId, progress]) => (
                    <WcagScanProgress
                      key={scanId}
                      scanId={scanId}
                      progress={progress}
                      onComplete={() => handleScanComplete(scanId)}
                      onCancel={handleScanCancel}
                    />
                  ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500 dark:text-gray-400">
                  No active scans. Start a scan to see progress here.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Queue Status */}
      {state.queueStatus && (
        <Card>
          <CardHeader>
            <CardTitle>Queue Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {state.queueStatus.pending}
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-400">Pending</p>
              </div>
              <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {state.queueStatus.running}
                </p>
                <p className="text-sm text-orange-600 dark:text-orange-400">Running</p>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {state.queueStatus.completed}
                </p>
                <p className="text-sm text-green-600 dark:text-green-400">Completed Today</p>
              </div>
            </div>
            {state.queueStatus.estimatedWaitTime > 0 && (
              <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Estimated wait time:</strong>{' '}
                  {Math.ceil(state.queueStatus.estimatedWaitTime / 60)} minutes
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Scan Guidelines</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Before You Start</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Ensure the website is publicly accessible</li>
                <li>• Choose the appropriate WCAG version and level</li>
                <li>• Consider enabling all scan options for comprehensive results</li>
                <li>• Set a reasonable page limit for large sites</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Scan Options</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>
                  • <strong>Contrast:</strong> Color contrast ratio checks
                </li>
                <li>
                  • <strong>Keyboard:</strong> Keyboard navigation testing
                </li>
                <li>
                  • <strong>Focus:</strong> Focus indicator visibility
                </li>
                <li>
                  • <strong>Semantic:</strong> HTML semantic structure
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Main WCAG Scan Page with Provider
 */
export default function WcagScanPage() {
  return (
    <WcagProvider>
      <WcagScanContent />
    </WcagProvider>
  );
}
