/**
 * Quick Database Connection Check
 * Simple script to verify database connectivity without full migration check
 */

import knex from 'knex';

const config = {
  client: 'postgresql',
  connection: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    user: process.env.POSTGRES_USER || 'complyuser',
    password: process.env.POSTGRES_PASSWORD || 'complypassword',
    database: process.env.POSTGRES_DB || 'complychecker_dev',
  },
  pool: {
    min: 2,
    max: 10,
  },
};

async function quickDbCheck() {
  const db = knex(config);

  try {
    console.log('🔍 Testing database connection...');
    
    // Test basic connectivity
    await db.raw('SELECT 1 as test');
    console.log('✅ Database connection successful');

    // Check if WCAG tables exist
    const wcagTablesExist = await db.schema.hasTable('wcag_scans');
    console.log(`📊 WCAG tables exist: ${wcagTablesExist ? '✅ Yes' : '❌ No'}`);

    if (wcagTablesExist) {
      // Check table structure
      const tables = [
        'wcag_scans',
        'wcag_automated_results',
        'wcag_manual_reviews',
        'wcag_contrast_analysis',
        'wcag_focus_analysis',
        'wcag_keyboard_analysis',
      ];

      console.log('\n📋 WCAG Table Status:');
      for (const table of tables) {
        const exists = await db.schema.hasTable(table);
        console.log(`  ${exists ? '✅' : '❌'} ${table}`);
      }

      // Check for enhanced columns
      const hasEnhancedColumns = await db.schema.hasColumn('wcag_automated_results', 'total_element_count');
      console.log(`\n🔧 Enhanced evidence columns: ${hasEnhancedColumns ? '✅ Present' : '❌ Missing'}`);
    }

    console.log('\n🎉 Database is ready for WCAG operations!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        console.log('\n💡 Suggestion: Start Docker services with: docker-compose up -d postgres');
      } else if (error.message.includes('authentication failed')) {
        console.log('\n💡 Suggestion: Check your .env file database credentials');
      }
    }
  } finally {
    await db.destroy();
  }
}

quickDbCheck();
