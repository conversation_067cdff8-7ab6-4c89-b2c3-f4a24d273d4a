/**
 * Direct Database Test
 * Tests evidence retrieval directly from the database
 */

const { Pool } = require('pg');

async function testDirectDatabase() {
  const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'comply_checker',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    console.log('🔍 Testing direct database connection...');

    // Test connection
    const client = await pool.connect();
    console.log('✅ Database connected successfully');

    // Check if wcag_scans table exists
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE 'wcag%'
    `);
    
    console.log('📋 WCAG tables found:', tablesResult.rows.map(r => r.table_name));

    // Get scan count
    const scanCountResult = await client.query('SELECT COUNT(*) as count FROM wcag_scans');
    console.log(`📊 Total scans: ${scanCountResult.rows[0].count}`);

    if (scanCountResult.rows[0].count > 0) {
      // Get a sample scan
      const sampleScanResult = await client.query(`
        SELECT id, target_url, scan_status 
        FROM wcag_scans 
        LIMIT 1
      `);
      
      const sampleScan = sampleScanResult.rows[0];
      console.log(`🔍 Sample scan: ${sampleScan.id} - ${sampleScan.target_url}`);

      // Get automated results for this scan
      const resultsQuery = await client.query(`
        SELECT rule_id, rule_name, status, details, 
               total_element_count, affected_selectors, fix_examples
        FROM wcag_automated_results 
        WHERE scan_id = $1
        LIMIT 5
      `, [sampleScan.id]);

      console.log(`📊 Automated results: ${resultsQuery.rows.length}`);

      for (const result of resultsQuery.rows) {
        console.log(`\n🔍 Rule: ${result.rule_id} (${result.rule_name})`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Element count: ${result.total_element_count}`);
        
        if (result.details) {
          try {
            const details = JSON.parse(result.details);
            console.log(`   Evidence items: ${details.evidence ? details.evidence.length : 0}`);
            if (details.evidence && details.evidence.length > 0) {
              console.log(`   First evidence: ${details.evidence[0].description}`);
            }
          } catch (e) {
            console.log(`   Details parse error: ${e.message}`);
          }
        }
        
        if (result.affected_selectors) {
          try {
            const selectors = JSON.parse(result.affected_selectors);
            console.log(`   Affected selectors: ${selectors.length}`);
          } catch (e) {
            console.log(`   Selectors parse error: ${e.message}`);
          }
        }
      }
    }

    client.release();
    await pool.end();
    console.log('✅ Database test completed');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    await pool.end();
  }
}

testDirectDatabase();
