
[nodemon] 3.1.10
[nodemon] to restart at any time, enter `rs`
[nodemon] watching path(s): src\**\* ..\lib\**\*
[nodemon] watching extensions: ts,json
[nodemon] starting `ts-node -r tsconfig-paths/register src/index.ts`
[ENV_DEBUG] NODE_ENV: development
[ENV_DEBUG] isTestEnv: false
[ENV_DEBUG] process.env.POSTGRES_HOST (raw): localhost
[ENV_DEBUG] parsedEnv.POSTGRES_HOST (from Zod): localhost
[ENV_DEBUG] dbHost chosen: localhost
[ENV_DEBUG] Constructed DATABASE_URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
2025-07-12T06:56:26.851Z [INFO] - 🗄️ Smart cache initialized - {"maxSize":200,"maxEntries":1000,"de faultTTL":14400000,"defaultTTLMinutes":240}
2025-07-12T06:56:27.848Z [DEBUG] - Readability analysis library not available
2025-07-12T06:56:27.856Z [DEBUG] - Language detection library not available
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🔧 Nuclei client initialized with path: D:\Web projects\Comply Checker\tools\nuclei\nuclei.exe
🔧 Templates path: D:\Web projects\Comply Checker\nuclei-templates
🔧 Initializing HIPAA Security Database...
📋 Database URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
📋 Environment: development
🚀 Browser pool initialized: 2 max browsers, 3 max pages per browser
2025-07-12T06:56:42.171Z [DEBUG] - Initialized 5 core accessibility patterns
2025-07-12T06:56:42.177Z [INFO] - 📚 Accessibility Pattern Library initialized - {"totalPatterns":5,"enabledCategories":6,"strictMode":false}
2025-07-12T06:56:42.179Z [INFO] - 🤖 AI-Powered Semantic Validator initialized - {"nlpAnalysis":true,"patternLibrary":true,"contextualAnalysis":true}
2025-07-12T06:56:42.181Z [INFO] - 📊 Enhanced Content Quality Analyzer initialized - {"readabilityAnalysis":true,"semanticAnalysis":true,"languageDetection":true}
2025-07-12T06:56:42.183Z [INFO] - ⚡ Modern Framework Optimizer initialized - {"svelte":true,"solidjs":true,"qwik":true,"buildTools":true}
2025-07-12T06:56:42.185Z [INFO] - 🏗️ Component Library Detector initialized - {"materialUI":true,"a ntDesign":true,"chakraUI":true,"deepAnalysis":true}
2025-07-12T06:56:42.187Z [INFO] - EnhancedColorAnalyzer initialized - {"getContrastLibrary":true,"colorJSLibrary":true,"enhancedAccuracy":true}
2025-07-12T06:56:42.188Z [INFO] - 📄 Headless CMS Detector initialized - {"strapi":true,"contentful":true,"sanity":true,"jamstack":true}
2025-07-12T06:56:42.191Z [INFO] - 🔧 Utility Integration Manager initialized
2025-07-12T06:56:42.193Z [INFO] - 🚀 Enhanced Performance Monitor initialized - {"trendAnalysis":true,"autoOptimization":true,"resourceCorrelation":true,"realTimeAlerts":true}
2025-07-12T06:56:42.197Z [INFO] - 🔗 Performance Integration Bridge initialized
2025-07-12T06:56:42.199Z [INFO] - 📊 Real-Time Monitoring Dashboard initialized
2025-07-12T06:56:42.200Z [INFO] - 🔮 Initialized 4 predictive models
2025-07-12T06:56:42.201Z [INFO] - 🔮 Predictive Performance Analytics initialized
2025-07-12T06:56:42.202Z [INFO] - 🤖 Automated Optimization Engine initialized
2025-07-12T06:56:42.203Z [INFO] - 🔌 Dashboard WebSocket Service initialized
2025-07-12T06:56:42.203Z [INFO] - 🎛️ Performance Automation Controller initialized
2025-07-12T06:56:42.327Z [INFO] - 🌐 Browser pool initialized - {"maxBrowsers":1,"maxPagesPerBrowser":3,"memoryThreshold":2048}
2025-07-12T06:56:42.331Z [INFO] - Detected VPS profile: xlarge - {"cpu":4,"memory":12,"recommendedLimits":{"maxConcurrentScans":12,"maxBrowserInstances":4,"maxPagesPerBrowser":4,"maxCacheSize":400,"maxMemoryUsage":12800,"scanTimeout":60000}}
2025-07-12T06:56:42.344Z [DEBUG] - Worker processes disabled - {"enableWorkerProcesses":false,"isMaster":true,"nodeEnv":"development"}
2025-07-12T06:56:42.348Z [DEBUG] - Connection pools configured - {"maxSockets":50,"keepAlive":true}
[INFO] Registering test-auth routes (development only)
🚀 Starting Comply Checker Backend in development mode
📋 Port: 3001
📋 Setting up API routes...
📋 Attempting to start server on port 3001...
✅ Server started successfully!
🌐 Server running on: http://localhost:3001
🏥 Health check: http://localhost:3001/health
🧪 HIPAA Analysis: http://localhost:3001/api/v1/compliance/scan
📋 Environment: development
🚀 Ready to accept HIPAA compliance requests!
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
User found: {
  id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
  keycloak_id: '4eb85cce-8784-4bf1-b50a-9ce3a80fb67b',
  email: '<EMAIL>',
  created_at: 2025-06-20T10:13:02.039Z,
  updated_at: 2025-06-20T10:13:02.039Z
}
2025-07-12T06:57:30.755Z [INFO] - 🔓 [8652edb2-e820-4a02-ae79-3dd6f6044082] Development mode: Mock authentication applied
2025-07-12T06:57:30.757Z [INFO] - 🚀 [8652edb2-e820-4a02-ae79-3dd6f6044082] WCAG scan request received
2025-07-12T06:57:30.757Z [INFO] - 📋 [8652edb2-e820-4a02-ae79-3dd6f6044082] Request headers: - {"content-type":"application/json","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","authorization":"Bearer [REDACTED]"}
2025-07-12T06:57:30.759Z [INFO] - 👤 [8652edb2-e820-4a02-ae79-3dd6f6044082] User context: - {"userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","email":"<EMAIL>","permissions":["wcag:scan","wcag:view","wcag:export"]}
2025-07-12T06:57:30.760Z [INFO] - 🎯 [8652edb2-e820-4a02-ae79-3dd6f6044082] Target URL: https://tigerconnect.com/
2025-07-12T06:57:30.761Z [INFO] - ⚙️ [8652edb2-e820-4a02-ae79-3dd6f6044082] Scan options: - {"enableContrastAnalysis":true,"enableKeyboardTesting":true,"enableFocusAnalysis":true,"enableSemanticValidation":true,"enableManualReview":true,"wcagVersion":"all","level":"AAA","maxPages":5,"timeout":60000}
2025-07-12T06:57:30.762Z [INFO] - ✅ [8652edb2-e820-4a02-ae79-3dd6f6044082] Target URL validation passed
2025-07-12T06:57:30.764Z [INFO] - 🔧 [8652edb2-e820-4a02-ae79-3dd6f6044082] Scan configuration created: - {"targetUrl":"https://tigerconnect.com/","userId":"9fed30a7-64b4-4ffe-b531-6e9cf592952b","level":"AAA","timeout":60000}
2025-07-12T06:57:30.765Z [INFO] - 🚀 [8652edb2-e820-4a02-ae79-3dd6f6044082] Starting comprehensive WCAG scan with orchestrator
2025-07-12T06:57:30.768Z [INFO] - 📝 Creating WCAG scan record...
2025-07-12T06:57:30.768Z [INFO] - 🆔 [8652edb2-e820-4a02-ae79-3dd6f6044082] Scan initiated with ID: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.769Z [INFO] - 📊 [8652edb2-e820-4a02-ae79-3dd6f6044082] Scan started successfully: - {"scanId":"ed85e707-19e7-4046-913e-301608345e71","status":"pending","targetUrl":"https://tigerconnect.com/"}
2025-07-12T06:57:30.770Z [INFO] - ✅ [8652edb2-e820-4a02-ae79-3dd6f6044082] WCAG scan initiated successfully in 13ms
2025-07-12T06:57:30.780Z [INFO] - ✅ WCAG scan record created with ID: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.782Z [INFO] - ✅ WCAG scan record created with ID: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.785Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> pending
2025-07-12T06:57:30.791Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> pending
2025-07-12T06:57:30.791Z [INFO] - 🚀 Initialized scan ed85e707-19e7-4046-913e-301608345e71 with 66 rules
2025-07-12T06:57:30.792Z [DEBUG] - 📊 Started performance monitoring for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.793Z [DEBUG] - 📊 Started performance monitoring for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.796Z [DEBUG] - 📊 Started performance monitoring for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.797Z [DEBUG] - 🔗 Integrated monitoring started for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:30.799Z [DEBUG] - 📊 Registered active scan: ed85e707-19e7-4046-913e-301608345e71 (total: 1)
2025-07-12T06:57:30.800Z [INFO] - 🎛️ Starting Performance Automation System...
2025-07-12T06:57:30.803Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T06:57:30.803Z [INFO] - 📊 Real-Time Monitoring Dashboard started
2025-07-12T06:57:30.805Z [INFO] - ✅ Dashboard monitoring started
2025-07-12T06:57:30.808Z [INFO] - 🔌 Dashboard WebSocket Service started on port 8081
2025-07-12T06:57:30.810Z [INFO] - ✅ WebSocket service started
2025-07-12T06:57:30.813Z [INFO] - 🔮 Prediction generated: cache_efficiency (90% confidence)
2025-07-12T06:57:30.814Z [WARN] - 🤖 Proactive alert: cache_efficiency (high)
2025-07-12T06:57:30.816Z [WARN] - 🚨 Proactive alert: cache_efficiency (high)
2025-07-12T06:57:30.817Z [DEBUG] - 🔮 Generated 1 predictions, 0 recommendations
2025-07-12T06:57:30.818Z [INFO] - 🔮 Predictive Performance Analytics started
2025-07-12T06:57:30.819Z [INFO] - ✅ Predictive analytics started
2025-07-12T06:57:30.821Z [INFO] - 🎛️ Performance Automation System fully operational
2025-07-12T06:57:30.821Z [INFO] - 🎛️ Started Performance Automation Controller for scan monitoring
2025-07-12T06:57:30.822Z [DEBUG] - 🔍 Getting page for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:32.662Z [INFO] - ✅ Created new browser: browser-1752303452662-r24n1j6da
2025-07-12T06:57:33.078Z [DEBUG] - 🆕 Created new page for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:33.090Z [DEBUG] - ✅ URL validation passed for: https://tigerconnect.com/
2025-07-12T06:57:33.093Z [DEBUG] - 🔍 Checking connectivity for: https://tigerconnect.com/
2025-07-12T06:57:34.761Z [DEBUG] - ✅ Connectivity check passed for: https://tigerconnect.com/ (status: 200)
2025-07-12T06:57:34.762Z [INFO] - 🔗 Navigating to: https://tigerconnect.com/ (timeout: 60000ms)
2025-07-12T06:57:35.804Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T06:57:40.395Z [DEBUG] - 📝 Registered session: ed85e707-19e7-4046-913e-301608345e71:https://tigerconnect.com/
2025-07-12T06:57:40.398Z [DEBUG] - 📝 Registered session for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:40.400Z [INFO] - ✅ Navigation completed
2025-07-12T06:57:40.403Z [INFO] - 🚀 Starting Phase 2 analysis: Website type detection and optimization
2025-07-12T06:57:40.405Z [DEBUG] - 🔍 Starting CMS platform detection
2025-07-12T06:57:40.412Z [DEBUG] - 🛒 Starting e-commerce accessibility analysis
2025-07-12T06:57:40.419Z [DEBUG] - 🎬 Starting media accessibility analysis
2025-07-12T06:57:40.424Z [DEBUG] - ⚛️ Starting framework-specific accessibility analysis
2025-07-12T06:57:40.453Z [WARN] - ⚠️ Phase 2 analysis encountered errors, continuing with standard analysis - {"error":"CMS detection failed - no results returned"}
2025-07-12T06:57:40.455Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:40.495Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:40.496Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 0% (0/66)
2025-07-12T06:57:40.499Z [INFO] - 🔍 Executing 66 WCAG checks...
2025-07-12T06:57:40.500Z [INFO] - 🔥 Pre-warming cache for WCAG scan...
2025-07-12T06:57:40.501Z [INFO] - 🔥 Pre-warming cache for WCAG scan: https://tigerconnect.com/
2025-07-12T06:57:40.502Z [INFO] - 🔥 Warming up cache with 1 URLs
2025-07-12T06:57:40.515Z [DEBUG] - 💾 Cache saved to file: bebee4be960508b81bf52717657b8895.json (key: https://tigerconnect.com/:ce50...)
2025-07-12T06:57:40.516Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:ce50a093 (62 bytes)
2025-07-12T06:57:40.524Z [DEBUG] - 💾 Cache saved to file: 95f19ad0cce33ee263801de200e8a74a.json (key: https://tigerconnect.com/:a43c...)
2025-07-12T06:57:40.526Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a43c1b0a (61 bytes)
2025-07-12T06:57:40.531Z [DEBUG] - 💾 Cache saved to file: 534c19df0f92f221836ddb58a770c182.json (key: https://tigerconnect.com/:0cc1...)
2025-07-12T06:57:40.533Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:0cc175b9 (57 bytes)
2025-07-12T06:57:40.538Z [DEBUG] - 💾 Cache saved to file: e5d81c1831723c1a497c41a53662ed26.json (key: https://tigerconnect.com/:b798...)
2025-07-12T06:57:40.539Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:b798abe6 (59 bytes)
2025-07-12T06:57:40.547Z [DEBUG] - 💾 Cache saved to file: 663792f6de7c4bd08e95ed6047b0752f.json (key: https://tigerconnect.com/:3fcd...)
2025-07-12T06:57:40.548Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3fcdb73d (60 bytes)
2025-07-12T06:57:40.551Z [DEBUG] - 💾 Cache saved to file: 0a43342a413174a1d2603306ccf44deb.json (key: https://tigerconnect.com/:d72e...)
2025-07-12T06:57:40.552Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d72e5ee9 (59 bytes)
2025-07-12T06:57:40.559Z [DEBUG] - 💾 Cache saved to file: 35c8746de252dfe5065a332b43442ab3.json (key: https://tigerconnect.com/:fad5...)
2025-07-12T06:57:40.559Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:fad58de7 (60 bytes)
2025-07-12T06:57:40.562Z [DEBUG] - 💾 Cache saved to file: f1f8620c79b22a3449d8feaceb632cfc.json (key: https://tigerconnect.com/:099f...)
2025-07-12T06:57:40.563Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:099fb995 (62 bytes)
2025-07-12T06:57:40.569Z [DEBUG] - 💾 Cache saved to file: 1a8ca4ce0660cb82f14b79daae26c351.json (key: https://tigerconnect.com/:251d...)
2025-07-12T06:57:40.570Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:251d1646 (62 bytes)
2025-07-12T06:57:40.574Z [DEBUG] - 💾 Cache saved to file: a5521ee36e71b2c9433c715f93408670.json (key: https://tigerconnect.com/:3f38...)
2025-07-12T06:57:40.575Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3f3852a9 (73 bytes)
2025-07-12T06:57:40.578Z [DEBUG] - 💾 Cache saved to file: 712629e01944fb3e69bd1e0f8c42ff5a.json (key: https://tigerconnect.com/:14e1...)
2025-07-12T06:57:40.579Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:14e1a17f (71 bytes)
2025-07-12T06:57:40.582Z [DEBUG] - 💾 Cache saved to file: bd8bd7fbd915dbba54e7f054fc53f744.json (key: https://tigerconnect.com/:2e71...)
2025-07-12T06:57:40.583Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:2e716448 (68 bytes)
2025-07-12T06:57:40.588Z [DEBUG] - 💾 Cache saved to file: ecec730c5f49a267feabefa9cd405473.json (key: https://tigerconnect.com/:cacf...)
2025-07-12T06:57:40.588Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:cacf55f1 (61 bytes)
2025-07-12T06:57:40.592Z [DEBUG] - 💾 Cache saved to file: 28d51e63f13fde14df4973c59c70fc6c.json (key: https://tigerconnect.com/:af04...)
2025-07-12T06:57:40.593Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:af04abc2 (66 bytes)
2025-07-12T06:57:40.595Z [DEBUG] - 💾 Cache saved to file: afcddacf2eb740820a22ed45014963ec.json (key: https://tigerconnect.com/:346b...)
2025-07-12T06:57:40.597Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:346b81a3 (58 bytes)
2025-07-12T06:57:40.603Z [DEBUG] - 💾 Cache saved to file: e5f6dfa7d9f997dcad6a7e1fdca8e5b5.json (key: https://tigerconnect.com/:490b...)
2025-07-12T06:57:40.603Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:490b2834 (58 bytes)
2025-07-12T06:57:40.608Z [DEBUG] - 💾 Cache saved to file: e1fadcb2de37bd42402f1408934ee8fa.json (key: https://tigerconnect.com/:6f20...)
2025-07-12T06:57:40.609Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:6f207f8b (58 bytes)
2025-07-12T06:57:40.613Z [DEBUG] - 💾 Cache saved to file: 4b977d1289dde40c75e5b724b0e5ddab.json (key: https://tigerconnect.com/:ce1b...)
2025-07-12T06:57:40.614Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:ce1b1e8c (58 bytes)
2025-07-12T06:57:40.617Z [DEBUG] - 💾 Cache saved to file: 8dd69209d8df0c5749198f87713c0c77.json (key: https://tigerconnect.com/:7723...)
2025-07-12T06:57:40.618Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:77230e94 (58 bytes)
2025-07-12T06:57:40.621Z [DEBUG] - 💾 Cache saved to file: bf790dbb37199cfaa73dde1b45b28211.json (key: https://tigerconnect.com/:d854...)
2025-07-12T06:57:40.622Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d8544ba1 (58 bytes)
2025-07-12T06:57:40.625Z [DEBUG] - 💾 Cache saved to file: b98a00e39bad8aaebc681168fb4179bd.json (key: https://tigerconnect.com/:73d5...)
2025-07-12T06:57:40.626Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:73d5342e (63 bytes)
2025-07-12T06:57:40.633Z [DEBUG] - 💾 Cache saved to file: b123149d68166737fedff453406f1877.json (key: https://tigerconnect.com/:92a2...)
2025-07-12T06:57:40.633Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:92a2b5cb (63 bytes)
2025-07-12T06:57:40.637Z [DEBUG] - 💾 Cache saved to file: 7b4bdc44c78075314d0a544b17c078b1.json (key: https://tigerconnect.com/:421b...)
2025-07-12T06:57:40.638Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:421b47ff (61 bytes)
2025-07-12T06:57:40.643Z [DEBUG] - 💾 Cache saved to file: c3a1426057f2930eac9f74423b62d8f0.json (key: https://tigerconnect.com/:a5ca...)
2025-07-12T06:57:40.644Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a5ca0b58 (61 bytes)
2025-07-12T06:57:40.648Z [DEBUG] - 💾 Cache saved to file: c4cf726155115372e8af6959ac180908.json (key: https://tigerconnect.com/:a598...)
2025-07-12T06:57:40.648Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a598e4f2 (62 bytes)
2025-07-12T06:57:40.651Z [DEBUG] - 💾 Cache saved to file: 5c4d62c175a69c1e9d3911b51ec38373.json (key: https://tigerconnect.com/:a8cf...)
2025-07-12T06:57:40.652Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:a8cfde63 (62 bytes)
2025-07-12T06:57:40.655Z [DEBUG] - 💾 Cache saved to file: bd000830ab45a8d7cdac2f207437912b.json (key: https://tigerconnect.com/:2696...)
2025-07-12T06:57:40.655Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:269605d4 (61 bytes)
2025-07-12T06:57:40.660Z [DEBUG] - 💾 Cache saved to file: 89160a289dabf8718c30977330411c01.json (key: https://tigerconnect.com/:d304...)
2025-07-12T06:57:40.662Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:d304ba20 (61 bytes)
2025-07-12T06:57:40.666Z [DEBUG] - 💾 Cache saved to file: da1cd83794fb9244cb67a6f4e4bf629c.json (key: https://tigerconnect.com/:6396...)
2025-07-12T06:57:40.666Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:639612e6 (64 bytes)
2025-07-12T06:57:40.669Z [DEBUG] - 💾 Cache saved to file: ef36f0c11a1d6cd780326394e9ce2933.json (key: https://tigerconnect.com/:3d4d...)
2025-07-12T06:57:40.669Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:3d4dcd6f (62 bytes)
2025-07-12T06:57:40.673Z [DEBUG] - 💾 Cache saved to file: 38cdf473277f920fa85ca5f9f4f2289d.json (key: https://tigerconnect.com/:9993...)
2025-07-12T06:57:40.674Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:99938282 (62 bytes)
2025-07-12T06:57:40.680Z [DEBUG] - 💾 Cache saved to file: 36a1e272427f69e6d72075e2e281f974.json (key: https://tigerconnect.com/:6394...)
2025-07-12T06:57:40.681Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:6394d816 (64 bytes)
2025-07-12T06:57:40.683Z [DEBUG] - 💾 Cache saved to file: 855230df3153f47bb32a65b46f01fe64.json (key: https://tigerconnect.com/:aab9...)
2025-07-12T06:57:40.684Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:aab9e1de (61 bytes)
2025-07-12T06:57:40.687Z [DEBUG] - 💾 Cache saved to file: 96925a000e150d4777fee5b1efdb0276.json (key: https://tigerconnect.com/:1fdc...)
2025-07-12T06:57:40.688Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:1fdc0f89 (58 bytes)
2025-07-12T06:57:40.693Z [DEBUG] - 💾 Cache saved to file: 14ad258288e72d4a6b61f0140e6d7d49.json (key: https://tigerconnect.com/:6267...)
2025-07-12T06:57:40.693Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:626726e6 (58 bytes)
2025-07-12T06:57:40.696Z [DEBUG] - 💾 Cache saved to file: 02ae13ea624f7e894ae36e7f362be6a7.json (key: https://tigerconnect.com/:7a7d...)
2025-07-12T06:57:40.697Z [DEBUG] - 💾 Cached: dom:https://tigerconnect.com/:7a7dc1cd (63 bytes)
2025-07-12T06:57:40.700Z [DEBUG] - 💾 Cache saved to file: ad72b5a662be888865387d8391c79d1b.json (key: https://tigerconnect.com/:imag...)
2025-07-12T06:57:40.700Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:image-analysis (82 bytes)
2025-07-12T06:57:40.703Z [DEBUG] - 💾 Cache saved to file: 8cab73c21d5b2e5d804f9a01f886d1fa.json (key: https://tigerconnect.com/:link...)
2025-07-12T06:57:40.703Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:link-analysis (81 bytes)
2025-07-12T06:57:40.706Z [DEBUG] - 💾 Cache saved to file: b5ef386e77eb6386a09f2f179305d2f3.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:57:40.706Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis (81 bytes)
2025-07-12T06:57:40.710Z [DEBUG] - 💾 Cache saved to file: 20ab0109264dc706ee7cc1921f847428.json (key: https://tigerconnect.com/:head...)
2025-07-12T06:57:40.711Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:heading-analysis (84 bytes)
2025-07-12T06:57:40.714Z [DEBUG] - 💾 Cache saved to file: 088fc800f61cab6c26bb949d47942450.json (key: https://tigerconnect.com/:colo...)
2025-07-12T06:57:40.714Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:color-contrast (82 bytes)
2025-07-12T06:57:40.717Z [DEBUG] - 💾 Cache saved to file: e16102238ff5e7451282d7e044253334.json (key: https://tigerconnect.com/:focu...)
2025-07-12T06:57:40.717Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:focus-management (84 bytes)
2025-07-12T06:57:40.720Z [DEBUG] - 💾 Cache saved to file: de769a9b5f84a8f90831e7232e232037.json (key: https://tigerconnect.com/:keyb...)
2025-07-12T06:57:40.720Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:keyboard-navigation (87 bytes)
2025-07-12T06:57:40.723Z [DEBUG] - 💾 Cache saved to file: 463c8f0eb64ca4ba49b84deb80487cb0.json (key: https://tigerconnect.com/:aria...)
2025-07-12T06:57:40.723Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:aria-validation (83 bytes)
2025-07-12T06:57:40.725Z [DEBUG] - 🔥 Warmed up cache for: https://tigerconnect.com/
2025-07-12T06:57:40.727Z [INFO] - 🔥 Cache warming completed for 1 URLs
2025-07-12T06:57:40.729Z [DEBUG] - 🔥 Pre-warming rule-specific caches for: https://tigerconnect.com/
2025-07-12T06:57:40.731Z [DEBUG] - 💾 Cache saved to file: 0c680842e4e43e2b60ed5369f3762039.json (key: WCAG-001:053b13d2:7e800487...)
2025-07-12T06:57:40.731Z [DEBUG] - 💾 Cached: rule:WCAG-001:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.734Z [DEBUG] - 💾 Cache saved to file: a75e35dd209843948b36c84329d22a7d.json (key: WCAG-002:053b13d2:7e800487...)
2025-07-12T06:57:40.734Z [DEBUG] - 💾 Cached: rule:WCAG-002:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.736Z [DEBUG] - 💾 Cache saved to file: 9cb4dc32d5df423f385c05f9b3176ed5.json (key: WCAG-003:053b13d2:7e800487...)
2025-07-12T06:57:40.737Z [DEBUG] - 💾 Cached: rule:WCAG-003:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.740Z [DEBUG] - 💾 Cache saved to file: 6a3ba030ed4f6bdd1430c84c076923c4.json (key: WCAG-004:053b13d2:7e800487...)
2025-07-12T06:57:40.742Z [DEBUG] - 💾 Cached: rule:WCAG-004:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.744Z [DEBUG] - 💾 Cache saved to file: 982de888cf5260e212204a91473b1660.json (key: WCAG-005:053b13d2:7e800487...)
2025-07-12T06:57:40.744Z [DEBUG] - 💾 Cached: rule:WCAG-005:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.746Z [DEBUG] - 💾 Cache saved to file: e2c4ec65380b1d83c294ae7167544945.json (key: WCAG-006:053b13d2:7e800487...)
2025-07-12T06:57:40.747Z [DEBUG] - 💾 Cached: rule:WCAG-006:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.749Z [DEBUG] - 💾 Cache saved to file: 44542e0fa17221195424bce0cb661872.json (key: WCAG-007:053b13d2:7e800487...)
2025-07-12T06:57:40.750Z [DEBUG] - 💾 Cached: rule:WCAG-007:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.752Z [DEBUG] - 💾 Cache saved to file: 088a0703edaba86c5235f3435f5dd24a.json (key: WCAG-008:053b13d2:7e800487...)
2025-07-12T06:57:40.752Z [DEBUG] - 💾 Cached: rule:WCAG-008:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.755Z [DEBUG] - 💾 Cache saved to file: b0e2c61f5c13d8949e43d1312f7b01b6.json (key: WCAG-009:053b13d2:7e800487...)
2025-07-12T06:57:40.757Z [DEBUG] - 💾 Cached: rule:WCAG-009:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.760Z [DEBUG] - 💾 Cache saved to file: 00004cdfca760e473a4e6b26f7b6b994.json (key: WCAG-010:053b13d2:7e800487...)
2025-07-12T06:57:40.760Z [DEBUG] - 💾 Cached: rule:WCAG-010:053b13d2:7e800487 (61 bytes)
2025-07-12T06:57:40.762Z [DEBUG] - 💾 Cache saved to file: 7fcabdbedc224ce24e39fafab2ef7405.json (key: https://tigerconnect.com/:sema...)
2025-07-12T06:57:40.763Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:semantic-validation (112 bytes)
2025-07-12T06:57:40.766Z [DEBUG] - 💾 Cache saved to file: 94a1fb81d4021b5adcf4359192244915.json (key: https://tigerconnect.com/:cms-...)
2025-07-12T06:57:40.766Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:cms-analysis (105 bytes)
2025-07-12T06:57:40.769Z [DEBUG] - 💾 Cache saved to file: 4b801483243e34625c13b8bedfdbbda8.json (key: https://tigerconnect.com/:enha...)
2025-07-12T06:57:40.771Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:enhanced-contrast (110 bytes)
2025-07-12T06:57:40.775Z [DEBUG] - 💾 Cache saved to file: b5ef386e77eb6386a09f2f179305d2f3.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:57:40.776Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis (106 bytes)
2025-07-12T06:57:40.778Z [DEBUG] - 💾 Cache saved to file: ad72b5a662be888865387d8391c79d1b.json (key: https://tigerconnect.com/:imag...)
2025-07-12T06:57:40.779Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:image-analysis (107 bytes)
2025-07-12T06:57:40.781Z [DEBUG] - 💾 Cache saved to file: 8cab73c21d5b2e5d804f9a01f886d1fa.json (key: https://tigerconnect.com/:link...)
2025-07-12T06:57:40.781Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:link-analysis (106 bytes)
2025-07-12T06:57:40.784Z [DEBUG] - 💾 Cache saved to file: 20ab0109264dc706ee7cc1921f847428.json (key: https://tigerconnect.com/:head...)
2025-07-12T06:57:40.786Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:heading-analysis (109 bytes)
2025-07-12T06:57:40.790Z [DEBUG] - 💾 Cache saved to file: 088fc800f61cab6c26bb949d47942450.json (key: https://tigerconnect.com/:colo...)
2025-07-12T06:57:40.790Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:color-contrast (107 bytes)
2025-07-12T06:57:40.793Z [DEBUG] - 💾 Cache saved to file: e16102238ff5e7451282d7e044253334.json (key: https://tigerconnect.com/:focu...)
2025-07-12T06:57:40.793Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:focus-management (109 bytes)
2025-07-12T06:57:40.797Z [DEBUG] - 💾 Cache saved to file: de769a9b5f84a8f90831e7232e232037.json (key: https://tigerconnect.com/:keyb...)
2025-07-12T06:57:40.797Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:keyboard-navigation (112 bytes)
2025-07-12T06:57:40.800Z [DEBUG] - 💾 Cache saved to file: 463c8f0eb64ca4ba49b84deb80487cb0.json (key: https://tigerconnect.com/:aria...)
2025-07-12T06:57:40.802Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:aria-validation (108 bytes)
2025-07-12T06:57:40.803Z [DEBUG] - ✅ Rule-specific cache warming completed for 10 rules and 11 analysis types
2025-07-12T06:57:40.804Z [INFO] - ✅ Cache pre-warming completed for: https://tigerconnect.com/
2025-07-12T06:57:40.805Z [INFO] - ✅ Cache pre-warming completed in 305ms
2025-07-12T06:57:40.806Z [INFO] - 📋 Extracting unified page structure for all checks...
2025-07-12T06:57:40.809Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"0.0%","alerts":0}
2025-07-12T06:57:40.827Z [DEBUG] - 📁 Cache file valid: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (166min remaining)
2025-07-12T06:57:40.827Z [DEBUG] - 📁 Cache loaded from file: 90bcb3e53c2a6e023e1ce985ebd4e9a7.json (key: dom-structure:https://tigercon...)
2025-07-12T06:57:40.828Z [DEBUG] - 📁 Restored from file cache: dom:dom-structure:https://tigerconnect.com/:053b13d2...
2025-07-12T06:57:40.829Z [DEBUG] - ✅ Cache hit: dom:dom-structure:https://tigerconnect.com/:053b13d2... (accessed 2 times, age: 4468s)
2025-07-12T06:57:40.830Z [DEBUG] - 📋 Using cached page structure (persistent)
2025-07-12T06:57:40.832Z [INFO] - 📋 Page structure extracted successfully: 761 elements found
2025-07-12T06:57:40.834Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:40.838Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:40.838Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 0% (0/66)
2025-07-12T06:57:40.839Z [INFO] - 🔍 Executing rule: Non-text Content (WCAG-001)
2025-07-12T06:57:40.841Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-001: Non-text Content
2025-07-12T06:57:40.843Z [DEBUG] - 📁 Cache file valid: b1c2386f72a4e403f0a9afccef14ee51.json (166min remaining)
2025-07-12T06:57:40.844Z [DEBUG] - 📁 Cache loaded from file: b1c2386f72a4e403f0a9afccef14ee51.json (key: WCAG-001:053b13d2:add92319...)
2025-07-12T06:57:40.845Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-001:053b13d2:add92319...
2025-07-12T06:57:40.848Z [DEBUG] - ✅ Cache hit: rule:WCAG-001:053b13d2:add92319... (accessed 2 times, age: 4468s)
2025-07-12T06:57:40.850Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-001
2025-07-12T06:57:40.853Z [DEBUG] - 📁 Cache file valid: 6bccbd724edc1ba02226d2db65245661.json (166min remaining)
2025-07-12T06:57:40.853Z [DEBUG] - 📁 Cache loaded from file: 6bccbd724edc1ba02226d2db65245661.json (key: WCAG-001:WCAG-001...)
2025-07-12T06:57:40.854Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-001:WCAG-001...
2025-07-12T06:57:40.855Z [DEBUG] - ✅ Cache hit: rule:WCAG-001:WCAG-001... (accessed 2 times, age: 4468s)
2025-07-12T06:57:40.856Z [DEBUG] - 📋 Using cached evidence for WCAG-001
2025-07-12T06:57:40.858Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-001 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:40.859Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:40.864Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:40.866Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:40.868Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:40.870Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:40.878Z [DEBUG] - 📁 Cache file valid: 80e3a546bf63e5f250cbac2e49f14df2.json (166min remaining)
2025-07-12T06:57:40.878Z [DEBUG] - 📁 Cache loaded from file: 80e3a546bf63e5f250cbac2e49f14df2.json (key: https://tigerconnect.com/:sema...)
2025-07-12T06:57:40.879Z [DEBUG] - 📁 Restored from file cache: site:https://tigerconnect.com/:semantic-validation-{"va...
2025-07-12T06:57:40.881Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 2 times, age: 4468s)
2025-07-12T06:57:40.882Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:40.883Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:40.884Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:40.886Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:40.887Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:40.929Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:40.948Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:41.021Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":1,"validPatterns":0,"overallScore":0}
2025-07-12T06:57:41.022Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-001 - {"utilitiesUsed":["content-quality","semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8999999999999999,"executionTime":164}
2025-07-12T06:57:41.027Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:41.028Z [DEBUG] - 🔧 Utility performance recorded for WCAG-001: - {"utilitiesUsed":5,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:41.028Z [DEBUG] - 🔧 Utility analysis completed for WCAG-001: - {"utilitiesUsed":5,"errors":0,"executionTime":187}
2025-07-12T06:57:41.030Z [DEBUG] - ⏱️ Check WCAG-001 completed in 190ms (success: true)
2025-07-12T06:57:41.030Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.035Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.036Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 2% (1/66)
2025-07-12T06:57:41.037Z [INFO] - ✅ Rule WCAG-001 completed: failed (0/100)
2025-07-12T06:57:41.037Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.043Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.045Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 2% (1/66)
2025-07-12T06:57:41.046Z [INFO] - 🔍 Executing rule: Captions (Prerecorded) (WCAG-002)
2025-07-12T06:57:41.048Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-002: Captions
2025-07-12T06:57:41.050Z [DEBUG] - 📁 Cache file valid: b5fceffd89ea9b0940a3056d55e299b1.json (166min remaining)
2025-07-12T06:57:41.051Z [DEBUG] - 📁 Cache loaded from file: b5fceffd89ea9b0940a3056d55e299b1.json (key: WCAG-002:053b13d2:add92319...)
2025-07-12T06:57:41.052Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-002:053b13d2:add92319...
2025-07-12T06:57:41.052Z [DEBUG] - ✅ Cache hit: rule:WCAG-002:053b13d2:add92319... (accessed 2 times, age: 4468s)
2025-07-12T06:57:41.053Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-002
2025-07-12T06:57:41.056Z [DEBUG] - 📁 Cache file valid: adad9a0534e9436bc170dbd5c01b3603.json (166min remaining)
2025-07-12T06:57:41.056Z [DEBUG] - 📁 Cache loaded from file: adad9a0534e9436bc170dbd5c01b3603.json (key: WCAG-002:WCAG-002...)
2025-07-12T06:57:41.057Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-002:WCAG-002...
2025-07-12T06:57:41.062Z [DEBUG] - ✅ Cache hit: rule:WCAG-002:WCAG-002... (accessed 2 times, age: 4468s)
2025-07-12T06:57:41.062Z [DEBUG] - 📋 Using cached evidence for WCAG-002
2025-07-12T06:57:41.064Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-002 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"enhance","maxExecutionTime":8000}}
2025-07-12T06:57:41.065Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:41.066Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 3 times, age: 4468s)
2025-07-12T06:57:41.067Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:41.069Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:41.069Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 2 times, age: 0s)
2025-07-12T06:57:41.071Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:41.072Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:41.073Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:41.075Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:41.076Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:41.077Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:41.105Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:41.124Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:41.128Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-002 - {"utilitiesUsed":["cms-detection","content-quality","semantic-validation"],"confidence":0.8,"accuracy":0.35,"executionTime":64}
2025-07-12T06:57:41.128Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-002: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:41.129Z [DEBUG] - 🔧 Utility performance recorded for WCAG-002: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:41.130Z [DEBUG] - 🔧 Utility analysis completed for WCAG-002: - {"utilitiesUsed":3,"errors":0,"executionTime":82}
2025-07-12T06:57:41.131Z [DEBUG] - ⏱️ Check WCAG-002 completed in 85ms (success: true)
2025-07-12T06:57:41.132Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.136Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.137Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 3% (2/66)
2025-07-12T06:57:41.138Z [INFO] - ✅ Rule WCAG-002 completed: failed (0/100)
2025-07-12T06:57:41.138Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.144Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.145Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 3% (2/66)
2025-07-12T06:57:41.146Z [INFO] - 🔍 Executing rule: Info and Relationships (WCAG-003)
2025-07-12T06:57:41.147Z [INFO] - 🌈 Wide Gamut Color Analyzer initialized - {"p3Analysis":true,"rec2020Analysis":true,"dynamicMonitoring":true}
2025-07-12T06:57:41.148Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-003: Info and Relationships
2025-07-12T06:57:41.151Z [DEBUG] - 📁 Cache file valid: 6b51c453280c02d19238652f55728191.json (166min remaining)
2025-07-12T06:57:41.151Z [DEBUG] - 📁 Cache loaded from file: 6b51c453280c02d19238652f55728191.json (key: WCAG-003:053b13d2:add92319...)
2025-07-12T06:57:41.152Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-003:053b13d2:add92319...
2025-07-12T06:57:41.152Z [DEBUG] - ✅ Cache hit: rule:WCAG-003:053b13d2:add92319... (accessed 2 times, age: 4466s)
2025-07-12T06:57:41.153Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-003
2025-07-12T06:57:41.158Z [DEBUG] - 📁 Cache file valid: a75e90580e5662e79e418867b28e1836.json (166min remaining)
2025-07-12T06:57:41.158Z [DEBUG] - 📁 Cache loaded from file: a75e90580e5662e79e418867b28e1836.json (key: WCAG-003:WCAG-003...)
2025-07-12T06:57:41.159Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-003:WCAG-003...
2025-07-12T06:57:41.160Z [DEBUG] - ✅ Cache hit: rule:WCAG-003:WCAG-003... (accessed 2 times, age: 4466s)
2025-07-12T06:57:41.161Z [DEBUG] - 📋 Using cached evidence for WCAG-003
2025-07-12T06:57:41.161Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-003 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableContentQualityAnalysis":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:41.162Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:41.163Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 4 times, age: 4468s)
2025-07-12T06:57:41.163Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:41.165Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:41.166Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:41.168Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:41.168Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:41.170Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:41.176Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:41.177Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:41.202Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:41.225Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:41.243Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:41.243Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-003 - {"utilitiesUsed":["content-quality","semantic-validation","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.7,"executionTime":82}
2025-07-12T06:57:41.244Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:41.245Z [DEBUG] - 🔧 Utility performance recorded for WCAG-003: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:41.246Z [DEBUG] - 🔧 Utility analysis completed for WCAG-003: - {"utilitiesUsed":4,"errors":0,"executionTime":98}
2025-07-12T06:57:41.248Z [DEBUG] - ⏱️ Check WCAG-003 completed in 102ms (success: true)
2025-07-12T06:57:41.250Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.255Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.255Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 5% (3/66)
2025-07-12T06:57:41.256Z [INFO] - ✅ Rule WCAG-003 completed: failed (0/100)
2025-07-12T06:57:41.257Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.262Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.263Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 5% (3/66)
2025-07-12T06:57:41.263Z [INFO] - 🔍 Executing rule: Contrast (Minimum) (WCAG-004)
2025-07-12T06:57:41.266Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-004: Contrast (Minimum)
2025-07-12T06:57:41.268Z [DEBUG] - 📁 Cache file valid: 79931a2e7b38e3a273482da1d4531a95.json (166min remaining)
2025-07-12T06:57:41.268Z [DEBUG] - 📁 Cache loaded from file: 79931a2e7b38e3a273482da1d4531a95.json (key: WCAG-004:053b13d2:add92319...)
2025-07-12T06:57:41.269Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-004:053b13d2:add92319...
2025-07-12T06:57:41.269Z [DEBUG] - ✅ Cache hit: rule:WCAG-004:053b13d2:add92319... (accessed 2 times, age: 4466s)
2025-07-12T06:57:41.270Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-004
2025-07-12T06:57:41.272Z [DEBUG] - 📁 Cache file valid: d2ad183430c8e3077869fa832c90b2ca.json (166min remaining)
2025-07-12T06:57:41.272Z [DEBUG] - 📁 Cache loaded from file: d2ad183430c8e3077869fa832c90b2ca.json (key: WCAG-004:WCAG-004...)
2025-07-12T06:57:41.273Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-004:WCAG-004...
2025-07-12T06:57:41.274Z [DEBUG] - ✅ Cache hit: rule:WCAG-004:WCAG-004... (accessed 2 times, age: 4466s)
2025-07-12T06:57:41.275Z [DEBUG] - 📋 Using cached evidence for WCAG-004
2025-07-12T06:57:41.276Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-004 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:41.282Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:41.284Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:41.286Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:41.516Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:41.517Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-004 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":241}
2025-07-12T06:57:41.518Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:41.520Z [DEBUG] - 🔧 Utility performance recorded for WCAG-004: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:41.521Z [DEBUG] - 🔧 Utility analysis completed for WCAG-004: - {"utilitiesUsed":3,"errors":0,"executionTime":255}
2025-07-12T06:57:41.522Z [DEBUG] - ⏱️ Check WCAG-004 completed in 259ms (success: true)
2025-07-12T06:57:41.523Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.530Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.530Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 6% (4/66)
2025-07-12T06:57:41.531Z [INFO] - ✅ Rule WCAG-004 completed: passed (100/100)
2025-07-12T06:57:41.531Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.535Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.535Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 6% (4/66)
2025-07-12T06:57:41.536Z [INFO] - 🔍 Executing rule: Keyboard (WCAG-005)
2025-07-12T06:57:41.537Z [INFO] - 🎯 Advanced Focus Tracker initialized - {"customIndicators":true,"flowAnalysis":true,"accessibilityTree":true}
2025-07-12T06:57:41.537Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-005: Keyboard
2025-07-12T06:57:41.543Z [DEBUG] - 📁 Cache file valid: a6ecce5051f9339949683209a1de4a1a.json (166min remaining)
2025-07-12T06:57:41.543Z [DEBUG] - 📁 Cache loaded from file: a6ecce5051f9339949683209a1de4a1a.json (key: WCAG-005:053b13d2:add92319...)
2025-07-12T06:57:41.544Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-005:053b13d2:add92319...
2025-07-12T06:57:41.545Z [DEBUG] - ✅ Cache hit: rule:WCAG-005:053b13d2:add92319... (accessed 2 times, age: 4465s)
2025-07-12T06:57:41.546Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-005
2025-07-12T06:57:41.547Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-005 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance","maxExecutionTime":6000}}
2025-07-12T06:57:41.547Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:41.548Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 5 times, age: 4469s)
2025-07-12T06:57:41.549Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:41.550Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:41.551Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:41.553Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:41.553Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:41.555Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:41.557Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:41.562Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:41.570Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:41.613Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:41.614Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-005 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":67}
2025-07-12T06:57:41.616Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:41.618Z [DEBUG] - 🔧 Utility performance recorded for WCAG-005: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:41.619Z [DEBUG] - 🔧 Utility analysis completed for WCAG-005: - {"utilitiesUsed":4,"errors":0,"executionTime":80}
2025-07-12T06:57:41.620Z [DEBUG] - ⏱️ Check WCAG-005 completed in 84ms (success: true)
2025-07-12T06:57:41.621Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.626Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.626Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 8% (5/66)
2025-07-12T06:57:41.627Z [INFO] - ✅ Rule WCAG-005 completed: failed (0/100)
2025-07-12T06:57:41.627Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.631Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.634Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 8% (5/66)
2025-07-12T06:57:41.635Z [INFO] - 🔍 Executing rule: Focus Order (WCAG-006)
2025-07-12T06:57:41.636Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-006: Focus Order
2025-07-12T06:57:41.639Z [DEBUG] - 📁 Cache file valid: c1c224c4d926d4c3a0d059b8492adcc4.json (166min remaining)
2025-07-12T06:57:41.639Z [DEBUG] - 📁 Cache loaded from file: c1c224c4d926d4c3a0d059b8492adcc4.json (key: WCAG-006:053b13d2:add92319...)
2025-07-12T06:57:41.640Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-006:053b13d2:add92319...
2025-07-12T06:57:41.641Z [DEBUG] - ✅ Cache hit: rule:WCAG-006:053b13d2:add92319... (accessed 2 times, age: 4463s)
2025-07-12T06:57:41.642Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-006
2025-07-12T06:57:41.643Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-006 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:41.643Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:41.645Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:41.646Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:41.906Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:41.907Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-006 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":265}
2025-07-12T06:57:41.909Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:41.911Z [DEBUG] - 🔧 Utility performance recorded for WCAG-006: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:41.912Z [DEBUG] - 🔧 Utility analysis completed for WCAG-006: - {"utilitiesUsed":3,"errors":0,"executionTime":274}
2025-07-12T06:57:41.912Z [DEBUG] - ⏱️ Check WCAG-006 completed in 278ms (success: true)
2025-07-12T06:57:41.913Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.970Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.971Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 9% (6/66)
2025-07-12T06:57:41.974Z [INFO] - ✅ Rule WCAG-006 completed: failed (0/100)
2025-07-12T06:57:41.976Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.980Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:41.980Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 9% (6/66)
2025-07-12T06:57:41.981Z [INFO] - 🔍 Executing rule: Focus Visible (WCAG-007)
2025-07-12T06:57:41.982Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-007: Focus Visible
2025-07-12T06:57:41.985Z [DEBUG] - 📁 Cache file valid: 1f4c31fe7cc78e443a634f63156f8b6a.json (166min remaining)
2025-07-12T06:57:41.985Z [DEBUG] - 📁 Cache loaded from file: 1f4c31fe7cc78e443a634f63156f8b6a.json (key: WCAG-007:053b13d2:add92319...)
2025-07-12T06:57:41.986Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-007:053b13d2:add92319...
2025-07-12T06:57:41.987Z [DEBUG] - ✅ Cache hit: rule:WCAG-007:053b13d2:add92319... (accessed 2 times, age: 4457s)
2025-07-12T06:57:41.988Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-007
2025-07-12T06:57:41.995Z [DEBUG] - 📁 Cache file valid: 92e29ae80832f8cf81c9ef789399e501.json (166min remaining)
2025-07-12T06:57:41.996Z [DEBUG] - 📁 Cache loaded from file: 92e29ae80832f8cf81c9ef789399e501.json (key: WCAG-007:WCAG-007...)
2025-07-12T06:57:41.997Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-007:WCAG-007...
2025-07-12T06:57:41.998Z [DEBUG] - ✅ Cache hit: rule:WCAG-007:WCAG-007... (accessed 2 times, age: 4457s)
2025-07-12T06:57:41.999Z [DEBUG] - 📋 Using cached evidence for WCAG-007
2025-07-12T06:57:42.000Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-007 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:42.001Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:42.003Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:42.005Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:42.240Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:42.241Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-007 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":241}
2025-07-12T06:57:42.243Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:42.245Z [DEBUG] - 🔧 Utility performance recorded for WCAG-007: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:42.246Z [DEBUG] - 🔧 Utility analysis completed for WCAG-007: - {"utilitiesUsed":3,"errors":0,"executionTime":262}
2025-07-12T06:57:42.247Z [DEBUG] - ⏱️ Check WCAG-007 completed in 266ms (success: true)
2025-07-12T06:57:42.247Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.253Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.253Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 11% (7/66)
2025-07-12T06:57:42.254Z [INFO] - ✅ Rule WCAG-007 completed: failed (0/100)
2025-07-12T06:57:42.254Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.261Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.262Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 11% (7/66)
2025-07-12T06:57:42.263Z [INFO] - 🔍 Executing rule: Error Identification (WCAG-008)
2025-07-12T06:57:42.264Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-008: Error Identification
2025-07-12T06:57:42.267Z [DEBUG] - 📁 Cache file valid: 06edb7a0699869f2657c51a560ea933c.json (166min remaining)
2025-07-12T06:57:42.267Z [DEBUG] - 📁 Cache loaded from file: 06edb7a0699869f2657c51a560ea933c.json (key: WCAG-008:053b13d2:add92319...)
2025-07-12T06:57:42.268Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-008:053b13d2:add92319...
2025-07-12T06:57:42.269Z [DEBUG] - ✅ Cache hit: rule:WCAG-008:053b13d2:add92319... (accessed 2 times, age: 4452s)
2025-07-12T06:57:42.269Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-008
2025-07-12T06:57:42.271Z [DEBUG] - 📁 Cache file valid: e98e5525ae11e3cecb7834f06806f786.json (166min remaining)
2025-07-12T06:57:42.272Z [DEBUG] - 📁 Cache loaded from file: e98e5525ae11e3cecb7834f06806f786.json (key: WCAG-008:WCAG-008...)
2025-07-12T06:57:42.273Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-008:WCAG-008...
2025-07-12T06:57:42.274Z [DEBUG] - ✅ Cache hit: rule:WCAG-008:WCAG-008... (accessed 2 times, age: 4452s)
2025-07-12T06:57:42.274Z [DEBUG] - 📋 Using cached evidence for WCAG-008
2025-07-12T06:57:42.278Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-008 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:42.279Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:42.280Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 6 times, age: 4469s)
2025-07-12T06:57:42.281Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:42.282Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:42.284Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:42.285Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:42.286Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:42.287Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:42.288Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:42.299Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:42.333Z [DEBUG] - 📊 Memory usage: 480MB / 521MB (growth: -374.0MB/min)
2025-07-12T06:57:42.333Z [WARN] - ⚠️ High memory usage detected: 480MB
2025-07-12T06:57:42.335Z [INFO] - 🧹 Starting proactive memory cleanup
2025-07-12T06:57:42.337Z [DEBUG] - 🧹 Clearing large objects from memory
2025-07-12T06:57:42.338Z [INFO] - ✅ Proactive cleanup completed
2025-07-12T06:57:42.339Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:42.340Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-008 - {"utilitiesUsed":["semantic-validation","component-library","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.55,"executionTime":62}
2025-07-12T06:57:42.341Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:42.342Z [DEBUG] - 🔧 Utility performance recorded for WCAG-008: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:42.342Z [DEBUG] - 🔧 Utility analysis completed for WCAG-008: - {"utilitiesUsed":3,"errors":0,"executionTime":78}
2025-07-12T06:57:42.343Z [DEBUG] - ⏱️ Check WCAG-008 completed in 80ms (success: true)
2025-07-12T06:57:42.344Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.350Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.350Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 12% (8/66)
2025-07-12T06:57:42.351Z [INFO] - ✅ Rule WCAG-008 completed: failed (0/100)
2025-07-12T06:57:42.355Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.361Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.361Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 12% (8/66)
2025-07-12T06:57:42.362Z [INFO] - 🔍 Executing rule: Name, Role, Value (WCAG-009)
2025-07-12T06:57:42.363Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-009: Name, Role, Value
2025-07-12T06:57:42.375Z [DEBUG] - 📁 Cache file valid: 6411962e15bff5d711f1d34d86b5a178.json (166min remaining)
2025-07-12T06:57:42.375Z [DEBUG] - 📁 Cache loaded from file: 6411962e15bff5d711f1d34d86b5a178.json (key: WCAG-009:053b13d2:add92319...)
2025-07-12T06:57:42.376Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-009:053b13d2:add92319...
2025-07-12T06:57:42.378Z [DEBUG] - ✅ Cache hit: rule:WCAG-009:053b13d2:add92319... (accessed 2 times, age: 4452s)
2025-07-12T06:57:42.380Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-009
2025-07-12T06:57:42.384Z [DEBUG] - 📁 Cache file valid: a3bf5ff732223bebe7ee44260f121f3e.json (166min remaining)
2025-07-12T06:57:42.384Z [DEBUG] - 📁 Cache loaded from file: a3bf5ff732223bebe7ee44260f121f3e.json (key: WCAG-009:WCAG-009...)
2025-07-12T06:57:42.385Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-009:WCAG-009...
2025-07-12T06:57:42.386Z [DEBUG] - ✅ Cache hit: rule:WCAG-009:WCAG-009... (accessed 2 times, age: 4452s)
2025-07-12T06:57:42.386Z [DEBUG] - 📋 Using cached evidence for WCAG-009
2025-07-12T06:57:42.387Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-009 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:42.388Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:42.389Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 7 times, age: 4469s)
2025-07-12T06:57:42.390Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:42.392Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:42.395Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:42.397Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:42.398Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:42.399Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:42.400Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:42.401Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:42.413Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:42.458Z [WARN] - High CPU usage detected - {"usage":94.29760323581223,"threshold":80,"loadAverage":0}
2025-07-12T06:57:42.460Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:42.460Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-009 - {"utilitiesUsed":["semantic-validation","component-library","framework-optimization","pattern-validation"],"confidence":1,"accuracy":0.8,"executionTime":73}
2025-07-12T06:57:42.461Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:42.463Z [DEBUG] - 🔧 Utility performance recorded for WCAG-009: - {"utilitiesUsed":4,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:42.464Z [DEBUG] - 🔧 Utility analysis completed for WCAG-009: - {"utilitiesUsed":4,"errors":0,"executionTime":99}
2025-07-12T06:57:42.465Z [DEBUG] - ⏱️ Check WCAG-009 completed in 103ms (success: true)
2025-07-12T06:57:42.465Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.470Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.470Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 14% (9/66)
2025-07-12T06:57:42.471Z [INFO] - ✅ Rule WCAG-009 completed: failed (0/100)
2025-07-12T06:57:42.472Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.477Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.477Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 14% (9/66)
2025-07-12T06:57:42.478Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Minimum) (WCAG-010)
2025-07-12T06:57:42.483Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-010: Focus Not Obscured (Minimum)
2025-07-12T06:57:42.487Z [DEBUG] - 📁 Cache file valid: d60e2dd8f7e1b2b08da70c1edb0ebaf5.json (166min remaining)
2025-07-12T06:57:42.487Z [DEBUG] - 📁 Cache loaded from file: d60e2dd8f7e1b2b08da70c1edb0ebaf5.json (key: WCAG-010:053b13d2:add92319...)
2025-07-12T06:57:42.488Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-010:053b13d2:add92319...
2025-07-12T06:57:42.489Z [DEBUG] - ✅ Cache hit: rule:WCAG-010:053b13d2:add92319... (accessed 2 times, age: 4447s)
2025-07-12T06:57:42.490Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-010
2025-07-12T06:57:42.494Z [DEBUG] - 📁 Cache file valid: b58e4c6bbef16c78192296d9cc3e9207.json (166min remaining)
2025-07-12T06:57:42.495Z [DEBUG] - 📁 Cache loaded from file: b58e4c6bbef16c78192296d9cc3e9207.json (key: WCAG-010:WCAG-010...)
2025-07-12T06:57:42.496Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-010:WCAG-010...
2025-07-12T06:57:42.498Z [DEBUG] - ✅ Cache hit: rule:WCAG-010:WCAG-010... (accessed 2 times, age: 4447s)
2025-07-12T06:57:42.498Z [DEBUG] - 📋 Using cached evidence for WCAG-010
2025-07-12T06:57:42.499Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-010 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:42.500Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:42.501Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:42.503Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:42.701Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:42.701Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-010 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":202}
2025-07-12T06:57:42.703Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:42.705Z [DEBUG] - 🔧 Utility performance recorded for WCAG-010: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:42.706Z [DEBUG] - 🔧 Utility analysis completed for WCAG-010: - {"utilitiesUsed":3,"errors":0,"executionTime":225}
2025-07-12T06:57:42.707Z [DEBUG] - ⏱️ Check WCAG-010 completed in 229ms (success: true)
2025-07-12T06:57:42.708Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.718Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.718Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 15% (10/66)
2025-07-12T06:57:42.719Z [INFO] - ✅ Rule WCAG-010 completed: failed (0/100)
2025-07-12T06:57:42.721Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.725Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.725Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 15% (10/66)
2025-07-12T06:57:42.726Z [INFO] - 🔍 Executing rule: Focus Not Obscured (Enhanced) (WCAG-011)
2025-07-12T06:57:42.727Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-011: Focus Not Obscured (Enhanced)
2025-07-12T06:57:42.731Z [DEBUG] - 📁 Cache file valid: 03cd0bc1a4ee73aa53c096bc5d54750a.json (166min remaining)
2025-07-12T06:57:42.732Z [DEBUG] - 📁 Cache loaded from file: 03cd0bc1a4ee73aa53c096bc5d54750a.json (key: WCAG-011:053b13d2:add92319...)
2025-07-12T06:57:42.732Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-011:053b13d2:add92319...
2025-07-12T06:57:42.734Z [DEBUG] - ✅ Cache hit: rule:WCAG-011:053b13d2:add92319... (accessed 2 times, age: 4443s)
2025-07-12T06:57:42.735Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-011
2025-07-12T06:57:42.738Z [DEBUG] - 📁 Cache file valid: 1903ba2567cc5b56b159dd13b2eeb39d.json (166min remaining)
2025-07-12T06:57:42.738Z [DEBUG] - 📁 Cache loaded from file: 1903ba2567cc5b56b159dd13b2eeb39d.json (key: WCAG-011:WCAG-011...)
2025-07-12T06:57:42.739Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-011:WCAG-011...
2025-07-12T06:57:42.740Z [DEBUG] - ✅ Cache hit: rule:WCAG-011:WCAG-011... (accessed 2 times, age: 4443s)
2025-07-12T06:57:42.741Z [DEBUG] - 📋 Using cached evidence for WCAG-011
2025-07-12T06:57:42.741Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-011 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:42.742Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:42.744Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:42.748Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:42.964Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:42.964Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-011 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":223}
2025-07-12T06:57:42.966Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:42.968Z [DEBUG] - 🔧 Utility performance recorded for WCAG-011: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:42.969Z [DEBUG] - 🔧 Utility analysis completed for WCAG-011: - {"utilitiesUsed":3,"errors":0,"executionTime":240}
2025-07-12T06:57:42.970Z [DEBUG] - ⏱️ Check WCAG-011 completed in 244ms (success: true)
2025-07-12T06:57:42.970Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.976Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.978Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 17% (11/66)
2025-07-12T06:57:42.979Z [INFO] - ✅ Rule WCAG-011 completed: passed (96/100)
2025-07-12T06:57:42.981Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.985Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:42.985Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 17% (11/66)
2025-07-12T06:57:42.986Z [INFO] - 🔍 Executing rule: Focus Appearance (WCAG-012)
2025-07-12T06:57:42.987Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-012: Focus Appearance
2025-07-12T06:57:42.991Z [DEBUG] - 📁 Cache file valid: 72aedcf6766a997165d0b6bb2a48954e.json (166min remaining)
2025-07-12T06:57:42.991Z [DEBUG] - 📁 Cache loaded from file: 72aedcf6766a997165d0b6bb2a48954e.json (key: WCAG-012:053b13d2:add92319...)
2025-07-12T06:57:42.994Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-012:053b13d2:add92319...
2025-07-12T06:57:42.995Z [DEBUG] - ✅ Cache hit: rule:WCAG-012:053b13d2:add92319... (accessed 2 times, age: 4434s)
2025-07-12T06:57:42.997Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-012
2025-07-12T06:57:43.000Z [DEBUG] - 📁 Cache file valid: c6ed0a7d6ff63e2693b81632a40525f4.json (166min remaining)
2025-07-12T06:57:43.000Z [DEBUG] - 📁 Cache loaded from file: c6ed0a7d6ff63e2693b81632a40525f4.json (key: WCAG-012:WCAG-012...)
2025-07-12T06:57:43.001Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-012:WCAG-012...
2025-07-12T06:57:43.001Z [DEBUG] - ✅ Cache hit: rule:WCAG-012:WCAG-012... (accessed 2 times, age: 4434s)
2025-07-12T06:57:43.002Z [DEBUG] - 📋 Using cached evidence for WCAG-012
2025-07-12T06:57:43.003Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-012 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:43.004Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:43.006Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:43.008Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:43.211Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:43.211Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-012 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":208}
2025-07-12T06:57:43.213Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:43.215Z [DEBUG] - 🔧 Utility performance recorded for WCAG-012: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:43.215Z [DEBUG] - 🔧 Utility analysis completed for WCAG-012: - {"utilitiesUsed":3,"errors":0,"executionTime":227}
2025-07-12T06:57:43.216Z [DEBUG] - ⏱️ Check WCAG-012 completed in 230ms (success: true)
2025-07-12T06:57:43.217Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.222Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.223Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 18% (12/66)
2025-07-12T06:57:43.224Z [INFO] - ✅ Rule WCAG-012 completed: failed (0/100)
2025-07-12T06:57:43.225Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.228Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.228Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 18% (12/66)
2025-07-12T06:57:43.229Z [INFO] - 🔍 Executing rule: Dragging Movements (WCAG-013)
2025-07-12T06:57:43.230Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-013: Dragging Movements
2025-07-12T06:57:43.233Z [DEBUG] - 📁 Cache file valid: d7c811b39f1be98996f445fb6f9bd998.json (166min remaining)
2025-07-12T06:57:43.233Z [DEBUG] - 📁 Cache loaded from file: d7c811b39f1be98996f445fb6f9bd998.json (key: WCAG-013:053b13d2:add92319...)
2025-07-12T06:57:43.234Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-013:053b13d2:add92319...
2025-07-12T06:57:43.234Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:053b13d2:add92319... (accessed 2 times, age: 4434s)
2025-07-12T06:57:43.235Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-013
2025-07-12T06:57:43.239Z [DEBUG] - 📁 Cache file valid: 27e7e292431047de714c2100e736b7bd.json (166min remaining)
2025-07-12T06:57:43.240Z [DEBUG] - 📁 Cache loaded from file: 27e7e292431047de714c2100e736b7bd.json (key: WCAG-013:WCAG-013...)
2025-07-12T06:57:43.241Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-013:WCAG-013...
2025-07-12T06:57:43.242Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:WCAG-013... (accessed 2 times, age: 4434s)
2025-07-12T06:57:43.242Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-12T06:57:43.243Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-013 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:43.244Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:43.245Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:43.466Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:43.466Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-013 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":223}
2025-07-12T06:57:43.469Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:43.470Z [DEBUG] - 🔧 Utility performance recorded for WCAG-013: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:43.472Z [DEBUG] - 🔧 Utility analysis completed for WCAG-013: - {"utilitiesUsed":2,"errors":0,"executionTime":240}
2025-07-12T06:57:43.473Z [DEBUG] - ⏱️ Check WCAG-013 completed in 244ms (success: true)
2025-07-12T06:57:43.474Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.478Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.478Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 20% (13/66)
2025-07-12T06:57:43.479Z [INFO] - ✅ Rule WCAG-013 completed: failed (0/100)
2025-07-12T06:57:43.480Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.500Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.500Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 20% (13/66)
2025-07-12T06:57:43.502Z [INFO] - 🔍 Executing rule: Target Size (Minimum) (WCAG-014)
2025-07-12T06:57:43.505Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-014: Target Size
2025-07-12T06:57:43.509Z [DEBUG] - 📁 Cache file valid: 8b42333cd82cdfc12b4027f127683b1b.json (166min remaining)
2025-07-12T06:57:43.509Z [DEBUG] - 📁 Cache loaded from file: 8b42333cd82cdfc12b4027f127683b1b.json (key: WCAG-014:053b13d2:add92319...)
2025-07-12T06:57:43.512Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-014:053b13d2:add92319...
2025-07-12T06:57:43.512Z [DEBUG] - ✅ Cache hit: rule:WCAG-014:053b13d2:add92319... (accessed 2 times, age: 4432s)
2025-07-12T06:57:43.514Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-014
2025-07-12T06:57:43.518Z [DEBUG] - 📁 Cache file valid: 45019f21152e7ac5a9a16de79e14d653.json (166min remaining)
2025-07-12T06:57:43.518Z [DEBUG] - 📁 Cache loaded from file: 45019f21152e7ac5a9a16de79e14d653.json (key: WCAG-014:WCAG-014...)
2025-07-12T06:57:43.519Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-014:WCAG-014...
2025-07-12T06:57:43.519Z [DEBUG] - ✅ Cache hit: rule:WCAG-014:WCAG-014... (accessed 2 times, age: 4432s)
2025-07-12T06:57:43.520Z [DEBUG] - 📋 Using cached evidence for WCAG-014
2025-07-12T06:57:43.521Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-014 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:43.522Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:43.524Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:43.715Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:43.716Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-014 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":194}
2025-07-12T06:57:43.717Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:43.719Z [DEBUG] - 🔧 Utility performance recorded for WCAG-014: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:43.720Z [DEBUG] - 🔧 Utility analysis completed for WCAG-014: - {"utilitiesUsed":2,"errors":0,"executionTime":215}
2025-07-12T06:57:43.721Z [DEBUG] - ⏱️ Check WCAG-014 completed in 219ms (success: true)
2025-07-12T06:57:43.722Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.727Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.728Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 21% (14/66)
2025-07-12T06:57:43.728Z [INFO] - ✅ Rule WCAG-014 completed: passed (81/100)
2025-07-12T06:57:43.729Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.733Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:43.733Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 21% (14/66)
2025-07-12T06:57:43.734Z [INFO] - 🔍 Executing rule: Consistent Help (WCAG-015)
2025-07-12T06:57:43.735Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-015: Consistent Help
2025-07-12T06:57:43.738Z [DEBUG] - 📁 Cache file valid: 697e44ab0d03e917e7e24943c6f491ae.json (166min remaining)
2025-07-12T06:57:43.739Z [DEBUG] - 📁 Cache loaded from file: 697e44ab0d03e917e7e24943c6f491ae.json (key: WCAG-015:053b13d2:add92319...)
2025-07-12T06:57:43.740Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-015:053b13d2:add92319...
2025-07-12T06:57:43.741Z [DEBUG] - ✅ Cache hit: rule:WCAG-015:053b13d2:add92319... (accessed 2 times, age: 4432s)
2025-07-12T06:57:43.742Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-015
2025-07-12T06:57:43.744Z [DEBUG] - 📁 Cache file valid: 8b605d087733e8f3be3611d94b7ebdae.json (166min remaining)
2025-07-12T06:57:43.745Z [DEBUG] - 📁 Cache loaded from file: 8b605d087733e8f3be3611d94b7ebdae.json (key: WCAG-035:WCAG-035...)
2025-07-12T06:57:43.745Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-035:WCAG-035...
2025-07-12T06:57:43.746Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:WCAG-035... (accessed 2 times, age: 4432s)
2025-07-12T06:57:43.747Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-12T06:57:43.748Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-015 - {"config":{"enablePatternValidation":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:43.748Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:43.749Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:43.793Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:43.987Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:43.988Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-015 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":241}
2025-07-12T06:57:43.990Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:43.992Z [DEBUG] - 🔧 Utility performance recorded for WCAG-015: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:43.993Z [DEBUG] - 🔧 Utility analysis completed for WCAG-015: - {"utilitiesUsed":2,"errors":0,"executionTime":256}
2025-07-12T06:57:43.994Z [DEBUG] - ⏱️ Check WCAG-015 completed in 260ms (success: true)
2025-07-12T06:57:43.995Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.005Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.005Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 23% (15/66)
2025-07-12T06:57:44.006Z [INFO] - ✅ Rule WCAG-015 completed: passed (100/100)
2025-07-12T06:57:44.008Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.018Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.018Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 23% (15/66)
2025-07-12T06:57:44.019Z [INFO] - 🔍 Executing rule: Redundant Entry (WCAG-016)
2025-07-12T06:57:44.022Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-016: Redundant Entry
2025-07-12T06:57:44.025Z [DEBUG] - 📁 Cache file valid: fa41840651a51956c64ee2b37855c8da.json (166min remaining)
2025-07-12T06:57:44.026Z [DEBUG] - 📁 Cache loaded from file: fa41840651a51956c64ee2b37855c8da.json (key: WCAG-016:053b13d2:add92319...)
2025-07-12T06:57:44.026Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-016:053b13d2:add92319...
2025-07-12T06:57:44.027Z [DEBUG] - ✅ Cache hit: rule:WCAG-016:053b13d2:add92319... (accessed 2 times, age: 4431s)
2025-07-12T06:57:44.028Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-016
2025-07-12T06:57:44.036Z [DEBUG] - 📁 Cache file valid: 00ec64e924e2b9439f3c7ff096f625cd.json (166min remaining)
2025-07-12T06:57:44.036Z [DEBUG] - 📁 Cache loaded from file: 00ec64e924e2b9439f3c7ff096f625cd.json (key: WCAG-016:WCAG-016...)
2025-07-12T06:57:44.038Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-016:WCAG-016...
2025-07-12T06:57:44.039Z [DEBUG] - ✅ Cache hit: rule:WCAG-016:WCAG-016... (accessed 2 times, age: 4431s)
2025-07-12T06:57:44.040Z [DEBUG] - 📋 Using cached evidence for WCAG-016
2025-07-12T06:57:44.041Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-016 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:44.042Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:44.045Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:44.326Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:44.326Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-016 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":285}
2025-07-12T06:57:44.329Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:44.330Z [DEBUG] - 🔧 Utility performance recorded for WCAG-016: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:44.332Z [DEBUG] - 🔧 Utility analysis completed for WCAG-016: - {"utilitiesUsed":2,"errors":0,"executionTime":309}
2025-07-12T06:57:44.333Z [DEBUG] - ⏱️ Check WCAG-016 completed in 314ms (success: true)
2025-07-12T06:57:44.333Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.338Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.341Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 24% (16/66)
2025-07-12T06:57:44.342Z [INFO] - ✅ Rule WCAG-016 completed: failed (0/100)
2025-07-12T06:57:44.344Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.348Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.348Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 24% (16/66)
2025-07-12T06:57:44.349Z [INFO] - 🔍 Executing rule: Image Alternatives (WCAG-017)
2025-07-12T06:57:44.350Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-017: Image Alternatives 3.0
2025-07-12T06:57:44.355Z [DEBUG] - 📁 Cache file valid: 2bd69c6d0cff0a882c80dbccc0ddbd2e.json (166min remaining)
2025-07-12T06:57:44.355Z [DEBUG] - 📁 Cache loaded from file: 2bd69c6d0cff0a882c80dbccc0ddbd2e.json (key: WCAG-017:053b13d2:add92319...)
2025-07-12T06:57:44.357Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-017:053b13d2:add92319...
2025-07-12T06:57:44.358Z [DEBUG] - ✅ Cache hit: rule:WCAG-017:053b13d2:add92319... (accessed 2 times, age: 4430s)
2025-07-12T06:57:44.359Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-017
2025-07-12T06:57:44.362Z [DEBUG] - 📁 Cache file valid: 5d32c0d91398f2b5e88f9e138f971a46.json (166min remaining)
2025-07-12T06:57:44.362Z [DEBUG] - 📁 Cache loaded from file: 5d32c0d91398f2b5e88f9e138f971a46.json (key: WCAG-017:WCAG-017...)
2025-07-12T06:57:44.363Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-017:WCAG-017...
2025-07-12T06:57:44.364Z [DEBUG] - ✅ Cache hit: rule:WCAG-017:WCAG-017... (accessed 2 times, age: 4430s)
2025-07-12T06:57:44.364Z [DEBUG] - 📋 Using cached evidence for WCAG-017
2025-07-12T06:57:44.365Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-017 - {"config":{"enableSemanticValidation":true,"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:44.366Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:44.367Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 8 times, age: 4471s)
2025-07-12T06:57:44.371Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:44.373Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:44.374Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:44.374Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:44.376Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:44.377Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:44.378Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:44.401Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:44.422Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:44.430Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:44.431Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-017 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":66}
2025-07-12T06:57:44.432Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:44.434Z [DEBUG] - 🔧 Utility performance recorded for WCAG-017: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:44.434Z [DEBUG] - 🔧 Utility analysis completed for WCAG-017: - {"utilitiesUsed":3,"errors":0,"executionTime":83}
2025-07-12T06:57:44.435Z [DEBUG] - ⏱️ Check WCAG-017 completed in 86ms (success: true)
2025-07-12T06:57:44.436Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.443Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.445Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 26% (17/66)
2025-07-12T06:57:44.445Z [INFO] - ✅ Rule WCAG-017 completed: failed (0/100)
2025-07-12T06:57:44.447Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.451Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.452Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 26% (17/66)
2025-07-12T06:57:44.453Z [INFO] - 🔍 Executing rule: Text and Wording (WCAG-018)
2025-07-12T06:57:44.454Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-018: Text and Wording
2025-07-12T06:57:44.457Z [DEBUG] - 📁 Cache file valid: e7fe6692ea6f27ac095ad2f9449b6b9c.json (166min remaining)
2025-07-12T06:57:44.457Z [DEBUG] - 📁 Cache loaded from file: e7fe6692ea6f27ac095ad2f9449b6b9c.json (key: WCAG-018:053b13d2:add92319...)
2025-07-12T06:57:44.460Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-018:053b13d2:add92319...
2025-07-12T06:57:44.463Z [DEBUG] - ✅ Cache hit: rule:WCAG-018:053b13d2:add92319... (accessed 2 times, age: 4430s)
2025-07-12T06:57:44.464Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-018
2025-07-12T06:57:44.465Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-018 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"enableCMSDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:44.466Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:44.467Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 9 times, age: 4471s)
2025-07-12T06:57:44.468Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:44.469Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:44.470Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 3 times, age: 4s)
2025-07-12T06:57:44.472Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:44.472Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:44.473Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:44.476Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:44.478Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:44.479Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:44.499Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:44.516Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:44.519Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-018 - {"utilitiesUsed":["cms-detection","content-quality","semantic-validation"],"confidence":0.8,"accuracy":0.35,"executionTime":54}
2025-07-12T06:57:44.521Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-018: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:44.523Z [DEBUG] - 🔧 Utility performance recorded for WCAG-018: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:44.524Z [DEBUG] - 🔧 Utility analysis completed for WCAG-018: - {"utilitiesUsed":3,"errors":0,"executionTime":67}
2025-07-12T06:57:44.524Z [DEBUG] - ⏱️ Check WCAG-018 completed in 72ms (success: true)
2025-07-12T06:57:44.525Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.531Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.531Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 27% (18/66)
2025-07-12T06:57:44.532Z [INFO] - ✅ Rule WCAG-018 completed: failed (0/100)
2025-07-12T06:57:44.532Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.539Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.539Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 27% (18/66)
2025-07-12T06:57:44.540Z [INFO] - 🔍 Executing rule: Keyboard Focus (WCAG-019)
2025-07-12T06:57:44.541Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-019: Keyboard Focus 3.0
2025-07-12T06:57:44.544Z [DEBUG] - 📁 Cache file valid: fe1748cdfb619e21f2bb4a61da77c615.json (166min remaining)
2025-07-12T06:57:44.544Z [DEBUG] - 📁 Cache loaded from file: fe1748cdfb619e21f2bb4a61da77c615.json (key: WCAG-019:053b13d2:add92319...)
2025-07-12T06:57:44.545Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-019:053b13d2:add92319...
2025-07-12T06:57:44.546Z [DEBUG] - ✅ Cache hit: rule:WCAG-019:053b13d2:add92319... (accessed 2 times, age: 4430s)
2025-07-12T06:57:44.546Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-019
2025-07-12T06:57:44.550Z [DEBUG] - 📁 Cache file valid: bc9fe1ac640ba8a7c64083f4dce51eff.json (166min remaining)
2025-07-12T06:57:44.550Z [DEBUG] - 📁 Cache loaded from file: bc9fe1ac640ba8a7c64083f4dce51eff.json (key: WCAG-019:WCAG-019...)
2025-07-12T06:57:44.551Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-019:WCAG-019...
2025-07-12T06:57:44.553Z [DEBUG] - ✅ Cache hit: rule:WCAG-019:WCAG-019... (accessed 2 times, age: 4430s)
2025-07-12T06:57:44.554Z [DEBUG] - 📋 Using cached evidence for WCAG-019
2025-07-12T06:57:44.555Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-019 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:44.556Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:44.557Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:44.558Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:44.742Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:44.742Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-019 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":187}
2025-07-12T06:57:44.744Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:44.746Z [DEBUG] - 🔧 Utility performance recorded for WCAG-019: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:44.747Z [DEBUG] - 🔧 Utility analysis completed for WCAG-019: - {"utilitiesUsed":3,"errors":0,"executionTime":204}
2025-07-12T06:57:44.747Z [DEBUG] - ⏱️ Check WCAG-019 completed in 207ms (success: true)
2025-07-12T06:57:44.748Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.754Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.754Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 29% (19/66)
2025-07-12T06:57:44.755Z [INFO] - ✅ Rule WCAG-019 completed: failed (0/100)
2025-07-12T06:57:44.756Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.762Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:44.762Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 29% (19/66)
2025-07-12T06:57:44.763Z [INFO] - 🔍 Executing rule: Motor (WCAG-020)
2025-07-12T06:57:44.764Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-058: Motor
2025-07-12T06:57:44.769Z [DEBUG] - 📁 Cache file valid: 4c83a99c9da2c4342974e94427876acd.json (166min remaining)
2025-07-12T06:57:44.769Z [DEBUG] - 📁 Cache loaded from file: 4c83a99c9da2c4342974e94427876acd.json (key: WCAG-058:053b13d2:add92319...)
2025-07-12T06:57:44.770Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-058:053b13d2:add92319...
2025-07-12T06:57:44.771Z [DEBUG] - ✅ Cache hit: rule:WCAG-058:053b13d2:add92319... (accessed 2 times, age: 4428s)
2025-07-12T06:57:44.772Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-058
2025-07-12T06:57:44.777Z [DEBUG] - 📁 Cache file valid: e75c3291ea913f99e367de4c994bd504.json (166min remaining)
2025-07-12T06:57:44.777Z [DEBUG] - 📁 Cache loaded from file: e75c3291ea913f99e367de4c994bd504.json (key: WCAG-058:WCAG-058...)
2025-07-12T06:57:44.778Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-058:WCAG-058...
2025-07-12T06:57:44.779Z [DEBUG] - ✅ Cache hit: rule:WCAG-058:WCAG-058... (accessed 2 times, age: 4428s)
2025-07-12T06:57:44.780Z [DEBUG] - 📋 Using cached evidence for WCAG-058
2025-07-12T06:57:44.780Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-020 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:44.781Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:44.782Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:44.784Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:44.990Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:44.991Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-020 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":211}
2025-07-12T06:57:44.992Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:44.994Z [DEBUG] - 🔧 Utility performance recorded for WCAG-020: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:44.995Z [DEBUG] - 🔧 Utility analysis completed for WCAG-020: - {"utilitiesUsed":3,"errors":0,"executionTime":229}
2025-07-12T06:57:44.996Z [DEBUG] - ⏱️ Check WCAG-020 completed in 233ms (success: true)
2025-07-12T06:57:44.997Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.002Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.002Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 30% (20/66)
2025-07-12T06:57:45.004Z [INFO] - ✅ Rule WCAG-020 completed: failed (0/100)
2025-07-12T06:57:45.004Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.011Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.011Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 30% (20/66)
2025-07-12T06:57:45.012Z [INFO] - 🔍 Executing rule: Pronunciation & Meaning (WCAG-021)
2025-07-12T06:57:45.013Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-065: Pronunciation & Meaning
2025-07-12T06:57:45.016Z [DEBUG] - 📁 Cache file valid: 7bdf34e25514cb352be82815f91affad.json (166min remaining)
2025-07-12T06:57:45.016Z [DEBUG] - 📁 Cache loaded from file: 7bdf34e25514cb352be82815f91affad.json (key: WCAG-065:053b13d2:add92319...)
2025-07-12T06:57:45.017Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-065:053b13d2:add92319...
2025-07-12T06:57:45.017Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:053b13d2:add92319... (accessed 2 times, age: 4428s)
2025-07-12T06:57:45.018Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-065
2025-07-12T06:57:45.021Z [DEBUG] - 📁 Cache file valid: ee411a3562e329e251a37ada2a1f5641.json (166min remaining)
2025-07-12T06:57:45.022Z [DEBUG] - 📁 Cache loaded from file: ee411a3562e329e251a37ada2a1f5641.json (key: WCAG-065:WCAG-065...)
2025-07-12T06:57:45.023Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-065:WCAG-065...
2025-07-12T06:57:45.025Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:WCAG-065... (accessed 2 times, age: 4428s)
2025-07-12T06:57:45.026Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-12T06:57:45.026Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-021 - {"config":{"enableFrameworkOptimization":true,"enableContentQualityAnalysis":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:45.027Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:45.028Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:45.064Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:45.074Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-021 - {"utilitiesUsed":["content-quality","framework-optimization"],"confidence":0.7,"accuracy":0.35,"executionTime":48}
2025-07-12T06:57:45.075Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:45.075Z [DEBUG] - 🔧 Utility performance recorded for WCAG-021: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:45.076Z [DEBUG] - 🔧 Utility analysis completed for WCAG-021: - {"utilitiesUsed":2,"errors":0,"executionTime":63}
2025-07-12T06:57:45.077Z [DEBUG] - ⏱️ Check WCAG-021 completed in 65ms (success: true)
2025-07-12T06:57:45.077Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.082Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.082Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 32% (21/66)
2025-07-12T06:57:45.083Z [INFO] - ✅ Rule WCAG-021 completed: passed (75/100)
2025-07-12T06:57:45.085Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.090Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.090Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 32% (21/66)
2025-07-12T06:57:45.091Z [INFO] - 🔍 Executing rule: Accessible Authentication (Minimum) (WCAG-022)
2025-07-12T06:57:45.092Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-022: Accessible Authentication (Minimum)
2025-07-12T06:57:45.095Z [DEBUG] - 📁 Cache file valid: bc5bd398704b1dbd65c4a3ece6d5f341.json (166min remaining)
2025-07-12T06:57:45.098Z [DEBUG] - 📁 Cache loaded from file: bc5bd398704b1dbd65c4a3ece6d5f341.json (key: WCAG-022:053b13d2:add92319...)
2025-07-12T06:57:45.099Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-022:053b13d2:add92319...
2025-07-12T06:57:45.100Z [DEBUG] - ✅ Cache hit: rule:WCAG-022:053b13d2:add92319... (accessed 2 times, age: 4427s)
2025-07-12T06:57:45.101Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-022
2025-07-12T06:57:45.105Z [DEBUG] - 📁 Cache file valid: 07a72359a8282ab6dba353d773a225d3.json (166min remaining)
2025-07-12T06:57:45.105Z [DEBUG] - 📁 Cache loaded from file: 07a72359a8282ab6dba353d773a225d3.json (key: WCAG-022:WCAG-022...)
2025-07-12T06:57:45.106Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-022:WCAG-022...
2025-07-12T06:57:45.106Z [DEBUG] - ✅ Cache hit: rule:WCAG-022:WCAG-022... (accessed 2 times, age: 4427s)
2025-07-12T06:57:45.107Z [DEBUG] - 📋 Using cached evidence for WCAG-022
2025-07-12T06:57:45.108Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-022 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:45.108Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:45.110Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:45.113Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:45.303Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:45.303Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-022 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":195}
2025-07-12T06:57:45.305Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:45.306Z [DEBUG] - 🔧 Utility performance recorded for WCAG-022: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:45.307Z [DEBUG] - 🔧 Utility analysis completed for WCAG-022: - {"utilitiesUsed":3,"errors":0,"executionTime":214}
2025-07-12T06:57:45.308Z [DEBUG] - ⏱️ Check WCAG-022 completed in 217ms (success: true)
2025-07-12T06:57:45.309Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.314Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.315Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 33% (22/66)
2025-07-12T06:57:45.315Z [INFO] - ✅ Rule WCAG-022 completed: failed (0/100)
2025-07-12T06:57:45.317Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.322Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.322Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 33% (22/66)
2025-07-12T06:57:45.323Z [INFO] - 🔍 Executing rule: Accessible Authentication (Enhanced) (WCAG-023)
🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting WCAG-037: Accessible Authentication (Enhanced) (40% automated)
✅ [ed85e707-19e7-4046-913e-301608345e71] Completed WCAG-037 in 15ms - Status: passed (100/100, Manual items: 1)
2025-07-12T06:57:45.342Z [DEBUG] - 📁 Cache file valid: e8778cf0e75a1f0e38b3c8636943fd56.json (166min remaining)
2025-07-12T06:57:45.342Z [DEBUG] - 📁 Cache loaded from file: e8778cf0e75a1f0e38b3c8636943fd56.json (key: WCAG-037:WCAG-037...)
2025-07-12T06:57:45.343Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-037:WCAG-037...
2025-07-12T06:57:45.345Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:WCAG-037... (accessed 2 times, age: 4427s)
2025-07-12T06:57:45.347Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-12T06:57:45.347Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-023 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:45.348Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:45.350Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:45.351Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:45.550Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:45.550Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-023 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":203}
2025-07-12T06:57:45.552Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:45.554Z [DEBUG] - 🔧 Utility performance recorded for WCAG-023: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:45.555Z [DEBUG] - 🔧 Utility analysis completed for WCAG-023: - {"utilitiesUsed":3,"errors":0,"executionTime":229}
2025-07-12T06:57:45.555Z [DEBUG] - ⏱️ Check WCAG-023 completed in 232ms (success: true)
2025-07-12T06:57:45.556Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.563Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.563Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 35% (23/66)
2025-07-12T06:57:45.564Z [INFO] - ✅ Rule WCAG-023 completed: passed (100/100)
2025-07-12T06:57:45.565Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.568Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.569Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 35% (23/66)
2025-07-12T06:57:45.570Z [INFO] - 🔍 Executing rule: Timing Adjustable (WCAG-044)
2025-07-12T06:57:45.571Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-044: Timing Adjustable
2025-07-12T06:57:45.573Z [DEBUG] - 📁 Cache file valid: 98e045dbc9aa729028791ef5084c3d73.json (166min remaining)
2025-07-12T06:57:45.576Z [DEBUG] - 📁 Cache loaded from file: 98e045dbc9aa729028791ef5084c3d73.json (key: WCAG-044:053b13d2:add92319...)
2025-07-12T06:57:45.577Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-044:053b13d2:add92319...
2025-07-12T06:57:45.578Z [DEBUG] - ✅ Cache hit: rule:WCAG-044:053b13d2:add92319... (accessed 2 times, age: 4427s)
2025-07-12T06:57:45.578Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-044
2025-07-12T06:57:45.581Z [DEBUG] - 📁 Cache file valid: d1e2f48c659bd8e8b7ae07436b74c326.json (166min remaining)
2025-07-12T06:57:45.581Z [DEBUG] - 📁 Cache loaded from file: d1e2f48c659bd8e8b7ae07436b74c326.json (key: WCAG-044:WCAG-044...)
2025-07-12T06:57:45.582Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-044:WCAG-044...
2025-07-12T06:57:45.583Z [DEBUG] - ✅ Cache hit: rule:WCAG-044:WCAG-044... (accessed 2 times, age: 4427s)
2025-07-12T06:57:45.583Z [DEBUG] - 📋 Using cached evidence for WCAG-044
2025-07-12T06:57:45.584Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-044 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:45.585Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:45.587Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:45.765Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:45.765Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-044 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":181}
2025-07-12T06:57:45.767Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:45.769Z [DEBUG] - 🔧 Utility performance recorded for WCAG-044: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:45.770Z [DEBUG] - 🔧 Utility analysis completed for WCAG-044: - {"utilitiesUsed":2,"errors":0,"executionTime":197}
2025-07-12T06:57:45.771Z [DEBUG] - ⏱️ Check WCAG-044 completed in 201ms (success: true)
2025-07-12T06:57:45.772Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.777Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.778Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 36% (24/66)
2025-07-12T06:57:45.778Z [INFO] - ✅ Rule WCAG-044 completed: passed (75/100)
2025-07-12T06:57:45.779Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.784Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:45.787Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 36% (24/66)
2025-07-12T06:57:45.788Z [INFO] - 🔍 Executing rule: Pause, Stop, Hide (WCAG-045)
2025-07-12T06:57:45.789Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-045: Pause, Stop, Hide
2025-07-12T06:57:45.795Z [DEBUG] - 📁 Cache file valid: e107a6a933c5dce72c582a57daf2513c.json (166min remaining)
2025-07-12T06:57:45.795Z [DEBUG] - 📁 Cache loaded from file: e107a6a933c5dce72c582a57daf2513c.json (key: WCAG-045:053b13d2:add92319...)
2025-07-12T06:57:45.796Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-045:053b13d2:add92319...
2025-07-12T06:57:45.797Z [DEBUG] - ✅ Cache hit: rule:WCAG-045:053b13d2:add92319... (accessed 2 times, age: 4426s)
2025-07-12T06:57:45.800Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-045
2025-07-12T06:57:45.806Z [DEBUG] - 📁 Cache file valid: 8073e0345c1bb47130e00ead626dd137.json (166min remaining)
2025-07-12T06:57:45.806Z [DEBUG] - 📁 Cache loaded from file: 8073e0345c1bb47130e00ead626dd137.json (key: WCAG-045:WCAG-045...)
2025-07-12T06:57:45.807Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-045:WCAG-045...
2025-07-12T06:57:45.808Z [DEBUG] - ✅ Cache hit: rule:WCAG-045:WCAG-045... (accessed 2 times, age: 4426s)
2025-07-12T06:57:45.809Z [DEBUG] - 📋 Using cached evidence for WCAG-045
2025-07-12T06:57:45.810Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-045 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:45.810Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:45.812Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:45.816Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T06:57:45.816Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T06:57:45.993Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:45.994Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-045 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":184}
2025-07-12T06:57:45.995Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:45.997Z [DEBUG] - 🔧 Utility performance recorded for WCAG-045: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:45.998Z [DEBUG] - 🔧 Utility analysis completed for WCAG-045: - {"utilitiesUsed":2,"errors":0,"executionTime":207}
2025-07-12T06:57:45.999Z [DEBUG] - ⏱️ Check WCAG-045 completed in 211ms (success: true)
2025-07-12T06:57:46.000Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.006Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.006Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 38% (25/66)
2025-07-12T06:57:46.007Z [INFO] - ✅ Rule WCAG-045 completed: failed (0/100)
2025-07-12T06:57:46.007Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.012Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.014Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 38% (25/66)
2025-07-12T06:57:46.015Z [INFO] - 🔍 Executing rule: Three Flashes or Below Threshold (WCAG-046)
2025-07-12T06:57:46.016Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-046: Three Flashes or Below Threshold
2025-07-12T06:57:46.019Z [DEBUG] - 📁 Cache file valid: a98794df9865343e56915dbbe43cd6a7.json (166min remaining)
2025-07-12T06:57:46.020Z [DEBUG] - 📁 Cache loaded from file: a98794df9865343e56915dbbe43cd6a7.json (key: WCAG-046:053b13d2:add92319...)
2025-07-12T06:57:46.020Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-046:053b13d2:add92319...
2025-07-12T06:57:46.021Z [DEBUG] - ✅ Cache hit: rule:WCAG-046:053b13d2:add92319... (accessed 2 times, age: 4425s)
2025-07-12T06:57:46.022Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-046
2025-07-12T06:57:46.025Z [DEBUG] - 📁 Cache file valid: 0effabcb96214c0f882ee2a31620da53.json (166min remaining)
2025-07-12T06:57:46.025Z [DEBUG] - 📁 Cache loaded from file: 0effabcb96214c0f882ee2a31620da53.json (key: WCAG-046:WCAG-046...)
2025-07-12T06:57:46.026Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-046:WCAG-046...
2025-07-12T06:57:46.027Z [DEBUG] - ✅ Cache hit: rule:WCAG-046:WCAG-046... (accessed 2 times, age: 4425s)
2025-07-12T06:57:46.030Z [DEBUG] - 📋 Using cached evidence for WCAG-046
2025-07-12T06:57:46.031Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-046 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:46.032Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:46.033Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:46.217Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:46.217Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-046 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":186}
2025-07-12T06:57:46.221Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:46.221Z [DEBUG] - 🔧 Utility performance recorded for WCAG-046: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:46.222Z [DEBUG] - 🔧 Utility analysis completed for WCAG-046: - {"utilitiesUsed":2,"errors":0,"executionTime":205}
2025-07-12T06:57:46.223Z [DEBUG] - ⏱️ Check WCAG-046 completed in 208ms (success: true)
2025-07-12T06:57:46.224Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.231Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.232Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 39% (26/66)
2025-07-12T06:57:46.235Z [INFO] - ✅ Rule WCAG-046 completed: passed (99/100)
2025-07-12T06:57:46.237Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.242Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.242Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 39% (26/66)
2025-07-12T06:57:46.243Z [INFO] - 🔍 Executing rule: Resize Text (WCAG-037)
2025-07-12T06:57:46.244Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-037: Resize Text
2025-07-12T06:57:46.249Z [DEBUG] - 📁 Cache file valid: 8b199614ebef71562728e34c7c5cbe56.json (166min remaining)
2025-07-12T06:57:46.249Z [DEBUG] - 📁 Cache loaded from file: 8b199614ebef71562728e34c7c5cbe56.json (key: WCAG-037:053b13d2:add92319...)
2025-07-12T06:57:46.250Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-037:053b13d2:add92319...
2025-07-12T06:57:46.252Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:053b13d2:add92319... (accessed 2 times, age: 4417s)
2025-07-12T06:57:46.253Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-037
2025-07-12T06:57:46.254Z [DEBUG] - ✅ Cache hit: rule:WCAG-037:WCAG-037... (accessed 3 times, age: 4428s)
2025-07-12T06:57:46.255Z [DEBUG] - 📋 Using cached evidence for WCAG-037
2025-07-12T06:57:46.255Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-037 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:46.256Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:46.258Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:46.259Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:46.716Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:46.716Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-037 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":461}
2025-07-12T06:57:46.718Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:46.720Z [DEBUG] - 🔧 Utility performance recorded for WCAG-037: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:46.721Z [DEBUG] - 🔧 Utility analysis completed for WCAG-037: - {"utilitiesUsed":3,"errors":0,"executionTime":475}
2025-07-12T06:57:46.721Z [DEBUG] - ⏱️ Check WCAG-037 completed in 478ms (success: true)
2025-07-12T06:57:46.722Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.731Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.731Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 41% (27/66)
2025-07-12T06:57:46.732Z [INFO] - ✅ Rule WCAG-037 completed: failed (0/100)
2025-07-12T06:57:46.734Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.739Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.739Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 41% (27/66)
2025-07-12T06:57:46.740Z [INFO] - 🔍 Executing rule: Images of Text (WCAG-039)
2025-07-12T06:57:46.741Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-039: Images of Text
2025-07-12T06:57:46.745Z [DEBUG] - 📁 Cache file valid: 0b9377f001d8de85e74a9756ae50f61f.json (166min remaining)
2025-07-12T06:57:46.745Z [DEBUG] - 📁 Cache loaded from file: 0b9377f001d8de85e74a9756ae50f61f.json (key: WCAG-039:053b13d2:add92319...)
2025-07-12T06:57:46.746Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-039:053b13d2:add92319...
2025-07-12T06:57:46.747Z [DEBUG] - ✅ Cache hit: rule:WCAG-039:053b13d2:add92319... (accessed 2 times, age: 4416s)
2025-07-12T06:57:46.748Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-039
2025-07-12T06:57:46.752Z [DEBUG] - 📁 Cache file valid: 5b4d8451893f90896a28cbbb74a4e6ea.json (166min remaining)
2025-07-12T06:57:46.753Z [DEBUG] - 📁 Cache loaded from file: 5b4d8451893f90896a28cbbb74a4e6ea.json (key: WCAG-039:WCAG-039...)
2025-07-12T06:57:46.753Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-039:WCAG-039...
2025-07-12T06:57:46.754Z [DEBUG] - ✅ Cache hit: rule:WCAG-039:WCAG-039... (accessed 2 times, age: 4416s)
2025-07-12T06:57:46.755Z [DEBUG] - 📋 Using cached evidence for WCAG-039
2025-07-12T06:57:46.756Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-039 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:46.756Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:46.760Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:46.798Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:46.950Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:46.950Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-039 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":194}
2025-07-12T06:57:46.953Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:46.954Z [DEBUG] - 🔧 Utility performance recorded for WCAG-039: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:46.955Z [DEBUG] - 🔧 Utility analysis completed for WCAG-039: - {"utilitiesUsed":2,"errors":0,"executionTime":213}
2025-07-12T06:57:46.956Z [DEBUG] - ⏱️ Check WCAG-039 completed in 216ms (success: true)
2025-07-12T06:57:46.956Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.960Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.960Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 42% (28/66)
2025-07-12T06:57:46.961Z [INFO] - ✅ Rule WCAG-039 completed: failed (0/100)
2025-07-12T06:57:46.962Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.965Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:46.965Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 42% (28/66)
2025-07-12T06:57:46.966Z [INFO] - 🔍 Executing rule: Reflow (WCAG-040)
2025-07-12T06:57:46.967Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-040: Reflow
2025-07-12T06:57:46.973Z [DEBUG] - 📁 Cache file valid: a90e5993c16b32f6ab78b892a8127444.json (166min remaining)
2025-07-12T06:57:46.973Z [DEBUG] - 📁 Cache loaded from file: a90e5993c16b32f6ab78b892a8127444.json (key: WCAG-040:053b13d2:add92319...)
2025-07-12T06:57:46.974Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-040:053b13d2:add92319...
2025-07-12T06:57:46.975Z [DEBUG] - ✅ Cache hit: rule:WCAG-040:053b13d2:add92319... (accessed 2 times, age: 4411s)
2025-07-12T06:57:46.975Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-040
2025-07-12T06:57:46.978Z [DEBUG] - 📁 Cache file valid: c1d7238a0d406c4e4d2b9057a9b92850.json (166min remaining)
2025-07-12T06:57:46.978Z [DEBUG] - 📁 Cache loaded from file: c1d7238a0d406c4e4d2b9057a9b92850.json (key: WCAG-041:WCAG-041...)
2025-07-12T06:57:46.979Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-041:WCAG-041...
2025-07-12T06:57:46.980Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:WCAG-041... (accessed 2 times, age: 4411s)
2025-07-12T06:57:46.980Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-12T06:57:46.981Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-040 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:46.982Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:46.983Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:47.175Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:47.175Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-040 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":194}
2025-07-12T06:57:47.177Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:47.179Z [DEBUG] - 🔧 Utility performance recorded for WCAG-040: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:47.180Z [DEBUG] - 🔧 Utility analysis completed for WCAG-040: - {"utilitiesUsed":2,"errors":0,"executionTime":211}
2025-07-12T06:57:47.181Z [DEBUG] - ⏱️ Check WCAG-040 completed in 215ms (success: true)
2025-07-12T06:57:47.181Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.188Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.188Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 44% (29/66)
2025-07-12T06:57:47.189Z [INFO] - ✅ Rule WCAG-040 completed: failed (0/100)
2025-07-12T06:57:47.189Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.193Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.193Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 44% (29/66)
2025-07-12T06:57:47.194Z [INFO] - 🔍 Executing rule: Non-text Contrast (WCAG-041)
2025-07-12T06:57:47.195Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-041: Non-text Contrast
2025-07-12T06:57:47.199Z [DEBUG] - 📁 Cache file valid: 51c48e5d1d70bfe79193b22e30987da9.json (167min remaining)
2025-07-12T06:57:47.202Z [DEBUG] - 📁 Cache loaded from file: 51c48e5d1d70bfe79193b22e30987da9.json (key: WCAG-041:053b13d2:add92319...)
2025-07-12T06:57:47.203Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-041:053b13d2:add92319...
2025-07-12T06:57:47.204Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:053b13d2:add92319... (accessed 2 times, age: 4404s)
2025-07-12T06:57:47.204Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-041
2025-07-12T06:57:47.205Z [DEBUG] - ✅ Cache hit: rule:WCAG-041:WCAG-041... (accessed 3 times, age: 4411s)
2025-07-12T06:57:47.206Z [DEBUG] - 📋 Using cached evidence for WCAG-041
2025-07-12T06:57:47.207Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-041 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:47.208Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:47.209Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:47.387Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:47.387Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-041 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":180}
2025-07-12T06:57:47.389Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:47.390Z [DEBUG] - 🔧 Utility performance recorded for WCAG-041: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:47.391Z [DEBUG] - 🔧 Utility analysis completed for WCAG-041: - {"utilitiesUsed":2,"errors":0,"executionTime":194}
2025-07-12T06:57:47.392Z [DEBUG] - ⏱️ Check WCAG-041 completed in 198ms (success: true)
2025-07-12T06:57:47.393Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.398Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.398Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 45% (30/66)
2025-07-12T06:57:47.399Z [INFO] - ✅ Rule WCAG-041 completed: failed (0/100)
2025-07-12T06:57:47.400Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.404Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.405Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 45% (30/66)
2025-07-12T06:57:47.405Z [INFO] - 🔍 Executing rule: Text Spacing (WCAG-042)
2025-07-12T06:57:47.406Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-042: Text Spacing
2025-07-12T06:57:47.423Z [DEBUG] - 📁 Cache file valid: a6023bea83e9245d90991fcca6d694c6.json (167min remaining)
2025-07-12T06:57:47.424Z [DEBUG] - 📁 Cache loaded from file: a6023bea83e9245d90991fcca6d694c6.json (key: WCAG-042:053b13d2:add92319...)
2025-07-12T06:57:47.424Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-042:053b13d2:add92319...
2025-07-12T06:57:47.425Z [DEBUG] - ✅ Cache hit: rule:WCAG-042:053b13d2:add92319... (accessed 2 times, age: 4403s)
2025-07-12T06:57:47.427Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-042
2025-07-12T06:57:47.439Z [DEBUG] - 📁 Cache file valid: 153cca37beb6d4b5378b7047a7af55f3.json (167min remaining)
2025-07-12T06:57:47.440Z [DEBUG] - 📁 Cache loaded from file: 153cca37beb6d4b5378b7047a7af55f3.json (key: WCAG-042:WCAG-042...)
2025-07-12T06:57:47.440Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-042:WCAG-042...
2025-07-12T06:57:47.441Z [DEBUG] - ✅ Cache hit: rule:WCAG-042:WCAG-042... (accessed 2 times, age: 4403s)
2025-07-12T06:57:47.442Z [DEBUG] - 📋 Using cached evidence for WCAG-042
2025-07-12T06:57:47.442Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-042 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:47.443Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:47.445Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:47.446Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:47.638Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:47.638Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-042 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":196}
2025-07-12T06:57:47.640Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:47.642Z [DEBUG] - 🔧 Utility performance recorded for WCAG-042: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:47.642Z [DEBUG] - 🔧 Utility analysis completed for WCAG-042: - {"utilitiesUsed":3,"errors":0,"executionTime":235}
2025-07-12T06:57:47.643Z [DEBUG] - ⏱️ Check WCAG-042 completed in 238ms (success: true)
2025-07-12T06:57:47.644Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.649Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.649Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 47% (31/66)
2025-07-12T06:57:47.650Z [INFO] - ✅ Rule WCAG-042 completed: failed (0/100)
2025-07-12T06:57:47.650Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.654Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.654Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 47% (31/66)
2025-07-12T06:57:47.655Z [INFO] - 🔍 Executing rule: Content on Hover or Focus (WCAG-043)
2025-07-12T06:57:47.659Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-043: Content on Hover or Focus
2025-07-12T06:57:47.662Z [DEBUG] - 📁 Cache file valid: 5214d88911d20698ac817297c9833426.json (167min remaining)
2025-07-12T06:57:47.662Z [DEBUG] - 📁 Cache loaded from file: 5214d88911d20698ac817297c9833426.json (key: WCAG-043:053b13d2:add92319...)
2025-07-12T06:57:47.663Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-043:053b13d2:add92319...
2025-07-12T06:57:47.664Z [DEBUG] - ✅ Cache hit: rule:WCAG-043:053b13d2:add92319... (accessed 2 times, age: 4401s)
2025-07-12T06:57:47.664Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-043
2025-07-12T06:57:47.667Z [DEBUG] - 📁 Cache file valid: 1b5418cf3c968be17db0f146897c9c17.json (167min remaining)
2025-07-12T06:57:47.667Z [DEBUG] - 📁 Cache loaded from file: 1b5418cf3c968be17db0f146897c9c17.json (key: WCAG-043:WCAG-043...)
2025-07-12T06:57:47.668Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-043:WCAG-043...
2025-07-12T06:57:47.669Z [DEBUG] - ✅ Cache hit: rule:WCAG-043:WCAG-043... (accessed 2 times, age: 4401s)
2025-07-12T06:57:47.670Z [DEBUG] - 📋 Using cached evidence for WCAG-043
2025-07-12T06:57:47.674Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-043 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:47.676Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:47.677Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:47.678Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:47.873Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:47.873Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-043 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":199}
2025-07-12T06:57:47.875Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:47.877Z [DEBUG] - 🔧 Utility performance recorded for WCAG-043: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:47.878Z [DEBUG] - 🔧 Utility analysis completed for WCAG-043: - {"utilitiesUsed":3,"errors":0,"executionTime":220}
2025-07-12T06:57:47.879Z [DEBUG] - ⏱️ Check WCAG-043 completed in 224ms (success: true)
2025-07-12T06:57:47.879Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.885Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.885Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 48% (32/66)
2025-07-12T06:57:47.886Z [INFO] - ✅ Rule WCAG-043 completed: failed (0/100)
2025-07-12T06:57:47.887Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.893Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:47.893Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 48% (32/66)
2025-07-12T06:57:47.894Z [INFO] - 🔍 Executing rule: Audio Control (WCAG-050)
2025-07-12T06:57:47.895Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-050: Audio Control
2025-07-12T06:57:47.897Z [DEBUG] - 📁 Cache file valid: 03a937c64b250f0e5f97bd540ecbe76c.json (167min remaining)
2025-07-12T06:57:47.898Z [DEBUG] - 📁 Cache loaded from file: 03a937c64b250f0e5f97bd540ecbe76c.json (key: WCAG-050:053b13d2:add92319...)
2025-07-12T06:57:47.899Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-050:053b13d2:add92319...
2025-07-12T06:57:47.899Z [DEBUG] - ✅ Cache hit: rule:WCAG-050:053b13d2:add92319... (accessed 2 times, age: 4401s)
2025-07-12T06:57:47.900Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-050
2025-07-12T06:57:47.903Z [DEBUG] - 📁 Cache file valid: 22c5971f253d1728b987c254eb8f0974.json (167min remaining)
2025-07-12T06:57:47.904Z [DEBUG] - 📁 Cache loaded from file: 22c5971f253d1728b987c254eb8f0974.json (key: WCAG-050:WCAG-050...)
2025-07-12T06:57:47.905Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-050:WCAG-050...
2025-07-12T06:57:47.907Z [DEBUG] - ✅ Cache hit: rule:WCAG-050:WCAG-050... (accessed 2 times, age: 4401s)
2025-07-12T06:57:47.908Z [DEBUG] - 📋 Using cached evidence for WCAG-050
2025-07-12T06:57:47.909Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-050 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:47.909Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:47.911Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:48.101Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:48.101Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-050 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":193}
2025-07-12T06:57:48.103Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:48.105Z [DEBUG] - 🔧 Utility performance recorded for WCAG-050: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:48.106Z [DEBUG] - 🔧 Utility analysis completed for WCAG-050: - {"utilitiesUsed":2,"errors":0,"executionTime":209}
2025-07-12T06:57:48.107Z [DEBUG] - ⏱️ Check WCAG-050 completed in 213ms (success: true)
2025-07-12T06:57:48.107Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.113Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.113Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 50% (33/66)
2025-07-12T06:57:48.114Z [INFO] - ✅ Rule WCAG-050 completed: passed (100/100)
2025-07-12T06:57:48.115Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.120Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.122Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 50% (33/66)
2025-07-12T06:57:48.124Z [INFO] - 🔍 Executing rule: Keyboard Accessible (WCAG-051)
2025-07-12T06:57:48.125Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-051: Keyboard Accessible
2025-07-12T06:57:48.127Z [DEBUG] - 📁 Cache file valid: 33a17a36ed1c6d42ea750fc0ef49cd03.json (167min remaining)
2025-07-12T06:57:48.128Z [DEBUG] - 📁 Cache loaded from file: 33a17a36ed1c6d42ea750fc0ef49cd03.json (key: WCAG-051:053b13d2:add92319...)
2025-07-12T06:57:48.129Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-051:053b13d2:add92319...
2025-07-12T06:57:48.129Z [DEBUG] - ✅ Cache hit: rule:WCAG-051:053b13d2:add92319... (accessed 2 times, age: 4399s)
2025-07-12T06:57:48.130Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-051
2025-07-12T06:57:48.133Z [DEBUG] - 📁 Cache file valid: 0782c8c437935165aea40991dcb4dc4a.json (167min remaining)
2025-07-12T06:57:48.133Z [DEBUG] - 📁 Cache loaded from file: 0782c8c437935165aea40991dcb4dc4a.json (key: WCAG-051:WCAG-051...)
2025-07-12T06:57:48.135Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-051:WCAG-051...
2025-07-12T06:57:48.136Z [DEBUG] - ✅ Cache hit: rule:WCAG-051:WCAG-051... (accessed 2 times, age: 4399s)
2025-07-12T06:57:48.139Z [DEBUG] - 📋 Using cached evidence for WCAG-051
2025-07-12T06:57:48.141Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-051 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:48.143Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:48.144Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:48.317Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:48.317Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-051 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":176}
2025-07-12T06:57:48.319Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:48.321Z [DEBUG] - 🔧 Utility performance recorded for WCAG-051: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:48.322Z [DEBUG] - 🔧 Utility analysis completed for WCAG-051: - {"utilitiesUsed":2,"errors":0,"executionTime":195}
2025-07-12T06:57:48.323Z [DEBUG] - ⏱️ Check WCAG-051 completed in 200ms (success: true)
2025-07-12T06:57:48.323Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.330Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.330Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 52% (34/66)
2025-07-12T06:57:48.331Z [INFO] - ✅ Rule WCAG-051 completed: failed (0/100)
2025-07-12T06:57:48.335Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.341Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.341Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 52% (34/66)
2025-07-12T06:57:48.342Z [INFO] - 🔍 Executing rule: Character Key Shortcuts (WCAG-052)
2025-07-12T06:57:48.343Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-052: Character Key Shortcuts
2025-07-12T06:57:48.345Z [DEBUG] - 📁 Cache file valid: 6cfd95a26006ae76535b7fc128712b8a.json (167min remaining)
2025-07-12T06:57:48.347Z [DEBUG] - 📁 Cache loaded from file: 6cfd95a26006ae76535b7fc128712b8a.json (key: WCAG-052:053b13d2:add92319...)
2025-07-12T06:57:48.348Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-052:053b13d2:add92319...
2025-07-12T06:57:48.350Z [DEBUG] - ✅ Cache hit: rule:WCAG-052:053b13d2:add92319... (accessed 2 times, age: 4398s)
2025-07-12T06:57:48.351Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-052
2025-07-12T06:57:48.353Z [DEBUG] - 📁 Cache file valid: c49e76409c1749df890ade80ef803b4f.json (167min remaining)
2025-07-12T06:57:48.354Z [DEBUG] - 📁 Cache loaded from file: c49e76409c1749df890ade80ef803b4f.json (key: WCAG-052:WCAG-052...)
2025-07-12T06:57:48.355Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-052:WCAG-052...
2025-07-12T06:57:48.355Z [DEBUG] - ✅ Cache hit: rule:WCAG-052:WCAG-052... (accessed 2 times, age: 4398s)
2025-07-12T06:57:48.356Z [DEBUG] - 📋 Using cached evidence for WCAG-052
2025-07-12T06:57:48.357Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-052 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:48.358Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:48.359Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:48.533Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:48.533Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-052 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":176}
2025-07-12T06:57:48.535Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:48.537Z [DEBUG] - 🔧 Utility performance recorded for WCAG-052: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:48.538Z [DEBUG] - 🔧 Utility analysis completed for WCAG-052: - {"utilitiesUsed":2,"errors":0,"executionTime":193}
2025-07-12T06:57:48.539Z [DEBUG] - ⏱️ Check WCAG-052 completed in 197ms (success: true)
2025-07-12T06:57:48.540Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.547Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.547Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 53% (35/66)
2025-07-12T06:57:48.548Z [INFO] - ✅ Rule WCAG-052 completed: passed (75/100)
2025-07-12T06:57:48.549Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.555Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.555Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 53% (35/66)
2025-07-12T06:57:48.556Z [INFO] - 🔍 Executing rule: Pointer Gestures (WCAG-053)
2025-07-12T06:57:48.558Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-053: Pointer Gestures
2025-07-12T06:57:48.560Z [DEBUG] - 📁 Cache file valid: 18f9e9e7dfa6a0644a8fbb82748d1d26.json (167min remaining)
2025-07-12T06:57:48.561Z [DEBUG] - 📁 Cache loaded from file: 18f9e9e7dfa6a0644a8fbb82748d1d26.json (key: WCAG-053:053b13d2:add92319...)
2025-07-12T06:57:48.562Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-053:053b13d2:add92319...
2025-07-12T06:57:48.562Z [DEBUG] - ✅ Cache hit: rule:WCAG-053:053b13d2:add92319... (accessed 2 times, age: 4398s)
2025-07-12T06:57:48.563Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-053
2025-07-12T06:57:48.567Z [DEBUG] - 📁 Cache file valid: 12cde2393923c93e264e54e16b815352.json (167min remaining)
2025-07-12T06:57:48.569Z [DEBUG] - 📁 Cache loaded from file: 12cde2393923c93e264e54e16b815352.json (key: WCAG-049:WCAG-049...)
2025-07-12T06:57:48.570Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-049:WCAG-049...
2025-07-12T06:57:48.571Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:WCAG-049... (accessed 2 times, age: 4398s)
2025-07-12T06:57:48.572Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-12T06:57:48.573Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-053 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:48.574Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:48.575Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:48.774Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:48.775Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-053 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":202}
2025-07-12T06:57:48.776Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:48.778Z [DEBUG] - 🔧 Utility performance recorded for WCAG-053: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:48.779Z [DEBUG] - 🔧 Utility analysis completed for WCAG-053: - {"utilitiesUsed":2,"errors":0,"executionTime":220}
2025-07-12T06:57:48.780Z [DEBUG] - ⏱️ Check WCAG-053 completed in 224ms (success: true)
2025-07-12T06:57:48.781Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.789Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.789Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 55% (36/66)
2025-07-12T06:57:48.790Z [INFO] - ✅ Rule WCAG-053 completed: passed (100/100)
2025-07-12T06:57:48.790Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.795Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:48.797Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 55% (36/66)
2025-07-12T06:57:48.798Z [INFO] - 🔍 Executing rule: Pointer Cancellation (WCAG-054)
2025-07-12T06:57:48.799Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-054: Pointer Cancellation
2025-07-12T06:57:48.802Z [DEBUG] - 📁 Cache file valid: c98dc2b02e1b577a00e483906e8ca192.json (167min remaining)
2025-07-12T06:57:48.802Z [DEBUG] - 📁 Cache loaded from file: c98dc2b02e1b577a00e483906e8ca192.json (key: WCAG-054:053b13d2:add92319...)
2025-07-12T06:57:48.803Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-054:053b13d2:add92319...
2025-07-12T06:57:48.804Z [DEBUG] - ✅ Cache hit: rule:WCAG-054:053b13d2:add92319... (accessed 2 times, age: 4397s)
2025-07-12T06:57:48.804Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-054
2025-07-12T06:57:48.807Z [DEBUG] - 📁 Cache file valid: d282ada86df9f29b48342af5f0aa8881.json (167min remaining)
2025-07-12T06:57:48.807Z [DEBUG] - 📁 Cache loaded from file: d282ada86df9f29b48342af5f0aa8881.json (key: WCAG-054:WCAG-054...)
2025-07-12T06:57:48.808Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-054:WCAG-054...
2025-07-12T06:57:48.809Z [DEBUG] - ✅ Cache hit: rule:WCAG-054:WCAG-054... (accessed 2 times, age: 4397s)
2025-07-12T06:57:48.809Z [DEBUG] - 📋 Using cached evidence for WCAG-054
2025-07-12T06:57:48.810Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-054 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:48.814Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:48.815Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:48.988Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:48.988Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-054 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":178}
2025-07-12T06:57:48.990Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:48.992Z [DEBUG] - 🔧 Utility performance recorded for WCAG-054: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:48.993Z [DEBUG] - 🔧 Utility analysis completed for WCAG-054: - {"utilitiesUsed":2,"errors":0,"executionTime":192}
2025-07-12T06:57:48.993Z [DEBUG] - ⏱️ Check WCAG-054 completed in 195ms (success: true)
2025-07-12T06:57:48.994Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.000Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.001Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 56% (37/66)
2025-07-12T06:57:49.001Z [INFO] - ✅ Rule WCAG-054 completed: passed (100/100)
2025-07-12T06:57:49.002Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.006Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.008Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 56% (37/66)
2025-07-12T06:57:49.009Z [INFO] - 🔍 Executing rule: Label in Name (WCAG-055)
2025-07-12T06:57:49.010Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-055: Label in Name
2025-07-12T06:57:49.012Z [DEBUG] - 📁 Cache file valid: a2f4f89f62b43b45ced080eefd45fdef.json (167min remaining)
2025-07-12T06:57:49.012Z [DEBUG] - 📁 Cache loaded from file: a2f4f89f62b43b45ced080eefd45fdef.json (key: WCAG-055:053b13d2:add92319...)
2025-07-12T06:57:49.013Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-055:053b13d2:add92319...
2025-07-12T06:57:49.014Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:053b13d2:add92319... (accessed 2 times, age: 4392s)
2025-07-12T06:57:49.015Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-055
2025-07-12T06:57:49.018Z [DEBUG] - 📁 Cache file valid: 5da1af541ddaa467b915848dbb0b3030.json (167min remaining)
2025-07-12T06:57:49.018Z [DEBUG] - 📁 Cache loaded from file: 5da1af541ddaa467b915848dbb0b3030.json (key: WCAG-055:WCAG-055...)
2025-07-12T06:57:49.019Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-055:WCAG-055...
2025-07-12T06:57:49.020Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:WCAG-055... (accessed 2 times, age: 4392s)
2025-07-12T06:57:49.020Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-12T06:57:49.023Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-055 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:49.024Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:49.025Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 10 times, age: 4476s)
2025-07-12T06:57:49.026Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:49.027Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:49.028Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:49.029Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:49.030Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:49.031Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:49.040Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:49.053Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:49.054Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-055 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":31}
2025-07-12T06:57:49.055Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:49.057Z [DEBUG] - 🔧 Utility performance recorded for WCAG-055: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:49.058Z [DEBUG] - 🔧 Utility analysis completed for WCAG-055: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T06:57:49.058Z [DEBUG] - ⏱️ Check WCAG-055 completed in 50ms (success: true)
2025-07-12T06:57:49.059Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.064Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.064Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 58% (38/66)
2025-07-12T06:57:49.065Z [INFO] - ✅ Rule WCAG-055 completed: failed (0/100)
2025-07-12T06:57:49.066Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.073Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.074Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 58% (38/66)
2025-07-12T06:57:49.074Z [INFO] - 🔍 Executing rule: Motion Actuation (WCAG-056)
2025-07-12T06:57:49.075Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-056: Motion Actuation
2025-07-12T06:57:49.078Z [DEBUG] - 📁 Cache file valid: 4871a48558d3f2cd5e11bf922bb8c891.json (167min remaining)
2025-07-12T06:57:49.078Z [DEBUG] - 📁 Cache loaded from file: 4871a48558d3f2cd5e11bf922bb8c891.json (key: WCAG-056:053b13d2:add92319...)
2025-07-12T06:57:49.079Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-056:053b13d2:add92319...
2025-07-12T06:57:49.079Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:053b13d2:add92319... (accessed 2 times, age: 4391s)
2025-07-12T06:57:49.080Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-056
2025-07-12T06:57:49.083Z [DEBUG] - 📁 Cache file valid: 8ff67000f1a92dc4301713b6e619f281.json (167min remaining)
2025-07-12T06:57:49.085Z [DEBUG] - 📁 Cache loaded from file: 8ff67000f1a92dc4301713b6e619f281.json (key: WCAG-056:WCAG-056...)
2025-07-12T06:57:49.086Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-056:WCAG-056...
2025-07-12T06:57:49.087Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:WCAG-056... (accessed 2 times, age: 4391s)
2025-07-12T06:57:49.087Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-12T06:57:49.088Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-056 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:49.089Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:49.090Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:49.269Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:49.269Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-056 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":181}
2025-07-12T06:57:49.271Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:49.273Z [DEBUG] - 🔧 Utility performance recorded for WCAG-056: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:49.273Z [DEBUG] - 🔧 Utility analysis completed for WCAG-056: - {"utilitiesUsed":2,"errors":0,"executionTime":197}
2025-07-12T06:57:49.274Z [DEBUG] - ⏱️ Check WCAG-056 completed in 200ms (success: true)
2025-07-12T06:57:49.275Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.281Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.281Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 59% (39/66)
2025-07-12T06:57:49.282Z [INFO] - ✅ Rule WCAG-056 completed: passed (100/100)
2025-07-12T06:57:49.283Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.290Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:49.290Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 59% (39/66)
2025-07-12T06:57:49.291Z [INFO] - 🔍 Executing rule: Target Size Enhanced (WCAG-058)
2025-07-12T06:57:49.293Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting WCAG-058: Target Size Enhanced
2025-07-12T06:57:49.293Z [DEBUG] - 📱 Starting responsive layout analysis
2025-07-12T06:57:50.095Z [WARN] - ⚠️ [ed85e707-19e7-4046-913e-301608345e71] WCAG-058 failed: 0.0% (threshold: 75%) - FAILED
2025-07-12T06:57:50.095Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Completed WCAG-058 in 802ms - Status: failed (0/100)
2025-07-12T06:57:50.099Z [DEBUG] - ✅ Cache hit: rule:WCAG-055:WCAG-055... (accessed 3 times, age: 4393s)
2025-07-12T06:57:50.100Z [DEBUG] - 📋 Using cached evidence for WCAG-055
2025-07-12T06:57:50.101Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-058 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.102Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:50.103Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:50.326Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:50.326Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-058 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":225}
2025-07-12T06:57:50.328Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:50.330Z [DEBUG] - 🔧 Utility performance recorded for WCAG-058: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:50.331Z [DEBUG] - 🔧 Utility analysis completed for WCAG-058: - {"utilitiesUsed":2,"errors":0,"executionTime":1037}
2025-07-12T06:57:50.332Z [DEBUG] - ⏱️ Check WCAG-058 completed in 1041ms (success: true)
2025-07-12T06:57:50.333Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.339Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.339Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 61% (40/66)
2025-07-12T06:57:50.340Z [INFO] - ✅ Rule WCAG-058 completed: failed (0/100)
2025-07-12T06:57:50.344Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.349Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.350Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 61% (40/66)
2025-07-12T06:57:50.351Z [INFO] - 🔍 Executing rule: Concurrent Input Mechanisms (WCAG-059)
2025-07-12T06:57:50.352Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-059: Concurrent Input Mechanisms
2025-07-12T06:57:50.355Z [DEBUG] - 📁 Cache file valid: de67f57cc2635b0704bae41ccf557edd.json (167min remaining)
2025-07-12T06:57:50.357Z [DEBUG] - 📁 Cache loaded from file: de67f57cc2635b0704bae41ccf557edd.json (key: WCAG-059:053b13d2:add92319...)
2025-07-12T06:57:50.358Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-059:053b13d2:add92319...
2025-07-12T06:57:50.360Z [DEBUG] - ✅ Cache hit: rule:WCAG-059:053b13d2:add92319... (accessed 2 times, age: 4390s)
2025-07-12T06:57:50.361Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-059
2025-07-12T06:57:50.365Z [DEBUG] - 📁 Cache file valid: c40334838e5426b6fddc1e74ce9760a1.json (167min remaining)
2025-07-12T06:57:50.365Z [DEBUG] - 📁 Cache loaded from file: c40334838e5426b6fddc1e74ce9760a1.json (key: WCAG-059:WCAG-059...)
2025-07-12T06:57:50.366Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-059:WCAG-059...
2025-07-12T06:57:50.367Z [DEBUG] - ✅ Cache hit: rule:WCAG-059:WCAG-059... (accessed 2 times, age: 4390s)
2025-07-12T06:57:50.368Z [DEBUG] - 📋 Using cached evidence for WCAG-059
2025-07-12T06:57:50.369Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-059 - {"config":{"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.370Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:50.372Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:50.407Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-059 - {"utilitiesUsed":["component-library","framework-optimization"],"confidence":0.7,"accuracy":0.45,"executionTime":38}
2025-07-12T06:57:50.407Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:50.408Z [DEBUG] - 🔧 Utility performance recorded for WCAG-059: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:50.410Z [DEBUG] - 🔧 Utility analysis completed for WCAG-059: - {"utilitiesUsed":2,"errors":0,"executionTime":56}
2025-07-12T06:57:50.411Z [DEBUG] - ⏱️ Check WCAG-059 completed in 59ms (success: true)
2025-07-12T06:57:50.411Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.416Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.417Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 62% (41/66)
2025-07-12T06:57:50.418Z [INFO] - ✅ Rule WCAG-059 completed: failed (0/100)
2025-07-12T06:57:50.419Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.423Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.423Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 62% (41/66)
2025-07-12T06:57:50.424Z [INFO] - 🔍 Executing rule: Unusual Words (WCAG-060)
2025-07-12T06:57:50.425Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-060: Unusual Words
2025-07-12T06:57:50.428Z [DEBUG] - 📁 Cache file valid: f33dbf5b167f7819cd8f5243d29a7d8d.json (167min remaining)
2025-07-12T06:57:50.429Z [DEBUG] - 📁 Cache loaded from file: f33dbf5b167f7819cd8f5243d29a7d8d.json (key: WCAG-060:053b13d2:add92319...)
2025-07-12T06:57:50.430Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-060:053b13d2:add92319...
2025-07-12T06:57:50.432Z [DEBUG] - ✅ Cache hit: rule:WCAG-060:053b13d2:add92319... (accessed 2 times, age: 4390s)
2025-07-12T06:57:50.433Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-060
2025-07-12T06:57:50.436Z [DEBUG] - 📁 Cache file valid: 6e0c6281873e8247f3f140619a454c39.json (167min remaining)
2025-07-12T06:57:50.436Z [DEBUG] - 📁 Cache loaded from file: 6e0c6281873e8247f3f140619a454c39.json (key: WCAG-060:WCAG-060...)
2025-07-12T06:57:50.437Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-060:WCAG-060...
2025-07-12T06:57:50.438Z [DEBUG] - ✅ Cache hit: rule:WCAG-060:WCAG-060... (accessed 2 times, age: 4390s)
2025-07-12T06:57:50.438Z [DEBUG] - 📋 Using cached evidence for WCAG-060
2025-07-12T06:57:50.439Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-060 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.440Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:50.441Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 11 times, age: 4477s)
2025-07-12T06:57:50.442Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:50.446Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:50.447Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:50.448Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:50.450Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:50.451Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:50.477Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:50.496Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:50.498Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-060 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":59}
2025-07-12T06:57:50.499Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:50.500Z [DEBUG] - 🔧 Utility performance recorded for WCAG-060: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:50.503Z [DEBUG] - 🔧 Utility analysis completed for WCAG-060: - {"utilitiesUsed":2,"errors":0,"executionTime":75}
2025-07-12T06:57:50.505Z [DEBUG] - ⏱️ Check WCAG-060 completed in 81ms (success: true)
2025-07-12T06:57:50.506Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.519Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.519Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 64% (42/66)
2025-07-12T06:57:50.520Z [INFO] - ✅ Rule WCAG-060 completed: failed (0/100)
2025-07-12T06:57:50.522Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.526Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.527Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 64% (42/66)
2025-07-12T06:57:50.528Z [INFO] - 🔍 Executing rule: Abbreviations (WCAG-061)
2025-07-12T06:57:50.528Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-061: Abbreviations
2025-07-12T06:57:50.531Z [DEBUG] - 📁 Cache file valid: 79b5cd7a70a41bc232e7bb4b6db82dfa.json (167min remaining)
2025-07-12T06:57:50.534Z [DEBUG] - 📁 Cache loaded from file: 79b5cd7a70a41bc232e7bb4b6db82dfa.json (key: WCAG-061:053b13d2:add92319...)
2025-07-12T06:57:50.535Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-061:053b13d2:add92319...
2025-07-12T06:57:50.535Z [DEBUG] - ✅ Cache hit: rule:WCAG-061:053b13d2:add92319... (accessed 2 times, age: 4378s)
2025-07-12T06:57:50.536Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-061
2025-07-12T06:57:50.539Z [DEBUG] - 📁 Cache file valid: 444acc81fee9b5e62cda4ea16fdaf9dc.json (167min remaining)
2025-07-12T06:57:50.540Z [DEBUG] - 📁 Cache loaded from file: 444acc81fee9b5e62cda4ea16fdaf9dc.json (key: WCAG-061:WCAG-061...)
2025-07-12T06:57:50.540Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-061:WCAG-061...
2025-07-12T06:57:50.541Z [DEBUG] - ✅ Cache hit: rule:WCAG-061:WCAG-061... (accessed 2 times, age: 4378s)
2025-07-12T06:57:50.542Z [DEBUG] - 📋 Using cached evidence for WCAG-061
2025-07-12T06:57:50.543Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-061 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.543Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:50.544Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 12 times, age: 4478s)
2025-07-12T06:57:50.545Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:50.551Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:50.551Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:50.553Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:50.554Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:50.555Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:50.574Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:50.591Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:50.593Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-061 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":50}
2025-07-12T06:57:50.594Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:50.595Z [DEBUG] - 🔧 Utility performance recorded for WCAG-061: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:50.596Z [DEBUG] - 🔧 Utility analysis completed for WCAG-061: - {"utilitiesUsed":2,"errors":0,"executionTime":65}
2025-07-12T06:57:50.596Z [DEBUG] - ⏱️ Check WCAG-061 completed in 69ms (success: true)
2025-07-12T06:57:50.597Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.603Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.603Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 65% (43/66)
2025-07-12T06:57:50.604Z [INFO] - ✅ Rule WCAG-061 completed: failed (0/100)
2025-07-12T06:57:50.604Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.611Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.611Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 65% (43/66)
2025-07-12T06:57:50.612Z [INFO] - 🔍 Executing rule: Reading Level (WCAG-062)
2025-07-12T06:57:50.614Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-062: Reading Level
2025-07-12T06:57:50.617Z [DEBUG] - 📁 Cache file valid: 42dc7144c5be663ce950fd291e4d98d7.json (167min remaining)
2025-07-12T06:57:50.617Z [DEBUG] - 📁 Cache loaded from file: 42dc7144c5be663ce950fd291e4d98d7.json (key: WCAG-062:053b13d2:add92319...)
2025-07-12T06:57:50.618Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-062:053b13d2:add92319...
2025-07-12T06:57:50.619Z [DEBUG] - ✅ Cache hit: rule:WCAG-062:053b13d2:add92319... (accessed 2 times, age: 4377s)
2025-07-12T06:57:50.620Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-062
2025-07-12T06:57:50.623Z [DEBUG] - 📁 Cache file valid: 55656632f66aff78067600822a1d12fd.json (167min remaining)
2025-07-12T06:57:50.623Z [DEBUG] - 📁 Cache loaded from file: 55656632f66aff78067600822a1d12fd.json (key: WCAG-062:WCAG-062...)
2025-07-12T06:57:50.624Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-062:WCAG-062...
2025-07-12T06:57:50.626Z [DEBUG] - ✅ Cache hit: rule:WCAG-062:WCAG-062... (accessed 2 times, age: 4377s)
2025-07-12T06:57:50.627Z [DEBUG] - 📋 Using cached evidence for WCAG-062
2025-07-12T06:57:50.627Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-062 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.628Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:50.629Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 13 times, age: 4478s)
2025-07-12T06:57:50.630Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:50.631Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:50.632Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:50.634Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:50.634Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:50.635Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:50.659Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:50.678Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:50.680Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-062 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":53}
2025-07-12T06:57:50.680Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:50.681Z [DEBUG] - 🔧 Utility performance recorded for WCAG-062: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:50.682Z [DEBUG] - 🔧 Utility analysis completed for WCAG-062: - {"utilitiesUsed":2,"errors":0,"executionTime":68}
2025-07-12T06:57:50.685Z [DEBUG] - ⏱️ Check WCAG-062 completed in 72ms (success: true)
2025-07-12T06:57:50.686Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.692Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.693Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 67% (44/66)
2025-07-12T06:57:50.693Z [INFO] - ✅ Rule WCAG-062 completed: failed (0/100)
2025-07-12T06:57:50.694Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.699Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.700Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 67% (44/66)
2025-07-12T06:57:50.701Z [INFO] - 🔍 Executing rule: Pronunciation (WCAG-063)
2025-07-12T06:57:50.702Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-063: Pronunciation
2025-07-12T06:57:50.705Z [DEBUG] - 📁 Cache file valid: 0c14c6b55c802116f5cf12801e8a61cb.json (170min remaining)
2025-07-12T06:57:50.706Z [DEBUG] - 📁 Cache loaded from file: 0c14c6b55c802116f5cf12801e8a61cb.json (key: WCAG-063:053b13d2:add92319...)
2025-07-12T06:57:50.707Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-063:053b13d2:add92319...
2025-07-12T06:57:50.707Z [DEBUG] - ✅ Cache hit: rule:WCAG-063:053b13d2:add92319... (accessed 2 times, age: 4204s)
2025-07-12T06:57:50.708Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-063
2025-07-12T06:57:50.712Z [DEBUG] - 📁 Cache file valid: 39507b86200ef80a538f4fcd9d085cfb.json (170min remaining)
2025-07-12T06:57:50.714Z [DEBUG] - 📁 Cache loaded from file: 39507b86200ef80a538f4fcd9d085cfb.json (key: WCAG-063:WCAG-063...)
2025-07-12T06:57:50.717Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-063:WCAG-063...
2025-07-12T06:57:50.718Z [DEBUG] - ✅ Cache hit: rule:WCAG-063:WCAG-063... (accessed 2 times, age: 4204s)
2025-07-12T06:57:50.719Z [DEBUG] - 📋 Using cached evidence for WCAG-063
2025-07-12T06:57:50.720Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-063 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.721Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:50.723Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 14 times, age: 4478s)
2025-07-12T06:57:50.724Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:50.726Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:50.729Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:50.734Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:50.736Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:50.739Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:50.775Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:50.796Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:50.799Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-063 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":79}
2025-07-12T06:57:50.799Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:50.800Z [DEBUG] - 🔧 Utility performance recorded for WCAG-063: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:50.801Z [DEBUG] - 🔧 Utility analysis completed for WCAG-063: - {"utilitiesUsed":2,"errors":0,"executionTime":98}
2025-07-12T06:57:50.802Z [DEBUG] - ⏱️ Check WCAG-063 completed in 101ms (success: true)
2025-07-12T06:57:50.802Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.808Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.808Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 68% (45/66)
2025-07-12T06:57:50.809Z [INFO] - ✅ Rule WCAG-063 completed: failed (0/100)
2025-07-12T06:57:50.811Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.816Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:50.816Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 68% (45/66)
2025-07-12T06:57:50.817Z [INFO] - 🔍 Executing rule: Change on Request (WCAG-064)
2025-07-12T06:57:50.818Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-064: Change on Request
2025-07-12T06:57:50.820Z [DEBUG] - 📊 Cache hit rate discrepancy: actual=100.0%, reported=0.0%
2025-07-12T06:57:50.822Z [DEBUG] - 📊 Dashboard metrics updated - {"activeScans":1,"systemHealth":"excellent","performanceScore":85,"cacheHitRate":"100.0%","alerts":0}
2025-07-12T06:57:50.825Z [DEBUG] - 📁 Cache file valid: 5a40d51c15a824f433f94fee6332db5e.json (170min remaining)
2025-07-12T06:57:50.825Z [DEBUG] - 📁 Cache loaded from file: 5a40d51c15a824f433f94fee6332db5e.json (key: WCAG-064:053b13d2:add92319...)
2025-07-12T06:57:50.826Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-064:053b13d2:add92319...
2025-07-12T06:57:50.827Z [DEBUG] - ✅ Cache hit: rule:WCAG-064:053b13d2:add92319... (accessed 2 times, age: 4204s)
2025-07-12T06:57:50.827Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-064
2025-07-12T06:57:50.830Z [DEBUG] - 📁 Cache file valid: bd402f34a3490413e5e9d577c7f15e8f.json (170min remaining)
2025-07-12T06:57:50.831Z [DEBUG] - 📁 Cache loaded from file: bd402f34a3490413e5e9d577c7f15e8f.json (key: WCAG-064:WCAG-064...)
2025-07-12T06:57:50.832Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-064:WCAG-064...
2025-07-12T06:57:50.837Z [DEBUG] - ✅ Cache hit: rule:WCAG-064:WCAG-064... (accessed 2 times, age: 4204s)
2025-07-12T06:57:50.838Z [DEBUG] - 📋 Using cached evidence for WCAG-064
2025-07-12T06:57:50.840Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-064 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:50.840Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:50.842Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:51.023Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:51.023Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-064 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":183}
2025-07-12T06:57:51.025Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:51.028Z [DEBUG] - 🔧 Utility performance recorded for WCAG-064: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:51.029Z [DEBUG] - 🔧 Utility analysis completed for WCAG-064: - {"utilitiesUsed":2,"errors":0,"executionTime":208}
2025-07-12T06:57:51.029Z [DEBUG] - ⏱️ Check WCAG-064 completed in 212ms (success: true)
2025-07-12T06:57:51.030Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.035Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.036Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 70% (46/66)
2025-07-12T06:57:51.037Z [INFO] - ✅ Rule WCAG-064 completed: failed (0/100)
2025-07-12T06:57:51.038Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.042Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.043Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 70% (46/66)
2025-07-12T06:57:51.043Z [INFO] - 🔍 Executing rule: Help (WCAG-065)
2025-07-12T06:57:51.045Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-065: Help
2025-07-12T06:57:51.045Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:053b13d2:add92319... (accessed 3 times, age: 4434s)
2025-07-12T06:57:51.046Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-065
2025-07-12T06:57:51.046Z [DEBUG] - ✅ Cache hit: rule:WCAG-065:WCAG-065... (accessed 3 times, age: 4434s)
2025-07-12T06:57:51.047Z [DEBUG] - 📋 Using cached evidence for WCAG-065
2025-07-12T06:57:51.048Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-065 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:51.049Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:51.050Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:51.051Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 4 times, age: 10s)
2025-07-12T06:57:51.052Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:51.086Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:51.087Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-065 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":39}
2025-07-12T06:57:51.089Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-065: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:51.091Z [DEBUG] - 🔧 Utility performance recorded for WCAG-065: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:51.091Z [DEBUG] - 🔧 Utility analysis completed for WCAG-065: - {"utilitiesUsed":2,"errors":0,"executionTime":45}
2025-07-12T06:57:51.092Z [DEBUG] - ⏱️ Check WCAG-065 completed in 49ms (success: true)
2025-07-12T06:57:51.093Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.097Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.098Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 71% (47/66)
2025-07-12T06:57:51.099Z [INFO] - ✅ Rule WCAG-065 completed: passed (75/100)
2025-07-12T06:57:51.101Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.106Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.107Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 71% (47/66)
2025-07-12T06:57:51.107Z [INFO] - 🔍 Executing rule: Error Prevention Enhanced (WCAG-066)
2025-07-12T06:57:51.109Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-066: Error Prevention Enhanced
2025-07-12T06:57:51.110Z [DEBUG] - 📁 Cache file valid: 42a6a4719458cd8c2297ef4ce76e8acd.json (170min remaining)
2025-07-12T06:57:51.111Z [DEBUG] - 📁 Cache loaded from file: 42a6a4719458cd8c2297ef4ce76e8acd.json (key: WCAG-066:053b13d2:add92319...)
2025-07-12T06:57:51.112Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-066:053b13d2:add92319...
2025-07-12T06:57:51.112Z [DEBUG] - ✅ Cache hit: rule:WCAG-066:053b13d2:add92319... (accessed 2 times, age: 4203s)
2025-07-12T06:57:51.113Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-066
2025-07-12T06:57:51.117Z [DEBUG] - 📁 Cache file valid: d9087b62feeb8ebe1c97494a35155993.json (170min remaining)
2025-07-12T06:57:51.117Z [DEBUG] - 📁 Cache loaded from file: d9087b62feeb8ebe1c97494a35155993.json (key: WCAG-066:WCAG-066...)
2025-07-12T06:57:51.118Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-066:WCAG-066...
2025-07-12T06:57:51.120Z [DEBUG] - ✅ Cache hit: rule:WCAG-066:WCAG-066... (accessed 2 times, age: 4203s)
2025-07-12T06:57:51.121Z [DEBUG] - 📋 Using cached evidence for WCAG-066
2025-07-12T06:57:51.121Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-066 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:51.122Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:51.124Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:51.309Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:51.309Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-066 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":188}
2025-07-12T06:57:51.311Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:51.315Z [DEBUG] - 🔧 Utility performance recorded for WCAG-066: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:51.316Z [DEBUG] - 🔧 Utility analysis completed for WCAG-066: - {"utilitiesUsed":2,"errors":0,"executionTime":204}
2025-07-12T06:57:51.317Z [DEBUG] - ⏱️ Check WCAG-066 completed in 210ms (success: true)
2025-07-12T06:57:51.318Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.324Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.324Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 73% (48/66)
2025-07-12T06:57:51.325Z [INFO] - ✅ Rule WCAG-066 completed: failed (0/100)
2025-07-12T06:57:51.326Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.330Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.331Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 73% (48/66)
2025-07-12T06:57:51.332Z [INFO] - 🔍 Executing rule: Status Messages (WCAG-057)
2025-07-12T06:57:51.333Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-057: Status Messages
2025-07-12T06:57:51.336Z [DEBUG] - 📁 Cache file valid: 45d26f3295717a92f8bd1aec0a3c8557.json (170min remaining)
2025-07-12T06:57:51.337Z [DEBUG] - 📁 Cache loaded from file: 45d26f3295717a92f8bd1aec0a3c8557.json (key: WCAG-057:053b13d2:add92319...)
2025-07-12T06:57:51.337Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-057:053b13d2:add92319...
2025-07-12T06:57:51.338Z [DEBUG] - ✅ Cache hit: rule:WCAG-057:053b13d2:add92319... (accessed 2 times, age: 4203s)
2025-07-12T06:57:51.339Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-057
2025-07-12T06:57:51.342Z [DEBUG] - 📁 Cache file valid: d5679298ae7d049fc06accf18537cdf9.json (170min remaining)
2025-07-12T06:57:51.342Z [DEBUG] - 📁 Cache loaded from file: d5679298ae7d049fc06accf18537cdf9.json (key: WCAG-057:WCAG-057...)
2025-07-12T06:57:51.343Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-057:WCAG-057...
2025-07-12T06:57:51.344Z [DEBUG] - ✅ Cache hit: rule:WCAG-057:WCAG-057... (accessed 2 times, age: 4203s)
2025-07-12T06:57:51.347Z [DEBUG] - 📋 Using cached evidence for WCAG-057
2025-07-12T06:57:51.350Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-057 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:51.350Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:51.351Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 15 times, age: 4478s)
2025-07-12T06:57:51.352Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:51.353Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:51.355Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:51.355Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:51.356Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:51.360Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:51.361Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:51.368Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:51.385Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:51.385Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-057 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":35}
2025-07-12T06:57:51.386Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:51.389Z [DEBUG] - 🔧 Utility performance recorded for WCAG-057: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:51.390Z [DEBUG] - 🔧 Utility analysis completed for WCAG-057: - {"utilitiesUsed":3,"errors":0,"executionTime":54}
2025-07-12T06:57:51.392Z [DEBUG] - ⏱️ Check WCAG-057 completed in 60ms (success: true)
2025-07-12T06:57:51.393Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.398Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.398Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 74% (49/66)
2025-07-12T06:57:51.399Z [INFO] - ✅ Rule WCAG-057 completed: passed (100/100)
2025-07-12T06:57:51.400Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.407Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.407Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 74% (49/66)
2025-07-12T06:57:51.408Z [INFO] - 🔍 Executing rule: Skip Links (WCAG-047)
2025-07-12T06:57:51.410Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-047: Skip Links
2025-07-12T06:57:51.413Z [DEBUG] - 📁 Cache file valid: 5003fdcdca0d68384074f375ad9bbced.json (170min remaining)
2025-07-12T06:57:51.413Z [DEBUG] - 📁 Cache loaded from file: 5003fdcdca0d68384074f375ad9bbced.json (key: WCAG-047:053b13d2:add92319...)
2025-07-12T06:57:51.415Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-047:053b13d2:add92319...
2025-07-12T06:57:51.415Z [DEBUG] - ✅ Cache hit: rule:WCAG-047:053b13d2:add92319... (accessed 2 times, age: 4200s)
2025-07-12T06:57:51.416Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-047
2025-07-12T06:57:51.419Z [DEBUG] - 📁 Cache file valid: 306a6611ec109dccc0c3cc13f1967422.json (170min remaining)
2025-07-12T06:57:51.420Z [DEBUG] - 📁 Cache loaded from file: 306a6611ec109dccc0c3cc13f1967422.json (key: WCAG-047:WCAG-047...)
2025-07-12T06:57:51.421Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-047:WCAG-047...
2025-07-12T06:57:51.423Z [DEBUG] - ✅ Cache hit: rule:WCAG-047:WCAG-047... (accessed 2 times, age: 4200s)
2025-07-12T06:57:51.423Z [DEBUG] - 📋 Using cached evidence for WCAG-047
2025-07-12T06:57:51.424Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-047 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T06:57:51.425Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:51.426Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:51.427Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:51.621Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:51.622Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-047 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":198}
2025-07-12T06:57:51.624Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:51.626Z [DEBUG] - 🔧 Utility performance recorded for WCAG-047: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:51.629Z [DEBUG] - 🔧 Utility analysis completed for WCAG-047: - {"utilitiesUsed":3,"errors":0,"executionTime":216}
2025-07-12T06:57:51.629Z [DEBUG] - ⏱️ Check WCAG-047 completed in 221ms (success: true)
2025-07-12T06:57:51.630Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.637Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.637Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 76% (50/66)
2025-07-12T06:57:51.638Z [INFO] - ✅ Rule WCAG-047 completed: failed (0/100)
2025-07-12T06:57:51.639Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.643Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.644Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 76% (50/66)
2025-07-12T06:57:51.645Z [INFO] - 🔍 Executing rule: Enhanced Focus Management (WCAG-048)
2025-07-12T06:57:51.646Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-048: Enhanced Focus Management
2025-07-12T06:57:51.649Z [DEBUG] - 📁 Cache file valid: 49b79b6e25ca864115e6cd6beea15c0b.json (170min remaining)
2025-07-12T06:57:51.649Z [DEBUG] - 📁 Cache loaded from file: 49b79b6e25ca864115e6cd6beea15c0b.json (key: WCAG-048:053b13d2:add92319...)
2025-07-12T06:57:51.650Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-048:053b13d2:add92319...
2025-07-12T06:57:51.651Z [DEBUG] - ✅ Cache hit: rule:WCAG-048:053b13d2:add92319... (accessed 2 times, age: 4199s)
2025-07-12T06:57:51.652Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-048
2025-07-12T06:57:51.653Z [DEBUG] - ✅ Cache hit: rule:WCAG-056:WCAG-056... (accessed 3 times, age: 4394s)
2025-07-12T06:57:51.653Z [DEBUG] - 📋 Using cached evidence for WCAG-056
2025-07-12T06:57:51.654Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-048 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T06:57:51.655Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:51.656Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:51.660Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:51.853Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:51.853Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-048 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":199}
2025-07-12T06:57:51.855Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:51.857Z [DEBUG] - 🔧 Utility performance recorded for WCAG-048: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:51.858Z [DEBUG] - 🔧 Utility analysis completed for WCAG-048: - {"utilitiesUsed":3,"errors":0,"executionTime":210}
2025-07-12T06:57:51.859Z [DEBUG] - ⏱️ Check WCAG-048 completed in 214ms (success: true)
2025-07-12T06:57:51.860Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.866Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.866Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 77% (51/66)
2025-07-12T06:57:51.867Z [INFO] - ✅ Rule WCAG-048 completed: failed (0/100)
2025-07-12T06:57:51.868Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.872Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:51.873Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 77% (51/66)
2025-07-12T06:57:51.876Z [INFO] - 🔍 Executing rule: Link Context (WCAG-049)
2025-07-12T06:57:51.877Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-049: Link Context
2025-07-12T06:57:51.880Z [DEBUG] - 📁 Cache file valid: 008eb54f4d432fcc3f7258c8e293b10f.json (170min remaining)
2025-07-12T06:57:51.880Z [DEBUG] - 📁 Cache loaded from file: 008eb54f4d432fcc3f7258c8e293b10f.json (key: WCAG-049:053b13d2:add92319...)
2025-07-12T06:57:51.881Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-049:053b13d2:add92319...
2025-07-12T06:57:51.882Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:053b13d2:add92319... (accessed 2 times, age: 4199s)
2025-07-12T06:57:51.883Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-049
2025-07-12T06:57:51.883Z [DEBUG] - ✅ Cache hit: rule:WCAG-049:WCAG-049... (accessed 3 times, age: 4401s)
2025-07-12T06:57:51.884Z [DEBUG] - 📋 Using cached evidence for WCAG-049
2025-07-12T06:57:51.885Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-049 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"enableComponentLibraryDetection":true,"integrationStrategy":"supplement","maxExecutionTime":5000}}
2025-07-12T06:57:51.885Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:51.887Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:51.888Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:52.087Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:52.087Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-049 - {"utilitiesUsed":["component-library","framework-optimization","pattern-validation"],"confidence":0.875,"accuracy":0.65,"executionTime":202}
2025-07-12T06:57:52.090Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.091Z [DEBUG] - 🔧 Utility performance recorded for WCAG-049: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.092Z [DEBUG] - 🔧 Utility analysis completed for WCAG-049: - {"utilitiesUsed":3,"errors":0,"executionTime":213}
2025-07-12T06:57:52.094Z [DEBUG] - ⏱️ Check WCAG-049 completed in 217ms (success: true)
2025-07-12T06:57:52.094Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.104Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.105Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 79% (52/66)
2025-07-12T06:57:52.106Z [INFO] - ✅ Rule WCAG-049 completed: failed (0/100)
2025-07-12T06:57:52.108Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.121Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.121Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 79% (52/66)
2025-07-12T06:57:52.122Z [INFO] - 🔍 Executing rule: Language of Page (WCAG-024)
2025-07-12T06:57:52.125Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-024: Language of Page
2025-07-12T06:57:52.127Z [DEBUG] - 📁 Cache file valid: 5c1e9d7eec6a203c208331b409edb500.json (170min remaining)
2025-07-12T06:57:52.127Z [DEBUG] - 📁 Cache loaded from file: 5c1e9d7eec6a203c208331b409edb500.json (key: WCAG-024:053b13d2:add92319...)
2025-07-12T06:57:52.128Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-024:053b13d2:add92319...
2025-07-12T06:57:52.129Z [DEBUG] - ✅ Cache hit: rule:WCAG-024:053b13d2:add92319... (accessed 2 times, age: 4198s)
2025-07-12T06:57:52.129Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-024
2025-07-12T06:57:52.132Z [DEBUG] - 📁 Cache file valid: cccac7bc70a2c5813d5114243f43e9fe.json (170min remaining)
2025-07-12T06:57:52.132Z [DEBUG] - 📁 Cache loaded from file: cccac7bc70a2c5813d5114243f43e9fe.json (key: WCAG-024:WCAG-024...)
2025-07-12T06:57:52.133Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-024:WCAG-024...
2025-07-12T06:57:52.136Z [DEBUG] - ✅ Cache hit: rule:WCAG-024:WCAG-024... (accessed 2 times, age: 4198s)
2025-07-12T06:57:52.138Z [DEBUG] - 📋 Using cached evidence for WCAG-024
2025-07-12T06:57:52.138Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-024 - {"config":{"enableContentQualityAnalysis":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:52.139Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:52.140Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:52.182Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:52.341Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:52.341Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-024 - {"utilitiesUsed":["content-quality","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.30000000000000004,"executionTime":203}
2025-07-12T06:57:52.343Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.345Z [DEBUG] - 🔧 Utility performance recorded for WCAG-024: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.346Z [DEBUG] - 🔧 Utility analysis completed for WCAG-024: - {"utilitiesUsed":2,"errors":0,"executionTime":221}
2025-07-12T06:57:52.347Z [DEBUG] - ⏱️ Check WCAG-024 completed in 225ms (success: true)
2025-07-12T06:57:52.348Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.354Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.355Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 80% (53/66)
2025-07-12T06:57:52.355Z [INFO] - ✅ Rule WCAG-024 completed: passed (100/100)
2025-07-12T06:57:52.356Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.366Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.368Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 80% (53/66)
2025-07-12T06:57:52.369Z [INFO] - 🔍 Executing rule: Page Content Landmarks (WCAG-025)
2025-07-12T06:57:52.370Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-025: Landmarks
2025-07-12T06:57:52.372Z [DEBUG] - 📁 Cache file valid: 04790cccdd869ffab5f77a1751f4a4f7.json (170min remaining)
2025-07-12T06:57:52.372Z [DEBUG] - 📁 Cache loaded from file: 04790cccdd869ffab5f77a1751f4a4f7.json (key: WCAG-025:053b13d2:add92319...)
2025-07-12T06:57:52.373Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-025:053b13d2:add92319...
2025-07-12T06:57:52.373Z [DEBUG] - ✅ Cache hit: rule:WCAG-025:053b13d2:add92319... (accessed 2 times, age: 4196s)
2025-07-12T06:57:52.374Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-025
2025-07-12T06:57:52.376Z [DEBUG] - 📁 Cache file valid: b7395b0b8421574ba7f2e384a624c76e.json (170min remaining)
2025-07-12T06:57:52.377Z [DEBUG] - 📁 Cache loaded from file: b7395b0b8421574ba7f2e384a624c76e.json (key: WCAG-025:WCAG-025...)
2025-07-12T06:57:52.377Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-025:WCAG-025...
2025-07-12T06:57:52.378Z [DEBUG] - ✅ Cache hit: rule:WCAG-025:WCAG-025... (accessed 2 times, age: 4196s)
2025-07-12T06:57:52.379Z [DEBUG] - 📋 Using cached evidence for WCAG-025
2025-07-12T06:57:52.380Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-025 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:52.381Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:52.384Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 16 times, age: 4479s)
2025-07-12T06:57:52.385Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:52.386Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:52.388Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:52.388Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:52.389Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:52.390Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:52.391Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:52.401Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:52.421Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:52.421Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-025 - {"utilitiesUsed":["semantic-validation","framework-optimization","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.6,"executionTime":41}
2025-07-12T06:57:52.422Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.423Z [DEBUG] - 🔧 Utility performance recorded for WCAG-025: - {"utilitiesUsed":3,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.423Z [DEBUG] - 🔧 Utility analysis completed for WCAG-025: - {"utilitiesUsed":3,"errors":0,"executionTime":53}
2025-07-12T06:57:52.424Z [DEBUG] - ⏱️ Check WCAG-025 completed in 56ms (success: true)
2025-07-12T06:57:52.425Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.440Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.440Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 82% (54/66)
2025-07-12T06:57:52.441Z [INFO] - ✅ Rule WCAG-025 completed: passed (75/100)
2025-07-12T06:57:52.442Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.451Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.451Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 82% (54/66)
2025-07-12T06:57:52.452Z [INFO] - 🔍 Executing rule: Link Purpose (In Context) (WCAG-026)
2025-07-12T06:57:52.453Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-026: Link Purpose
2025-07-12T06:57:52.456Z [DEBUG] - 📁 Cache file valid: e87613a3972eabcdb786083f8cc131bf.json (170min remaining)
2025-07-12T06:57:52.456Z [DEBUG] - 📁 Cache loaded from file: e87613a3972eabcdb786083f8cc131bf.json (key: WCAG-026:053b13d2:add92319...)
2025-07-12T06:57:52.457Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-026:053b13d2:add92319...
2025-07-12T06:57:52.459Z [DEBUG] - ✅ Cache hit: rule:WCAG-026:053b13d2:add92319... (accessed 2 times, age: 4196s)
2025-07-12T06:57:52.460Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-026
2025-07-12T06:57:52.462Z [DEBUG] - 📁 Cache file valid: 4463855cd76edcf65e62e0abbfe2bdaf.json (170min remaining)
2025-07-12T06:57:52.462Z [DEBUG] - 📁 Cache loaded from file: 4463855cd76edcf65e62e0abbfe2bdaf.json (key: WCAG-026:WCAG-026...)
2025-07-12T06:57:52.464Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-026:WCAG-026...
2025-07-12T06:57:52.464Z [DEBUG] - ✅ Cache hit: rule:WCAG-026:WCAG-026... (accessed 2 times, age: 4196s)
2025-07-12T06:57:52.465Z [DEBUG] - 📋 Using cached evidence for WCAG-026
2025-07-12T06:57:52.466Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-026 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:52.466Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:52.467Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 17 times, age: 4479s)
2025-07-12T06:57:52.468Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:52.472Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:52.473Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:52.474Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:52.475Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:52.476Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:52.484Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:52.495Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:52.495Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-026 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":29}
2025-07-12T06:57:52.497Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.498Z [DEBUG] - 🔧 Utility performance recorded for WCAG-026: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.499Z [DEBUG] - 🔧 Utility analysis completed for WCAG-026: - {"utilitiesUsed":2,"errors":0,"executionTime":45}
2025-07-12T06:57:52.500Z [DEBUG] - ⏱️ Check WCAG-026 completed in 48ms (success: true)
2025-07-12T06:57:52.502Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.508Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.508Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 83% (55/66)
2025-07-12T06:57:52.509Z [INFO] - ✅ Rule WCAG-026 completed: passed (86/100)
2025-07-12T06:57:52.509Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.518Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.518Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 83% (55/66)
2025-07-12T06:57:52.519Z [INFO] - 🔍 Executing rule: No Keyboard Trap (WCAG-027)
2025-07-12T06:57:52.522Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-027: No Keyboard Trap
2025-07-12T06:57:52.524Z [DEBUG] - 📁 Cache file valid: 566fd9979fbf5569e1a137c821bf54f5.json (170min remaining)
2025-07-12T06:57:52.524Z [DEBUG] - 📁 Cache loaded from file: 566fd9979fbf5569e1a137c821bf54f5.json (key: WCAG-027:053b13d2:add92319...)
2025-07-12T06:57:52.525Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-027:053b13d2:add92319...
2025-07-12T06:57:52.526Z [DEBUG] - ✅ Cache hit: rule:WCAG-027:053b13d2:add92319... (accessed 2 times, age: 4193s)
2025-07-12T06:57:52.526Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-027
2025-07-12T06:57:52.529Z [DEBUG] - 📁 Cache file valid: d5fb2d75ab65b657ffac81435872fadc.json (170min remaining)
2025-07-12T06:57:52.529Z [DEBUG] - 📁 Cache loaded from file: d5fb2d75ab65b657ffac81435872fadc.json (key: WCAG-027:WCAG-027...)
2025-07-12T06:57:52.531Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-027:WCAG-027...
2025-07-12T06:57:52.533Z [DEBUG] - ✅ Cache hit: rule:WCAG-027:WCAG-027... (accessed 2 times, age: 4193s)
2025-07-12T06:57:52.535Z [DEBUG] - 📋 Using cached evidence for WCAG-027
2025-07-12T06:57:52.536Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-027 - {"config":{"enablePatternValidation":true,"enableFrameworkOptimization":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:52.537Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:52.538Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:52.696Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:52.696Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-027 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":160}
2025-07-12T06:57:52.698Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.700Z [DEBUG] - 🔧 Utility performance recorded for WCAG-027: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.700Z [DEBUG] - 🔧 Utility analysis completed for WCAG-027: - {"utilitiesUsed":2,"errors":0,"executionTime":179}
2025-07-12T06:57:52.701Z [DEBUG] - ⏱️ Check WCAG-027 completed in 182ms (success: true)
2025-07-12T06:57:52.702Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.706Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.707Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 85% (56/66)
2025-07-12T06:57:52.707Z [INFO] - ✅ Rule WCAG-027 completed: failed (0/100)
2025-07-12T06:57:52.708Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.711Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.712Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 85% (56/66)
2025-07-12T06:57:52.713Z [INFO] - 🔍 Executing rule: Bypass Blocks (WCAG-028)
2025-07-12T06:57:52.714Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-028: Bypass Blocks
2025-07-12T06:57:52.716Z [DEBUG] - 📁 Cache file valid: 00899b8aae33a74dcbf491eb938cbcbd.json (170min remaining)
2025-07-12T06:57:52.717Z [DEBUG] - 📁 Cache loaded from file: 00899b8aae33a74dcbf491eb938cbcbd.json (key: WCAG-028:053b13d2:add92319...)
2025-07-12T06:57:52.717Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-028:053b13d2:add92319...
2025-07-12T06:57:52.718Z [DEBUG] - ✅ Cache hit: rule:WCAG-028:053b13d2:add92319... (accessed 2 times, age: 4193s)
2025-07-12T06:57:52.719Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-028
2025-07-12T06:57:52.725Z [DEBUG] - 📁 Cache file valid: 0372d08002092230cca2af735c55eddb.json (170min remaining)
2025-07-12T06:57:52.725Z [DEBUG] - 📁 Cache loaded from file: 0372d08002092230cca2af735c55eddb.json (key: WCAG-028:WCAG-028...)
2025-07-12T06:57:52.726Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-028:WCAG-028...
2025-07-12T06:57:52.727Z [DEBUG] - ✅ Cache hit: rule:WCAG-028:WCAG-028... (accessed 2 times, age: 4193s)
2025-07-12T06:57:52.728Z [DEBUG] - 📋 Using cached evidence for WCAG-028
2025-07-12T06:57:52.729Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-028 - {"config":{"enablePatternValidation":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:52.729Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:52.730Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 18 times, age: 4480s)
2025-07-12T06:57:52.731Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:52.732Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:52.733Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:52.734Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:52.738Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:52.739Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:52.747Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:52.757Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:52.757Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-028 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":28}
2025-07-12T06:57:52.758Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.760Z [DEBUG] - 🔧 Utility performance recorded for WCAG-028: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.761Z [DEBUG] - 🔧 Utility analysis completed for WCAG-028: - {"utilitiesUsed":2,"errors":0,"executionTime":46}
2025-07-12T06:57:52.762Z [DEBUG] - ⏱️ Check WCAG-028 completed in 50ms (success: true)
2025-07-12T06:57:52.763Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.768Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.769Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 86% (57/66)
2025-07-12T06:57:52.769Z [INFO] - ✅ Rule WCAG-028 completed: passed (80/100)
2025-07-12T06:57:52.770Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.774Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.774Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 86% (57/66)
2025-07-12T06:57:52.775Z [INFO] - 🔍 Executing rule: Page Titled (WCAG-029)
2025-07-12T06:57:52.776Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-029: Page Titled
2025-07-12T06:57:52.778Z [DEBUG] - 📁 Cache file valid: 0622341e9cae4a03c93eb461d777af4f.json (170min remaining)
2025-07-12T06:57:52.778Z [DEBUG] - 📁 Cache loaded from file: 0622341e9cae4a03c93eb461d777af4f.json (key: WCAG-029:053b13d2:add92319...)
2025-07-12T06:57:52.779Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-029:053b13d2:add92319...
2025-07-12T06:57:52.780Z [DEBUG] - ✅ Cache hit: rule:WCAG-029:053b13d2:add92319... (accessed 2 times, age: 4192s)
2025-07-12T06:57:52.783Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-029
2025-07-12T06:57:52.785Z [DEBUG] - ✅ Cache hit: rule:WCAG-013:WCAG-013... (accessed 3 times, age: 4443s)
2025-07-12T06:57:52.785Z [DEBUG] - 📋 Using cached evidence for WCAG-013
2025-07-12T06:57:52.786Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-029 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:52.787Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:52.788Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:52.788Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 5 times, age: 12s)
2025-07-12T06:57:52.790Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:52.825Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:52.825Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-029 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":39}
2025-07-12T06:57:52.828Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-029: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.830Z [DEBUG] - 🔧 Utility performance recorded for WCAG-029: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.831Z [DEBUG] - 🔧 Utility analysis completed for WCAG-029: - {"utilitiesUsed":2,"errors":0,"executionTime":52}
2025-07-12T06:57:52.832Z [DEBUG] - ⏱️ Check WCAG-029 completed in 57ms (success: true)
2025-07-12T06:57:52.833Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.836Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.836Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 88% (58/66)
2025-07-12T06:57:52.837Z [INFO] - ✅ Rule WCAG-029 completed: passed (90/100)
2025-07-12T06:57:52.838Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.842Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.844Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 88% (58/66)
2025-07-12T06:57:52.844Z [INFO] - 🔍 Executing rule: Labels or Instructions (WCAG-030)
2025-07-12T06:57:52.847Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-030: Labels or Instructions
2025-07-12T06:57:52.849Z [DEBUG] - 📁 Cache file valid: 318873c4e04faa8d89a794c60564b6a5.json (170min remaining)
2025-07-12T06:57:52.849Z [DEBUG] - 📁 Cache loaded from file: 318873c4e04faa8d89a794c60564b6a5.json (key: WCAG-030:053b13d2:add92319...)
2025-07-12T06:57:52.850Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-030:053b13d2:add92319...
2025-07-12T06:57:52.851Z [DEBUG] - ✅ Cache hit: rule:WCAG-030:053b13d2:add92319... (accessed 2 times, age: 4187s)
2025-07-12T06:57:52.851Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-030
2025-07-12T06:57:52.854Z [DEBUG] - 📁 Cache file valid: 9f51555c21b6b178e061e7b0de6eaa08.json (170min remaining)
2025-07-12T06:57:52.854Z [DEBUG] - 📁 Cache loaded from file: 9f51555c21b6b178e061e7b0de6eaa08.json (key: WCAG-030:WCAG-030...)
2025-07-12T06:57:52.855Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-030:WCAG-030...
2025-07-12T06:57:52.855Z [DEBUG] - ✅ Cache hit: rule:WCAG-030:WCAG-030... (accessed 2 times, age: 4187s)
2025-07-12T06:57:52.856Z [DEBUG] - 📋 Using cached evidence for WCAG-030
2025-07-12T06:57:52.859Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-030 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:52.860Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:52.861Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 19 times, age: 4480s)
2025-07-12T06:57:52.862Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:52.863Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:52.864Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:52.865Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:52.866Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:52.867Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:52.876Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:52.888Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:52.889Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-030 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":30}
2025-07-12T06:57:52.890Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.892Z [DEBUG] - 🔧 Utility performance recorded for WCAG-030: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.893Z [DEBUG] - 🔧 Utility analysis completed for WCAG-030: - {"utilitiesUsed":2,"errors":0,"executionTime":45}
2025-07-12T06:57:52.894Z [DEBUG] - ⏱️ Check WCAG-030 completed in 50ms (success: true)
2025-07-12T06:57:52.894Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.899Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.899Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 89% (59/66)
2025-07-12T06:57:52.900Z [INFO] - ✅ Rule WCAG-030 completed: failed (0/100)
2025-07-12T06:57:52.901Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.907Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.908Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 89% (59/66)
2025-07-12T06:57:52.909Z [INFO] - 🔍 Executing rule: Error Suggestion (WCAG-031)
2025-07-12T06:57:52.910Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-031: Error Suggestion
2025-07-12T06:57:52.913Z [DEBUG] - 📁 Cache file valid: afd4e8553d2527a61d9bf9674019f478.json (170min remaining)
2025-07-12T06:57:52.913Z [DEBUG] - 📁 Cache loaded from file: afd4e8553d2527a61d9bf9674019f478.json (key: WCAG-031:053b13d2:add92319...)
2025-07-12T06:57:52.914Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-031:053b13d2:add92319...
2025-07-12T06:57:52.915Z [DEBUG] - ✅ Cache hit: rule:WCAG-031:053b13d2:add92319... (accessed 2 times, age: 4181s)
2025-07-12T06:57:52.916Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-031
2025-07-12T06:57:52.919Z [DEBUG] - 📁 Cache file valid: 7f773532f54478ddb2be2ece7257b22c.json (170min remaining)
2025-07-12T06:57:52.920Z [DEBUG] - 📁 Cache loaded from file: 7f773532f54478ddb2be2ece7257b22c.json (key: WCAG-031:WCAG-031...)
2025-07-12T06:57:52.921Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-031:WCAG-031...
2025-07-12T06:57:52.923Z [DEBUG] - ✅ Cache hit: rule:WCAG-031:WCAG-031... (accessed 2 times, age: 4181s)
2025-07-12T06:57:52.924Z [DEBUG] - 📋 Using cached evidence for WCAG-031
2025-07-12T06:57:52.925Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-031 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:52.926Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:52.927Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:52.927Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 6 times, age: 12s)
2025-07-12T06:57:52.929Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:52.973Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:52.974Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-031 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":49}
2025-07-12T06:57:52.976Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-031: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:52.978Z [DEBUG] - 🔧 Utility performance recorded for WCAG-031: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:52.979Z [DEBUG] - 🔧 Utility analysis completed for WCAG-031: - {"utilitiesUsed":2,"errors":0,"executionTime":67}
2025-07-12T06:57:52.983Z [DEBUG] - ⏱️ Check WCAG-031 completed in 73ms (success: true)
2025-07-12T06:57:52.984Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.991Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:52.991Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 91% (60/66)
2025-07-12T06:57:52.992Z [INFO] - ✅ Rule WCAG-031 completed: failed (0/100)
2025-07-12T06:57:52.993Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.002Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.002Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 91% (60/66)
2025-07-12T06:57:53.003Z [INFO] - 🔍 Executing rule: Error Prevention (WCAG-032)
2025-07-12T06:57:53.005Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-032: Error Prevention
2025-07-12T06:57:53.007Z [DEBUG] - 📁 Cache file valid: 44c080abba45af2ff4a6ac832f660888.json (170min remaining)
2025-07-12T06:57:53.007Z [DEBUG] - 📁 Cache loaded from file: 44c080abba45af2ff4a6ac832f660888.json (key: WCAG-032:053b13d2:add92319...)
2025-07-12T06:57:53.008Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-032:053b13d2:add92319...
2025-07-12T06:57:53.011Z [DEBUG] - ✅ Cache hit: rule:WCAG-032:053b13d2:add92319... (accessed 2 times, age: 4176s)
2025-07-12T06:57:53.013Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-032
2025-07-12T06:57:53.016Z [DEBUG] - 📁 Cache file valid: e401202f7944ff2eec72489ab235f330.json (170min remaining)
2025-07-12T06:57:53.016Z [DEBUG] - 📁 Cache loaded from file: e401202f7944ff2eec72489ab235f330.json (key: WCAG-032:WCAG-032...)
2025-07-12T06:57:53.017Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-032:WCAG-032...
2025-07-12T06:57:53.017Z [DEBUG] - ✅ Cache hit: rule:WCAG-032:WCAG-032... (accessed 2 times, age: 4176s)
2025-07-12T06:57:53.018Z [DEBUG] - 📋 Using cached evidence for WCAG-032
2025-07-12T06:57:53.019Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-032 - {"config":{"enablePatternValidation":true,"enableComponentLibraryDetection":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:53.020Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:53.021Z [DEBUG] - 🏗️ Starting component library analysis
2025-07-12T06:57:53.204Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:53.204Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-032 - {"utilitiesUsed":["component-library","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.4,"executionTime":185}
2025-07-12T06:57:53.206Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:53.208Z [DEBUG] - 🔧 Utility performance recorded for WCAG-032: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:53.208Z [DEBUG] - 🔧 Utility analysis completed for WCAG-032: - {"utilitiesUsed":2,"errors":0,"executionTime":203}
2025-07-12T06:57:53.209Z [DEBUG] - ⏱️ Check WCAG-032 completed in 206ms (success: true)
2025-07-12T06:57:53.210Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.214Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.214Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 92% (61/66)
2025-07-12T06:57:53.215Z [INFO] - ✅ Rule WCAG-032 completed: failed (0/100)
2025-07-12T06:57:53.215Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.219Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.219Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 92% (61/66)
2025-07-12T06:57:53.222Z [INFO] - 🔍 Executing rule: Audio-only and Video-only (WCAG-033)
2025-07-12T06:57:53.226Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-033: Audio-only and Video-only
2025-07-12T06:57:53.229Z [DEBUG] - 📁 Cache file valid: 949b30f4e5c95acf0bd0f077c7ba9124.json (170min remaining)
2025-07-12T06:57:53.230Z [DEBUG] - 📁 Cache loaded from file: 949b30f4e5c95acf0bd0f077c7ba9124.json (key: WCAG-033:053b13d2:add92319...)
2025-07-12T06:57:53.230Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-033:053b13d2:add92319...
2025-07-12T06:57:53.231Z [DEBUG] - ✅ Cache hit: rule:WCAG-033:053b13d2:add92319... (accessed 2 times, age: 4175s)
2025-07-12T06:57:53.232Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-033
2025-07-12T06:57:53.234Z [DEBUG] - 📁 Cache file valid: 49c6f131e0988b238b6cd294050269cb.json (170min remaining)
2025-07-12T06:57:53.234Z [DEBUG] - 📁 Cache loaded from file: 49c6f131e0988b238b6cd294050269cb.json (key: WCAG-033:WCAG-033...)
2025-07-12T06:57:53.235Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-033:WCAG-033...
2025-07-12T06:57:53.236Z [DEBUG] - ✅ Cache hit: rule:WCAG-033:WCAG-033... (accessed 2 times, age: 4175s)
2025-07-12T06:57:53.238Z [DEBUG] - 📋 Using cached evidence for WCAG-033
2025-07-12T06:57:53.240Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-033 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:53.241Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:53.242Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:53.242Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 7 times, age: 12s)
2025-07-12T06:57:53.244Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:53.275Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:53.276Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-033 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":36}
2025-07-12T06:57:53.278Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-033: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:53.281Z [DEBUG] - 🔧 Utility performance recorded for WCAG-033: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:53.281Z [DEBUG] - 🔧 Utility analysis completed for WCAG-033: - {"utilitiesUsed":2,"errors":0,"executionTime":56}
2025-07-12T06:57:53.282Z [DEBUG] - ⏱️ Check WCAG-033 completed in 60ms (success: true)
2025-07-12T06:57:53.283Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.287Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.287Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 94% (62/66)
2025-07-12T06:57:53.288Z [INFO] - ✅ Rule WCAG-033 completed: passed (100/100)
2025-07-12T06:57:53.288Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.292Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.292Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 94% (62/66)
2025-07-12T06:57:53.293Z [INFO] - 🔍 Executing rule: Audio Description (WCAG-034)
2025-07-12T06:57:53.294Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-034: Audio Description
2025-07-12T06:57:53.297Z [DEBUG] - 📁 Cache file valid: 475e798d534b8f0ef7bb04c71985d0af.json (170min remaining)
2025-07-12T06:57:53.299Z [DEBUG] - 📁 Cache loaded from file: 475e798d534b8f0ef7bb04c71985d0af.json (key: WCAG-034:053b13d2:add92319...)
2025-07-12T06:57:53.300Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-034:053b13d2:add92319...
2025-07-12T06:57:53.301Z [DEBUG] - ✅ Cache hit: rule:WCAG-034:053b13d2:add92319... (accessed 2 times, age: 4175s)
2025-07-12T06:57:53.302Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-034
2025-07-12T06:57:53.304Z [DEBUG] - 📁 Cache file valid: d66098f699797d33163e6b8427f7847e.json (170min remaining)
2025-07-12T06:57:53.304Z [DEBUG] - 📁 Cache loaded from file: d66098f699797d33163e6b8427f7847e.json (key: WCAG-034:WCAG-034...)
2025-07-12T06:57:53.305Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-034:WCAG-034...
2025-07-12T06:57:53.305Z [DEBUG] - ✅ Cache hit: rule:WCAG-034:WCAG-034... (accessed 2 times, age: 4175s)
2025-07-12T06:57:53.306Z [DEBUG] - 📋 Using cached evidence for WCAG-034
2025-07-12T06:57:53.307Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-034 - {"config":{"enableContentQualityAnalysis":true,"enableCMSDetection":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:53.308Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:53.309Z [DEBUG] - 📄 Starting headless CMS analysis
2025-07-12T06:57:53.309Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:cms-analysis... (accessed 8 times, age: 13s)
2025-07-12T06:57:53.311Z [DEBUG] - 📄 Using cached CMS analysis result
2025-07-12T06:57:53.349Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:53.349Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-034 - {"utilitiesUsed":["cms-detection","content-quality"],"confidence":0.7,"accuracy":0.2,"executionTime":42}
2025-07-12T06:57:53.351Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-034: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:53.353Z [DEBUG] - 🔧 Utility performance recorded for WCAG-034: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:53.354Z [DEBUG] - 🔧 Utility analysis completed for WCAG-034: - {"utilitiesUsed":2,"errors":0,"executionTime":58}
2025-07-12T06:57:53.355Z [DEBUG] - ⏱️ Check WCAG-034 completed in 62ms (success: true)
2025-07-12T06:57:53.356Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.361Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.362Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 95% (63/66)
2025-07-12T06:57:53.364Z [INFO] - ✅ Rule WCAG-034 completed: passed (100/100)
2025-07-12T06:57:53.365Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.369Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.369Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 95% (63/66)
2025-07-12T06:57:53.370Z [INFO] - 🔍 Executing rule: Multiple Ways (WCAG-035)
2025-07-12T06:57:53.371Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-035: Multiple Ways
2025-07-12T06:57:53.373Z [DEBUG] - 📁 Cache file valid: cc45a01e4bffb1e837dd3935974aa92b.json (170min remaining)
2025-07-12T06:57:53.373Z [DEBUG] - 📁 Cache loaded from file: cc45a01e4bffb1e837dd3935974aa92b.json (key: WCAG-035:053b13d2:add92319...)
2025-07-12T06:57:53.374Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-035:053b13d2:add92319...
2025-07-12T06:57:53.375Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:053b13d2:add92319... (accessed 2 times, age: 4173s)
2025-07-12T06:57:53.380Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Cache hit for WCAG-035
2025-07-12T06:57:53.381Z [DEBUG] - ✅ Cache hit: rule:WCAG-035:WCAG-035... (accessed 3 times, age: 4442s)
2025-07-12T06:57:53.382Z [DEBUG] - 📋 Using cached evidence for WCAG-035
2025-07-12T06:57:53.383Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-035 - {"config":{"enableFrameworkOptimization":true,"enablePatternValidation":true,"integrationStrategy":"supplement"}}
2025-07-12T06:57:53.383Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:53.385Z [DEBUG] - ⚡ Starting modern framework analysis
2025-07-12T06:57:53.590Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":5,"validPatterns":2,"overallScore":50}
2025-07-12T06:57:53.590Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-035 - {"utilitiesUsed":["framework-optimization","pattern-validation"],"confidence":0.7749999999999999,"accuracy":0.45,"executionTime":207}
2025-07-12T06:57:53.592Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":100,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:53.593Z [DEBUG] - 🔧 Utility performance recorded for WCAG-035: - {"utilitiesUsed":2,"cacheHitRate":"100.0%","utilityOverhead":"0.0%"}
2025-07-12T06:57:53.594Z [DEBUG] - 🔧 Utility analysis completed for WCAG-035: - {"utilitiesUsed":2,"errors":0,"executionTime":221}
2025-07-12T06:57:53.595Z [DEBUG] - ⏱️ Check WCAG-035 completed in 225ms (success: true)
2025-07-12T06:57:53.596Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.601Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.601Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 97% (64/66)
2025-07-12T06:57:53.602Z [INFO] - ✅ Rule WCAG-035 completed: passed (100/100)
2025-07-12T06:57:53.603Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.607Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.607Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 97% (64/66)
2025-07-12T06:57:53.608Z [INFO] - 🔍 Executing rule: Headings and Labels (WCAG-036)
2025-07-12T06:57:53.609Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-036: Headings and Labels
2025-07-12T06:57:53.610Z [DEBUG] - 📁 Cache file not found: 154e205869bd5401ba0ec01a183a7960.json (key: WCAG-036:053b13d2:add92319...)
2025-07-12T06:57:53.611Z [DEBUG] - 🔍 Cache miss: rule:WCAG-036:053b13d2:add92319...
2025-07-12T06:57:53.611Z [DEBUG] - 📊 Cache stats: 149 hits, 1 misses, 124 entries
2025-07-12T06:57:53.612Z [DEBUG] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Cache miss for WCAG-036, executing check
2025-07-12T06:57:53.613Z [DEBUG] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Cache key: WCAG-036:053b13d2:add92319
2025-07-12T06:57:53.617Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting WCAG-036: Headings and Labels
2025-07-12T06:57:53.619Z [DEBUG] - 📋 Using unified DOM structure: 64 headings found
2025-07-12T06:57:53.619Z [DEBUG] - 📋 Using unified DOM structure: 1 forms found
2025-07-12T06:57:53.621Z [DEBUG] - 📁 Cache file not found: 81fd93f4610e191362e7346e12cd8f1f.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:57:53.621Z [DEBUG] - 🔍 Cache miss: site:https://tigerconnect.com/:form-analysis-{"analyzeL...
2025-07-12T06:57:53.622Z [DEBUG] - 📊 Cache stats: 149 hits, 2 misses, 12 entries
2025-07-12T06:57:53.623Z [DEBUG] - 📝 Starting advanced form accessibility analysis
2025-07-12T06:57:53.625Z [DEBUG] - 🔄 Updated session activity: ed85e707-19e7-4046-913e-301608345e71:https://tigerconnect.com/ (checks: 1)
2025-07-12T06:57:53.650Z [DEBUG] - ✅ Form analysis functions injected and validated successfully (attempt 1)
2025-07-12T06:57:53.660Z [WARN] - Error analyzing form #[object HTMLInputElement] - {"error":"SyntaxError: Failed to execute 'querySelector' on 'Document': '#[object HTMLInputElement]' is not a valid selector."}
2025-07-12T06:57:53.664Z [DEBUG] - 💾 Cache saved to file: 81fd93f4610e191362e7346e12cd8f1f.json (key: https://tigerconnect.com/:form...)
2025-07-12T06:57:53.664Z [DEBUG] - 💾 Cached: site:https://tigerconnect.com/:form-analysis-{"analyzeLabels":true,"analyzeValidation":true,"analyzeGrouping":true,"analyzeAutocomplete":true,"analyzeErrorHandling":true,"analyzeKeyboardAccess":true,"includeHiddenFields":false,"strictMode":true} (650 bytes)
2025-07-12T06:57:53.665Z [INFO] - ✅ Form accessibility analysis completed - {"totalForms":0,"accessibleForms":0,"totalFields":0,"overallScore":100}
2025-07-12T06:57:53.703Z [INFO] - ✅ [ed85e707-19e7-4046-913e-301608345e71] WCAG-036 passed: 85.0% (threshold: 75%) - PASSED
2025-07-12T06:57:53.704Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Completed WCAG-036 in 86ms - Status: passed (85/100)
2025-07-12T06:57:53.710Z [DEBUG] - Page state validation passed
2025-07-12T06:57:53.710Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Applying utility enhancements for WCAG-036
2025-07-12T06:57:53.711Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-036 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"supplement","enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-12T06:57:53.712Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:53.713Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 20 times, age: 4481s)
2025-07-12T06:57:53.714Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:53.715Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:53.715Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:53.717Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:53.718Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:53.719Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:53.727Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:53.738Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:53.738Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-036 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":27}
2025-07-12T06:57:53.747Z [DEBUG] - 💾 Cache saved to file: 154e205869bd5401ba0ec01a183a7960.json (key: WCAG-036:053b13d2:add92319...)
2025-07-12T06:57:53.747Z [DEBUG] - 💾 Cached: rule:WCAG-036:053b13d2:add92319 (33580 bytes)
2025-07-12T06:57:53.748Z [DEBUG] - 💾 [ed85e707-19e7-4046-913e-301608345e71] Cached result for WCAG-036
2025-07-12T06:57:53.750Z [DEBUG] - 📁 Cache file valid: 0d417ffff4e46e5e78395b1c8b5fb28a.json (171min remaining)
2025-07-12T06:57:53.753Z [DEBUG] - 📁 Cache loaded from file: 0d417ffff4e46e5e78395b1c8b5fb28a.json (key: WCAG-036:WCAG-036...)
2025-07-12T06:57:53.754Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-036:WCAG-036...
2025-07-12T06:57:53.756Z [DEBUG] - ✅ Cache hit: rule:WCAG-036:WCAG-036... (accessed 2 times, age: 4169s)
2025-07-12T06:57:53.756Z [DEBUG] - 📋 Using cached evidence for WCAG-036
2025-07-12T06:57:53.757Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-036 - {"config":{"enableSemanticValidation":true,"enablePatternValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:53.758Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:53.759Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 21 times, age: 4481s)
2025-07-12T06:57:53.760Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:53.762Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:53.762Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:53.764Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:53.765Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:53.766Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:53.779Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:53.791Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:53.791Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-036 - {"utilitiesUsed":["semantic-validation","pattern-validation"],"confidence":0.85,"accuracy":0.35,"executionTime":34}
2025-07-12T06:57:53.792Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-036: - {"utilitiesUsed":2,"cacheHitRate":98.7012987012987,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:53.794Z [DEBUG] - 🔧 Utility performance recorded for WCAG-036: - {"utilitiesUsed":2,"cacheHitRate":"98.7%","utilityOverhead":"0.0%"}
2025-07-12T06:57:53.795Z [DEBUG] - 🔧 Utility analysis completed for WCAG-036: - {"utilitiesUsed":2,"errors":0,"executionTime":184}
2025-07-12T06:57:53.796Z [DEBUG] - ⏱️ Check WCAG-036 completed in 188ms (success: true)
2025-07-12T06:57:53.799Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.804Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.804Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 98% (65/66)
2025-07-12T06:57:53.804Z [INFO] - ✅ Rule WCAG-036 completed: passed (85/100)
2025-07-12T06:57:53.805Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.810Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:53.810Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 98% (65/66)
2025-07-12T06:57:53.810Z [INFO] - 🔍 Executing rule: Language of Parts (WCAG-038)
2025-07-12T06:57:53.813Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting enhanced WCAG-038: Language of Parts
2025-07-12T06:57:53.816Z [DEBUG] - 📁 Cache file not found: d6f9f1148034ce678e20a72e06c80357.json (key: WCAG-038:053b13d2:add92319...)
2025-07-12T06:57:53.817Z [DEBUG] - 🔍 Cache miss: rule:WCAG-038:053b13d2:add92319...
2025-07-12T06:57:53.818Z [DEBUG] - 📊 Cache stats: 152 hits, 3 misses, 126 entries
2025-07-12T06:57:53.819Z [DEBUG] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Cache miss for WCAG-038, executing check
2025-07-12T06:57:53.819Z [DEBUG] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Cache key: WCAG-038:053b13d2:add92319
2025-07-12T06:57:53.820Z [INFO] - 🔍 [ed85e707-19e7-4046-913e-301608345e71] Starting WCAG-038: Language of Parts
2025-07-12T06:57:53.879Z [INFO] - ✅ [ed85e707-19e7-4046-913e-301608345e71] WCAG-038 passed: 90.0% (threshold: 75%) - PASSED
2025-07-12T06:57:53.879Z [INFO] - 🎯 [ed85e707-19e7-4046-913e-301608345e71] Completed WCAG-038 in 59ms - Status: passed (90/100)
2025-07-12T06:57:53.885Z [DEBUG] - Page state validation passed
2025-07-12T06:57:53.885Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Applying utility enhancements for WCAG-038
2025-07-12T06:57:53.886Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-038 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"supplement","enablePatternValidation":true,"enableCaching":true,"enableGracefulFallback":true,"maxExecutionTime":5000}}
2025-07-12T06:57:53.887Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:53.888Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 22 times, age: 4481s)
2025-07-12T06:57:53.889Z [DEBUG] - 🔍 Starting accessibility pattern analysis
2025-07-12T06:57:53.890Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:53.891Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:53.892Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:53.893Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:53.894Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:53.895Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:53.921Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:53.940Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:53.952Z [INFO] - ✅ Pattern analysis completed - {"detectedPatterns":0,"validPatterns":0,"overallScore":100}
2025-07-12T06:57:53.952Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-038 - {"utilitiesUsed":["content-quality","semantic-validation","pattern-validation"],"confidence":0.9500000000000001,"accuracy":0.44999999999999996,"executionTime":66}
2025-07-12T06:57:53.954Z [DEBUG] - 💾 Cache saved to file: d6f9f1148034ce678e20a72e06c80357.json (key: WCAG-038:053b13d2:add92319...)
2025-07-12T06:57:53.955Z [DEBUG] - 💾 Cached: rule:WCAG-038:053b13d2:add92319 (1759 bytes)
2025-07-12T06:57:53.955Z [DEBUG] - 💾 [ed85e707-19e7-4046-913e-301608345e71] Cached result for WCAG-038
2025-07-12T06:57:53.959Z [DEBUG] - 📁 Cache file valid: 80f25140fbdeab47c10554bc75405e69.json (171min remaining)
2025-07-12T06:57:53.961Z [DEBUG] - 📁 Cache loaded from file: 80f25140fbdeab47c10554bc75405e69.json (key: WCAG-038:WCAG-038...)
2025-07-12T06:57:53.961Z [DEBUG] - 📁 Restored from file cache: rule:WCAG-038:WCAG-038...
2025-07-12T06:57:53.964Z [DEBUG] - ✅ Cache hit: rule:WCAG-038:WCAG-038... (accessed 2 times, age: 4168s)
2025-07-12T06:57:53.965Z [DEBUG] - 📋 Using cached evidence for WCAG-038
2025-07-12T06:57:53.966Z [DEBUG] - 🔧 [ed85e707-19e7-4046-913e-301608345e71] Starting utility analysis for WCAG-038 - {"config":{"enableContentQualityAnalysis":true,"enableSemanticValidation":true,"integrationStrategy":"enhance"}}
2025-07-12T06:57:53.966Z [DEBUG] - 🤖 Starting AI-powered semantic validation
2025-07-12T06:57:53.967Z [DEBUG] - ✅ Cache hit: site:https://tigerconnect.com/:semantic-validation-{"va... (accessed 23 times, age: 4481s)
2025-07-12T06:57:53.968Z [DEBUG] - 📊 Starting comprehensive content quality analysis
2025-07-12T06:57:53.969Z [DEBUG] - 🏗️ Using cached semantic validation result
2025-07-12T06:57:53.969Z [DEBUG] - 📝 Analyzing content quality with NLP
2025-07-12T06:57:53.971Z [DEBUG] - 🔍 Validating accessibility patterns
2025-07-12T06:57:53.975Z [DEBUG] - 🔗 Validating cross-references
2025-07-12T06:57:53.977Z [DEBUG] - 🎯 Analyzing page context
2025-07-12T06:57:53.999Z [DEBUG] - Sentiment analysis failed - {"error":{}}
2025-07-12T06:57:54.018Z [INFO] - ✅ Content quality analysis completed - {"overallQuality":70,"readabilityGrade":18.9,"accessibilityLevel":"fair"}
2025-07-12T06:57:54.019Z [DEBUG] - ✅ [ed85e707-19e7-4046-913e-301608345e71] Utility analysis completed for WCAG-038 - {"utilitiesUsed":["content-quality","semantic-validation"],"confidence":0.7,"accuracy":0.25,"executionTime":53}
2025-07-12T06:57:54.020Z [DEBUG] - 🔧 Utility metrics recorded for WCAG-038: - {"utilitiesUsed":2,"cacheHitRate":98.10126582278481,"utilityOverhead":0,"totalUtilityTime":0}
2025-07-12T06:57:54.021Z [DEBUG] - 🔧 Utility performance recorded for WCAG-038: - {"utilitiesUsed":2,"cacheHitRate":"98.1%","utilityOverhead":"0.0%"}
2025-07-12T06:57:54.023Z [DEBUG] - 🔧 Utility analysis completed for WCAG-038: - {"utilitiesUsed":2,"errors":0,"executionTime":210}
2025-07-12T06:57:54.025Z [DEBUG] - ⏱️ Check WCAG-038 completed in 215ms (success: true)
2025-07-12T06:57:54.025Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:54.031Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> running
2025-07-12T06:57:54.032Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 100% (66/66)
2025-07-12T06:57:54.032Z [INFO] - ✅ Rule WCAG-038 completed: passed (90/100)
2025-07-12T06:57:54.033Z [INFO] - ✅ All checks completed: 66 results
2025-07-12T06:57:54.034Z [INFO] - 📊 Final Cache Performance Report: - {"totalRequests":158,"hits":155,"misses":3,"hitRate":"98.1%","totalEntries":178,"totalSize":"6.60MB","evictions":0}
2025-07-12T06:57:54.034Z [INFO] - 🚀 Cache Performance Benefits: - {"hitRate":"98.1%","estimatedTimeSaved":"7750ms","cacheEfficiency":"Excellent"}
🔍 WCAG Scoring Debug - Input Results: [
  {
    ruleId: 'WCAG-001',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-002',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-003',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-004',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0763
  },
  {
    ruleId: 'WCAG-005',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-006',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-007',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-008',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0534
  },
  {
    ruleId: 'WCAG-009',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-010',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-011',
    status: 'passed',
    score: 96,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-012',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-013',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-014',
    status: 'passed',
    score: 81,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.2',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-015',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-016',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-017',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-018',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-019',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '3.0',
    weight: 0.0382
  },
  {
    ruleId: 'WCAG-020',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '3.0',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-021',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '3.0',
    weight: 0.0229
  },
  {
    ruleId: 'WCAG-022',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.05
  },
  {
    ruleId: 'WCAG-023',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.2',
    weight: 0.03
  },
  {
    ruleId: 'WCAG-044',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-045',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-046',
    status: 'passed',
    score: 99,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-037',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-039',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-040',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-041',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-042',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-043',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-050',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-051',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-052',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-053',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-054',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-055',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-056',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-058',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-059',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-060',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-061',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-062',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-063',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-064',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-065',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-066',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0305
  },
  {
    ruleId: 'WCAG-057',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'robust',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-047',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-048',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-049',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-024',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-025',
    status: 'passed',
    score: 75,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0687
  },
  {
    ruleId: 'WCAG-026',
    status: 'passed',
    score: 86,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-027',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0916
  },
  {
    ruleId: 'WCAG-028',
    status: 'passed',
    score: 80,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-029',
    status: 'passed',
    score: 90,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0611
  },
  {
    ruleId: 'WCAG-030',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0534
  },
  {
    ruleId: 'WCAG-031',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-032',
    status: 'failed',
    score: 0,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-033',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-034',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'perceivable',
    wcagVersion: '2.1',
    weight: 0.0458
  },
  {
    ruleId: 'WCAG-035',
    status: 'passed',
    score: 100,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-036',
    status: 'passed',
    score: 85,
    maxScore: 100,
    category: 'operable',
    wcagVersion: '2.1',
    weight: 0.0535
  },
  {
    ruleId: 'WCAG-038',
    status: 'passed',
    score: 90,
    maxScore: 100,
    category: 'understandable',
    wcagVersion: '2.1',
    weight: 0.0535
  }
]
📊 Rule WCAG-001: score=0/100 (0.0%), weight=0.008
📊 Rule WCAG-002: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-003: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-004: score=100/100 (100.0%), weight=0.010
📊 Rule WCAG-005: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-006: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-007: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-008: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-009: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-010: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-011: score=96/100 (96.0%), weight=0.004
📊 Rule WCAG-012: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-013: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-014: score=81/100 (81.0%), weight=0.006
📊 Rule WCAG-015: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-016: score=0/100 (0.0%), weight=0.003
📊 Rule WCAG-017: score=0/100 (0.0%), weight=0.001
📊 Rule WCAG-018: score=0/100 (0.0%), weight=0.001
📊 Rule WCAG-019: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-020: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-021: score=75/100 (75.0%), weight=0.001
📊 Rule WCAG-022: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-023: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-044: score=75/100 (75.0%), weight=0.012
📊 Rule WCAG-045: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-046: score=99/100 (99.0%), weight=0.008
📊 Rule WCAG-037: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-039: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-040: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-041: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-042: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-043: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-050: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-051: score=0/100 (0.0%), weight=0.012
📊 Rule WCAG-052: score=75/100 (75.0%), weight=0.008
📊 Rule WCAG-053: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-054: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-055: score=0/100 (0.0%), weight=0.008
📊 Rule WCAG-056: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-058: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-059: score=0/100 (0.0%), weight=0.005
📊 Rule WCAG-060: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-061: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-062: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-063: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-064: score=0/100 (0.0%), weight=0.004
📊 Rule WCAG-065: score=75/100 (75.0%), weight=0.002
📊 Rule WCAG-066: score=0/100 (0.0%), weight=0.002
📊 Rule WCAG-057: score=100/100 (100.0%), weight=0.003
📊 Rule WCAG-047: score=0/100 (0.0%), weight=0.011
📊 Rule WCAG-048: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-049: score=0/100 (0.0%), weight=0.009
📊 Rule WCAG-024: score=100/100 (100.0%), weight=0.008
📊 Rule WCAG-025: score=75/100 (75.0%), weight=0.009
📊 Rule WCAG-026: score=86/100 (86.0%), weight=0.011
📊 Rule WCAG-027: score=0/100 (0.0%), weight=0.016
📊 Rule WCAG-028: score=80/100 (80.0%), weight=0.011
📊 Rule WCAG-029: score=90/100 (90.0%), weight=0.011
📊 Rule WCAG-030: score=0/100 (0.0%), weight=0.007
📊 Rule WCAG-031: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-032: score=0/100 (0.0%), weight=0.006
📊 Rule WCAG-033: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-034: score=100/100 (100.0%), weight=0.006
📊 Rule WCAG-035: score=100/100 (100.0%), weight=0.009
📊 Rule WCAG-036: score=85/100 (85.0%), weight=0.009
📊 Rule WCAG-038: score=90/100 (90.0%), weight=0.007
🎯 WCAG Final Score Calculation:
      - Total Weighted Score: 15.77
      - Total Weight: 0.436
      - Final Score: 36%
      - Rules Processed: 66/66
      - Passed Rules: 25
      - Failed Rules: 41
2025-07-12T06:57:54.125Z [INFO] - 💾 Saving WCAG scan result: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.126Z [INFO] - 📊 Scan summary: {"scanId":"ed85e707-19e7-4046-913e-301608345e71","totalRules":66,"overallScore":36,"levelAchieved":"None","riskLevel":"Critical"}
2025-07-12T06:57:54.127Z [INFO] - 💾 Saving WCAG scan result: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.246Z [INFO] - ✅ WCAG scan result saved: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.247Z [INFO] - ✅ WCAG scan result saved successfully: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.249Z [INFO] - 📝 Storing 1 manual review items for scan ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.255Z [INFO] - ✅ Stored 1 manual review items for scan ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.255Z [INFO] - ✅ Stored 1 manual review items for scan ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.257Z [INFO] - 📝 Updating WCAG scan status: ed85e707-19e7-4046-913e-301608345e71 -> completed
2025-07-12T06:57:54.261Z [INFO] - ✅ WCAG scan status updated: ed85e707-19e7-4046-913e-301608345e71 -> completed
2025-07-12T06:57:54.261Z [INFO] - 📈 Scan ed85e707-19e7-4046-913e-301608345e71: 100% (66/66)
2025-07-12T06:57:54.264Z [INFO] - ✅ Comprehensive WCAG scan completed: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.265Z [INFO] - 📊 Overall Score: 36% | Level: None | Risk: Critical
2025-07-12T06:57:54.267Z [INFO] - 📈 New performance baseline established - {"performanceScore":100,"averageCheckDuration":182.87878787878788,"memoryPeakMB":487}
2025-07-12T06:57:54.267Z [INFO] - 📊 Performance report generated for scan: ed85e707-19e7-4046-913e-301608345e71 - {"duration":23470,"checksExecuted":66,"successRate":100,"memoryPeak":487,"performanceScore":100}
2025-07-12T06:57:54.268Z [INFO] - 📊 Performance report for scan ed85e707-19e7-4046-913e-301608345e71: - {"duration":23470,"performanceScore":100,"memoryPeak":487,"recommendations":["Low browser pool efficiency - consider increasing pool size"]}
2025-07-12T06:57:54.366Z [DEBUG] - ✅ Released page back to pool for scan: ed85e707-19e7-4046-913e-301608345e71
2025-07-12T06:57:54.366Z [DEBUG] - 📊 Unregistered active scan: ed85e707-19e7-4046-913e-301608345e71 (total: 0)
2025-07-12T06:57:54.368Z [INFO] - ✅ [8652edb2-e820-4a02-ae79-3dd6f6044082] WCAG scan completed successfully: ed85e707-19e7-4046-913e-301608345e71
