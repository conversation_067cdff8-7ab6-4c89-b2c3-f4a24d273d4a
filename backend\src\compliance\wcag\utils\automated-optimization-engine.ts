/**
 * Automated Optimization Engine
 * Implements automated performance optimization based on predictive analytics recommendations
 */

import { EventEmitter } from 'events';
import PredictivePerformanceAnalytics, {
  PerformancePrediction,
} from './predictive-performance-analytics';
import { UtilityIntegrationManager } from './utility-integration-manager';
import RealTimeMonitoringDashboard from './real-time-monitoring-dashboard';
import SmartCache from './smart-cache';
import logger from '../../../utils/logger';

export interface OptimizationAction {
  id: string;
  timestamp: Date;
  type: 'cache_optimization' | 'utility_tuning' | 'resource_management' | 'performance_tuning';
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'rolled_back';

  action: {
    name: string;
    description: string;
    parameters: Record<string, unknown>;
    automationScript: string;
    rollbackScript?: string;
  };

  trigger: {
    predictionId?: string;
    recommendationId?: string;
    manualTrigger?: boolean;
    threshold?: string;
  };

  results: {
    executionTime?: number;
    success?: boolean;
    error?: string;
    performanceImpact?: {
      before: Record<string, number>;
      after: Record<string, number>;
      improvement: Record<string, number>;
    };
  };

  approval: {
    required: boolean;
    approved?: boolean;
    approvedBy?: string;
    approvedAt?: Date;
  };
}

export interface OptimizationConfig {
  enableAutomatedOptimization: boolean;
  requireApprovalForCritical: boolean;
  requireApprovalForHigh: boolean;
  maxConcurrentOptimizations: number;
  rollbackOnFailure: boolean;
  monitoringPeriodAfterOptimization: number; // minutes
  optimizationCooldown: number; // minutes
}

/**
 * Automated Optimization Engine Class
 */
export class AutomatedOptimizationEngine extends EventEmitter {
  private static instance: AutomatedOptimizationEngine;

  private predictiveAnalytics: PredictivePerformanceAnalytics;
  private _utilityManager: UtilityIntegrationManager;
  private dashboard: RealTimeMonitoringDashboard;
  private _smartCache: SmartCache;

  private config: OptimizationConfig;
  private activeOptimizations: Map<string, OptimizationAction> = new Map();
  private optimizationHistory: OptimizationAction[] = [];
  private lastOptimizationTime: Map<string, Date> = new Map();

  private monitoringTimer: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  private constructor() {
    super();

    this.predictiveAnalytics = PredictivePerformanceAnalytics.getInstance();
    this._utilityManager = UtilityIntegrationManager.getInstance();
    this.dashboard = RealTimeMonitoringDashboard.getInstance();
    this._smartCache = SmartCache.getInstance();

    this.config = {
      enableAutomatedOptimization: false, // Start disabled for safety
      requireApprovalForCritical: true,
      requireApprovalForHigh: true,
      maxConcurrentOptimizations: 3,
      rollbackOnFailure: true,
      monitoringPeriodAfterOptimization: 30,
      optimizationCooldown: 60,
    };

    this.setupAnalyticsListeners();

    logger.info('🤖 Automated Optimization Engine initialized');
  }

  static getInstance(): AutomatedOptimizationEngine {
    if (!AutomatedOptimizationEngine.instance) {
      AutomatedOptimizationEngine.instance = new AutomatedOptimizationEngine();
    }
    return AutomatedOptimizationEngine.instance;
  }

  /**
   * Start automated optimization engine
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('🤖 Optimization engine already running');
      return;
    }

    this.isRunning = true;

    // Start monitoring timer
    this.monitoringTimer = setInterval(() => {
      this.monitorOptimizations();
    }, 30000); // 30 seconds

    logger.info('🤖 Automated Optimization Engine started');
    this.emit('engineStarted');
  }

  /**
   * Stop automated optimization engine
   */
  stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;

    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }

    // Wait for active optimizations to complete
    if (this.activeOptimizations.size > 0) {
      logger.info(
        `🤖 Waiting for ${this.activeOptimizations.size} active optimizations to complete...`,
      );
    }

    logger.info('🤖 Automated Optimization Engine stopped');
    this.emit('engineStopped');
  }

  /**
   * Setup analytics event listeners
   */
  private setupAnalyticsListeners(): void {
    this.predictiveAnalytics.on('predictionGenerated', (prediction: PerformancePrediction) => {
      if (this.config.enableAutomatedOptimization) {
        this.evaluatePredictionForOptimization(prediction);
      }
    });

    this.predictiveAnalytics.on('proactiveAlert', (prediction: PerformancePrediction) => {
      this.handleProactiveAlert(prediction);
    });
  }

  /**
   * Evaluate prediction for automated optimization
   */
  private async evaluatePredictionForOptimization(
    prediction: PerformancePrediction,
  ): Promise<void> {
    try {
      // Check if optimization is needed and allowed
      if (!this.shouldOptimize(prediction)) return;

      // Generate optimization action
      const optimizationAction = this.generateOptimizationAction(prediction);
      if (!optimizationAction) return;

      // Check if approval is required
      if (this.requiresApproval(optimizationAction)) {
        optimizationAction.approval.required = true;
        this.emit('approvalRequired', optimizationAction);
        logger.info(`🤖 Optimization requires approval: ${optimizationAction.action.name}`);
        return;
      }

      // Execute optimization
      await this.executeOptimization(optimizationAction);
    } catch (error) {
      logger.error('🤖 Failed to evaluate prediction for optimization:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Check if optimization should be performed
   */
  private shouldOptimize(prediction: PerformancePrediction): boolean {
    // Check if too many concurrent optimizations
    if (this.activeOptimizations.size >= this.config.maxConcurrentOptimizations) {
      logger.debug('🤖 Max concurrent optimizations reached, skipping');
      return false;
    }

    // Check cooldown period
    const lastOptimization = this.lastOptimizationTime.get(prediction.type);
    if (lastOptimization) {
      const cooldownEnd = new Date(
        lastOptimization.getTime() + this.config.optimizationCooldown * 60000,
      );
      if (new Date() < cooldownEnd) {
        logger.debug(`🤖 Optimization cooldown active for ${prediction.type}, skipping`);
        return false;
      }
    }

    // Check confidence threshold
    if (prediction.confidence < 80) {
      logger.debug(`🤖 Prediction confidence too low (${prediction.confidence}%), skipping`);
      return false;
    }

    return true;
  }

  /**
   * Generate optimization action from prediction
   */
  private generateOptimizationAction(prediction: PerformancePrediction): OptimizationAction | null {
    const actionId = `opt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    switch (prediction.type) {
      case 'cache_efficiency':
        return {
          id: actionId,
          timestamp: new Date(),
          type: 'cache_optimization',
          status: 'pending',

          action: {
            name: 'Optimize Cache Configuration',
            description: 'Adjust cache settings to improve hit rate',
            parameters: {
              targetHitRate: 85,
              cacheSize: 'auto',
              evictionPolicy: 'lru',
            },
            automationScript: 'optimizeCache',
            rollbackScript: 'rollbackCacheOptimization',
          },

          trigger: {
            predictionId: prediction.id,
            threshold: 'cache_hit_rate < 70%',
          },

          results: {},

          approval: {
            required: false,
          },
        };

      case 'utility_optimization':
        return {
          id: actionId,
          timestamp: new Date(),
          type: 'utility_tuning',
          status: 'pending',

          action: {
            name: 'Optimize Utility Integration',
            description: 'Adjust utility selection and configuration',
            parameters: {
              targetOverhead: 15,
              priorityAdjustment: true,
              cacheUtilityResults: true,
            },
            automationScript: 'optimizeUtilityIntegration',
            rollbackScript: 'rollbackUtilityOptimization',
          },

          trigger: {
            predictionId: prediction.id,
            threshold: 'utility_overhead > 20%',
          },

          results: {},

          approval: {
            required: prediction.severity === 'high' || prediction.severity === 'critical',
          },
        };

      case 'resource_exhaustion':
        return {
          id: actionId,
          timestamp: new Date(),
          type: 'resource_management',
          status: 'pending',

          action: {
            name: 'Resource Cleanup and Optimization',
            description: 'Clean up resources and optimize memory usage',
            parameters: {
              memoryCleanup: true,
              browserPoolOptimization: true,
              garbageCollection: true,
            },
            automationScript: 'optimizeResourceUsage',
            rollbackScript: 'rollbackResourceOptimization',
          },

          trigger: {
            predictionId: prediction.id,
            threshold: 'memory_usage > 2000MB',
          },

          results: {},

          approval: {
            required: true, // Always require approval for resource changes
          },
        };

      default:
        return null;
    }
  }

  /**
   * Check if optimization requires approval
   */
  private requiresApproval(action: OptimizationAction): boolean {
    if (action.approval.required) return true;

    if (action.type === 'resource_management') return true;

    // Check severity-based approval requirements
    const prediction = this.predictiveAnalytics
      .getPredictions(1)
      .find((p) => p.id === action.trigger.predictionId);

    if (prediction) {
      if (prediction.severity === 'critical' && this.config.requireApprovalForCritical) return true;
      if (prediction.severity === 'high' && this.config.requireApprovalForHigh) return true;
    }

    return false;
  }

  /**
   * Execute optimization action
   */
  private async executeOptimization(action: OptimizationAction): Promise<void> {
    try {
      action.status = 'executing';
      this.activeOptimizations.set(action.id, action);

      const startTime = Date.now();

      // Capture before metrics
      const beforeMetrics = this.captureCurrentMetrics();

      logger.info(`🤖 Executing optimization: ${action.action.name}`);
      this.emit('optimizationStarted', action);

      // Execute the optimization script
      const success = await this.runOptimizationScript(action);

      if (success) {
        action.status = 'completed';
        action.results.success = true;
        action.results.executionTime = Date.now() - startTime;

        // Schedule post-optimization monitoring
        this.schedulePostOptimizationMonitoring(action, beforeMetrics);

        logger.info(`🤖 Optimization completed: ${action.action.name}`);
        this.emit('optimizationCompleted', action);
      } else {
        throw new Error('Optimization script failed');
      }
    } catch (error) {
      action.status = 'failed';
      action.results.success = false;
      action.results.error = error instanceof Error ? error.message : String(error);

      logger.error(`🤖 Optimization failed: ${action.action.name}`, {
        error: error instanceof Error ? error.message : String(error),
      });
      this.emit('optimizationFailed', action);

      // Attempt rollback if configured
      if (this.config.rollbackOnFailure && action.action.rollbackScript) {
        await this.rollbackOptimization(action);
      }
    } finally {
      this.activeOptimizations.delete(action.id);
      this.optimizationHistory.push(action);
      this.lastOptimizationTime.set(action.type, new Date());
    }
  }

  /**
   * Run optimization script
   */
  private async runOptimizationScript(action: OptimizationAction): Promise<boolean> {
    switch (action.action.automationScript) {
      case 'optimizeCache':
        return this.optimizeCache(action.action.parameters);

      case 'optimizeUtilityIntegration':
        return this.optimizeUtilityIntegration(action.action.parameters);

      case 'optimizeResourceUsage':
        return this.optimizeResourceUsage(action.action.parameters);

      default:
        logger.warn(`🤖 Unknown optimization script: ${action.action.automationScript}`);
        return false;
    }
  }

  /**
   * Optimize cache configuration
   */
  private async optimizeCache(parameters: Record<string, unknown>): Promise<boolean> {
    try {
      // Implement cache optimization logic
      const targetHitRate = parameters.targetHitRate || 85;

      // Example optimization: adjust cache size and policies
      logger.info(`🤖 Optimizing cache for ${targetHitRate}% hit rate`);

      // This would interact with SmartCache to adjust settings
      // For now, return success
      return true;
    } catch (error) {
      logger.error('🤖 Cache optimization failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Optimize utility integration
   */
  private async optimizeUtilityIntegration(parameters: Record<string, unknown>): Promise<boolean> {
    try {
      const targetOverhead = parameters.targetOverhead || 15;

      logger.info(`🤖 Optimizing utility integration for ${targetOverhead}% overhead`);

      // This would interact with UtilityIntegrationManager to adjust settings
      // For now, return success
      return true;
    } catch (error) {
      logger.error('🤖 Utility optimization failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Optimize resource usage
   */
  private async optimizeResourceUsage(parameters: Record<string, unknown>): Promise<boolean> {
    try {
      logger.info('🤖 Optimizing resource usage');

      if (parameters.memoryCleanup) {
        // Trigger garbage collection
        if (global.gc) {
          global.gc();
        }
      }

      if (parameters.browserPoolOptimization) {
        // This would optimize browser pool settings
      }

      return true;
    } catch (error) {
      logger.error('🤖 Resource optimization failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Handle proactive alert
   */
  private handleProactiveAlert(prediction: PerformancePrediction): void {
    logger.warn(`🤖 Proactive alert: ${prediction.type} (${prediction.severity})`);
    this.emit('proactiveAlert', prediction);
  }

  /**
   * Monitor active optimizations
   */
  private monitorOptimizations(): void {
    // Monitor active optimizations for timeouts, etc.
    this.activeOptimizations.forEach((action) => {
      const runtime = Date.now() - action.timestamp.getTime();
      if (runtime > 300000) {
        // 5 minutes timeout
        logger.warn(`🤖 Optimization timeout: ${action.action.name}`);
        action.status = 'failed';
        action.results.error = 'Optimization timeout';
        this.activeOptimizations.delete(action.id);
      }
    });
  }

  /**
   * Capture current metrics for comparison
   */
  private captureCurrentMetrics(): Record<string, number> {
    const currentMetrics = this.dashboard.getCurrentMetrics();
    if (!currentMetrics) return {};

    return {
      performanceScore: currentMetrics.averagePerformanceScore,
      cacheHitRate: currentMetrics.performance.cacheHitRate,
      memoryUsage: currentMetrics.performance.memoryUsage,
      errorRate: currentMetrics.performance.errorRate,
      utilityOverhead: currentMetrics.utilityIntegration.averageUtilityOverhead,
    };
  }

  /**
   * Schedule post-optimization monitoring
   */
  private schedulePostOptimizationMonitoring(
    action: OptimizationAction,
    beforeMetrics: Record<string, number>,
  ): void {
    setTimeout(() => {
      const afterMetrics = this.captureCurrentMetrics();
      const improvement: Record<string, number> = {};

      Object.keys(beforeMetrics).forEach((key) => {
        if (afterMetrics[key] !== undefined) {
          improvement[key] = afterMetrics[key] - beforeMetrics[key];
        }
      });

      action.results.performanceImpact = {
        before: beforeMetrics,
        after: afterMetrics,
        improvement,
      };

      logger.info(`🤖 Post-optimization metrics for ${action.action.name}:`, improvement);
      this.emit('optimizationImpactMeasured', action);
    }, this.config.monitoringPeriodAfterOptimization * 60000);
  }

  /**
   * Rollback optimization
   */
  private async rollbackOptimization(action: OptimizationAction): Promise<void> {
    try {
      logger.info(`🤖 Rolling back optimization: ${action.action.name}`);

      // Execute rollback script
      if (action.action.rollbackScript) {
        // Implement rollback logic
        action.status = 'rolled_back';
        this.emit('optimizationRolledBack', action);
      }
    } catch (error: unknown) {
      logger.error(
        `🤖 Rollback failed for ${action.action.name}:`,
        error as Record<string, unknown>,
      );
    }
  }

  // Public API methods
  approveOptimization(actionId: string, approvedBy: string): boolean {
    const action =
      this.activeOptimizations.get(actionId) ||
      this.optimizationHistory.find((a) => a.id === actionId);

    if (!action || action.status !== 'pending') return false;

    action.approval.approved = true;
    action.approval.approvedBy = approvedBy;
    action.approval.approvedAt = new Date();

    // Execute the optimization
    this.executeOptimization(action);

    return true;
  }

  getActiveOptimizations(): OptimizationAction[] {
    return Array.from(this.activeOptimizations.values());
  }

  getOptimizationHistory(hours: number = 24): OptimizationAction[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.optimizationHistory.filter((a) => a.timestamp >= cutoff);
  }

  getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('🤖 Optimization engine configuration updated');
    this.emit('configUpdated', this.config);
  }
}

export default AutomatedOptimizationEngine;
