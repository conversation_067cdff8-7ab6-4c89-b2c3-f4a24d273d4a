/**
 * Verify Utility Integration Completion
 * Quick check to confirm all 69 checks have utility integration
 */

import { CHECK_UTILITY_PRIORITY } from '../src/compliance/wcag/utils/utility-integration-manager';

function verifyUtilityCompletion(): void {
  console.log('🔧 Verifying Utility Integration Completion');
  console.log('=' .repeat(50));

  const configuredRuleIds = Object.keys(CHECK_UTILITY_PRIORITY).filter(key => key !== 'default').sort();
  const totalChecks = 69;
  const configuredChecks = configuredRuleIds.length;
  const missingChecks = totalChecks - configuredChecks;

  console.log(`\n📊 COMPLETION STATUS:`);
  console.log(`  Total WCAG Checks: ${totalChecks}`);
  console.log(`  Configured Checks: ${configuredChecks}`);
  console.log(`  Missing Checks: ${missingChecks}`);
  console.log(`  Completion Rate: ${((configuredChecks / totalChecks) * 100).toFixed(1)}%`);

  if (missingChecks === 0) {
    console.log('\n🎉 SUCCESS: All WCAG checks have utility integration!');
    
    // Show newly added checks
    const newlyAdded = ['WCAG-005', 'WCAG-047', 'WCAG-048', 'WCAG-049', 'WCAG-067', 'WCAG-068', 'WCAG-069'];
    console.log('\n✅ NEWLY INTEGRATED CHECKS:');
    newlyAdded.forEach((ruleId, index) => {
      const config = CHECK_UTILITY_PRIORITY[ruleId];
      if (config) {
        const utilities = [];
        if (config.enableSemanticValidation) utilities.push('Semantic');
        if (config.enablePatternValidation) utilities.push('Pattern');
        if (config.enableContentQualityAnalysis) utilities.push('Content');
        if (config.enableFrameworkOptimization) utilities.push('Framework');
        if (config.enableComponentLibraryDetection) utilities.push('Component');
        if (config.enableCMSDetection) utilities.push('CMS');
        
        console.log(`  ${index + 1}. ${ruleId} - Strategy: ${config.integrationStrategy} - Utilities: [${utilities.join(', ')}]`);
      }
    });
  } else {
    console.log('\n❌ INCOMPLETE: Some checks still need utility integration');
    
    // Find missing checks
    const allPossibleRuleIds = [];
    for (let i = 1; i <= 69; i++) {
      const ruleId = `WCAG-${i.toString().padStart(3, '0')}`;
      allPossibleRuleIds.push(ruleId);
    }
    
    const missingRuleIds = allPossibleRuleIds.filter(ruleId => !configuredRuleIds.includes(ruleId));
    console.log('\n❌ MISSING CHECKS:');
    missingRuleIds.forEach((ruleId, index) => {
      console.log(`  ${index + 1}. ${ruleId}`);
    });
  }

  console.log('\n✅ Verification complete!');
}

// Run the verification
verifyUtilityCompletion();
