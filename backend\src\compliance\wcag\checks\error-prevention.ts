/**
 * WCAG-032: Error Prevention Check
 * Success Criterion: 3.3.4 Error Prevention (Legal, Financial, Data) (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export interface ErrorPreventionConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableCriticalOperationDetection?: boolean;
  enablePreventionMethodValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableRiskAssessment?: boolean;
}

export class ErrorPreventionCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();

  // Keywords that indicate critical/sensitive operations
  private readonly criticalKeywords = {
    legal: ['agreement', 'contract', 'terms', 'legal', 'binding', 'consent', 'authorize'],
    financial: [
      'payment',
      'purchase',
      'buy',
      'order',
      'billing',
      'credit',
      'debit',
      'transfer',
      'withdraw',
    ],
    data: ['delete', 'remove', 'clear', 'reset', 'erase', 'destroy', 'permanent', 'irreversible'],
  };

  // Prevention method indicators
  private readonly preventionIndicators = {
    confirmation: ['confirm', 'verify', 'are you sure', 'proceed', 'continue', 'yes'],
    reversible: ['undo', 'cancel', 'reverse', 'rollback', 'restore'],
    checking: ['review', 'check', 'validate', 'preview', 'summary'],
  };

  async performCheck(config: ErrorPreventionConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorPreventionConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableCriticalOperationDetection: true,
      enablePreventionMethodValidation: true,
      enableAccessibilityPatterns: true,
      enableRiskAssessment: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-032',
      'Error Prevention',
      'understandable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeErrorPreventionCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with error prevention analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-032',
        ruleName: 'Error Prevention',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'error-prevention-analysis',
          formAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          criticalOperationDetection: enhancedConfig.enableCriticalOperationDetection,
          preventionMethodValidation: enhancedConfig.enablePreventionMethodValidation,
          riskAssessment: enhancedConfig.enableRiskAssessment,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'error-prevention-analysis',
        confidence: 0.7,
        additionalData: {
          checkType: 'form-safety',
          automationLevel: 'medium-high',
        },
      },
    };
  }

  private async executeErrorPreventionCheck(
    page: Page,
    config: ErrorPreventionConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer with session management
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeErrorHandling: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
      config.scanId || 'error-prevention-check'
    );

    // Analyze error prevention mechanisms using enhanced analyzer
    const errorPreventionAnalysis = await this.analyzeErrorPreventionMechanismsEnhanced(
      page,
      formAccessibilityReport,
    );

    // Analyze risk assessment and prevention requirements
    const riskAssessmentAnalysis = await this.analyzeRiskAssessment(page);

    // Analyze validation enhancement opportunities
    const validationEnhancementAnalysis = await this.analyzeValidationEnhancements(page);

    // Combine analysis results
    const allAnalyses = [
      errorPreventionAnalysis,
      riskAssessmentAnalysis,
      validationEnhancementAnalysis,
    ];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Enhanced error prevention mechanisms analysis using FormAccessibilityAnalyzer
   */
  private async analyzeErrorPreventionMechanismsEnhanced(
    page: Page,
    formAccessibilityReport: {
      forms: Array<{
        selector: string;
        errorHandling?: { hasErrorSummary: boolean };
        progressIndicator?: { hasProgress: boolean };
      }>;
    },
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach(
      (
        form: {
          selector: string;
          errorHandling?: { hasErrorSummary: boolean };
          progressIndicator?: { hasProgress: boolean };
        },
        formIndex: number,
      ) => {
        totalChecks++;

        // Determine if form handles critical data
        const isCriticalForm = this.isCriticalForm(form);

        if (isCriticalForm) {
          // Check for error prevention mechanisms
          const hasPreventionMechanisms = this.hasErrorPreventionMechanisms(form);

          if (hasPreventionMechanisms) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Critical form ${formIndex + 1} has error prevention mechanisms`,
              value: `${form.selector} - critical form with prevention mechanisms`,
              selector: form.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Critical form ${formIndex + 1} lacks error prevention mechanisms`);
            evidence.push({
              type: 'code',
              description: `Critical form ${formIndex + 1} requires error prevention`,
              value: `${form.selector} - critical form without adequate prevention`,
              selector: form.selector,
              severity: 'error',
            });
            recommendations.push(
              `Add error prevention mechanisms to critical form ${formIndex + 1} (confirmation, review, or undo)`,
            );
          }
        } else {
          // Non-critical forms pass by default
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Form ${formIndex + 1} is not critical and does not require error prevention`,
            value: `${form.selector} - non-critical form`,
            selector: form.selector,
            severity: 'info',
          });
        }
      },
    );

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze risk assessment for forms
   */
  private async analyzeRiskAssessment(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Analyze forms for risk indicators
    const riskAnalysis = await page.$$eval('form', (forms) => {
      return forms.map((form, index) => {
        const formText = form.textContent?.toLowerCase() || '';
        const formHTML = form.innerHTML.toLowerCase();

        // Risk indicators
        const financialKeywords = [
          'payment',
          'purchase',
          'buy',
          'order',
          'billing',
          'credit',
          'debit',
          'transfer',
          'withdraw',
          'money',
          'price',
          'cost',
        ];
        const legalKeywords = [
          'agreement',
          'contract',
          'terms',
          'legal',
          'binding',
          'consent',
          'authorize',
          'sign',
          'accept',
        ];
        const dataKeywords = [
          'delete',
          'remove',
          'clear',
          'reset',
          'erase',
          'destroy',
          'permanent',
          'irreversible',
        ];

        const hasFinancialRisk = financialKeywords.some(
          (keyword) => formText.includes(keyword) || formHTML.includes(keyword),
        );
        const hasLegalRisk = legalKeywords.some(
          (keyword) => formText.includes(keyword) || formHTML.includes(keyword),
        );
        const hasDataRisk = dataKeywords.some(
          (keyword) => formText.includes(keyword) || formHTML.includes(keyword),
        );

        // Prevention mechanisms
        const hasConfirmation =
          form.querySelector('.confirm, .confirmation, [data-confirm]') !== null ||
          formText.includes('confirm') ||
          formText.includes('are you sure');
        const hasReview =
          form.querySelector('.review, .summary, .preview') !== null ||
          formText.includes('review') ||
          formText.includes('summary');
        const hasUndo =
          form.querySelector('.undo, .cancel, [type="reset"]') !== null ||
          formText.includes('undo') ||
          formText.includes('cancel');

        const riskLevel = hasFinancialRisk || hasLegalRisk || hasDataRisk ? 'high' : 'low';
        const preventionCount = [hasConfirmation, hasReview, hasUndo].filter(Boolean).length;

        return {
          index,
          selector: `form:nth-of-type(${index + 1})`,
          riskLevel,
          hasFinancialRisk,
          hasLegalRisk,
          hasDataRisk,
          hasConfirmation,
          hasReview,
          hasUndo,
          preventionCount,
          needsPrevention: riskLevel === 'high',
          hasAdequatePrevention: riskLevel === 'low' || preventionCount >= 1,
        };
      });
    });

    const totalChecks = riskAnalysis.length;
    let passedChecks = 0;

    riskAnalysis.forEach((form, index) => {
      if (form.hasAdequatePrevention) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Form ${index + 1} has adequate error prevention for its risk level`,
          value: `${form.selector} - risk: ${form.riskLevel}, prevention mechanisms: ${form.preventionCount}`,
          selector: form.selector,
          severity: 'info',
        });
      } else {
        issues.push(`High-risk form ${index + 1} lacks adequate error prevention`);
        evidence.push({
          type: 'code',
          description: `High-risk form ${index + 1} requires error prevention mechanisms`,
          value: `${form.selector} - risk: ${form.riskLevel}, prevention mechanisms: ${form.preventionCount}/3 (financial: ${form.hasFinancialRisk}, legal: ${form.hasLegalRisk}, data: ${form.hasDataRisk})`,
          selector: form.selector,
          severity: 'error',
        });
        recommendations.push(
          `Add error prevention mechanisms to high-risk form ${index + 1} (confirmation, review, or undo functionality)`,
        );
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze validation enhancement opportunities
   */
  private async analyzeValidationEnhancements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for validation enhancement opportunities
    const validationAnalysis = await page.$$eval('input, select, textarea', (fields) => {
      return fields.map((field, index) => {
        const type = field.getAttribute('type') || field.tagName.toLowerCase();
        const isRequired =
          field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';
        const hasPattern = field.hasAttribute('pattern');
        const hasValidation =
          field.hasAttribute('min') ||
          field.hasAttribute('max') ||
          field.hasAttribute('minlength') ||
          field.hasAttribute('maxlength') ||
          hasPattern ||
          type === 'email' ||
          type === 'url' ||
          type === 'tel';

        const hasRealTimeValidation =
          field.hasAttribute('data-validate') ||
          field.classList.contains('validate') ||
          field.closest('form')?.hasAttribute('data-live-validate');

        const hasErrorPrevention = hasValidation && (hasRealTimeValidation || hasPattern);

        return {
          index,
          selector: `${field.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          type,
          isRequired,
          hasValidation,
          hasRealTimeValidation,
          hasErrorPrevention,
          needsEnhancement: isRequired && !hasErrorPrevention,
        };
      });
    });

    const totalChecks = validationAnalysis.filter((field) => field.isRequired).length;
    let passedChecks = 0;

    validationAnalysis.forEach((field, index) => {
      if (field.isRequired) {
        if (field.hasErrorPrevention) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Required field ${index + 1} has error prevention validation`,
            value: `${field.selector} - has validation and real-time feedback`,
            selector: field.selector,
            severity: 'info',
          });
        } else {
          issues.push(`Required field ${index + 1} lacks error prevention validation`);
          evidence.push({
            type: 'code',
            description: `Required field ${index + 1} needs validation enhancement`,
            value: `${field.selector} - required field without adequate validation`,
            selector: field.selector,
            severity: 'warning',
          });
          recommendations.push(
            `Add real-time validation to required field ${index + 1} to prevent errors`,
          );
        }
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Determine if a form handles critical data
   */
  private isCriticalForm(form: { selector: string }): boolean {
    const formText = form.selector.toLowerCase();
    const criticalKeywords = [
      'payment',
      'purchase',
      'buy',
      'order',
      'billing',
      'credit',
      'debit',
      'transfer',
      'agreement',
      'contract',
      'terms',
      'legal',
      'binding',
      'consent',
      'delete',
      'remove',
      'clear',
      'reset',
      'erase',
      'destroy',
      'permanent',
    ];

    return criticalKeywords.some((keyword) => formText.includes(keyword));
  }

  /**
   * Check if form has error prevention mechanisms
   */
  private hasErrorPreventionMechanisms(form: {
    errorHandling?: { hasErrorSummary: boolean };
    progressIndicator?: { hasProgress: boolean };
  }): boolean {
    // Check if form has confirmation, review, or undo mechanisms
    // This would need to be enhanced based on the actual form structure
    return Boolean(form.errorHandling?.hasErrorSummary || form.progressIndicator?.hasProgress);
  }

  private generateBeforeExample(formType: string): string {
    switch (formType) {
      case 'financial':
        return '<form><input type="number" name="amount"><button type="submit">Transfer Money</button></form>';
      case 'data':
        return '<form><input type="text" name="data"><button type="submit">Delete Account</button></form>';
      case 'legal':
        return '<form><input type="text" name="signature"><button type="submit">Sign Contract</button></form>';
      default:
        return '<form><button type="submit">Submit</button></form>';
    }
  }

  private generateAfterExample(formType: string): string {
    switch (formType) {
      case 'financial':
        return `<form>
  <button type="button" onclick="showConfirmation()">Complete Purchase</button>
</form>
<div id="confirmation" role="dialog" style="display: none;">
  <p>Confirm your purchase of $99.99?</p>
  <button onclick="submitPurchase()">Yes, Complete Purchase</button>
  <button onclick="hideConfirmation()">Cancel</button>
</div>`;
      case 'data':
        return `<form>
  <button type="button" onclick="showDeleteConfirmation()">Delete Account</button>
</form>
<div id="delete-confirmation" role="dialog" style="display: none;">
  <p>Are you sure? This action cannot be undone.</p>
  <input type="text" placeholder="Type 'DELETE' to confirm">
  <button onclick="deleteAccount()">Delete Account</button>
  <button onclick="hideConfirmation()">Cancel</button>
</div>`;
      case 'legal':
        return `<form>
  <div class="agreement-review">
    <h3>Agreement Summary</h3>
    <p>Please review the terms before accepting...</p>
  </div>
  <label><input type="checkbox" required> I have read and agree to the terms</label>
  <button type="submit">Accept Agreement</button>
</form>`;
      default:
        return '<form><button type="submit" onclick="return confirm(\'Are you sure?\')">Submit</button></form>';
    }
  }

  private generateCodeExample(formType: string): string {
    return `
// JavaScript for ${formType} form error prevention
function showConfirmation() {
  document.getElementById('confirmation').style.display = 'block';
  document.getElementById('confirmation').focus();
}

function hideConfirmation() {
  document.getElementById('confirmation').style.display = 'none';
}

function submitForm() {
  // Additional validation before submission
  if (validateForm()) {
    document.forms[0].submit();
  }
}

// For reversible actions, provide undo functionality
function provideUndo(actionType) {
  const undoNotification = document.createElement('div');
  undoNotification.innerHTML = \`
    <p>\${actionType} completed. <button onclick="undoAction()">Undo</button></p>
  \`;
  document.body.appendChild(undoNotification);

  setTimeout(() => {
    undoNotification.remove();
  }, 10000); // 10 second undo window
}
    `;
  }
}
