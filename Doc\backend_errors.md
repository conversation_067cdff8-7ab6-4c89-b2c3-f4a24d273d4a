[ENV_DEBUG] NODE_ENV: development
[ENV_DEBUG] isTestEnv: false
[ENV_DEBUG] process.env.POSTGRES_HOST (raw): localhost
[ENV_DEBUG] parsedEnv.POSTGRES_HOST (from Zod): localhost
[ENV_DEBUG] dbHost chosen: localhost
[ENV_DEBUG] Constructed DATABASE_URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
2025-07-11T16:48:00.711Z [INFO] - 🗄️ Smart cache initialized - {"maxSize":100,"maxEntries":500,"def aultTTL":1800000,"defaultTTLMinutes":30}
2025-07-11T16:48:01.664Z [DEBUG] - Readability analysis library not available
2025-07-11T16:48:01.672Z [DEBUG] - Language detection library not available
D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:859
    return new TSError(diagnosticText, diagnosticCodes, diagnostics);
           ^
TSError: ⨯ Unable to compile TypeScript:
src/compliance/wcag/utils/form-accessibility-analyzer.ts:160:30 - error TS2339: Property 'getForms' does not exist on type 'FormAccessibilityAnalyzer'.

160     const forms = await this.getForms(page, fullConfig);
                                 ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:166:37 - error TS2339: Property 'analyzeForm' does not exist on type 'FormAccessibilityAnalyzer'.

166         const analysis = await this.analyzeForm(page, form, fullConfig);
                                        ~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:174:25 - error TS2339: Property 'generateReport' does not exist on type 'FormAccessibilityAnalyzer'.

174     const report = this.generateReport(formAnalyses);
                            ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:9 - error TS2304: Cannot find name 'isVisible'.

276         isVisible: function(element) {
            ~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:28 - error TS2393: Duplicate function implementation.

276         isVisible: function(element) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:29 - error TS7006: Parameter 'element' implicitly has an 'any' type.

276         isVisible: function(element) {
                                ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:9 - error TS1344: 'A label is not allowed here.

284         analyzeFieldLabel: function(field) {
            ~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:36 - error TS2393: Duplicate function implementation.

284         analyzeFieldLabel: function(field) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:37 - error TS7006: Parameter 'field' implicitly has an 'any' type.

284         analyzeFieldLabel: function(field) {
                                        ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:360:9 - error TS1344: 'A label is not allowed here.

360         analyzeFieldValidation: function(field) {
            ~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:360:41 - error TS2393: Duplicate function implementation.

360         analyzeFieldValidation: function(field) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:360:42 - error TS7006: Parameter 'field' implicitly has an 'any' type.

360         analyzeFieldValidation: function(field) {
                                             ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:408:36 - error TS7006: Parameter 'errorEl' implicitly has an 'any' type.

408             errorElements.forEach((errorEl) => {
                                       ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:436:9 - error TS1344: 'A label is not allowed here.

436         analyzeFieldGrouping: function(field) {
            ~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:436:39 - error TS2393: Duplicate function implementation.

436         analyzeFieldGrouping: function(field) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:436:40 - error TS7006: Parameter 'field' implicitly has an 'any' type.

436         analyzeFieldGrouping: function(field) {
                                           ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:470:9 - error TS1344: 'A label is not allowed here.

470         analyzeAutocomplete: function(field) {
            ~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:470:38 - error TS2393: Duplicate function implementation.

470         analyzeAutocomplete: function(field) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:470:39 - error TS7006: Parameter 'field' implicitly has an 'any' type.

470         analyzeAutocomplete: function(field) {
                                          ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:510:9 - error TS1344: 'A label is not allowed here.

510         analyzeAccessibility: function(field) {
            ~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:510:39 - error TS2393: Duplicate function implementation.

510         analyzeAccessibility: function(field) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:510:40 - error TS7006: Parameter 'field' implicitly has an 'any' type.

510         analyzeAccessibility: function(field) {
                                           ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:535:17 - error TS18046: 'attr' is of type 'unknown'.

535             if (attr.name.startsWith('aria-')) {
                    ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:536:35 - error TS18046: 'attr' is of type 'unknown'.

536               ariaAttributes.push(attr.name);
                                      ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:552:9 - error TS1344: 'A label is not allowed here.

552         getFormFields: function(form, includeHidden) {
            ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:552:32 - error TS2393: Duplicate function implementation.

552         getFormFields: function(form, includeHidden) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:552:33 - error TS7006: Parameter 'form' implicitly has an 'any' type.

552         getFormFields: function(form, includeHidden) {
                                    ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:552:39 - error TS7006: Parameter 'includeHidden' implicitly has an 'any' type.

552         getFormFields: function(form, includeHidden) {
                                          ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:576:31 - error TS7006: Parameter 'el' implicitly has an 'any' type.

576             elements.forEach((el) => {
                                  ~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:590:9 - error TS1344: 'A label is not allowed here.

590         analyzeErrorHandling: function(form) {
            ~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:590:39 - error TS2393: Duplicate function implementation.

590         analyzeErrorHandling: function(form) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:590:40 - error TS7006: Parameter 'form' implicitly has an 'any' type.

590         analyzeErrorHandling: function(form) {
                                           ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:603:34 - error TS7006: Parameter 'errorEl' implicitly has an 'any' type.

603           errorElements.forEach((errorEl) => {
                                     ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:627:9 - error TS1344: 'A label is not allowed here.

627         analyzeProgressIndicator: function(form) {
            ~~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:627:43 - error TS2393: Duplicate function implementation.

627         analyzeProgressIndicator: function(form) {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:627:44 - error TS7006: Parameter 'form' implicitly has an 'any' type.

627         analyzeProgressIndicator: function(form) {
                                               ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:635:39 - error TS7006: Parameter 'progress' implicitly has an 'any' type.

635             progressElements.forEach((progress) => {
                                          ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:682:12 - error TS7006: Parameter 'injectionError' implicitly has an 'any' type.

682   } catch (injectionError) {
               ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:691:11 - error TS2304: Cannot find name 'async'.

691   private async getForms(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:691:17 - error TS2304: Cannot find name 'getForms'.

691   private async getForms(
                    ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:692:5 - error TS2552: Cannot find name 'page'. Did you mean 'Page'?

692     page: Page,
        ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:693:5 - error TS2304: Cannot find name '_config'.

693     _config: FormAnalysisConfig,
        ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:693:14 - error TS2693: 'FormAnalysisConfig' only refers to a type, but is being used as a value here.

693     _config: FormAnalysisConfig,
                 ~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:6 - error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ return: any; }'.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
695     return await page.evaluate(() => {
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
...
717     });
    ~~~~~~~
718   }
    ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:14 - error TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ selector: any; element: { tagName: any; action: any; method: any; }; }'.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:26 - error TS2693: 'string' only refers to a type, but is being used as a value here.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                             ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:54 - error TS2693: 'string' only refers to a type, but is being used as a value here.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:70 - error TS2693: 'string' only refers to a type, but is being used as a value here.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:86 - error TS2693: 'string' only refers to a type, but is being used as a value here.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                                                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:695:18 - error TS2552: Cannot find name 'page'. Did you mean 'Page'?

695     return await page.evaluate(() => {
                     ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:723:11 - error TS2304: Cannot find name 'async'.

723   private async analyzeForm(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:723:17 - error TS2304: Cannot find name 'analyzeForm'.

723   private async analyzeForm(
                    ~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:724:5 - error TS2552: Cannot find name 'page'. Did you mean 'Page'?

724     page: Page,
        ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:5 - error TS2304: Cannot find name 'formInfo'.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
        ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:27 - error TS2693: 'string' only refers to a type, but is being used as a value here.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                              ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:55 - error TS2693: 'string' only refers to a type, but is being used as a value here.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:71 - error TS2693: 'string' only refers to a type, but is being used as a value here.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:87 - error TS2693: 'string' only refers to a type, but is being used as a value here.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                                          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:5 - error TS2304: Cannot find name 'config'.

726     config: FormAnalysisConfig,
        ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:13 - error TS2693: 'FormAnalysisConfig' only refers to a type, but is being used as a value here.

726     config: FormAnalysisConfig,
                ~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:727:6 - error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ return: any; }'.

727   ): Promise<FormAnalysis> {
         ~~~~~~~~~~~~~~~~~~~~~~~
728     return await page.evaluate(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
...
962     );
    ~~~~~~
963   }
    ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:727:14 - error TS2693: 'FormAnalysis' only refers to a type, but is being used as a value here.

727   ): Promise<FormAnalysis> {
                 ~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:728:18 - error TS2304: Cannot find name 'page'.

728     return await page.evaluate(
                     ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:8 - error TS7006: Parameter 'selector' implicitly has an 'any' type.

729       (selector, analysisConfig) => {
           ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:18 - error TS7006: Parameter 'analysisConfig' implicitly has an 'any' type.

729       (selector, analysisConfig) => {
                     ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:960:7 - error TS2304: Cannot find name 'formInfo'.

960       formInfo.selector,
          ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:961:7 - error TS2304: Cannot find name 'config'.

961       config,
          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:11 - error TS2304: Cannot find name 'generateReport'.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
              ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:26 - error TS2304: Cannot find name 'forms'.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                             ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:33 - error TS2693: 'FormAnalysis' only refers to a type, but is being used as a value here.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                    ~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:50 - error TS2693: 'FormAccessibilityReport' only refers to a type, but is being used as a value here.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                                     ~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:969:24 - error TS2304: Cannot find name 'forms'.

969     const totalForms = forms.length;
                           ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:970:25 - error TS2304: Cannot find name 'forms'.

970     const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
                            ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:970:39 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

970     const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
                                          ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:970:44 - error TS7006: Parameter 'form' implicitly has an 'any' type.

970     const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
                                               ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:971:29 - error TS2304: Cannot find name 'forms'.

971     const accessibleForms = forms.filter((form) => form.overallScore >= 80).length;
                                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:971:43 - error TS7006: Parameter 'form' implicitly has an 'any' type.

971     const accessibleForms = forms.filter((form) => form.overallScore >= 80).length;
                                              ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:972:30 - error TS2304: Cannot find name 'forms'.

972     const accessibleFields = forms.reduce(
                                 ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:973:8 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

973       (sum, form) => sum + form.fields.filter((field) => field.score >= 80).length,
           ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:973:13 - error TS7006: Parameter 'form' implicitly has an 'any' type.

973       (sum, form) => sum + form.fields.filter((field) => field.score >= 80).length,
                ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:973:48 - error TS7006: Parameter 'field' implicitly has an 'any' type.

973       (sum, form) => sum + form.fields.filter((field) => field.score >= 80).length,
                                                   ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:978:24 - error TS2304: Cannot find name 'forms'.

978     const formScores = forms.map((form) => form.overallScore);
                           ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:978:35 - error TS7006: Parameter 'form' implicitly has an 'any' type.

978     const formScores = forms.map((form) => form.overallScore);
                                      ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:981:41 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

981         ? Math.round(formScores.reduce((sum, score) => sum + score, 0) / formScores.length)
                                            ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:981:46 - error TS7006: Parameter 'score' implicitly has an 'any' type.

981         ? Math.round(formScores.reduce((sum, score) => sum + score, 0) / formScores.length)
                                                 ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:986:5 - error TS2304: Cannot find name 'forms'.

986     forms.forEach((form) => {
        ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:986:20 - error TS7006: Parameter 'form' implicitly has an 'any' type.

986     forms.forEach((form) => {
                       ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:988:28 - error TS7006: Parameter 'field' implicitly has an 'any' type.

988       form.fields.forEach((field) => {
                               ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:18 - error TS1005: ';' expected.

276         isVisible: function(element) {
                     ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:28 - error TS1003: Identifier expected.

276         isVisible: function(element) {
                               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:279:10 - error TS1128: Declaration or statement expected.

279         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:36 - error TS1003: Identifier expected.

284         analyzeFieldLabel: function(field) {
                                       ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:355:10 - error TS1128: Declaration or statement expected.

355         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:360:41 - error TS1003: Identifier expected.

360         analyzeFieldValidation: function(field) {
                                            ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:431:10 - error TS1128: Declaration or statement expected.

431         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:436:39 - error TS1003: Identifier expected.

436         analyzeFieldGrouping: function(field) {
                                          ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:465:10 - error TS1128: Declaration or statement expected.

465         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:470:38 - error TS1003: Identifier expected.

470         analyzeAutocomplete: function(field) {
                                         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:505:10 - error TS1128: Declaration or statement expected.

505         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:510:39 - error TS1003: Identifier expected.

510         analyzeAccessibility: function(field) {
                                          ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:547:10 - error TS1128: Declaration or statement expected.

547         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:552:32 - error TS1003: Identifier expected.

552         getFormFields: function(form, includeHidden) {
                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:585:10 - error TS1128: Declaration or statement expected.

585         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:590:39 - error TS1003: Identifier expected.

590         analyzeErrorHandling: function(form) {
                                          ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:622:10 - error TS1128: Declaration or statement expected.

622         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:627:43 - error TS1003: Identifier expected.

627         analyzeProgressIndicator: function(form) {
                                              ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:656:10 - error TS1128: Declaration or statement expected.

656         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:657:8 - error TS1005: ')' expected.

657       };
           ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:665:6 - error TS1472: 'catch' or 'finally' expected.

665     });
         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:691:3 - error TS1128: Declaration or statement expected.

691   private async getForms(
      ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:691:11 - error TS1434: Unexpected keyword or identifier.

691   private async getForms(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:692:9 - error TS1005: ',' expected.

692     page: Page,
            ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:693:12 - error TS1005: ',' expected.

693     _config: FormAnalysisConfig,
               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:4 - error TS1005: ';' expected.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
       ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:32 - error TS1005: ',' expected.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:60 - error TS1005: ',' expected.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:76 - error TS1005: ',' expected.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                                               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:694:97 - error TS1011: An element access expression should take an argument.

694   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:695:12 - error TS1005: ':' expected.

695     return await page.evaluate(() => {
               ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:717:7 - error TS1005: ',' expected.

717     });
          ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:723:3 - error TS1128: Declaration or statement expected.

723   private async analyzeForm(
      ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:723:11 - error TS1434: Unexpected keyword or identifier.

723   private async analyzeForm(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:724:9 - error TS1005: ',' expected.

724     page: Page,
            ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:13 - error TS1005: ',' expected.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:33 - error TS1005: ',' expected.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                    ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:61 - error TS1005: ',' expected.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:725:77 - error TS1005: ',' expected.

725     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:11 - error TS1005: ',' expected.

726     config: FormAnalysisConfig,
              ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:727:4 - error TS1005: ';' expected.

727   ): Promise<FormAnalysis> {
       ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:728:12 - error TS1005: ':' expected.

728     return await page.evaluate(
               ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:962:6 - error TS1005: ',' expected.

962     );
         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:3 - error TS1128: Declaration or statement expected.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
      ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:31 - error TS1005: ',' expected.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                  ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:46 - error TS1011: An element access expression should take an argument.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:48 - error TS1005: ';' expected.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:968:50 - error TS1434: Unexpected keyword or identifier.

968   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                                     ~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1043:1 - error TS1128: Declaration or statement expected.

1043 }
     ~

    at createTSError (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:859:12)
    at reportTSError (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:863:19)
    at getOutput (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1077:36)
    at Object.compile (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1433:41)
    at Module.m._compile (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at Object.require.extensions.<computed> [as .ts] (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
    at Module.require (node:internal/modules/cjs/loader:1311:19) {
  diagnosticCodes: [
    2339, 2339, 2339, 2304,  2393,  7006, 1344, 2393, 7006,
    1344, 2393, 7006, 7006,  1344,  2393, 7006, 1344, 2393,
    7006, 1344, 2393, 7006, 18046, 18046, 1344, 2393, 7006,
    7006, 7006, 1344, 2393,  7006,  7006, 1344, 2393, 7006,
    7006, 7006, 2304, 2304,  2552,  2304, 2693, 2365, 7053,
    2693, 2693, 2693, 2693,  2552,  2304, 2304, 2552, 2304,
    2693, 2693, 2693, 2693,  2304,  2693, 2365, 2693, 2304,
    7006, 7006, 2304, 2304,  2304,  2304, 2693, 2693, 2304,
    2304, 7006, 7006, 2304,  7006,  2304, 7006, 7006, 7006,
    2304, 7006, 7006, 7006,  2304,  7006, 7006, 1005, 1003,
    1128, 1003, 1128, 1003,  1128,  1003, 1128, 1003, 1128,
    1003,
    ... 37 more items
  ]
}
[nodemon] app crashed - waiting for file changes before starting...