/**
 * Comprehensive Runtime WCAG Validation
 * Actually executes WCAG checks to verify fixes work in practice
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { EnhancedColorAnalyzer } from '../utils/enhanced-color-analyzer';
import { AccessibleAuthenticationCheck } from '../checks/accessible-authentication';
import { AccessibleAuthenticationEnhancedCheck } from '../checks/accessible-authentication-enhanced';
import SmartCache from '../utils/smart-cache';
import { ContrastMinimumCheck } from '../checks/contrast-minimum';
import { FocusVisibleCheck } from '../checks/focus-visible';
import { NonTextContentCheck } from '../checks/non-text-content';
import logger from '../../../utils/logger';

interface RuntimeTestResult {
  testName: string;
  category: string;
  passed: boolean;
  executionTime: number;
  error?: string;
  details?: any;
}

class ComprehensiveRuntimeValidator {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private results: RuntimeTestResult[] = [];
  private startTime: number = 0;

  async initialize(): Promise<void> {
    console.log('🚀 Initializing Comprehensive Runtime Validation...');
    this.startTime = Date.now();
    
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-extensions'
      ],
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1280, height: 720 });
    
    // Create comprehensive test page with all scenarios
    await this.page.setContent(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <title>Comprehensive WCAG Runtime Test</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          .low-contrast { color: #ccc; background: #fff; }
          .good-contrast { color: #000; background: #fff; }
          .focus-visible { outline: 2px solid blue; }
          .focus-hidden { outline: none; }
          .interactive { cursor: pointer; padding: 10px; }
          .form-section { margin: 20px 0; }
          .auth-alternative { background: #f0f0f0; padding: 5px; }
          .hidden { display: none; }
          .visible { display: block; }
          .complex-element { 
            position: relative; 
            z-index: 1; 
            background: linear-gradient(45deg, #ff0000, #00ff00);
            color: #ffffff;
            padding: 15px;
            margin: 10px;
            border-radius: 5px;
          }
        </style>
      </head>
      <body>
        <header>
          <h1>WCAG Runtime Validation Test Page</h1>
          <nav aria-label="Main navigation">
            <ul>
              <li><a href="#auth-section">Authentication</a></li>
              <li><a href="#content-section">Content</a></li>
              <li><a href="#interactive-section">Interactive</a></li>
            </ul>
          </nav>
        </header>
        
        <main>
          <section id="auth-section" class="form-section">
            <h2>Authentication Testing Section</h2>
            <form id="login-form" method="post" action="/login">
              <div>
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" class="interactive" required>
              </div>
              
              <div>
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" class="interactive" required>
              </div>
              
              <div>
                <label for="captcha">CAPTCHA:</label>
                <input type="text" id="captcha" name="captcha" class="interactive">
                <img src="captcha.jpg" alt="CAPTCHA image">
              </div>
              
              <div class="auth-alternative">
                <button type="button" data-auth-alternative="true" class="social-login">
                  Login with Google
                </button>
                <button type="button" data-auth-alternative="true" class="oauth-login">
                  Login with GitHub
                </button>
              </div>
              
              <button type="submit" class="interactive focus-visible">Submit</button>
            </form>
          </section>
          
          <section id="content-section">
            <h2>Content Analysis Section</h2>
            <p class="good-contrast">This text has good contrast ratio.</p>
            <p class="low-contrast">This text has poor contrast ratio.</p>
            
            <img src="test-image.jpg" alt="Test image with proper alt text">
            <img src="decorative.jpg" alt="" role="presentation">
            <img src="missing-alt.jpg">
            
            <div class="complex-element" tabindex="0">
              Complex interactive element with multiple classes
            </div>
          </section>
          
          <section id="interactive-section">
            <h2>Interactive Elements Section</h2>
            <button class="interactive focus-visible" tabindex="0">Focusable Button</button>
            <div class="interactive focus-hidden" tabindex="0">Focusable Div</div>
            <a href="#" class="interactive">Test Link</a>
            
            <div class="hidden">Hidden content</div>
            <div class="visible">Visible content</div>
          </section>
        </main>
        
        <footer>
          <p>&copy; 2024 Runtime Test Page</p>
        </footer>
      </body>
      </html>
    `);
    
    console.log('✅ Test page loaded with comprehensive scenarios');
  }

  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }
  }

  private async executeTest(
    testName: string,
    category: string,
    testFunction: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🧪 Running: ${testName}`);
      const result = await testFunction();
      const executionTime = Date.now() - startTime;
      
      this.results.push({
        testName,
        category,
        passed: true,
        executionTime,
        details: result
      });
      
      console.log(`✅ ${testName} - PASSED (${executionTime}ms)`);
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.results.push({
        testName,
        category,
        passed: false,
        executionTime,
        error: error.message,
        details: error.stack
      });
      
      console.log(`❌ ${testName} - FAILED (${executionTime}ms): ${error.message}`);
    }
  }

  async validatePatternDetectionRuntime(): Promise<void> {
    console.log('\n🔍 RUNTIME PATTERN DETECTION VALIDATION');
    console.log('='.repeat(50));
    
    const patternLibrary = AccessibilityPatternLibrary.getInstance();
    
    // Test 1: Function injection and execution
    await this.executeTest(
      'Pattern Detection Function Injection',
      'Pattern Detection',
      async () => {
        const result = await patternLibrary.injectPatternDetectionFunctions(this.page!);
        if (!result) throw new Error('Function injection failed');
        return { injected: true };
      }
    );
    
    // Test 2: Real pattern analysis with various selectors
    await this.executeTest(
      'Pattern Analysis with Valid Selectors',
      'Pattern Detection',
      async () => {
        const pattern = {
          name: 'Form Elements Pattern',
          selectors: ['input[type="text"]', 'input[type="password"]', 'button[type="submit"]'],
          description: 'Form input and submit elements'
        };
        
        const result = await patternLibrary.analyzePattern(this.page!, pattern);
        if (!result || !Array.isArray(result.elements)) {
          throw new Error('Pattern analysis failed to return valid results');
        }
        if (result.elements.length === 0) {
          throw new Error('No elements found - pattern detection may be broken');
        }
        
        return {
          patternName: result.patternName,
          elementCount: result.elements.length,
          elements: result.elements.slice(0, 3) // First 3 for verification
        };
      }
    );
    
    // Test 3: Error handling with invalid inputs (the main fix)
    await this.executeTest(
      'Pattern Detection Error Handling',
      'Pattern Detection',
      async () => {
        const results = await this.page!.evaluate(() => {
          const detection = (window as any).accessibilityPatternDetection;
          if (!detection) throw new Error('Detection functions not available');
          
          // Test all the scenarios that were causing errors
          const tests = [
            { name: 'null input', input: null },
            { name: 'undefined input', input: undefined },
            { name: 'string input', input: 'not-an-array' },
            { name: 'number input', input: 123 },
            { name: 'object input', input: { not: 'array' } },
            { name: 'invalid selectors', input: ['invalid[[[selector', 'input[type="text"]'] }
          ];
          
          const testResults = [];
          
          for (const test of tests) {
            try {
              const result = detection.findPatternElements(test.input);
              testResults.push({
                test: test.name,
                passed: Array.isArray(result),
                resultType: typeof result,
                isArray: Array.isArray(result),
                length: result?.length || 0
              });
            } catch (error) {
              testResults.push({
                test: test.name,
                passed: false,
                error: error.message
              });
            }
          }
          
          return testResults;
        });
        
        // Verify all tests passed (should return empty arrays, not throw errors)
        const failedTests = results.filter(r => !r.passed);
        if (failedTests.length > 0) {
          throw new Error(`Error handling failed: ${failedTests.map(t => t.test).join(', ')}`);
        }
        
        return { errorHandlingTests: results };
      }
    );
    
    // Test 4: Complex pattern analysis
    await this.executeTest(
      'Complex Multi-Pattern Analysis',
      'Pattern Detection',
      async () => {
        const patterns = [
          {
            name: 'Navigation Pattern',
            selectors: ['nav', 'nav ul', 'nav li', 'nav a'],
            description: 'Navigation structure'
          },
          {
            name: 'Form Pattern',
            selectors: ['form', 'input', 'label', 'button'],
            description: 'Form structure'
          },
          {
            name: 'Interactive Pattern',
            selectors: ['[tabindex]', 'button', 'a[href]', '.interactive'],
            description: 'Interactive elements'
          }
        ];
        
        const results = await patternLibrary.analyzePatterns(this.page!, patterns);
        
        if (!Array.isArray(results) || results.length !== patterns.length) {
          throw new Error('Multi-pattern analysis failed');
        }
        
        return {
          patternCount: results.length,
          totalElements: results.reduce((sum, r) => sum + r.elements.length, 0),
          patterns: results.map(r => ({ name: r.patternName, elementCount: r.elements.length }))
        };
      }
    );
  }

  async validateColorAnalyzerRuntime(): Promise<void> {
    console.log('\n🎨 RUNTIME COLOR ANALYZER VALIDATION');
    console.log('='.repeat(50));
    
    const colorAnalyzer = EnhancedColorAnalyzer.getInstance();
    
    // Test 1: Focus appearance analysis (tests the className.split fix)
    await this.executeTest(
      'Focus Appearance Analysis',
      'Color Analyzer',
      async () => {
        const result = await colorAnalyzer.analyzeFocusAppearance(this.page!);
        
        if (!result || !Array.isArray(result.focusableElements)) {
          throw new Error('Focus appearance analysis failed');
        }
        
        return {
          focusableElementCount: result.focusableElements.length,
          visibleFocusCount: result.focusableElements.filter(el => el.hasVisibleFocus).length,
          elements: result.focusableElements.slice(0, 3)
        };
      }
    );
    
    // Test 2: Element selector generation with various className scenarios
    await this.executeTest(
      'Element Selector Generation with className Edge Cases',
      'Color Analyzer',
      async () => {
        const results = await this.page!.evaluate(() => {
          // Inject the color analyzer functions
          const detection = (window as any).accessibilityPatternDetection;
          if (!detection) throw new Error('Detection functions not available');
          
          // Create test elements with various className scenarios
          const testCases = [
            { className: 'single-class', expected: 'single-class' },
            { className: 'multiple classes here', expected: 'multiple.classes.here' },
            { className: '  spaced  classes  ', expected: 'spaced.classes' },
            { className: '', expected: '' },
            { className: null, expected: '' },
            { className: undefined, expected: '' }
          ];
          
          const results = [];
          
          testCases.forEach((testCase, index) => {
            try {
              const element = document.createElement('div');
              element.id = `test-element-${index}`;
              
              // Set className based on test case
              if (testCase.className !== null && testCase.className !== undefined) {
                element.className = testCase.className;
              }
              
              document.body.appendChild(element);
              
              // Test the selector generation
              const selector = detection.getElementSelector(element);
              
              results.push({
                testCase: testCase.className,
                selector: selector,
                passed: typeof selector === 'string' && selector.length > 0,
                containsExpected: testCase.expected ? selector.includes(testCase.expected) : true
              });
              
              document.body.removeChild(element);
            } catch (error) {
              results.push({
                testCase: testCase.className,
                passed: false,
                error: error.message
              });
            }
          });
          
          return results;
        });
        
        const failedTests = results.filter(r => !r.passed);
        if (failedTests.length > 0) {
          throw new Error(`Selector generation failed for: ${failedTests.map(t => t.testCase).join(', ')}`);
        }
        
        return { selectorTests: results };
      }
    );
  }

  async validateAuthenticationChecksRuntime(): Promise<void> {
    console.log('\n🔐 RUNTIME AUTHENTICATION CHECKS VALIDATION');
    console.log('='.repeat(50));
    
    // Test 1: Basic authentication check execution
    await this.executeTest(
      'Basic Authentication Check Execution',
      'Authentication',
      async () => {
        const authCheck = new AccessibleAuthenticationCheck();
        
        const config = {
          targetUrl: 'test://localhost',
          scanId: 'runtime-validation-test',
          enableManualReview: false,
          enableManualTracking: false,
          maxManualItems: 0,
          page: this.page!,
        };
        
        const result = await authCheck.performCheck(config);
        
        if (!result || typeof result.score !== 'number') {
          throw new Error('Authentication check failed to return valid result');
        }
        
        return {
          score: result.score,
          status: result.status,
          issueCount: result.issues?.length || 0,
          recommendationCount: result.recommendations?.length || 0
        };
      }
    );
    
    // Test 2: Enhanced authentication check execution
    await this.executeTest(
      'Enhanced Authentication Check Execution',
      'Authentication',
      async () => {
        const enhancedAuthCheck = new AccessibleAuthenticationEnhancedCheck();
        
        const config = {
          targetUrl: 'test://localhost',
          scanId: 'runtime-validation-enhanced-test',
          enableManualReview: false,
          enableManualTracking: false,
          maxManualItems: 0,
          page: this.page!,
          enableUtilityIntegration: true,
          utilityConfig: {
            enablePatternValidation: true,
            enableCaching: true,
            enableGracefulFallback: true,
            integrationStrategy: 'supplement' as const,
            maxExecutionTime: 5000,
          }
        };
        
        const result = await enhancedAuthCheck.performCheck(config);
        
        if (!result || typeof result.score !== 'number') {
          throw new Error('Enhanced authentication check failed');
        }
        
        return {
          score: result.score,
          status: result.status,
          enhancedFeatures: result.enhancedFeatures || {},
          utilityIntegration: result.utilityIntegration || {}
        };
      }
    );
    
    // Test 3: Alternative detection (the fixed method)
    await this.executeTest(
      'Authentication Alternative Detection',
      'Authentication',
      async () => {
        const hasAlternatives = await this.page!.evaluate(() => {
          const form = document.querySelector('#login-form');
          if (!form) return false;
          
          const alternatives = form.querySelectorAll('[data-auth-alternative]');
          return {
            formFound: !!form,
            alternativeCount: alternatives.length,
            alternativeTypes: Array.from(alternatives).map(el => el.textContent?.trim())
          };
        });
        
        if (!hasAlternatives.formFound) {
          throw new Error('Test form not found');
        }
        
        if (hasAlternatives.alternativeCount === 0) {
          throw new Error('No authentication alternatives detected');
        }
        
        return hasAlternatives;
      }
    );
  }

  async validateCacheSystemRuntime(): Promise<void> {
    console.log('\n💾 RUNTIME CACHE SYSTEM VALIDATION');
    console.log('='.repeat(50));
    
    const cache = SmartCache.getInstance();
    
    // Test 1: Cache operations
    await this.executeTest(
      'Cache Store and Retrieve Operations',
      'Cache System',
      async () => {
        const testData = { 
          test: 'runtime-validation', 
          timestamp: Date.now(),
          complexData: { nested: { value: 'test' } }
        };
        
        // Store data
        await cache.cacheRuleResult('RUNTIME-001', 'test-content-hash', 'test-config-hash', testData);
        
        // Retrieve data
        const retrievedData = await cache.getRuleResult('RUNTIME-001', 'test-content-hash', 'test-config-hash');
        
        if (!retrievedData) {
          throw new Error('Cache retrieve failed - data not found');
        }
        
        if (retrievedData.test !== testData.test) {
          throw new Error('Cache data integrity failed');
        }
        
        return {
          stored: testData,
          retrieved: retrievedData,
          dataIntegrity: retrievedData.test === testData.test
        };
      }
    );
    
    // Test 2: Cache statistics and performance
    await this.executeTest(
      'Cache Statistics and Performance',
      'Cache System',
      async () => {
        // Perform multiple cache operations to test statistics
        const operations = [];
        
        for (let i = 0; i < 5; i++) {
          const key = `test-key-${i}`;
          const data = { iteration: i, data: `test-data-${i}` };
          
          await cache.cacheRuleResult(`TEST-${i}`, key, 'config', data);
          const retrieved = await cache.getRuleResult(`TEST-${i}`, key, 'config');
          
          operations.push({
            stored: !!data,
            retrieved: !!retrieved,
            matches: retrieved?.iteration === i
          });
        }
        
        const stats = cache.getStats();
        
        if (!stats || typeof stats.hits !== 'number') {
          throw new Error('Cache statistics not available');
        }
        
        return {
          operations,
          stats,
          allOperationsSuccessful: operations.every(op => op.stored && op.retrieved && op.matches)
        };
      }
    );
  }

  async validateActualWCAGChecksRuntime(): Promise<void> {
    console.log('\n🎯 RUNTIME WCAG CHECKS VALIDATION');
    console.log('='.repeat(50));
    
    // Test actual WCAG checks to ensure they work end-to-end
    const checksToTest = [
      { name: 'Contrast Minimum Check', check: ContrastMinimumCheck, ruleId: 'WCAG-004' },
      { name: 'Focus Visible Check', check: FocusVisibleCheck, ruleId: 'WCAG-007' },
      { name: 'Non-Text Content Check', check: NonTextContentCheck, ruleId: 'WCAG-001' }
    ];
    
    for (const checkInfo of checksToTest) {
      await this.executeTest(
        `${checkInfo.name} Execution`,
        'WCAG Checks',
        async () => {
          const checkInstance = new checkInfo.check();
          
          const config = {
            targetUrl: 'test://localhost',
            scanId: `runtime-validation-${checkInfo.ruleId}`,
            enableManualReview: false,
            enableManualTracking: false,
            maxManualItems: 0,
            page: this.page!,
          };
          
          const result = await checkInstance.performCheck(config);
          
          if (!result) {
            throw new Error(`${checkInfo.name} returned null result`);
          }
          
          if (typeof result.score !== 'number' || result.score < 0 || result.score > 100) {
            throw new Error(`${checkInfo.name} returned invalid score: ${result.score}`);
          }
          
          return {
            ruleId: checkInfo.ruleId,
            score: result.score,
            status: result.status,
            hasIssues: Array.isArray(result.issues),
            hasRecommendations: Array.isArray(result.recommendations),
            executedSuccessfully: true
          };
        }
      );
    }
  }

  async runComprehensiveValidation(): Promise<RuntimeTestResult[]> {
    try {
      await this.initialize();
      
      // Run all validation categories
      await this.validatePatternDetectionRuntime();
      await this.validateColorAnalyzerRuntime();
      await this.validateAuthenticationChecksRuntime();
      await this.validateCacheSystemRuntime();
      await this.validateActualWCAGChecksRuntime();
      
      return this.results;
    } finally {
      await this.cleanup();
    }
  }

  printComprehensiveSummary(): void {
    const totalTime = Date.now() - this.startTime;
    
    console.log('\n' + '='.repeat(70));
    console.log('🎯 COMPREHENSIVE RUNTIME VALIDATION SUMMARY');
    console.log('='.repeat(70));
    
    const categories = [...new Set(this.results.map(r => r.category))];
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);
    
    console.log(`\n📊 OVERALL RESULTS:`);
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${percentage}%`);
    console.log(`Total Execution Time: ${totalTime}ms`);
    
    console.log(`\n📋 RESULTS BY CATEGORY:`);
    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category);
      const categoryPassed = categoryResults.filter(r => r.passed).length;
      const categoryTotal = categoryResults.length;
      const categoryPercentage = Math.round((categoryPassed / categoryTotal) * 100);
      
      console.log(`${category}: ${categoryPassed}/${categoryTotal} (${categoryPercentage}%)`);
    });
    
    if (passed === total) {
      console.log('\n🎉 ALL COMPREHENSIVE RUNTIME TESTS PASSED!');
      console.log('✅ All fixes are working perfectly in runtime scenarios');
      console.log('✅ WCAG system is fully functional and production-ready');
    } else {
      console.log('\n⚠️ SOME RUNTIME TESTS FAILED');
      console.log('Failed tests:');
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`❌ ${result.testName} (${result.category}): ${result.error}`);
      });
    }
    
    console.log('\n🚀 NEXT STEPS:');
    if (percentage >= 95) {
      console.log('1. Deploy to production - all systems validated');
      console.log('2. Monitor performance metrics in production');
      console.log('3. Run periodic validation tests');
    } else {
      console.log('1. Review and fix failed tests');
      console.log('2. Re-run validation after fixes');
      console.log('3. Ensure 95%+ success rate before production');
    }
  }
}

export { ComprehensiveRuntimeValidator, RuntimeTestResult };

// Run validation if called directly
if (require.main === module) {
  const validator = new ComprehensiveRuntimeValidator();

  console.log('🧪 Starting Comprehensive Runtime WCAG Validation...');
  console.log('This will test actual execution of all fixes with real browser scenarios\n');

  validator.runComprehensiveValidation()
    .then((results) => {
      validator.printComprehensiveSummary();

      const passed = results.filter(r => r.passed).length;
      const total = results.length;
      const successRate = (passed / total) * 100;

      if (successRate >= 95) {
        console.log('\n🎉 COMPREHENSIVE VALIDATION SUCCESSFUL!');
        process.exit(0);
      } else {
        console.log('\n❌ COMPREHENSIVE VALIDATION FAILED!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('❌ Comprehensive validation failed:', error);
      process.exit(1);
    });
}
