import { Knex } from 'knex';

/**
 * Enhanced WCAG Evidence Migration
 * ADDITIVE ONLY - No breaking changes to existing schema
 * Adds enhanced columns to wcag_automated_results table for improved reporting
 */

export async function up(knex: Knex): Promise<void> {
  console.log('🚀 Starting WCAG evidence enhancement migration...');
  
  try {
    // Check if table exists before attempting to alter it
    const tableExists = await knex.schema.hasTable('wcag_automated_results');
    if (!tableExists) {
      console.log('⚠️ wcag_automated_results table does not exist. Skipping migration.');
      return;
    }

    // Add enhanced columns to wcag_automated_results (all optional with defaults)
    await knex.schema.alterTable('wcag_automated_results', (table) => {
      console.log('📊 Adding element count tracking columns...');
      table.integer('total_element_count').nullable().defaultTo(null);
      table.integer('failed_element_count').nullable().defaultTo(null);
      
      console.log('🔍 Adding selector tracking columns...');
      table.jsonb('affected_selectors').nullable().defaultTo(null);
      
      console.log('🛠️ Adding fix guidance columns...');
      table.jsonb('fix_examples').nullable().defaultTo(null);
      
      console.log('📈 Adding metadata columns...');
      table.jsonb('evidence_metadata').nullable().defaultTo(null);
      
      console.log('⏱️ Adding performance tracking columns...');
      table.integer('scan_duration_ms').nullable().defaultTo(null);
      table.integer('elements_analyzed').nullable().defaultTo(null);
      
      console.log('🔧 Adding check metadata column...');
      table.jsonb('check_metadata').nullable().defaultTo(null);
    });

    console.log('📇 Creating performance indexes...');
    
    // Create indexes for performance (non-blocking)
    // Use IF NOT EXISTS to prevent errors on re-runs
    await knex.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_element_count 
      ON wcag_automated_results(failed_element_count) 
      WHERE failed_element_count IS NOT NULL AND failed_element_count > 0
    `);

    await knex.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_scan_duration 
      ON wcag_automated_results(scan_duration_ms) 
      WHERE scan_duration_ms IS NOT NULL
    `);

    await knex.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_elements_analyzed 
      ON wcag_automated_results(elements_analyzed) 
      WHERE elements_analyzed IS NOT NULL
    `);

    // Create composite index for enhanced reporting queries
    await knex.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_enhanced_reporting 
      ON wcag_automated_results(scan_id, rule_id, failed_element_count) 
      WHERE failed_element_count IS NOT NULL
    `);

    console.log('✅ WCAG evidence enhancement migration completed successfully');
    
    // Log migration summary
    const tableInfo = await knex('wcag_automated_results').count('* as count').first();
    console.log(`📊 Migration applied to ${tableInfo?.count || 0} existing records`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

export async function down(knex: Knex): Promise<void> {
  console.log('🔄 Rolling back WCAG evidence enhancement migration...');
  
  try {
    // Check if table exists before attempting to alter it
    const tableExists = await knex.schema.hasTable('wcag_automated_results');
    if (!tableExists) {
      console.log('⚠️ wcag_automated_results table does not exist. Nothing to rollback.');
      return;
    }

    console.log('🗑️ Removing performance indexes...');
    
    // Remove indexes first (order matters for dependencies)
    await knex.raw('DROP INDEX IF EXISTS idx_wcag_enhanced_reporting');
    await knex.raw('DROP INDEX IF EXISTS idx_wcag_elements_analyzed');
    await knex.raw('DROP INDEX IF EXISTS idx_wcag_scan_duration');
    await knex.raw('DROP INDEX IF EXISTS idx_wcag_element_count');

    console.log('📊 Removing enhanced columns...');
    
    // Remove enhanced columns (data will be lost)
    await knex.schema.alterTable('wcag_automated_results', (table) => {
      table.dropColumn('total_element_count');
      table.dropColumn('failed_element_count');
      table.dropColumn('affected_selectors');
      table.dropColumn('fix_examples');
      table.dropColumn('evidence_metadata');
      table.dropColumn('scan_duration_ms');
      table.dropColumn('elements_analyzed');
      table.dropColumn('check_metadata');
    });

    console.log('✅ Rollback completed successfully');
    console.log('⚠️ Enhanced evidence data has been permanently removed');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}
