import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',
        outline: 'text-foreground',
        success: 'border-transparent bg-green-600 text-green-50 shadow hover:bg-green-600/80',
        warning: 'border-transparent bg-yellow-400 text-yellow-900 shadow hover:bg-yellow-400/80',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

/**
 * Displays a badge or a component that looks like a badge.
 * @param {BadgeProps} props - The properties for the Badge component.
 * @param {string} [props.className] - Optional CSS class names.
 * @param {('default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | null | undefined)} [props.variant] - The variant of the badge.
 * @returns {JSX.Element} The rendered badge component.
 */
function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />;
}

export { Badge, badgeVariants };
