/**
 * Scoring System Migration Utility
 * Ensures backward compatibility and provides migration tools for enhanced scoring
 */

import { WcagCheckResult } from '../types';
import { SCORING_CONFIG, PENALTY_TIERS } from '../constants';
import logger from '../../../utils/logger';

export interface ScoringMigrationReport {
  totalChecks: number;
  improvedScores: number;
  unchangedScores: number;
  averageImprovement: number;
  maxImprovement: number;
  detailedChanges: ScoringChange[];
}

export interface ScoringChange {
  ruleId: string;
  oldScore: number;
  newScore: number;
  improvement: number;
  oldStatus: string;
  newStatus: string;
  reason: string;
}

export class ScoringMigration {
  /**
   * Migrate legacy scoring results to enhanced scoring
   */
  static migrateLegacyResults(legacyResults: WcagCheckResult[]): ScoringMigrationReport {
    const changes: ScoringChange[] = [];
    let totalImprovement = 0;
    let maxImprovement = 0;
    let improvedCount = 0;

    for (const result of legacyResults) {
      const change = this.migrateSingleResult(result);
      if (change) {
        changes.push(change);
        if (change.improvement > 0) {
          improvedCount++;
          totalImprovement += change.improvement;
          maxImprovement = Math.max(maxImprovement, change.improvement);
        }
      }
    }

    const averageImprovement = improvedCount > 0 ? totalImprovement / improvedCount : 0;

    return {
      totalChecks: legacyResults.length,
      improvedScores: improvedCount,
      unchangedScores: legacyResults.length - improvedCount,
      averageImprovement,
      maxImprovement,
      detailedChanges: changes
    };
  }

  /**
   * Migrate a single legacy result to enhanced scoring
   */
  private static migrateSingleResult(result: WcagCheckResult): ScoringChange | null {
    // Skip if already using enhanced scoring
    if (result.enhancedStatus || result.penaltyTier !== undefined) {
      return null;
    }

    const originalScore = result.originalScore || result.score;
    const maxScore = result.maxScore;
    
    if (maxScore === 0) {
      return null; // Can't migrate zero max score
    }

    const originalPercentage = (originalScore / maxScore) * 100;
    const currentScore = result.score;
    const currentStatus = result.status;

    // Apply enhanced scoring logic
    const newScoringResult = this.applyEnhancedScoring(originalScore, maxScore, result);
    
    if (newScoringResult.score !== currentScore) {
      return {
        ruleId: result.ruleId,
        oldScore: currentScore,
        newScore: newScoringResult.score,
        improvement: newScoringResult.score - currentScore,
        oldStatus: currentStatus,
        newStatus: newScoringResult.status,
        reason: newScoringResult.reason
      };
    }

    return null;
  }

  /**
   * Apply enhanced scoring to a legacy result
   */
  private static applyEnhancedScoring(
    originalScore: number, 
    maxScore: number, 
    result: WcagCheckResult
  ): { score: number; status: string; reason: string } {
    const percentage = (originalScore / maxScore) * 100;
    const threshold = result.thresholdApplied || SCORING_CONFIG.DEFAULT_PASS_THRESHOLD;

    // Find appropriate penalty tier
    const penaltyTier = PENALTY_TIERS.find(tier => percentage >= tier.min);
    
    if (!penaltyTier) {
      return {
        score: 0,
        status: 'failed',
        reason: 'Below minimum threshold'
      };
    }

    const newScore = Math.round(originalScore * penaltyTier.multiplier);
    let reason = '';

    if (percentage >= threshold) {
      reason = 'Passed threshold - full credit';
    } else if (penaltyTier.multiplier > 0) {
      reason = `Partial credit (${(penaltyTier.multiplier * 100).toFixed(0)}% of score)`;
    } else {
      reason = 'Below minimum threshold - no credit';
    }

    return {
      score: newScore,
      status: penaltyTier.status,
      reason
    };
  }

  /**
   * Generate migration report for logging
   */
  static generateMigrationReport(report: ScoringMigrationReport): string {
    const lines = [
      '📊 ENHANCED SCORING MIGRATION REPORT',
      '=====================================',
      '',
      `📈 Total Checks Analyzed: ${report.totalChecks}`,
      `✅ Improved Scores: ${report.improvedScores}`,
      `➡️ Unchanged Scores: ${report.unchangedScores}`,
      `📊 Average Improvement: +${report.averageImprovement.toFixed(1)} points`,
      `🚀 Maximum Improvement: +${report.maxImprovement} points`,
      '',
      '🔍 DETAILED CHANGES:',
      '===================='
    ];

    // Sort changes by improvement (highest first)
    const sortedChanges = report.detailedChanges
      .sort((a, b) => b.improvement - a.improvement)
      .slice(0, 10); // Show top 10 improvements

    for (const change of sortedChanges) {
      lines.push(
        `${change.ruleId}: ${change.oldScore} → ${change.newScore} (+${change.improvement}) | ${change.oldStatus} → ${change.newStatus}`
      );
      lines.push(`  Reason: ${change.reason}`);
    }

    if (report.detailedChanges.length > 10) {
      lines.push(`... and ${report.detailedChanges.length - 10} more improvements`);
    }

    return lines.join('\n');
  }

  /**
   * Validate enhanced scoring configuration
   */
  static validateScoringConfiguration(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Validate penalty tiers
    if (PENALTY_TIERS.length === 0) {
      issues.push('No penalty tiers defined');
    }

    // Check penalty tiers are in descending order
    for (let i = 0; i < PENALTY_TIERS.length - 1; i++) {
      if (PENALTY_TIERS[i].min <= PENALTY_TIERS[i + 1].min) {
        issues.push(`Penalty tier ${i} min (${PENALTY_TIERS[i].min}) should be greater than tier ${i + 1} min (${PENALTY_TIERS[i + 1].min})`);
      }
    }

    // Check multipliers are valid
    for (const tier of PENALTY_TIERS) {
      if (tier.multiplier < 0 || tier.multiplier > 1) {
        issues.push(`Penalty tier multiplier ${tier.multiplier} should be between 0 and 1`);
      }
    }

    // Check highest tier gives full credit
    if (PENALTY_TIERS[0].multiplier !== 1.0) {
      issues.push('Highest penalty tier should give full credit (multiplier = 1.0)');
    }

    // Check lowest tier gives no credit
    const lowestTier = PENALTY_TIERS[PENALTY_TIERS.length - 1];
    if (lowestTier.multiplier !== 0.0) {
      issues.push('Lowest penalty tier should give no credit (multiplier = 0.0)');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Calculate expected score improvement for a scan
   */
  static calculateExpectedImprovement(currentResults: WcagCheckResult[]): {
    currentScore: number;
    projectedScore: number;
    improvement: number;
    improvementPercentage: number;
  } {
    let currentTotal = 0;
    let currentMax = 0;
    let projectedTotal = 0;
    let projectedMax = 0;

    for (const result of currentResults) {
      currentTotal += result.score;
      currentMax += result.maxScore;

      // Calculate what the score would be with enhanced scoring
      const originalScore = result.originalScore || result.score;
      const enhanced = this.applyEnhancedScoring(originalScore, result.maxScore, result);
      
      projectedTotal += enhanced.score;
      projectedMax += result.maxScore;
    }

    const currentScore = currentMax > 0 ? (currentTotal / currentMax) * 100 : 0;
    const projectedScore = projectedMax > 0 ? (projectedTotal / projectedMax) * 100 : 0;
    const improvement = projectedScore - currentScore;
    const improvementPercentage = currentScore > 0 ? (improvement / currentScore) * 100 : 0;

    return {
      currentScore,
      projectedScore,
      improvement,
      improvementPercentage
    };
  }

  /**
   * Log migration results
   */
  static logMigrationResults(report: ScoringMigrationReport): void {
    const reportText = this.generateMigrationReport(report);
    logger.info(reportText);

    if (report.improvedScores > 0) {
      logger.info(`🎉 Enhanced scoring improved ${report.improvedScores} checks with average improvement of +${report.averageImprovement.toFixed(1)} points`);
    } else {
      logger.info('ℹ️ No score improvements found - results already optimized');
    }
  }
}

/**
 * Utility function to quickly test enhanced scoring on sample data
 */
export function testEnhancedScoring(): void {
  const sampleResults: WcagCheckResult[] = [
    {
      ruleId: 'WCAG-001',
      ruleName: 'Test Rule 1',
      category: 'perceivable',
      wcagVersion: '2.1',
      successCriterion: '1.1.1',
      level: 'A',
      status: 'failed',
      score: 0,
      maxScore: 100,
      weight: 1,
      automated: true,
      evidence: [],
      recommendations: [],
      executionTime: 100,
      originalScore: 70 // Would get partial credit with enhanced scoring
    },
    {
      ruleId: 'WCAG-002',
      ruleName: 'Test Rule 2',
      category: 'operable',
      wcagVersion: '2.1',
      successCriterion: '2.1.1',
      level: 'AA',
      status: 'failed',
      score: 0,
      maxScore: 100,
      weight: 1,
      automated: true,
      evidence: [],
      recommendations: [],
      executionTime: 100,
      originalScore: 35 // Would get some partial credit
    }
  ];

  const report = ScoringMigration.migrateLegacyResults(sampleResults);
  ScoringMigration.logMigrationResults(report);
}
