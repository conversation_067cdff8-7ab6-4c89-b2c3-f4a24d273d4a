/**
 * WCAG-035: Multiple Ways Check
 * Success Criterion: 2.4.5 Multiple Ways (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

interface NavigationMethod {
  type: 'sitemap' | 'search' | 'navigation' | 'breadcrumbs' | 'table-of-contents' | 'site-index';
  found: boolean;
  selector?: string;
  description: string;
  quality: 'poor' | 'good' | 'excellent';
}

export interface MultipleWaysConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableHeadlessCMSDetection?: boolean;
  enableNavigationPatternDetection?: boolean;
  enableAdvancedNavigationAnalysis?: boolean;
}

export class MultipleWaysCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private advancedLayoutAnalyzer = AdvancedLayoutAnalyzer.getAdvancedInstance();

  async performCheck(config: MultipleWaysConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: MultipleWaysConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableHeadlessCMSDetection: true,
      enableNavigationPatternDetection: true,
      enableAdvancedNavigationAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-035',
      'Multiple Ways',
      'operable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeMultipleWaysCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with navigation method analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-035',
        ruleName: 'Multiple Ways',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'navigation-method-analysis',
          navigationAnalysis: true,
          methodDetection: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 20,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'multiple-ways-analysis',
        confidence: 0.7,
        additionalData: {
          checkType: 'navigation-accessibility',
          automationLevel: 'medium-high',
        },
      },
    };
  }

  private async executeMultipleWaysCheck(
    page: Page,
    _config: MultipleWaysConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Enhanced multiple ways analysis using AdvancedLayoutAnalyzer
    try {
      const navigationAnalysis = await this.advancedLayoutAnalyzer.analyzeResponsiveLayout(page);

      // Add enhanced evidence from advanced navigation analysis
      evidence.push({
        type: 'info',
        description: 'Advanced navigation structure analysis for multiple ways validation',
        element: 'navigation-elements',
        value: JSON.stringify({
          overallScore: navigationAnalysis.overallScore,
          criticalIssues: navigationAnalysis.criticalIssues,
          recommendations: navigationAnalysis.recommendations,
          performanceMetrics: navigationAnalysis.performanceMetrics,
        }),
        severity: navigationAnalysis.criticalIssues.length > 0 ? 'error' : 'info',
      });

      // Collect issues and recommendations from advanced analysis
      if (navigationAnalysis.criticalIssues.length > 0) {
        issues.push(...navigationAnalysis.criticalIssues);
        recommendations.push(...navigationAnalysis.recommendations);
      }
    } catch (error) {
      console.warn('Advanced navigation analysis failed, falling back to basic analysis:', error);
    }

    // Analyze multiple ways to locate web pages - Basic fallback analysis
    const multipleWaysAnalysis = await page.evaluate(() => {
      const navigationIndicators = {
        sitemap: ['sitemap', 'site map', 'site-map', 'site_map'],
        search: ['search', 'find', 'query'],
        navigation: ['nav', 'menu', 'navigation'],
        breadcrumbs: ['breadcrumb', 'breadcrumbs', 'you are here', 'current location'],
        tableOfContents: ['table of contents', 'contents', 'toc', 'index'],
        siteIndex: ['site index', 'index', 'directory', 'all pages'],
      };

      const processIndicators = [
        'checkout',
        'payment',
        'step',
        'wizard',
        'form submission',
        'registration',
        'login',
        'signup',
        'cart',
        'order',
      ];

      // Check if this is a process step (exempt from requirement)
      const pageText = document.body.textContent?.toLowerCase() || '';
      const pageTitle = document.title.toLowerCase();
      const url = window.location.href.toLowerCase();

      const isProcessStep = processIndicators.some(
        (indicator) =>
          pageText.includes(indicator) || pageTitle.includes(indicator) || url.includes(indicator),
      );

      // Initialize navigation methods to check
      const navigationMethods: NavigationMethod[] = [
        {
          type: 'sitemap',
          found: false,
          description: 'Sitemap or site map page',
          quality: 'poor',
        },
        {
          type: 'search',
          found: false,
          description: 'Search functionality',
          quality: 'poor',
        },
        {
          type: 'navigation',
          found: false,
          description: 'Navigation menu or links',
          quality: 'poor',
        },
        {
          type: 'breadcrumbs',
          found: false,
          description: 'Breadcrumb navigation',
          quality: 'poor',
        },
        {
          type: 'table-of-contents',
          found: false,
          description: 'Table of contents',
          quality: 'poor',
        },
        {
          type: 'site-index',
          found: false,
          description: 'Site index or directory',
          quality: 'poor',
        },
      ];

      // Check for sitemap
      const sitemapLinks = Array.from(document.querySelectorAll('a')).filter((link) => {
        const text = link.textContent?.toLowerCase() || '';
        const href = link.getAttribute('href')?.toLowerCase() || '';
        return navigationIndicators.sitemap.some(
          (indicator) => text.includes(indicator) || href.includes(indicator),
        );
      });

      if (sitemapLinks.length > 0) {
        navigationMethods[0].found = true;
        navigationMethods[0].selector = 'a[href*="sitemap"], a:contains("sitemap")';
        navigationMethods[0].quality = sitemapLinks.length > 1 ? 'excellent' : 'good';
      }

      // Check for search functionality
      const searchElements = document.querySelectorAll(
        'input[type="search"], input[name*="search"], input[placeholder*="search"], [role="search"]',
      );
      const searchForms = document.querySelectorAll('form[action*="search"]');

      if (searchElements.length > 0 || searchForms.length > 0) {
        navigationMethods[1].found = true;
        navigationMethods[1].selector = 'input[type="search"], [role="search"]';
        navigationMethods[1].quality = searchElements.length > 0 ? 'excellent' : 'good';
      }

      // Check for navigation menus
      const navElements = document.querySelectorAll(
        'nav, [role="navigation"], .navigation, .menu, .nav',
      );
      const navLinks = Array.from(document.querySelectorAll('a')).filter((link) => {
        const parent = link.closest('nav, [role="navigation"], .navigation, .menu');
        return parent !== null;
      });

      if (navElements.length > 0 || navLinks.length > 3) {
        navigationMethods[2].found = true;
        navigationMethods[2].selector = 'nav, [role="navigation"]';
        navigationMethods[2].quality =
          navLinks.length > 10 ? 'excellent' : navLinks.length > 5 ? 'good' : 'poor';
      }

      // Check for breadcrumbs
      const breadcrumbElements = document.querySelectorAll(
        '[aria-label*="breadcrumb"], .breadcrumb, .breadcrumbs',
      );
      const breadcrumbText = Array.from(document.querySelectorAll('*')).filter((el) => {
        const text = el.textContent?.toLowerCase() || '';
        return navigationIndicators.breadcrumbs.some((indicator) => text.includes(indicator));
      });

      if (breadcrumbElements.length > 0 || breadcrumbText.length > 0) {
        navigationMethods[3].found = true;
        navigationMethods[3].selector = '[aria-label*="breadcrumb"], .breadcrumb';
        navigationMethods[3].quality = breadcrumbElements.length > 0 ? 'excellent' : 'good';
      }

      // Check for table of contents
      const tocElements = document.querySelectorAll('.toc, .table-of-contents, #toc');
      const tocText = Array.from(document.querySelectorAll('*')).filter((el) => {
        const text = el.textContent?.toLowerCase() || '';
        return navigationIndicators.tableOfContents.some((indicator) => text.includes(indicator));
      });

      if (tocElements.length > 0 || tocText.length > 0) {
        navigationMethods[4].found = true;
        navigationMethods[4].selector = '.toc, .table-of-contents';
        navigationMethods[4].quality = tocElements.length > 0 ? 'excellent' : 'good';
      }

      // Check for site index
      const indexLinks = Array.from(document.querySelectorAll('a')).filter((link) => {
        const text = link.textContent?.toLowerCase() || '';
        const href = link.getAttribute('href')?.toLowerCase() || '';
        return navigationIndicators.siteIndex.some(
          (indicator) => text.includes(indicator) || href.includes(indicator),
        );
      });

      if (indexLinks.length > 0) {
        navigationMethods[5].found = true;
        navigationMethods[5].selector = 'a[href*="index"], a:contains("index")';
        navigationMethods[5].quality = indexLinks.length > 1 ? 'excellent' : 'good';
      }

      const foundMethods = navigationMethods.filter((method) => method.found);
      const missingMethods = navigationMethods.filter((method) => !method.found);
      const methodsFound = foundMethods.length;
      const hasMinimumMethods = methodsFound >= 2; // WCAG requires at least 2 ways

      return {
        navigationMethods,
        foundMethods,
        missingMethods,
        totalMethods: navigationMethods.length,
        methodsFound,
        hasMinimumMethods,
        isWebPage: true, // Assume it's a web page unless proven otherwise
        isProcessStep,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;

    // If this is a process step, it's exempt from the requirement
    if (multipleWaysAnalysis.isProcessStep) {
      evidence.push({
        type: 'info',
        description: 'Process step page exempt from multiple ways requirement',
        value: 'Page appears to be part of a process or sequence, exempt from WCAG 2.4.5',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            isProcessStep: true,
            exemptFromRequirement: true,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Continue ensuring process steps are clearly marked and accessible'],
      };
    }

    // Check if minimum methods are available
    if (!multipleWaysAnalysis.hasMinimumMethods) {
      const missingMethodsCount = 2 - multipleWaysAnalysis.methodsFound;
      score = Math.max(0, Math.round(100 * (multipleWaysAnalysis.methodsFound / 2)));

      issues.push(
        `Only ${multipleWaysAnalysis.methodsFound} navigation method(s) found, need at least 2`,
      );

      evidence.push({
        type: 'error',
        description: 'Insufficient navigation methods available',
        value: `Found ${multipleWaysAnalysis.methodsFound} method(s), need at least 2 ways to locate pages`,
        selector: 'body',
        elementCount: missingMethodsCount,
        affectedSelectors: ['nav', 'form', 'a'],
        severity: 'error',
        fixExample: {
          before: '<nav><a href="/">Home</a></nav>',
          after: `<nav>
  <a href="/">Home</a>
  <a href="/sitemap">Site Map</a>
</nav>
<form role="search">
  <input type="search" placeholder="Search site">
  <button type="submit">Search</button>
</form>
<nav aria-label="Breadcrumb">
  <ol>
    <li><a href="/">Home</a></li>
    <li><a href="/category">Category</a></li>
    <li aria-current="page">Current Page</li>
  </ol>
</nav>`,
          description: 'Provide multiple ways to navigate and locate pages',
          codeExample: `
<!-- Method 1: Site Navigation -->
<nav role="navigation" aria-label="Main navigation">
  <ul>
    <li><a href="/">Home</a></li>
    <li><a href="/products">Products</a></li>
    <li><a href="/about">About</a></li>
    <li><a href="/contact">Contact</a></li>
  </ul>
</nav>

<!-- Method 2: Search Functionality -->
<form role="search" action="/search">
  <label for="search">Search site</label>
  <input type="search" id="search" name="q" placeholder="Enter search terms">
  <button type="submit">Search</button>
</form>

<!-- Method 3: Breadcrumb Navigation -->
<nav aria-label="Breadcrumb">
  <ol>
    <li><a href="/">Home</a></li>
    <li><a href="/category">Category</a></li>
    <li aria-current="page">Current Page</li>
  </ol>
</nav>

<!-- Method 4: Site Map Link -->
<footer>
  <a href="/sitemap">Site Map</a>
  <a href="/site-index">Site Index</a>
</footer>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/multiple-ways.html',
            'https://webaim.org/techniques/navigation/',
            'https://www.w3.org/WAI/tutorials/menus/',
          ],
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: multipleWaysAnalysis.totalMethods,
          checkSpecificData: {
            methodsFound: multipleWaysAnalysis.methodsFound,
            methodsRequired: 2,
            missingMethodsCount,
            foundMethods: multipleWaysAnalysis.foundMethods.map((m) => m.type).join(', '),
            missingMethods: multipleWaysAnalysis.missingMethods.map((m) => m.type).join(', '),
          },
        },
      });
    }

    // Add evidence for found methods
    if (multipleWaysAnalysis.foundMethods.length > 0) {
      multipleWaysAnalysis.foundMethods.forEach((method) => {
        evidence.push({
          type: 'info',
          description: `Navigation method found: ${method.type}`,
          value: `${method.description} - Quality: ${method.quality}`,
          selector: method.selector || 'body',
          elementCount: 1,
          affectedSelectors: method.selector ? [method.selector] : [],
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              methodType: method.type,
              methodQuality: method.quality,
              methodDescription: method.description,
            },
          },
        });
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Multiple ways navigation analysis summary',
      value: `${multipleWaysAnalysis.methodsFound} navigation methods found: ${multipleWaysAnalysis.foundMethods.map((m) => m.type).join(', ')}`,
      selector: 'nav, form, a',
      elementCount: multipleWaysAnalysis.methodsFound,
      affectedSelectors: ['nav', 'form', 'a'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: multipleWaysAnalysis.totalMethods,
        checkSpecificData: {
          totalMethods: multipleWaysAnalysis.totalMethods,
          methodsFound: multipleWaysAnalysis.methodsFound,
          hasMinimumMethods: multipleWaysAnalysis.hasMinimumMethods,
          isProcessStep: multipleWaysAnalysis.isProcessStep,
          foundMethodTypes: multipleWaysAnalysis.foundMethods.map((m) => m.type).join(', '),
          methodQualities: multipleWaysAnalysis.foundMethods.map((m) => m.quality).join(', '),
        },
      },
    });

    // Generate recommendations
    if (!multipleWaysAnalysis.hasMinimumMethods) {
      recommendations.push('Implement at least 2 different ways to locate and navigate to pages');
      recommendations.push('Consider adding a search function for large sites');
      recommendations.push('Provide a sitemap or site index page');
      recommendations.push('Use breadcrumb navigation for hierarchical content');
    } else {
      recommendations.push('Continue providing multiple navigation methods');
      recommendations.push('Consider improving the quality of existing navigation methods');
    }

    if (multipleWaysAnalysis.foundMethods.some((m) => m.quality === 'poor')) {
      recommendations.push('Improve the quality and usability of existing navigation methods');
    }

    if (!multipleWaysAnalysis.foundMethods.some((m) => m.type === 'search')) {
      recommendations.push('Consider adding search functionality for better user experience');
    }

    if (!multipleWaysAnalysis.foundMethods.some((m) => m.type === 'sitemap')) {
      recommendations.push('Consider providing a sitemap for comprehensive site overview');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
