/**
 * WCAG-033: Audio-only and Video-only Check
 * Success Criterion: 1.2.1 Audio-only and Video-only (Prerecorded) (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

import MultimediaAccessibilityTester, {
  MultimediaAccessibilityReport,
} from '../utils/multimedia-accessibility-tester';

export interface AudioVideoOnlyConfig extends EnhancedCheckConfig {
  enableMultimediaAccessibilityTesting?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAdvancedMediaAnalysis?: boolean;
}

export class AudioVideoOnlyCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();

  private multimediaAccessibilityTester = MultimediaAccessibilityTester.getInstance();

  async performCheck(config: AudioVideoOnlyConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: AudioVideoOnlyConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 3000,
      },
      enableMultimediaAccessibilityTesting: true,
      enableContentQualityAnalysis: true,
      enableAdvancedMediaAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-033',
      'Audio-only and Video-only',
      'perceivable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeAudioVideoOnlyCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with audio/video-only analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-033',
        ruleName: 'Audio-only and Video-only',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'audio-video-only-analysis',
          mediaElementAnalysis: true,
          alternativeContentDetection: true,
          decorativeContentDetection: true,
          multimediaAccessibilityTesting: enhancedConfig.enableMultimediaAccessibilityTesting,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'media-alternative-analysis',
        confidence: 0.65,
        additionalData: {
          checkType: 'media-accessibility',
          automationLevel: 'medium',
        },
      },
    };
  }

  private async executeAudioVideoOnlyCheck(
    page: Page,
    _config: AudioVideoOnlyConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced multimedia accessibility testing
    const multimediaReport =
      await this.multimediaAccessibilityTester.testMultimediaAccessibility(page);

    // Analyze audio-only and video-only elements using MultimediaAccessibilityTester results
    const audioOnlyAnalysis = await this.analyzeAudioOnlyElements(page, multimediaReport);
    const videoOnlyAnalysis = await this.analyzeVideoOnlyElements(page, multimediaReport);

    // Combine analyses
    const allAnalyses = [audioOnlyAnalysis, videoOnlyAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze audio-only elements using MultimediaAccessibilityTester
   */
  private async analyzeAudioOnlyElements(
    _page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const audioElements = multimediaReport.audioElements;
    const totalChecks = audioElements.length;
    let passedChecks = 0;

    audioElements.forEach((audio, index) => {
      // Check for transcript availability
      if (audio.transcript.hasTranscript && audio.transcript.isLinked) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Audio-only element ${index + 1} has accessible transcript`,
          value: `${audio.selector} - transcript: ${audio.transcript.location}`,
          selector: audio.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Audio-only element ${index + 1} lacks accessible transcript`);
        evidence.push({
          type: 'code',
          description: `Audio-only element ${index + 1} requires transcript`,
          value: `${audio.selector} - hasTranscript: ${audio.transcript.hasTranscript}`,
          selector: audio.selector,
          severity: 'error',
        });
        recommendations.push(`Add accessible transcript for audio-only element ${index + 1}`);
      }

      // Add issues from MultimediaAccessibilityTester
      if (audio.issues.length > 0) {
        audio.issues.forEach((issue) => {
          issues.push(`Audio ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (audio.recommendations.length > 0) {
        audio.recommendations.forEach((recommendation) => {
          recommendations.push(`Audio ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze video-only elements using MultimediaAccessibilityTester
   */
  private async analyzeVideoOnlyElements(
    _page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Filter video elements that are video-only (no audio)
    const videoOnlyElements = multimediaReport.videoElements.filter((video) => {
      // Check if video has no audio track or is muted
      return !video.audioDescription.hasAudioDescription;
    });

    const totalChecks = videoOnlyElements.length;
    let passedChecks = 0;

    videoOnlyElements.forEach((video, index) => {
      // Check for audio description or transcript
      if (
        video.audioDescription.hasAudioDescription ||
        video.transcript.hasTranscript ||
        video.captions.hasTextTracks
      ) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Video-only element ${index + 1} has accessible alternative`,
          value: `${video.selector} - audioDescription: ${video.audioDescription.hasAudioDescription}, transcript: ${video.transcript.hasTranscript}`,
          selector: video.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Video-only element ${index + 1} lacks accessible alternative`);
        evidence.push({
          type: 'code',
          description: `Video-only element ${index + 1} requires audio description or transcript`,
          value: `${video.selector} - no accessible alternatives found`,
          selector: video.selector,
          severity: 'error',
        });
        recommendations.push(
          `Add audio description or transcript for video-only element ${index + 1}`,
        );
      }

      // Add issues from MultimediaAccessibilityTester
      if (video.issues.length > 0) {
        video.issues.forEach((issue) => {
          issues.push(`Video ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (video.recommendations.length > 0) {
        video.recommendations.forEach((recommendation) => {
          recommendations.push(`Video ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }
}
