/**
 * Identify WCAG Checks Using Basic Template
 * Scans all check files to identify which ones need utility integration
 */

import fs from 'fs';
import path from 'path';

interface CheckAnalysis {
  fileName: string;
  ruleId: string;
  usesEnhancedTemplate: boolean;
  usesBasicTemplate: boolean;
  hasUtilityIntegration: boolean;
  hasSmartCache: boolean;
  templateType: 'enhanced' | 'basic' | 'mixed' | 'unknown';
}

function analyzeCheckFile(filePath: string, fileName: string): CheckAnalysis {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Extract rule ID from content
  const ruleIdMatch = content.match(/WCAG-(\d{3})/);
  const ruleId = ruleIdMatch ? `WCAG-${ruleIdMatch[1]}` : 'Unknown';
  
  // Check for template usage
  const usesEnhancedTemplate = content.includes('EnhancedCheckTemplate') || 
                               content.includes('enhancedTemplate') ||
                               content.includes('executeEnhancedCheck');
  
  const usesBasicTemplate = content.includes('CheckTemplate') && 
                           !content.includes('EnhancedCheckTemplate');
  
  // Check for utility integration
  const hasUtilityIntegration = content.includes('enableUtilityIntegration') ||
                               content.includes('utilityConfig') ||
                               content.includes('UtilityIntegrationManager');
  
  // Check for SmartCache usage
  const hasSmartCache = content.includes('SmartCache') ||
                       content.includes('smartCache');
  
  // Determine template type
  let templateType: 'enhanced' | 'basic' | 'mixed' | 'unknown';
  if (usesEnhancedTemplate && !usesBasicTemplate) {
    templateType = 'enhanced';
  } else if (usesBasicTemplate && !usesEnhancedTemplate) {
    templateType = 'basic';
  } else if (usesEnhancedTemplate && usesBasicTemplate) {
    templateType = 'mixed';
  } else {
    templateType = 'unknown';
  }
  
  return {
    fileName,
    ruleId,
    usesEnhancedTemplate,
    usesBasicTemplate,
    hasUtilityIntegration,
    hasSmartCache,
    templateType,
  };
}

function identifyBasicTemplateChecks(): void {
  console.log('🔍 Identifying WCAG Checks Using Basic Template');
  console.log('=' .repeat(60));
  
  const checksDir = path.join(__dirname, '../src/compliance/wcag/checks');
  const checkFiles = fs.readdirSync(checksDir)
    .filter(file => file.endsWith('.ts') && file !== 'index.ts')
    .sort();
  
  const analyses: CheckAnalysis[] = [];
  
  console.log(`\n📁 Analyzing ${checkFiles.length} check files...\n`);
  
  for (const fileName of checkFiles) {
    const filePath = path.join(checksDir, fileName);
    const analysis = analyzeCheckFile(filePath, fileName);
    analyses.push(analysis);
  }
  
  // Categorize results
  const enhancedChecks = analyses.filter(a => a.templateType === 'enhanced');
  const basicChecks = analyses.filter(a => a.templateType === 'basic');
  const mixedChecks = analyses.filter(a => a.templateType === 'mixed');
  const unknownChecks = analyses.filter(a => a.templateType === 'unknown');
  
  // Display results
  console.log('📊 ANALYSIS RESULTS:');
  console.log(`  Total Checks: ${analyses.length}`);
  console.log(`  Enhanced Template: ${enhancedChecks.length} (${((enhancedChecks.length / analyses.length) * 100).toFixed(1)}%)`);
  console.log(`  Basic Template: ${basicChecks.length} (${((basicChecks.length / analyses.length) * 100).toFixed(1)}%)`);
  console.log(`  Mixed Template: ${mixedChecks.length} (${((mixedChecks.length / analyses.length) * 100).toFixed(1)}%)`);
  console.log(`  Unknown Template: ${unknownChecks.length} (${((unknownChecks.length / analyses.length) * 100).toFixed(1)}%)`);
  
  // Show checks that need migration
  if (basicChecks.length > 0) {
    console.log('\n❌ CHECKS NEEDING MIGRATION TO ENHANCED TEMPLATE:');
    basicChecks.forEach((check, index) => {
      console.log(`  ${index + 1}. ${check.ruleId} - ${check.fileName}`);
    });
  }
  
  if (mixedChecks.length > 0) {
    console.log('\n⚠️ CHECKS WITH MIXED TEMPLATE USAGE (NEED CLEANUP):');
    mixedChecks.forEach((check, index) => {
      console.log(`  ${index + 1}. ${check.ruleId} - ${check.fileName}`);
    });
  }
  
  if (unknownChecks.length > 0) {
    console.log('\n❓ CHECKS WITH UNKNOWN TEMPLATE USAGE:');
    unknownChecks.forEach((check, index) => {
      console.log(`  ${index + 1}. ${check.ruleId} - ${check.fileName}`);
    });
  }
  
  // Show utility integration status
  const withUtilities = analyses.filter(a => a.hasUtilityIntegration);
  const withoutUtilities = analyses.filter(a => !a.hasUtilityIntegration);
  
  console.log('\n🔧 UTILITY INTEGRATION STATUS:');
  console.log(`  With Utility Integration: ${withUtilities.length} (${((withUtilities.length / analyses.length) * 100).toFixed(1)}%)`);
  console.log(`  Without Utility Integration: ${withoutUtilities.length} (${((withoutUtilities.length / analyses.length) * 100).toFixed(1)}%)`);
  
  if (withoutUtilities.length > 0) {
    console.log('\n🔧 CHECKS NEEDING UTILITY INTEGRATION:');
    withoutUtilities.forEach((check, index) => {
      console.log(`  ${index + 1}. ${check.ruleId} - ${check.fileName} (${check.templateType})`);
    });
  }
  
  // Show SmartCache usage
  const withCache = analyses.filter(a => a.hasSmartCache);
  const withoutCache = analyses.filter(a => !a.hasSmartCache);
  
  console.log('\n💾 SMARTCACHE INTEGRATION STATUS:');
  console.log(`  With SmartCache: ${withCache.length} (${((withCache.length / analyses.length) * 100).toFixed(1)}%)`);
  console.log(`  Without SmartCache: ${withoutCache.length} (${((withoutCache.length / analyses.length) * 100).toFixed(1)}%)`);
  
  // Priority migration list
  const priorityMigrations = basicChecks.concat(mixedChecks).concat(withoutUtilities);
  const uniquePriorityMigrations = priorityMigrations.filter((check, index, arr) => 
    arr.findIndex(c => c.fileName === check.fileName) === index
  );
  
  console.log('\n🎯 PRIORITY MIGRATION LIST:');
  console.log(`  Total checks needing migration: ${uniquePriorityMigrations.length}`);
  
  if (uniquePriorityMigrations.length > 0) {
    console.log('\n📋 MIGRATION CHECKLIST:');
    uniquePriorityMigrations.forEach((check, index) => {
      const issues = [];
      if (check.templateType === 'basic') issues.push('Basic Template');
      if (check.templateType === 'mixed') issues.push('Mixed Template');
      if (!check.hasUtilityIntegration) issues.push('No Utilities');
      if (!check.hasSmartCache) issues.push('No Cache');
      
      console.log(`  [ ] ${index + 1}. ${check.ruleId} - ${check.fileName}`);
      console.log(`      Issues: ${issues.join(', ')}`);
    });
  }
  
  console.log('\n✅ Analysis complete!');
}

// Run the analysis
identifyBasicTemplateChecks();
