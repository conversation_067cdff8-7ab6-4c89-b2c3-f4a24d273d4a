/**
 * Performance Automation Controller
 * Central controller for automated performance optimization, coordinating all monitoring and optimization systems
 */

import { EventEmitter } from 'events';
import RealTimeMonitoringDashboard from './real-time-monitoring-dashboard';
import PredictivePerformanceAnalytics from './predictive-performance-analytics';
import AutomatedOptimizationEngine from './automated-optimization-engine';
import { PerformanceIntegrationBridge } from './performance-integration-bridge';
import { UtilityIntegrationManager } from './utility-integration-manager';
import DashboardWebSocketService from './dashboard-websocket-service';
import WCAGPerformanceMonitor from './performance-monitor';
import EnhancedPerformanceMonitor from './enhanced-performance-monitor';
import SmartCache from './smart-cache';
import logger from '../../../utils/logger';

export interface AutomationConfig {
  enableFullAutomation: boolean;
  enableDashboardMonitoring: boolean;
  enablePredictiveAnalytics: boolean;
  enableAutomatedOptimization: boolean;
  enableWebSocketUpdates: boolean;

  monitoring: {
    dashboardUpdateInterval: number;
    analyticsInterval: number;
    optimizationCheckInterval: number;
  };

  thresholds: {
    performanceScore: number;
    cacheHitRate: number;
    memoryUsage: number;
    errorRate: number;
    utilityOverhead: number;
  };

  automation: {
    requireApprovalForCritical: boolean;
    maxConcurrentOptimizations: number;
    rollbackOnFailure: boolean;
    monitoringPeriod: number;
  };
}

export interface SystemStatus {
  timestamp: Date;
  overall: 'excellent' | 'good' | 'warning' | 'critical';

  components: {
    dashboard: { status: 'running' | 'stopped' | 'error'; uptime: number };
    analytics: { status: 'running' | 'stopped' | 'error'; predictions: number };
    optimization: { status: 'running' | 'stopped' | 'error'; activeOptimizations: number };
    websocket: { status: 'running' | 'stopped' | 'error'; connectedClients: number };
    monitoring: { status: 'running' | 'stopped' | 'error'; activeScans: number };
  };

  performance: {
    averageScore: number;
    cacheEfficiency: number;
    memoryUsage: number;
    systemLoad: number;
  };

  automation: {
    totalOptimizations: number;
    successfulOptimizations: number;
    failedOptimizations: number;
    averageImprovementPercentage: number;
  };
}

/**
 * Performance Automation Controller Class
 */
export class PerformanceAutomationController extends EventEmitter {
  private static instance: PerformanceAutomationController;

  // Core components
  private dashboard: RealTimeMonitoringDashboard;
  private analytics: PredictivePerformanceAnalytics;
  private optimization: AutomatedOptimizationEngine;
  private performanceBridge: PerformanceIntegrationBridge;
  private utilityManager: UtilityIntegrationManager;
  private websocketService: DashboardWebSocketService;
  private performanceMonitor: WCAGPerformanceMonitor;
  private enhancedMonitor: EnhancedPerformanceMonitor;
  private smartCache: SmartCache;

  private config: AutomationConfig;
  private isRunning: boolean = false;
  private startTime: Date | null = null;
  private systemStatus: SystemStatus | null = null;

  // Timers
  private statusUpdateTimer: NodeJS.Timeout | null = null;
  private healthCheckTimer: NodeJS.Timeout | null = null;

  private constructor() {
    super();

    // Initialize all components
    this.dashboard = RealTimeMonitoringDashboard.getInstance();
    this.analytics = PredictivePerformanceAnalytics.getInstance();
    this.optimization = AutomatedOptimizationEngine.getInstance();
    this.performanceBridge = PerformanceIntegrationBridge.getInstance();
    this.utilityManager = UtilityIntegrationManager.getInstance();
    this.websocketService = DashboardWebSocketService.getInstance();
    this.performanceMonitor = WCAGPerformanceMonitor.getInstance();
    this.enhancedMonitor = EnhancedPerformanceMonitor.getInstance();
    this.smartCache = SmartCache.getInstance();

    this.config = {
      enableFullAutomation: false, // Start conservatively
      enableDashboardMonitoring: true,
      enablePredictiveAnalytics: true,
      enableAutomatedOptimization: false,
      enableWebSocketUpdates: true,

      monitoring: {
        dashboardUpdateInterval: 5000, // 5 seconds
        analyticsInterval: 60000, // 1 minute
        optimizationCheckInterval: 120000, // 2 minutes
      },

      thresholds: {
        performanceScore: 75,
        cacheHitRate: 70,
        memoryUsage: 2500, // MB
        errorRate: 5, // %
        utilityOverhead: 20, // %
      },

      automation: {
        requireApprovalForCritical: true,
        maxConcurrentOptimizations: 3,
        rollbackOnFailure: true,
        monitoringPeriod: 30, // minutes
      },
    };

    this.setupComponentListeners();

    logger.info('🎛️ Performance Automation Controller initialized');
  }

  static getInstance(): PerformanceAutomationController {
    if (!PerformanceAutomationController.instance) {
      PerformanceAutomationController.instance = new PerformanceAutomationController();
    }
    return PerformanceAutomationController.instance;
  }

  /**
   * Start full automation system
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('🎛️ Automation controller already running');
      return;
    }

    try {
      this.isRunning = true;
      this.startTime = new Date();

      logger.info('🎛️ Starting Performance Automation System...');

      // Start components in order
      if (this.config.enableDashboardMonitoring) {
        this.dashboard.start();
        logger.info('✅ Dashboard monitoring started');
      }

      if (this.config.enableWebSocketUpdates) {
        this.websocketService.start(8081);
        logger.info('✅ WebSocket service started');
      }

      if (this.config.enablePredictiveAnalytics) {
        this.analytics.start();
        logger.info('✅ Predictive analytics started');
      }

      if (this.config.enableAutomatedOptimization) {
        this.optimization.start();
        logger.info('✅ Automated optimization started');
      }

      // Start system monitoring
      this.startSystemMonitoring();

      logger.info('🎛️ Performance Automation System fully operational');
      this.emit('systemStarted');
    } catch (error) {
      logger.error('🎛️ Failed to start automation system:', {
        error: error instanceof Error ? error.message : String(error),
      });
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Stop automation system
   */
  async stop(): Promise<void> {
    if (!this.isRunning) return;

    try {
      logger.info('🎛️ Stopping Performance Automation System...');

      // Stop timers
      if (this.statusUpdateTimer) {
        clearInterval(this.statusUpdateTimer);
        this.statusUpdateTimer = null;
      }

      if (this.healthCheckTimer) {
        clearInterval(this.healthCheckTimer);
        this.healthCheckTimer = null;
      }

      // Stop components
      this.optimization.stop();
      this.analytics.stop();
      this.dashboard.stop();
      this.websocketService.stop();

      this.isRunning = false;
      this.startTime = null;

      logger.info('🎛️ Performance Automation System stopped');
      this.emit('systemStopped');
    } catch (error) {
      logger.error('🎛️ Error stopping automation system:', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Setup component event listeners
   */
  private setupComponentListeners(): void {
    // Dashboard events
    this.dashboard.on('metricsUpdated', (metrics) => {
      this.handleMetricsUpdate(metrics);
    });

    this.dashboard.on('dashboardStarted', () => {
      this.updateSystemStatus();
    });

    // Analytics events
    this.analytics.on('predictionGenerated', (prediction) => {
      this.handlePrediction(prediction);
    });

    this.analytics.on('proactiveAlert', (alert) => {
      this.handleProactiveAlert(alert);
    });

    // Optimization events
    this.optimization.on('optimizationCompleted', (action) => {
      this.handleOptimizationCompleted(action);
    });

    this.optimization.on('optimizationFailed', (action) => {
      this.handleOptimizationFailed(action);
    });

    this.optimization.on('approvalRequired', (action) => {
      this.handleApprovalRequired(action);
    });

    // WebSocket events
    this.websocketService.on('clientConnected', () => {
      this.updateSystemStatus();
    });
  }

  /**
   * Start system monitoring
   */
  private startSystemMonitoring(): void {
    // Status update timer
    this.statusUpdateTimer = setInterval(() => {
      this.updateSystemStatus();
    }, 30000); // 30 seconds

    // Health check timer
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, 60000); // 1 minute

    // Initial status update
    this.updateSystemStatus();
  }

  /**
   * Update system status
   */
  private updateSystemStatus(): void {
    try {
      const currentMetrics = this.dashboard.getCurrentMetrics();
      const optimizationHistory = this.optimization.getOptimizationHistory(24);
      const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;

      this.systemStatus = {
        timestamp: new Date(),
        overall: this.calculateOverallStatus(),

        components: {
          dashboard: {
            status: this.isRunning ? 'running' : 'stopped',
            uptime: Math.floor(uptime / 1000),
          },
          analytics: {
            status: this.isRunning ? 'running' : 'stopped',
            predictions: this.analytics.getPredictions(1).length,
          },
          optimization: {
            status: this.isRunning ? 'running' : 'stopped',
            activeOptimizations: this.optimization.getActiveOptimizations().length,
          },
          websocket: {
            status: this.isRunning ? 'running' : 'stopped',
            connectedClients: this.websocketService.getConnectedClientsCount(),
          },
          monitoring: {
            status: this.isRunning ? 'running' : 'stopped',
            activeScans: currentMetrics?.activeScans || 0,
          },
        },

        performance: {
          averageScore: currentMetrics?.averagePerformanceScore || 0,
          cacheEfficiency: currentMetrics?.performance.cacheHitRate || 0,
          memoryUsage: currentMetrics?.performance.memoryUsage || 0,
          systemLoad: this.calculateSystemLoad(),
        },

        automation: {
          totalOptimizations: optimizationHistory.length,
          successfulOptimizations: optimizationHistory.filter((o) => o.status === 'completed')
            .length,
          failedOptimizations: optimizationHistory.filter((o) => o.status === 'failed').length,
          averageImprovementPercentage: this.calculateAverageImprovement(optimizationHistory),
        },
      };

      this.emit('statusUpdated', this.systemStatus);
    } catch (error) {
      logger.error('🎛️ Failed to update system status:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Calculate overall system status
   */
  private calculateOverallStatus(): 'excellent' | 'good' | 'warning' | 'critical' {
    const currentMetrics = this.dashboard.getCurrentMetrics();
    if (!currentMetrics) return 'warning';

    let score = 100;

    // Performance score impact
    if (currentMetrics.averagePerformanceScore < this.config.thresholds.performanceScore) {
      score -= 20;
    }

    // Cache efficiency impact
    if (currentMetrics.performance.cacheHitRate < this.config.thresholds.cacheHitRate) {
      score -= 15;
    }

    // Memory usage impact
    if (currentMetrics.performance.memoryUsage > this.config.thresholds.memoryUsage) {
      score -= 25;
    }

    // Error rate impact
    if (currentMetrics.performance.errorRate > this.config.thresholds.errorRate) {
      score -= 20;
    }

    // Utility overhead impact
    if (
      currentMetrics.utilityIntegration.averageUtilityOverhead >
      this.config.thresholds.utilityOverhead
    ) {
      score -= 10;
    }

    // System health based on alerts
    const recentAlerts = currentMetrics.alerts.filter(
      (a) => Date.now() - a.timestamp.getTime() < 300000, // Last 5 minutes
    );

    const criticalAlerts = recentAlerts.filter((a) => a.severity === 'critical').length;
    const highAlerts = recentAlerts.filter((a) => a.severity === 'high').length;

    score -= criticalAlerts * 15;
    score -= highAlerts * 10;

    if (score >= 90) return 'excellent';
    if (score >= 75) return 'good';
    if (score >= 50) return 'warning';
    return 'critical';
  }

  /**
   * Calculate system load
   */
  private calculateSystemLoad(): number {
    // Simplified system load calculation
    const currentMetrics = this.dashboard.getCurrentMetrics();
    if (!currentMetrics) return 0;

    const memoryLoad = (currentMetrics.performance.memoryUsage / 4000) * 100; // Assume 4GB max
    const errorLoad = currentMetrics.performance.errorRate * 2;
    const utilityLoad = currentMetrics.utilityIntegration.averageUtilityOverhead;

    return Math.min(100, (memoryLoad + errorLoad + utilityLoad) / 3);
  }

  /**
   * Calculate average improvement from optimizations
   */
  private calculateAverageImprovement(
    optimizations: Array<{
      status: string;
      results?: {
        performanceImpact?: {
          improvement?: {
            performanceScore?: number;
          };
        };
      };
    }>,
  ): number {
    const successfulOptimizations = optimizations.filter(
      (o) => o.status === 'completed' && o.results?.performanceImpact,
    );

    if (successfulOptimizations.length === 0) return 0;

    const totalImprovement = successfulOptimizations.reduce((sum, opt) => {
      const impact = opt.results?.performanceImpact;
      if (impact && impact.improvement) {
        const performanceImprovement = impact.improvement.performanceScore || 0;
        return sum + Math.abs(performanceImprovement);
      }
      return sum;
    }, 0);

    return totalImprovement / successfulOptimizations.length;
  }

  /**
   * Perform health check
   */
  private performHealthCheck(): void {
    try {
      const status = this.getSystemStatus();

      if (status?.overall === 'critical') {
        logger.warn('🎛️ System health critical - triggering emergency procedures');
        this.handleCriticalSystemState();
      } else if (status?.overall === 'warning') {
        logger.info('🎛️ System health warning - monitoring closely');
        this.handleWarningSystemState();
      }

      this.emit('healthCheckCompleted', status);
    } catch (error) {
      logger.error('🎛️ Health check failed:', {
        error: error instanceof Error ? error.message : String(error),
      });
      this.emit('healthCheckFailed', error);
    }
  }

  /**
   * Handle critical system state
   */
  private handleCriticalSystemState(): void {
    // Emergency procedures for critical system state
    logger.error('🚨 CRITICAL SYSTEM STATE DETECTED', {});

    // Send critical alert
    this.websocketService.sendAlert({
      type: 'system',
      severity: 'critical',
      message: 'System performance critical - immediate attention required',
    });

    // Trigger emergency optimization if enabled
    if (this.config.enableAutomatedOptimization) {
      logger.info('🤖 Triggering emergency optimization procedures');
      // Emergency optimization logic would go here
    }

    this.emit('criticalSystemState', this.systemStatus);
  }

  /**
   * Handle warning system state
   */
  private handleWarningSystemState(): void {
    // Proactive measures for warning state
    logger.warn('⚠️ System performance warning detected');

    this.websocketService.sendAlert({
      type: 'system',
      severity: 'medium',
      message: 'System performance degraded - monitoring and optimization recommended',
    });

    this.emit('warningSystemState', this.systemStatus);
  }

  // Event handlers
  private handleMetricsUpdate(metrics: Record<string, unknown>): void {
    // Process metrics update
    this.emit('metricsProcessed', metrics);
  }

  private handlePrediction(prediction: { type: string; confidence: number }): void {
    logger.info(
      `🔮 Prediction generated: ${prediction.type} (${prediction.confidence}% confidence)`,
    );
    this.emit('predictionProcessed', prediction);
  }

  private handleProactiveAlert(alert: { type: string; severity: string }): void {
    logger.warn(`🚨 Proactive alert: ${alert.type} (${alert.severity})`);
    this.emit('proactiveAlertProcessed', alert);
  }

  private handleOptimizationCompleted(action: { action: { name: string } }): void {
    logger.info(`✅ Optimization completed: ${action.action.name}`);
    this.emit('optimizationProcessed', action);
  }

  private handleOptimizationFailed(action: { action: { name: string } }): void {
    logger.error(`❌ Optimization failed: ${action.action.name}`, {});
    this.emit('optimizationFailed', action);
  }

  private handleApprovalRequired(action: {
    action: { name: string };
    trigger: { scanId: string };
  }): void {
    logger.info(`📋 Optimization approval required: ${action.action.name}`);

    // Send approval request via WebSocket
    this.websocketService.sendAlert({
      type: 'approval_required',
      severity: 'medium',
      message: `Optimization approval required: ${action.action.name}`,
      scanId: action.trigger.scanId,
    });

    this.emit('approvalRequired', action);
  }

  // Public API methods
  getSystemStatus(): SystemStatus | null {
    return this.systemStatus;
  }

  getConfig(): AutomationConfig {
    return { ...this.config };
  }

  updateConfig(newConfig: Partial<AutomationConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // Apply configuration changes
    if (this.isRunning) {
      this.applyConfigurationChanges(oldConfig, this.config);
    }

    logger.info('🎛️ Automation controller configuration updated');
    this.emit('configUpdated', this.config);
  }

  /**
   * Apply configuration changes
   */
  private applyConfigurationChanges(
    oldConfig: AutomationConfig,
    newConfig: AutomationConfig,
  ): void {
    // Update component configurations
    if (oldConfig.enableDashboardMonitoring !== newConfig.enableDashboardMonitoring) {
      if (newConfig.enableDashboardMonitoring) {
        this.dashboard.start();
      } else {
        this.dashboard.stop();
      }
    }

    if (oldConfig.enablePredictiveAnalytics !== newConfig.enablePredictiveAnalytics) {
      if (newConfig.enablePredictiveAnalytics) {
        this.analytics.start();
      } else {
        this.analytics.stop();
      }
    }

    if (oldConfig.enableAutomatedOptimization !== newConfig.enableAutomatedOptimization) {
      if (newConfig.enableAutomatedOptimization) {
        this.optimization.start();
      } else {
        this.optimization.stop();
      }
    }

    // Update component-specific configurations
    this.dashboard.updateConfig({
      updateInterval: newConfig.monitoring.dashboardUpdateInterval,
    });

    this.analytics.updateConfig({
      predictionInterval: newConfig.monitoring.analyticsInterval,
    });

    this.optimization.updateConfig({
      requireApprovalForCritical: newConfig.automation.requireApprovalForCritical,
      maxConcurrentOptimizations: newConfig.automation.maxConcurrentOptimizations,
      rollbackOnFailure: newConfig.automation.rollbackOnFailure,
      monitoringPeriodAfterOptimization: newConfig.automation.monitoringPeriod,
    });
  }

  /**
   * Enable full automation
   */
  enableFullAutomation(): void {
    this.updateConfig({
      enableFullAutomation: true,
      enableDashboardMonitoring: true,
      enablePredictiveAnalytics: true,
      enableAutomatedOptimization: true,
      enableWebSocketUpdates: true,
    });

    logger.info('🎛️ Full automation enabled');
  }

  /**
   * Disable automation (monitoring only)
   */
  disableAutomation(): void {
    this.updateConfig({
      enableFullAutomation: false,
      enableAutomatedOptimization: false,
    });

    logger.info('🎛️ Automation disabled - monitoring only');
  }

  /**
   * Get system uptime
   */
  getUptime(): number {
    return this.startTime ? Date.now() - this.startTime.getTime() : 0;
  }

  /**
   * Get system statistics
   */
  getStatistics(): {
    uptime: number;
    totalOptimizations: number;
    totalPredictions: number;
    systemStatus: string;
    connectedClients: number;
    activeScans: number;
  } {
    const uptime = this.getUptime();
    const optimizationHistory = this.optimization.getOptimizationHistory(24);
    const predictions = this.analytics.getPredictions(24);

    return {
      uptime: Math.floor(uptime / 1000),
      totalOptimizations: optimizationHistory.length,
      totalPredictions: predictions.length,
      systemStatus: this.systemStatus?.overall || 'unknown',
      connectedClients: this.websocketService.getConnectedClientsCount(),
      activeScans: this.dashboard.getCurrentMetrics()?.activeScans || 0,
    };
  }
}
