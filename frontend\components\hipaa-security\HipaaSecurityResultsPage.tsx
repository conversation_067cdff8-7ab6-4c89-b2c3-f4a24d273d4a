import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, CheckCircle, XCircle, Download, RefreshCw, Shield } from 'lucide-react';
import { HipaaSecurityScanResult, RiskLevel } from '@/types/hipaa-security';

interface HipaaSecurityResultsPageProps {
  scanResult: HipaaSecurityScanResult;
  onExportReport?: () => void;
  onStartNewScan?: () => void;
  onRecalculateScoring?: () => void;
}

export const HipaaSecurityResultsPage: React.FC<HipaaSecurityResultsPageProps> = ({
  scanResult,
  onExportReport,
  onStartNewScan,
  onRecalculateScoring,
}) => {
  const [activeTab, setActiveTab] = useState('summary');

  const getRiskLevelColor = (riskLevel: RiskLevel): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskLevelIcon = (riskLevel: RiskLevel) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div
      className="container mx-auto p-6 space-y-6"
      style={{ backgroundColor: '#F5F5F5', color: '#333333' }}
    >
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold mb-2" style={{ color: '#333333' }}>
            HIPAA Security Compliance Results
          </h1>
          <p style={{ color: '#666666' }} className="mt-2">
            Scan completed for: <span className="font-medium">{scanResult.targetUrl}</span>
          </p>
          <p className="text-sm" style={{ color: '#999999' }}>
            Scanned on {new Date(scanResult.scanTimestamp).toLocaleString()} • Duration:{' '}
            {formatDuration(scanResult.scanDuration)}
          </p>
        </div>
        <div className="flex gap-2">
          {onRecalculateScoring && (
            <Button
              variant="outline"
              onClick={onRecalculateScoring}
              style={{ borderColor: '#0055A4', color: '#0055A4' }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Update Scoring
            </Button>
          )}
          {onExportReport && (
            <Button
              variant="outline"
              onClick={onExportReport}
              style={{ borderColor: '#0055A4', color: '#0055A4' }}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          )}
          {onStartNewScan && (
            <Button onClick={onStartNewScan} style={{ backgroundColor: '#0055A4', color: 'white' }}>
              <RefreshCw className="h-4 w-4 mr-2" />
              New Scan
            </Button>
          )}
        </div>
      </div>

      {/* Overall Score Card */}
      <Card style={{ backgroundColor: 'white', color: '#333333' }}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2" style={{ color: '#333333' }}>
            {getRiskLevelIcon(scanResult.riskLevel)}
            Overall HIPAA Security Compliance Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex justify-between items-center mb-3">
                <span className="text-3xl font-bold" style={{ color: '#333333' }}>
                  {Math.round(scanResult.overallScore)}%
                </span>
                <Badge
                  className={`${getRiskLevelColor(scanResult.riskLevel)} text-white px-3 py-1`}
                >
                  {scanResult.riskLevel.toUpperCase()}
                </Badge>
              </div>
              <Progress value={scanResult.overallScore} className="h-3 mb-4" />
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <span style={{ color: '#666666' }}>Passed Tests:</span>
                  <div className="font-semibold text-green-600">
                    {scanResult.passedTests?.length || 0}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Failed Tests:</span>
                  <div className="font-semibold text-red-600">
                    {scanResult.failedTests?.length || 0}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Vulnerabilities:</span>
                  <div className="font-semibold text-orange-600">
                    {scanResult.vulnerabilities?.length || 0}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Pages Scanned:</span>
                  <div className="font-semibold" style={{ color: '#333333' }}>
                    {scanResult.pagesScanned?.length || 0}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList
          className="grid w-full grid-cols-4"
          style={{
            backgroundColor: 'white',
            border: '1px solid #E5E7EB',
            borderRadius: '8px',
          }}
        >
          <TabsTrigger
            value="summary"
            style={{
              backgroundColor: activeTab === 'summary' ? '#0055A4' : 'transparent',
              color: activeTab === 'summary' ? 'white' : '#333333',
            }}
          >
            Summary
          </TabsTrigger>
          <TabsTrigger
            value="tests"
            style={{
              backgroundColor: activeTab === 'tests' ? '#0055A4' : 'transparent',
              color: activeTab === 'tests' ? 'white' : '#333333',
            }}
          >
            Test Results
          </TabsTrigger>
          <TabsTrigger
            value="vulnerabilities"
            style={{
              backgroundColor: activeTab === 'vulnerabilities' ? '#0055A4' : 'transparent',
              color: activeTab === 'vulnerabilities' ? 'white' : '#333333',
            }}
          >
            Vulnerabilities
          </TabsTrigger>
          <TabsTrigger
            value="details"
            style={{
              backgroundColor: activeTab === 'details' ? '#0055A4' : 'transparent',
              color: activeTab === 'details' ? 'white' : '#333333',
            }}
          >
            Scan Details
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <Card style={{ backgroundColor: 'white', color: '#333333' }}>
            <CardHeader>
              <CardTitle style={{ color: '#333333' }}>Security Analysis Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2" style={{ color: '#333333' }}>
                    Scan Configuration
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div style={{ color: '#666666' }}>
                      Target URL:{' '}
                      <span className="font-medium" style={{ color: '#333333' }}>
                        {scanResult.targetUrl}
                      </span>
                    </div>
                    <div style={{ color: '#666666' }}>
                      Scan Duration:{' '}
                      <span className="font-medium" style={{ color: '#333333' }}>
                        {formatDuration(scanResult.scanDuration)}
                      </span>
                    </div>
                    <div style={{ color: '#666666' }}>
                      Tools Used:{' '}
                      <span className="font-medium" style={{ color: '#333333' }}>
                        {scanResult.toolsUsed?.join(', ') || 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2" style={{ color: '#333333' }}>
                    Risk Assessment
                  </h4>
                  <Badge
                    variant="outline"
                    className={`${getRiskLevelColor(scanResult.riskLevel)} text-white`}
                  >
                    {scanResult.riskLevel.toUpperCase()} RISK
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tests" className="space-y-4">
          <div className="grid gap-4">
            {/* Passed Tests */}
            {scanResult.passedTests && scanResult.passedTests.length > 0 && (
              <Card style={{ backgroundColor: 'white', color: '#333333' }}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2" style={{ color: '#333333' }}>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Passed Tests ({scanResult.passedTests.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {scanResult.passedTests.slice(0, 5).map((test, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span style={{ color: '#666666' }}>
                          {test.testName || test.description || `Test ${index + 1}`}
                        </span>
                      </div>
                    ))}
                    {scanResult.passedTests.length > 5 && (
                      <div style={{ color: '#666666' }}>
                        ... and {scanResult.passedTests.length - 5} more
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Failed Tests */}
            {scanResult.failedTests && scanResult.failedTests.length > 0 && (
              <Card style={{ backgroundColor: 'white', color: '#333333' }}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2" style={{ color: '#333333' }}>
                    <XCircle className="h-5 w-5 text-red-500" />
                    Failed Tests ({scanResult.failedTests.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {scanResult.failedTests.slice(0, 5).map((test, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-500" />
                        <span style={{ color: '#666666' }}>
                          {test.testName || test.description || `Test ${index + 1}`}
                        </span>
                      </div>
                    ))}
                    {scanResult.failedTests.length > 5 && (
                      <div style={{ color: '#666666' }}>
                        ... and {scanResult.failedTests.length - 5} more
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="vulnerabilities" className="space-y-4">
          <div className="grid gap-4">
            {scanResult.vulnerabilities && scanResult.vulnerabilities.length > 0 ? (
              scanResult.vulnerabilities.map((vuln, index) => (
                <Card key={index} style={{ backgroundColor: 'white', color: '#333333' }}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2" style={{ color: '#333333' }}>
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      {vuln.type || `Vulnerability ${index + 1}`}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p style={{ color: '#666666' }}>{vuln.description}</p>
                    <div className="mt-3 flex gap-2">
                      <Badge variant="outline" style={{ borderColor: '#EF4444', color: '#EF4444' }}>
                        Severity: {vuln.severity}
                      </Badge>
                      {vuln.location && (
                        <Badge
                          variant="outline"
                          style={{ borderColor: '#0055A4', color: '#0055A4' }}
                        >
                          Location: {vuln.location}
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card style={{ backgroundColor: 'white', color: '#333333' }}>
                <CardContent className="text-center py-8">
                  <Shield className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2" style={{ color: '#333333' }}>
                    No Vulnerabilities Found
                  </h3>
                  <p style={{ color: '#666666' }}>
                    Great! No security vulnerabilities were detected during the scan.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          <Card style={{ backgroundColor: 'white', color: '#333333' }}>
            <CardHeader>
              <CardTitle style={{ color: '#333333' }}>Scan Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span style={{ color: '#666666' }}>Scan ID:</span>
                  <div className="font-mono text-sm" style={{ color: '#333333' }}>
                    {scanResult.scanId}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Scan Status:</span>
                  <div className="font-semibold" style={{ color: '#333333' }}>
                    {scanResult.scanStatus || 'Completed'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
