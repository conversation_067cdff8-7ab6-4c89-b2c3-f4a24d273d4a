{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".", // Base for path resolution is this tsconfig's directory
    "paths": {
      "@lib/*": ["../lib/*", "./lib/*"] // Support both local dev and Docker paths
    },
    "outDir": "./dist", // Output directory relative to backend directory
    "module": "commonjs",
    "target": "ES2020",
    "lib": ["ES2020", "ES2018", "DOM"],
    "sourceMap": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "moduleResolution": "node",
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "noEmit": false, // We want to emit JS files for the backend
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "isolatedModules": false,
    "declaration": true,
    "declarationMap": true,
    "downlevelIteration": true, // Enable iteration over ES2015+ iterables
    "allowJs": true,
    "checkJs": false
  },
  "include": [
    "src/**/*.ts", // Backend source files
    "scripts/**/*.ts", // Backend scripts
    "../lib/**/*.ts", // Shared lib files
    "knexfile.ts", // Knex configuration
    "jest.config.js", // Jest configuration
    "preload-env.js", // Environment preloader
    ".eslintrc.js", // ESLint configuration
    "babel.config.js", // Babel configuration
    "./knexfile.d.ts" // Knex declaration file
  ],
  "exclude": ["node_modules"]
}
