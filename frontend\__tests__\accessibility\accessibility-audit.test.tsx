/**
 * Accessibility Audit Tests
 * Comprehensive WCAG AA compliance testing for HIPAA Dashboard
 */

import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import {
  HipaaDashboard,
  type HipaaDashboardData,
} from '@/components/dashboard/hipaa/HipaaDashboard';
import { HipaaOverviewCard } from '@/components/dashboard/hipaa/HipaaOverviewCard';
import { HipaaModuleCard } from '@/components/dashboard/hipaa/HipaaModuleCard';
import {
  ComplianceMetrics,
  type ComplianceMetric,
} from '@/components/dashboard/shared/ComplianceMetrics';
import { RiskLevelIndicator } from '@/components/dashboard/shared/RiskLevelIndicator';
import { ScanStatusBadge } from '@/components/dashboard/shared/ScanStatusBadge';
import { ToastProvider } from '@/components/ui/Toast';
import { getContrastRatio, meetsWCAGAA } from '@/utils/accessibility';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock responsive hooks
jest.mock('@/hooks/useResponsive', () => ({
  useResponsiveDashboard: () => ({
    layout: { isMobile: false, isTablet: false, isDesktop: true, stackVertically: false },
    spacing: { section: '1.5rem', container: '2rem' },
    cards: { columns: 3, gap: '2rem', padding: '2rem' },
  }),
  useBreakpoint: () => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    currentBreakpoint: 'lg',
  }),
}));

const mockDashboardData = {
  overview: {
    overallScore: 85,
    riskLevel: 'medium',
    complianceStatus: 'partially_compliant',
    lastScanDate: '2025-06-24T10:30:00Z',
    totalScans: 24,
  },
  privacyModule: {
    latestScore: 82,
    scanCount: 12,
    lastScanDate: '2025-06-24T10:30:00Z',
    status: 'active',
    recentScans: [],
  },
  securityModule: {
    latestScore: 88,
    scanCount: 12,
    lastScanDate: '2025-06-23T14:15:00Z',
    status: 'active',
    recentScans: [],
  },
  recentActivity: [
    {
      id: 'activity-1',
      type: 'privacy',
      url: 'https://example.com',
      timestamp: '2025-06-24T10:30:00Z',
      score: 82,
      status: 'completed',
      riskLevel: 'medium',
    },
  ],
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(<ToastProvider>{component}</ToastProvider>);
};

describe('Accessibility Audit - WCAG AA Compliance', () => {
  describe('Main Dashboard Component', () => {
    it('should not have any accessibility violations', async () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
          onStartPrivacyScan={jest.fn()}
          onStartSecurityScan={jest.fn()}
          onViewPrivacyResults={jest.fn()}
          onViewSecurityResults={jest.fn()}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper heading hierarchy', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const h1 = container.querySelector('h1');
      expect(h1).toBeInTheDocument();
      expect(h1).toHaveTextContent('HIPAA Compliance Dashboard');

      // Check that there are no heading level skips
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const headingLevels = Array.from(headings).map((h) => parseInt(h.tagName.charAt(1)));

      for (let i = 1; i < headingLevels.length; i++) {
        const currentLevel = headingLevels[i];
        const previousLevel = headingLevels[i - 1];
        expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
      }
    });

    it('should have proper ARIA landmarks', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      expect(container.querySelector('[role="main"]')).toBeInTheDocument();
      expect(container.querySelector('[role="main"]')).toHaveAttribute('aria-label');
    });

    it('should have keyboard accessible interactive elements', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const interactiveElements = container.querySelectorAll(
        'button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])',
      );

      interactiveElements.forEach((element) => {
        expect(element).toBeVisible();
        expect(element).not.toHaveAttribute('tabindex', '-1');
      });
    });
  });

  describe('Overview Card Component', () => {
    it('should not have any accessibility violations', async () => {
      const { container } = renderWithProviders(
        <HipaaOverviewCard
          overallScore={85}
          riskLevel="medium"
          lastScanDate="2025-06-24T10:30:00Z"
          totalScans={24}
          complianceStatus="partially_compliant"
          loading={false}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper ARIA labels for scores', () => {
      const { container } = renderWithProviders(
        <HipaaOverviewCard
          overallScore={85}
          riskLevel="medium"
          lastScanDate="2025-06-24T10:30:00Z"
          totalScans={24}
          complianceStatus="partially_compliant"
          loading={false}
        />,
      );

      const scoreElement = container.querySelector('[aria-label*="score"]');
      expect(scoreElement).toBeInTheDocument();
    });
  });

  describe('Module Card Component', () => {
    it('should not have any accessibility violations', async () => {
      const { container } = renderWithProviders(
        <HipaaModuleCard
          moduleType="privacy"
          latestScore={82}
          scanCount={12}
          lastScanDate="2025-06-24T10:30:00Z"
          status="active"
          onStartScan={jest.fn()}
          onViewResults={jest.fn()}
          loading={false}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have accessible button labels', () => {
      const { container } = renderWithProviders(
        <HipaaModuleCard
          moduleType="privacy"
          latestScore={82}
          scanCount={12}
          lastScanDate="2025-06-24T10:30:00Z"
          status="active"
          onStartScan={jest.fn()}
          onViewResults={jest.fn()}
          loading={false}
        />,
      );

      const buttons = container.querySelectorAll('button');
      buttons.forEach((button) => {
        expect(button).toHaveAttribute('aria-label');
        expect(button.getAttribute('aria-label')).toBeTruthy();
      });
    });
  });

  describe('Shared Components', () => {
    it('ComplianceMetrics should not have accessibility violations', async () => {
      const mockMetrics = {
        title: 'Test Metrics',
        score: 85,
        status: 'good',
        trend: 'up',
        details: 'Test details',
      };

      const { container } = renderWithProviders(
        <ComplianceMetrics
          title="Test Metrics"
          metrics={mockMetrics}
          showProgress={true}
          showTrends={true}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('RiskLevelIndicator should not have accessibility violations', async () => {
      const { container } = renderWithProviders(
        <RiskLevelIndicator
          riskLevel="medium"
          score={75}
          variant="card"
          showProgress={true}
          showDescription={true}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('ScanStatusBadge should not have accessibility violations', async () => {
      const { container } = renderWithProviders(
        <ScanStatusBadge status="completed" animated={false} />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Color Contrast Compliance', () => {
    it('should meet WCAG AA contrast requirements for primary colors', () => {
      const primaryBlue = '#0055A4';
      const white = '#FFFFFF';
      const darkGray = '#333333';
      const lightGray = '#F5F5F5';

      // Primary blue on white background
      expect(meetsWCAGAA(primaryBlue, white)).toBe(true);

      // Dark gray text on white background
      expect(meetsWCAGAA(darkGray, white)).toBe(true);

      // White text on primary blue background
      expect(meetsWCAGAA(white, primaryBlue)).toBe(true);

      // Dark gray on light gray should not meet requirements
      expect(meetsWCAGAA(darkGray, lightGray)).toBe(true);
    });

    it('should meet contrast requirements for status colors', () => {
      const statusColors = {
        success: '#2E7D32',
        warning: '#F57C00',
        error: '#C62828',
        info: '#1976D2',
      };
      const white = '#FFFFFF';

      Object.entries(statusColors).forEach(([status, color]) => {
        expect(meetsWCAGAA(color, white)).toBe(true);
        expect(meetsWCAGAA(white, color)).toBe(true);
      });
    });

    it('should calculate contrast ratios correctly', () => {
      const contrastRatio = getContrastRatio('#000000', '#FFFFFF');
      expect(contrastRatio).toBeCloseTo(21, 0); // Perfect contrast

      const lowContrast = getContrastRatio('#888888', '#999999');
      expect(lowContrast).toBeLessThan(4.5); // Should not meet WCAG AA
    });
  });

  describe('Keyboard Navigation', () => {
    it('should have proper tab order', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const tabbableElements = container.querySelectorAll(
        'button:not([disabled]), a[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])',
      );

      // Check that all tabbable elements have proper tabindex
      tabbableElements.forEach((element, index) => {
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex !== null) {
          expect(parseInt(tabIndex)).toBeGreaterThanOrEqual(0);
        }
      });
    });

    it('should have focus indicators', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const focusableElements = container.querySelectorAll('button, a');
      focusableElements.forEach((element) => {
        // Check that elements have focus styles (this would need to be tested with actual CSS)
        expect(element).toBeInTheDocument();
      });
    });
  });

  describe('Screen Reader Support', () => {
    it('should have proper ARIA labels and descriptions', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      // Check for ARIA labels
      const elementsWithAriaLabel = container.querySelectorAll('[aria-label]');
      expect(elementsWithAriaLabel.length).toBeGreaterThan(0);

      // Check for ARIA descriptions
      const elementsWithAriaDescribedBy = container.querySelectorAll('[aria-describedby]');
      elementsWithAriaDescribedBy.forEach((element) => {
        const describedById = element.getAttribute('aria-describedby');
        const descriptionElement = container.querySelector(`#${describedById}`);
        expect(descriptionElement).toBeInTheDocument();
      });
    });

    it('should have proper live regions for dynamic content', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={true}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const liveRegions = container.querySelectorAll('[aria-live]');
      expect(liveRegions.length).toBeGreaterThan(0);

      liveRegions.forEach((region) => {
        const ariaLive = region.getAttribute('aria-live');
        expect(['polite', 'assertive', 'off']).toContain(ariaLive);
      });
    });

    it('should have proper role attributes', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      // Check for main landmark
      expect(container.querySelector('[role="main"]')).toBeInTheDocument();

      // Check for button roles
      const buttons = container.querySelectorAll('button');
      buttons.forEach((button) => {
        expect(button.getAttribute('role')).toBeOneOf([null, 'button']);
      });
    });
  });

  describe('Error States Accessibility', () => {
    it('should have accessible error messages', async () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error="Test error message"
          onRefresh={jest.fn()}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();

      // Check for error alert
      const errorAlert = container.querySelector('[role="alert"]');
      expect(errorAlert).toBeInTheDocument();
    });
  });

  describe('Loading States Accessibility', () => {
    it('should have accessible loading indicators', async () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={true}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();

      // Check for loading status
      const loadingElements = container.querySelectorAll(
        '[role="status"], [aria-label*="loading"], [aria-label*="Loading"]',
      );
      expect(loadingElements.length).toBeGreaterThan(0);
    });
  });

  describe('Mobile Accessibility', () => {
    beforeEach(() => {
      // Mock mobile viewport
      jest.doMock('@/hooks/useResponsive', () => ({
        useResponsiveDashboard: () => ({
          layout: { isMobile: true, isTablet: false, isDesktop: false, stackVertically: true },
          spacing: { section: '1rem', container: '1rem' },
          cards: { columns: 1, gap: '1rem', padding: '1rem' },
        }),
        useBreakpoint: () => ({
          isMobile: true,
          isTablet: false,
          isDesktop: false,
          currentBreakpoint: 'sm',
        }),
      }));
    });

    it('should maintain accessibility on mobile devices', async () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have proper touch target sizes', () => {
      const { container } = renderWithProviders(
        <HipaaDashboard
          data={mockDashboardData}
          loading={false}
          error={null}
          onRefresh={jest.fn()}
        />,
      );

      const buttons = container.querySelectorAll('button');
      buttons.forEach((button) => {
        const styles = window.getComputedStyle(button);
        const minHeight = parseInt(styles.minHeight) || parseInt(styles.height);
        const minWidth = parseInt(styles.minWidth) || parseInt(styles.width);

        // WCAG recommends minimum 44px touch targets
        expect(minHeight).toBeGreaterThanOrEqual(44);
        expect(minWidth).toBeGreaterThanOrEqual(44);
      });
    });
  });
});
