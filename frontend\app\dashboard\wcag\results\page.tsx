'use client';

/**
 * WCAG Results Page
 * Redirects to the history page since results and history are the same
 */

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function WcagResultsPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to history page
    router.replace('/dashboard/wcag/history');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );
}
