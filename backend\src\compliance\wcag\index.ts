/**
 * WCAG Module Main Export
 * Central export point for all WCAG functionality
 */

// Core Types
export * from './types';
export * from './constants';

// Services
export { WcagOrchestrator, WcagScanConfig, ScanProgress } from './orchestrator';
// export { WcagDatabase } from './database/wcag-database'; // Will be implemented in Part 3

// Utilities
export {
  ColorAnalyzer,
  ColorInfo,
  ContrastResult,
  COLOR_PATTERNS,
  CONTRAST_THRESHOLDS,
} from './utils/color-analyzer';
export {
  FocusTracker,
  FocusableElement,
  FocusOrderResult,
  FocusVisibilityResult,
} from './utils/focus-tracker';
export {
  KeyboardTester,
  KeyboardTestResult,
  InteractiveElement,
  KeyboardNavigationResult,
} from './utils/keyboard-tester';
export {
  LayoutAnalyzer,
  ElementLayout,
  OverlapResult,
  TargetSizeResult,
  LayoutAnalysisResult,
} from './utils/layout-analyzer';
export {
  CheckTemplate,
  CheckConfig,
  EnhancedCheckConfig,
  CheckFunction,
} from './utils/check-template-fixed';
export {
  ManualReviewTemplate,
  ManualReviewConfig,
  ManualReviewItem,
  ManualReviewCheckFunction,
} from './utils/manual-review-template';

// Automated Checks (Part 3 Implementation)
export * from './checks';

/**
 * WCAG Module Version
 */
export const WCAG_MODULE_VERSION = '1.0.0';

/**
 * Supported WCAG Rules
 */
export const SUPPORTED_WCAG_RULES = [
  'WCAG-001',
  'WCAG-002',
  'WCAG-003',
  'WCAG-004',
  'WCAG-005',
  'WCAG-006',
  'WCAG-007',
  'WCAG-008',
  'WCAG-009',
  'WCAG-010',
  'WCAG-011',
  'WCAG-012',
  'WCAG-013',
  'WCAG-014',
  'WCAG-015',
  'WCAG-016',
  'WCAG-017',
  'WCAG-018',
  'WCAG-019',
  'WCAG-020',
  'WCAG-021',
  'WCAG-022',
  'WCAG-023',
  'WCAG-024',
  'WCAG-025',
  'WCAG-026',
  'WCAG-027',
  'WCAG-028',
  'WCAG-029',
  'WCAG-030',
  'WCAG-031',
  'WCAG-032',
  'WCAG-033',
  'WCAG-034',
  'WCAG-035',
  'WCAG-036',
  'WCAG-038',
  'WCAG-044',
  'WCAG-045',
  'WCAG-046',
  'WCAG-047',
  'WCAG-048',
  'WCAG-049',
  'WCAG-037',
  'WCAG-039',
  'WCAG-040',
  'WCAG-041',
  'WCAG-042',
  'WCAG-043',
  'WCAG-050',
  'WCAG-051',
  'WCAG-052',
  'WCAG-053',
  'WCAG-054',
  'WCAG-055',
  'WCAG-056',
  'WCAG-058',
  'WCAG-059',
  'WCAG-060',
  'WCAG-061',
  'WCAG-062',
  'WCAG-063',
  'WCAG-064',
  'WCAG-065',
  'WCAG-066',
  'WCAG-057',
] as const;

/**
 * Module Configuration
 */
export const WCAG_MODULE_CONFIG = {
  version: WCAG_MODULE_VERSION,
  totalRules: 66, // Complete implementation - all planned checks implemented!
  averageAutomation: 0.82, // Comprehensive coverage across all WCAG levels
  supportedVersions: ['2.1', '2.2', '3.0'],
  supportedLevels: ['A', 'AA', 'AAA'],
} as const;
