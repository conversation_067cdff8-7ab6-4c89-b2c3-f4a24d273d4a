import React from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Check<PERSON>ircle,
  XCircle,
  Clock,
  Loader2,
  AlertTriangle,
  Pause,
  Play,
  RefreshCw,
} from 'lucide-react';

export type ScanStatus =
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'paused'
  | 'retrying';

export interface ScanStatusBadgeProps {
  status: ScanStatus;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  animated?: boolean;
  className?: string;
  customLabel?: string;
}

/**
 * Scan Status Badge Component
 * Displays scan status with consistent styling and animations
 */
export const ScanStatusBadge: React.FC<ScanStatusBadgeProps> = ({
  status,
  size = 'md',
  showIcon = true,
  animated = true,
  className = '',
  customLabel,
}) => {
  const getStatusConfig = (status: ScanStatus) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          variant: 'secondary' as const,
          icon: <Clock className="h-4 w-4" />,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          description: '<PERSON>an is queued and waiting to start',
        };
      case 'running':
        return {
          label: 'Running',
          variant: 'default' as const,
          icon: animated ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Play className="h-4 w-4" />
          ),
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          description: 'Scan is currently in progress',
        };
      case 'completed':
        return {
          label: 'Completed',
          variant: 'success' as const,
          icon: <CheckCircle className="h-4 w-4" />,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          description: 'Scan completed successfully',
        };
      case 'failed':
        return {
          label: 'Failed',
          variant: 'destructive' as const,
          icon: <XCircle className="h-4 w-4" />,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          description: 'Scan failed due to an error',
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          variant: 'secondary' as const,
          icon: <XCircle className="h-4 w-4" />,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          description: 'Scan was cancelled by user',
        };
      case 'paused':
        return {
          label: 'Paused',
          variant: 'warning' as const,
          icon: <Pause className="h-4 w-4" />,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          description: 'Scan is temporarily paused',
        };
      case 'retrying':
        return {
          label: 'Retrying',
          variant: 'warning' as const,
          icon: animated ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          ),
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          description: 'Scan is retrying after a failure',
        };
      default:
        return {
          label: 'Unknown',
          variant: 'secondary' as const,
          icon: <AlertTriangle className="h-4 w-4" />,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          description: 'Unknown scan status',
        };
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          text: 'text-xs',
          padding: 'px-2 py-1',
          icon: 'h-3 w-3',
          gap: 'gap-1',
        };
      case 'lg':
        return {
          text: 'text-base',
          padding: 'px-4 py-2',
          icon: 'h-5 w-5',
          gap: 'gap-2',
        };
      default: // md
        return {
          text: 'text-sm',
          padding: 'px-3 py-1',
          icon: 'h-4 w-4',
          gap: 'gap-1.5',
        };
    }
  };

  const config = getStatusConfig(status);
  const sizeClasses = getSizeClasses(size);
  const label = customLabel || config.label;

  return (
    <Badge
      variant={config.variant}
      className={`${sizeClasses.padding} ${sizeClasses.text} font-medium flex items-center ${sizeClasses.gap} ${className}`}
      title={config.description}
    >
      {showIcon && <span className={sizeClasses.icon}>{config.icon}</span>}
      {label}
    </Badge>
  );
};

// Extended status badge with additional information
export interface ExtendedScanStatusBadgeProps extends ScanStatusBadgeProps {
  progress?: number;
  duration?: number;
  showProgress?: boolean;
  showDuration?: boolean;
}

export const ExtendedScanStatusBadge: React.FC<ExtendedScanStatusBadgeProps> = ({
  status,
  progress,
  duration,
  showProgress = false,
  showDuration = false,
  ...props
}) => {
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const getProgressText = (): string => {
    if (progress !== undefined) {
      return ` (${progress}%)`;
    }
    return '';
  };

  const getDurationText = (): string => {
    if (duration !== undefined) {
      return ` - ${formatDuration(duration)}`;
    }
    return '';
  };

  const customLabel =
    props.customLabel ||
    `${getStatusConfig(status).label}${showProgress ? getProgressText() : ''}${showDuration ? getDurationText() : ''}`;

  return <ScanStatusBadge {...props} status={status} customLabel={customLabel} />;
};

// Utility function to get status config for external use
export const getStatusConfig = (status: ScanStatus) => {
  // Create a temporary instance to access the method
  const tempBadge = { getStatusConfig: (s: ScanStatus) => getStatusConfigInternal(s) };
  return tempBadge.getStatusConfig(status);
};

// Internal function to get status configuration
const getStatusConfigInternal = (status: ScanStatus) => {
  switch (status) {
    case 'pending':
      return {
        label: 'Pending',
        variant: 'secondary' as const,
        className: 'bg-gray-100 text-gray-800',
        icon: '⏳',
      };
    case 'running':
      return {
        label: 'Running',
        variant: 'default' as const,
        className: 'bg-blue-100 text-blue-800',
        icon: '🔄',
      };
    case 'completed':
      return {
        label: 'Completed',
        variant: 'success' as const,
        className: 'bg-green-100 text-green-800',
        icon: '✅',
      };
    case 'failed':
      return {
        label: 'Failed',
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800',
        icon: '❌',
      };
    default:
      return {
        label: 'Unknown',
        variant: 'secondary' as const,
        className: 'bg-gray-100 text-gray-800',
        icon: '❓',
      };
  }
};

// Utility function to determine if status is active (running/pending)
export const isActiveStatus = (status: ScanStatus): boolean => {
  return ['pending', 'running', 'retrying'].includes(status);
};

// Utility function to determine if status is terminal (completed/failed/cancelled)
export const isTerminalStatus = (status: ScanStatus): boolean => {
  return ['completed', 'failed', 'cancelled'].includes(status);
};

// Utility function to get status priority for sorting
export const getStatusPriority = (status: ScanStatus): number => {
  switch (status) {
    case 'running':
      return 6;
    case 'retrying':
      return 5;
    case 'pending':
      return 4;
    case 'paused':
      return 3;
    case 'completed':
      return 2;
    case 'failed':
      return 1;
    case 'cancelled':
      return 0;
    default:
      return -1;
  }
};
