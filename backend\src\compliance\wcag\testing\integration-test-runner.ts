/**
 * WCAG Integration Test Runner
 * Executes comprehensive integration tests for enhanced WCAG checks
 * Part of Milestone 4.3: Integration Testing and Validation
 */

import { Page } from 'playwright';
import logger from '../../../utils/logger';
import { CheckConfig } from '../types';
import WCAGIntegrationTestFramework, {
  IntegrationTestResult,
  ValidationReport,
} from './integration-test-framework';

// Import enhanced WCAG checks for testing
import { ContrastMinimumCheck } from '../checks/contrast-minimum';
import { FocusVisibleCheck } from '../checks/focus-visible';
import { FocusNotObscuredMinimumCheck } from '../checks/focus-not-obscured-minimum';
import { FocusNotObscuredEnhancedCheck } from '../checks/focus-not-obscured-enhanced';
import { FocusAppearanceCheck } from '../checks/focus-appearance';
import { TargetSizeCheck } from '../checks/target-size';
import { ResizeTextCheck } from '../checks/resize-text';

export interface TestSuite {
  name: string;
  checks: Array<{
    checkId: string;
    checkClass: any;
    category: 'color' | 'focus' | 'layout' | 'content';
  }>;
}

export interface TestRunnerConfig {
  enableDetailedLogging: boolean;
  generateReport: boolean;
  exportResults: boolean;
  testSuites: string[];
  maxConcurrentTests: number;
}

/**
 * Integration Test Runner for WCAG Enhanced Checks
 */
export class WCAGIntegrationTestRunner {
  private testFramework: WCAGIntegrationTestFramework;
  private config: TestRunnerConfig;

  constructor(config?: Partial<TestRunnerConfig>) {
    this.config = {
      enableDetailedLogging: config?.enableDetailedLogging ?? true,
      generateReport: config?.generateReport ?? true,
      exportResults: config?.exportResults ?? true,
      testSuites: config?.testSuites ?? ['enhanced-checks'],
      maxConcurrentTests: config?.maxConcurrentTests ?? 3,
    };

    this.testFramework = WCAGIntegrationTestFramework.getInstance();
  }

  /**
   * Run integration tests for all enhanced WCAG checks
   */
  async runAllTests(page: Page): Promise<ValidationReport> {
    logger.info('🚀 Starting comprehensive WCAG integration tests');

    const testSuites = this.getTestSuites();
    const allResults: IntegrationTestResult[] = [];

    try {
      for (const suite of testSuites) {
        if (this.config.testSuites.includes(suite.name)) {
          logger.info(`📋 Running test suite: ${suite.name}`);
          const suiteResults = await this.runTestSuite(suite, page);
          allResults.push(...suiteResults);
        }
      }

      // Generate comprehensive report
      const report = await this.testFramework.generateValidationReport();

      if (this.config.generateReport) {
        await this.saveValidationReport(report);
      }

      if (this.config.exportResults) {
        await this.exportTestResults();
      }

      logger.info(
        `✅ Integration tests completed: ${report.passedChecks}/${report.totalChecks} checks passed`,
      );
      return report;
    } catch (error) {
      logger.error(`❌ Integration tests failed: ${error}`);
      throw error;
    }
  }

  /**
   * Run tests for a specific test suite
   */
  private async runTestSuite(suite: TestSuite, page: Page): Promise<IntegrationTestResult[]> {
    logger.info(`🧪 Testing ${suite.checks.length} checks in ${suite.name} suite`);

    const results: IntegrationTestResult[] = [];
    const config: CheckConfig = this.createTestConfig();

    // Run tests with concurrency control
    const chunks = this.chunkArray(suite.checks, this.config.maxConcurrentTests);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (check) => {
        try {
          logger.debug(`🔍 Testing ${check.checkId} (${check.category})`);
          const checkResults = await this.testFramework.runIntegrationTests(
            check.checkClass,
            check.checkId,
            page,
            config,
          );
          results.push(...checkResults);

          if (this.config.enableDetailedLogging) {
            this.logTestResults(check.checkId, checkResults);
          }
        } catch (error) {
          logger.error(`❌ Test failed for ${check.checkId}: ${error}`);
        }
      });

      await Promise.all(chunkPromises);
    }

    return results;
  }

  /**
   * Get all available test suites
   */
  private getTestSuites(): TestSuite[] {
    return [
      {
        name: 'enhanced-checks',
        checks: [
          { checkId: 'WCAG-004', checkClass: ContrastMinimumCheck, category: 'color' },
          { checkId: 'WCAG-007', checkClass: FocusVisibleCheck, category: 'focus' },
          { checkId: 'WCAG-010', checkClass: FocusNotObscuredMinimumCheck, category: 'focus' },
          { checkId: 'WCAG-011', checkClass: FocusNotObscuredEnhancedCheck, category: 'focus' },
          { checkId: 'WCAG-012', checkClass: FocusAppearanceCheck, category: 'focus' },
          { checkId: 'WCAG-014', checkClass: TargetSizeCheck, category: 'layout' },
          { checkId: 'WCAG-037', checkClass: ResizeTextCheck, category: 'layout' },
        ],
      },
      {
        name: 'color-contrast-checks',
        checks: [
          { checkId: 'WCAG-004', checkClass: ContrastMinimumCheck, category: 'color' },
          { checkId: 'WCAG-012', checkClass: FocusAppearanceCheck, category: 'color' },
        ],
      },
      {
        name: 'focus-management-checks',
        checks: [
          { checkId: 'WCAG-007', checkClass: FocusVisibleCheck, category: 'focus' },
          { checkId: 'WCAG-010', checkClass: FocusNotObscuredMinimumCheck, category: 'focus' },
          { checkId: 'WCAG-011', checkClass: FocusNotObscuredEnhancedCheck, category: 'focus' },
          { checkId: 'WCAG-012', checkClass: FocusAppearanceCheck, category: 'focus' },
        ],
      },
      {
        name: 'layout-analysis-checks',
        checks: [
          { checkId: 'WCAG-014', checkClass: TargetSizeCheck, category: 'layout' },
          { checkId: 'WCAG-037', checkClass: ResizeTextCheck, category: 'layout' },
        ],
      },
    ];
  }

  /**
   * Create test configuration
   */
  private createTestConfig(): CheckConfig {
    return {
      url: 'https://example.com',
      enableUtilityIntegration: true,
      enableSmartCaching: true,
      enableEvidenceStandardization: true,
      performanceMode: 'balanced',
      timeout: 10000,
      retryAttempts: 2,
      enableDetailedLogging: this.config.enableDetailedLogging,
    };
  }

  /**
   * Log test results for a check
   */
  private logTestResults(checkId: string, results: IntegrationTestResult[]): void {
    const passed = results.filter((r) => r.passed).length;
    const total = results.length;

    if (passed === total) {
      logger.info(`✅ ${checkId}: All ${total} tests passed`);
    } else {
      logger.warn(`⚠️ ${checkId}: ${passed}/${total} tests passed`);

      const failed = results.filter((r) => !r.passed);
      failed.forEach((result) => {
        logger.warn(`   - ${result.testType}: ${result.errors.join(', ')}`);
      });
    }
  }

  /**
   * Save validation report to file
   */
  private async saveValidationReport(report: ValidationReport): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const reportPath = path.join(
        process.cwd(),
        'backend/src/compliance/wcag/reports',
        `integration-test-report-${Date.now()}.json`,
      );

      // Ensure directory exists
      await fs.mkdir(path.dirname(reportPath), { recursive: true });

      // Save report
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

      logger.info(`📊 Validation report saved: ${reportPath}`);
    } catch (error) {
      logger.error(`❌ Failed to save validation report: ${error}`);
    }
  }

  /**
   * Export test results to file
   */
  private async exportTestResults(): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const exportPath = path.join(
        process.cwd(),
        'backend/src/compliance/wcag/reports',
        `integration-test-results-${Date.now()}.json`,
      );

      // Ensure directory exists
      await fs.mkdir(path.dirname(exportPath), { recursive: true });

      // Export results
      const exportData = this.testFramework.exportTestResults();
      await fs.writeFile(exportPath, exportData);

      logger.info(`📁 Test results exported: ${exportPath}`);
    } catch (error) {
      logger.error(`❌ Failed to export test results: ${error}`);
    }
  }

  /**
   * Chunk array into smaller arrays
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Run tests for specific checks only
   */
  async runSpecificTests(checkIds: string[], page: Page): Promise<IntegrationTestResult[]> {
    logger.info(`🎯 Running integration tests for specific checks: ${checkIds.join(', ')}`);

    const allSuites = this.getTestSuites();
    const relevantChecks = allSuites
      .flatMap((suite) => suite.checks)
      .filter((check) => checkIds.includes(check.checkId));

    if (relevantChecks.length === 0) {
      logger.warn('⚠️ No matching checks found for the specified IDs');
      return [];
    }

    const results: IntegrationTestResult[] = [];
    const config = this.createTestConfig();

    for (const check of relevantChecks) {
      try {
        const checkResults = await this.testFramework.runIntegrationTests(
          check.checkClass,
          check.checkId,
          page,
          config,
        );
        results.push(...checkResults);

        if (this.config.enableDetailedLogging) {
          this.logTestResults(check.checkId, checkResults);
        }
      } catch (error) {
        logger.error(`❌ Test failed for ${check.checkId}: ${error}`);
      }
    }

    return results;
  }
}

export default WCAGIntegrationTestRunner;
