/**
 * Advanced Pattern Integration Utility
 * Provides easy integration of advanced pattern detection across all WCAG checks
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import {
  AdvancedPatternDetector,
  AdvancedPatternAnalysisReport,
} from './advanced-pattern-detector';
import { PatternRecognitionEngine, PatternRecognitionReport } from './pattern-recognition-engine';
import {
  AccessibilityPatternLibrary,
  PatternAnalysisReport,
} from './accessibility-pattern-library';
import { WcagEvidence } from '../types';

export interface AdvancedPatternIntegrationConfig {
  enableAdvancedPatternDetection: boolean;
  enablePatternRecognition: boolean;
  enableAccessibilityPatterns: boolean;
  enableCrossCheckAnalysis: boolean;
  maxExecutionTime: number;
  confidenceThreshold: number;
}

export interface IntegratedPatternAnalysis {
  basePatternReport: PatternAnalysisReport;
  advancedPatternReport?: AdvancedPatternAnalysisReport;
  recognitionReport?: PatternRecognitionReport;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
  overallScore: number;
  executionTime: number;
}

export interface PatternIntegrationMetrics {
  totalPatternsDetected: number;
  advancedPatternsDetected: number;
  recognizedPatterns: number;
  averageConfidence: number;
  criticalIssuesFound: number;
  recommendationsGenerated: number;
  processingTime: number;
}

/**
 * Advanced Pattern Integration Utility
 * Simplifies integration of all pattern detection capabilities
 */
export class AdvancedPatternIntegration {
  private static instance: AdvancedPatternIntegration;
  private advancedDetector: AdvancedPatternDetector;
  private recognitionEngine: PatternRecognitionEngine;
  private patternLibrary: AccessibilityPatternLibrary;

  private constructor() {
    this.advancedDetector = AdvancedPatternDetector.getInstance();
    this.recognitionEngine = PatternRecognitionEngine.getInstance();
    this.patternLibrary = AccessibilityPatternLibrary.getInstance();
  }

  static getInstance(): AdvancedPatternIntegration {
    if (!AdvancedPatternIntegration.instance) {
      AdvancedPatternIntegration.instance = new AdvancedPatternIntegration();
    }
    return AdvancedPatternIntegration.instance;
  }

  /**
   * Perform comprehensive pattern analysis for any WCAG check
   */
  async performIntegratedPatternAnalysis(
    page: Page,
    checkId: string,
    config: AdvancedPatternIntegrationConfig,
  ): Promise<IntegratedPatternAnalysis> {
    const startTime = Date.now();

    logger.info(`🎯 Starting integrated pattern analysis for ${checkId}`);

    try {
      // Base pattern analysis (always performed)
      const basePatternReport = await this.patternLibrary.analyzePatterns(page);

      // Advanced pattern detection (if enabled)
      let advancedPatternReport: AdvancedPatternAnalysisReport | undefined;
      if (config.enableAdvancedPatternDetection) {
        try {
          advancedPatternReport = await this.advancedDetector.performAdvancedPatternDetection(
            page,
            {
              confidenceThreshold: config.confidenceThreshold,
              maxAnalysisDepth: 5,
            },
          );
        } catch (error: unknown) {
          logger.warn(
            `Advanced pattern detection failed for ${checkId}:`,
            error as Record<string, unknown>,
          );
        }
      }

      // Pattern recognition (if enabled)
      let recognitionReport: PatternRecognitionReport | undefined;
      if (config.enablePatternRecognition) {
        try {
          recognitionReport = await this.recognitionEngine.recognizePatterns(page, {
            analysisTimeout: config.maxExecutionTime,
          });
        } catch (error: unknown) {
          logger.warn(
            `Pattern recognition failed for ${checkId}:`,
            error as Record<string, unknown>,
          );
        }
      }

      // Generate integrated analysis
      const analysis = this.generateIntegratedAnalysis(
        checkId,
        basePatternReport,
        advancedPatternReport,
        recognitionReport,
        startTime,
      );

      logger.info(`✅ Integrated pattern analysis completed for ${checkId}`, {
        totalPatterns: analysis.evidence.length,
        overallScore: analysis.overallScore,
        executionTime: analysis.executionTime,
      });

      return analysis;
    } catch (error: unknown) {
      logger.error(
        `❌ Integrated pattern analysis failed for ${checkId}:`,
        error as Record<string, unknown>,
      );
      throw error;
    }
  }

  /**
   * Generate evidence for WCAG checks from pattern analysis
   */
  generatePatternEvidence(checkId: string, analysis: IntegratedPatternAnalysis): WcagEvidence[] {
    const evidence: WcagEvidence[] = [];

    // Base pattern evidence
    evidence.push({
      type: 'info',
      description: `Base accessibility pattern analysis for ${checkId}`,
      element: 'base-patterns',
      value: JSON.stringify({
        totalPatterns: analysis.basePatternReport.totalPatterns,
        validPatterns: analysis.basePatternReport.validPatterns,
        overallScore: analysis.basePatternReport.overallScore,
        categoryBreakdown: analysis.basePatternReport.categoryBreakdown,
      }),
      severity: analysis.basePatternReport.overallScore >= 80 ? 'info' : 'warning',
    });

    // Advanced pattern evidence
    if (analysis.advancedPatternReport) {
      evidence.push({
        type: 'info',
        description: `Advanced pattern detection for ${checkId}`,
        element: 'advanced-patterns',
        value: JSON.stringify({
          semanticPatterns: analysis.advancedPatternReport.semanticPatterns.length,
          behavioralPatterns: analysis.advancedPatternReport.behavioralPatterns.length,
          crossElementPatterns: analysis.advancedPatternReport.crossElementPatterns.length,
          mlClassificationResults: analysis.advancedPatternReport.mlClassificationResults.length,
          advancedMetrics: analysis.advancedPatternReport.advancedMetrics,
        }),
        severity:
          analysis.advancedPatternReport.advancedMetrics.semanticAccuracy > 0.8
            ? 'info'
            : 'warning',
      });
    }

    // Pattern recognition evidence
    if (analysis.recognitionReport) {
      evidence.push({
        type: 'info',
        description: `Pattern recognition analysis for ${checkId}`,
        element: 'pattern-recognition',
        value: JSON.stringify({
          totalPatterns: analysis.recognitionReport.totalPatterns,
          patternCategories: analysis.recognitionReport.patternCategories,
          complianceBreakdown: analysis.recognitionReport.complianceBreakdown,
          overallScore: analysis.recognitionReport.overallScore,
          analysisMetrics: analysis.recognitionReport.analysisMetrics,
        }),
        severity: analysis.recognitionReport.overallScore >= 80 ? 'info' : 'warning',
      });
    }

    return evidence;
  }

  /**
   * Extract issues from pattern analysis
   */
  extractPatternIssues(analysis: IntegratedPatternAnalysis): string[] {
    const issues: string[] = [];

    // Base pattern issues
    if (analysis.basePatternReport.criticalIssues.length > 0) {
      issues.push(
        ...analysis.basePatternReport.criticalIssues.slice(0, 3).map((issue) => issue.message),
      );
    }

    // Advanced pattern issues
    if (analysis.advancedPatternReport) {
      // Extract issues from semantic patterns
      analysis.advancedPatternReport.semanticPatterns.forEach((pattern) => {
        if (pattern.confidence < 0.7) {
          issues.push(`Low confidence semantic pattern detected: ${pattern.pattern.name}`);
        }
      });

      // Extract issues from behavioral patterns
      analysis.advancedPatternReport.behavioralPatterns.forEach((pattern) => {
        if (pattern.compliance === 'fail') {
          issues.push(`Behavioral pattern failure: ${pattern.name}`);
        }
      });
    }

    // Recognition report issues
    if (analysis.recognitionReport) {
      issues.push(...analysis.recognitionReport.criticalIssues.slice(0, 3));
    }

    return issues;
  }

  /**
   * Extract recommendations from pattern analysis
   */
  extractPatternRecommendations(analysis: IntegratedPatternAnalysis): string[] {
    const recommendations: string[] = [];

    // Base pattern recommendations
    recommendations.push(...analysis.basePatternReport.recommendations.slice(0, 3));

    // Advanced pattern recommendations
    if (analysis.advancedPatternReport) {
      recommendations.push(...analysis.advancedPatternReport.recommendations.slice(0, 3));
    }

    // Recognition report recommendations
    if (analysis.recognitionReport) {
      recommendations.push(...analysis.recognitionReport.recommendations.slice(0, 3));
    }

    // Remove duplicates and limit
    return Array.from(new Set(recommendations)).slice(0, 10);
  }

  /**
   * Calculate pattern integration metrics
   */
  calculateIntegrationMetrics(analysis: IntegratedPatternAnalysis): PatternIntegrationMetrics {
    let totalPatternsDetected = analysis.basePatternReport.totalPatterns;
    let advancedPatternsDetected = 0;
    let recognizedPatterns = 0;
    let averageConfidence = 0;

    if (analysis.advancedPatternReport) {
      advancedPatternsDetected =
        analysis.advancedPatternReport.semanticPatterns.length +
        analysis.advancedPatternReport.behavioralPatterns.length +
        analysis.advancedPatternReport.crossElementPatterns.length;

      totalPatternsDetected += advancedPatternsDetected;
    }

    if (analysis.recognitionReport) {
      recognizedPatterns = analysis.recognitionReport.totalPatterns;
      averageConfidence = analysis.recognitionReport.analysisMetrics.accuracyScore / 100;
    }

    return {
      totalPatternsDetected,
      advancedPatternsDetected,
      recognizedPatterns,
      averageConfidence,
      criticalIssuesFound: analysis.issues.length,
      recommendationsGenerated: analysis.recommendations.length,
      processingTime: analysis.executionTime,
    };
  }

  /**
   * Generate integrated analysis from all pattern detection results
   */
  private generateIntegratedAnalysis(
    _checkId: string,
    baseReport: PatternAnalysisReport,
    advancedReport?: AdvancedPatternAnalysisReport,
    recognitionReport?: PatternRecognitionReport,
    startTime?: number,
  ): IntegratedPatternAnalysis {
    const executionTime = startTime ? Date.now() - startTime : 0;

    // Generate evidence
    const evidence: WcagEvidence[] = [];

    // Base evidence
    evidence.push({
      type: 'info',
      description: 'Base pattern analysis',
      element: 'patterns',
      value: JSON.stringify({
        totalPatterns: baseReport.totalPatterns,
        validPatterns: baseReport.validPatterns,
        overallScore: baseReport.overallScore,
      }),
      severity: baseReport.overallScore >= 80 ? 'info' : 'warning',
    });

    // Advanced evidence
    if (advancedReport) {
      evidence.push({
        type: 'info',
        description: 'Advanced pattern detection',
        element: 'advanced-patterns',
        value: JSON.stringify({
          semanticAccuracy: advancedReport.advancedMetrics.semanticAccuracy,
          behavioralCompliance: advancedReport.advancedMetrics.behavioralCompliance,
        }),
        severity: advancedReport.advancedMetrics.semanticAccuracy > 0.8 ? 'info' : 'warning',
      });
    }

    // Recognition evidence
    if (recognitionReport) {
      evidence.push({
        type: 'info',
        description: 'Pattern recognition',
        element: 'recognition',
        value: JSON.stringify({
          totalPatterns: recognitionReport.totalPatterns,
          overallScore: recognitionReport.overallScore,
        }),
        severity: recognitionReport.overallScore >= 80 ? 'info' : 'warning',
      });
    }

    // Extract issues and recommendations
    const issues: string[] = [];
    const recommendations: string[] = [];

    issues.push(...baseReport.criticalIssues.slice(0, 2).map((issue) => issue.message));
    recommendations.push(...baseReport.recommendations.slice(0, 3));

    if (advancedReport) {
      recommendations.push(...advancedReport.recommendations.slice(0, 2));
    }

    if (recognitionReport) {
      issues.push(...recognitionReport.criticalIssues.slice(0, 2));
      recommendations.push(...recognitionReport.recommendations.slice(0, 2));
    }

    // Calculate overall score
    let overallScore = baseReport.overallScore;
    let scoreCount = 1;

    if (advancedReport) {
      overallScore += advancedReport.advancedMetrics.semanticAccuracy * 100;
      scoreCount++;
    }

    if (recognitionReport) {
      overallScore += recognitionReport.overallScore;
      scoreCount++;
    }

    overallScore = overallScore / scoreCount;

    return {
      basePatternReport: baseReport,
      advancedPatternReport: advancedReport,
      recognitionReport: recognitionReport,
      evidence,
      issues: Array.from(new Set(issues)),
      recommendations: Array.from(new Set(recommendations)),
      overallScore,
      executionTime,
    };
  }

  /**
   * Get default configuration for pattern integration
   */
  static getDefaultConfig(): AdvancedPatternIntegrationConfig {
    return {
      enableAdvancedPatternDetection: true,
      enablePatternRecognition: true,
      enableAccessibilityPatterns: true,
      enableCrossCheckAnalysis: false,
      maxExecutionTime: 10000,
      confidenceThreshold: 0.7,
    };
  }

  /**
   * Create configuration for specific check types
   */
  static createConfigForCheckType(
    checkType: 'semantic' | 'interactive' | 'content' | 'form',
  ): AdvancedPatternIntegrationConfig {
    const baseConfig = AdvancedPatternIntegration.getDefaultConfig();

    switch (checkType) {
      case 'semantic':
        return {
          ...baseConfig,
          enableAdvancedPatternDetection: true,
          enablePatternRecognition: true,
          confidenceThreshold: 0.8,
        };

      case 'interactive':
        return {
          ...baseConfig,
          enableAdvancedPatternDetection: true,
          enablePatternRecognition: true,
          maxExecutionTime: 15000,
        };

      case 'content':
        return {
          ...baseConfig,
          enableAdvancedPatternDetection: true,
          confidenceThreshold: 0.6,
        };

      case 'form':
        return {
          ...baseConfig,
          enableAdvancedPatternDetection: true,
          enablePatternRecognition: true,
          enableCrossCheckAnalysis: true,
        };

      default:
        return baseConfig;
    }
  }
}
