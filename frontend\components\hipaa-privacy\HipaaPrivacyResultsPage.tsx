import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, CheckCircle, XCircle, Download, RefreshCw, Shield } from 'lucide-react';
import {
  HipaaPrivacyRiskLevel,
  HipaaPrivacyResultsPageProps,
  HipaaPrivacyFinding,
} from '@/types/hipaa-privacy';

export const HipaaPrivacyResultsPage: React.FC<HipaaPrivacyResultsPageProps> = ({
  scanResult,
  onExportReport,
  onStartNewScan,
}) => {
  const [activeTab, setActiveTab] = useState('summary');

  const getRiskLevelColor = (riskLevel: HipaaPrivacyRiskLevel | undefined): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskLevelIcon = (riskLevel: HipaaPrivacyRiskLevel | undefined) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Shield className="h-6 w-6 text-gray-500" />;
    }
  };

  // const failedChecks = scanResult.checks.filter(check => !check.passed);
  // const passedChecks = scanResult.checks.filter(check => check.passed);

  return (
    <div
      className="container mx-auto p-6 space-y-6"
      style={{ backgroundColor: '#F5F5F5', color: '#333333' }}
    >
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold mb-2" style={{ color: '#333333' }}>
            HIPAA Privacy Policy Compliance Results
          </h1>
          <p style={{ color: '#666666' }}>
            Scan completed for: <span className="font-medium">{scanResult.targetUrl}</span>
          </p>
          <p className="text-sm" style={{ color: '#999999' }}>
            Scanned on {new Date(scanResult.timestamp).toLocaleString()}
          </p>
        </div>
        <div className="flex gap-3">
          {onExportReport && (
            <Button
              onClick={onExportReport}
              variant="outline"
              className="flex items-center gap-2"
              style={{
                backgroundColor: 'white',
                borderColor: '#0055A4',
                color: '#0055A4',
              }}
            >
              <Download className="h-4 w-4" />
              Export Report
            </Button>
          )}
          {onStartNewScan && (
            <Button
              onClick={onStartNewScan}
              className="flex items-center gap-2"
              style={{
                backgroundColor: '#0055A4',
                color: 'white',
                border: 'none',
              }}
            >
              <RefreshCw className="h-4 w-4" />
              New Scan
            </Button>
          )}
        </div>
      </div>

      {/* Overall Score Card */}
      <Card style={{ backgroundColor: 'white', color: '#333333' }}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2" style={{ color: '#333333' }}>
            {getRiskLevelIcon(scanResult.summary.riskLevel)}
            Overall HIPAA Privacy Compliance Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex justify-between items-center mb-3">
                <span className="text-3xl font-bold" style={{ color: '#333333' }}>
                  {Math.round(scanResult.overallScore)}%
                </span>
                <Badge
                  className={`${getRiskLevelColor(scanResult.summary.riskLevel)} text-white px-3 py-1`}
                >
                  {scanResult.summary.riskLevel?.toUpperCase() || 'UNKNOWN'}
                </Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                <div
                  className="h-3 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.min(100, Math.max(0, scanResult.overallScore))}%`,
                    backgroundColor:
                      scanResult.overallScore >= 80
                        ? '#10B981'
                        : scanResult.overallScore >= 60
                          ? '#F59E0B'
                          : scanResult.overallScore >= 40
                            ? '#EF4444'
                            : '#DC2626',
                  }}
                />
              </div>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span style={{ color: '#666666' }}>Total Checks:</span>
                  <div className="font-semibold" style={{ color: '#333333' }}>
                    {scanResult.summary.totalChecks}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Passed:</span>
                  <div className="font-semibold text-green-600">
                    {scanResult.summary.passedChecks}
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Failed:</span>
                  <div className="font-semibold text-red-600">
                    {scanResult.summary.failedChecks}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList
          className="grid w-full grid-cols-4"
          style={{
            backgroundColor: 'white',
            border: '1px solid #E5E7EB',
            borderRadius: '8px',
          }}
        >
          <TabsTrigger
            value="summary"
            style={{
              backgroundColor: activeTab === 'summary' ? '#0055A4' : 'transparent',
              color: activeTab === 'summary' ? 'white' : '#333333',
            }}
          >
            Summary
          </TabsTrigger>
          <TabsTrigger
            value="checks"
            style={{
              backgroundColor: activeTab === 'checks' ? '#0055A4' : 'transparent',
              color: activeTab === 'checks' ? 'white' : '#333333',
            }}
          >
            Check Results
          </TabsTrigger>
          <TabsTrigger
            value="recommendations"
            style={{
              backgroundColor: activeTab === 'recommendations' ? '#0055A4' : 'transparent',
              color: activeTab === 'recommendations' ? 'white' : '#333333',
            }}
          >
            Recommendations
          </TabsTrigger>
          <TabsTrigger
            value="metadata"
            style={{
              backgroundColor: activeTab === 'metadata' ? '#0055A4' : 'transparent',
              color: activeTab === 'metadata' ? 'white' : '#333333',
            }}
          >
            Scan Details
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          <Card style={{ backgroundColor: 'white', color: '#333333' }}>
            <CardHeader>
              <CardTitle style={{ color: '#333333' }}>Compliance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2" style={{ color: '#333333' }}>
                    Analysis Levels Used
                  </h4>
                  <div className="space-y-1">
                    {scanResult.summary.analysisLevelsUsed.map((level) => (
                      <Badge
                        key={level}
                        variant="outline"
                        style={{ borderColor: '#0055A4', color: '#0055A4' }}
                      >
                        Level {level}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2" style={{ color: '#333333' }}>
                    Compliance Level
                  </h4>
                  <Badge
                    variant="outline"
                    style={{
                      borderColor:
                        scanResult.summary.complianceLevel === 'compliant' ? '#22C55E' : '#EF4444',
                      color:
                        scanResult.summary.complianceLevel === 'compliant' ? '#22C55E' : '#EF4444',
                    }}
                  >
                    {scanResult.summary.complianceLevel.replace('_', ' ').toUpperCase()}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="checks" className="space-y-4">
          <div className="grid gap-4">
            {scanResult.checks.map((check, index) => (
              <Card key={index} style={{ backgroundColor: 'white', color: '#333333' }}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2" style={{ color: '#333333' }}>
                    {check.passed ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    {check.checkName}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p style={{ color: '#666666' }}>{check.description}</p>
                  {check.findings && check.findings.length > 0 && (
                    <div className="mt-3">
                      <h5 className="font-semibold mb-2" style={{ color: '#333333' }}>
                        Findings:
                      </h5>
                      <ul className="list-disc list-inside space-y-1">
                        {check.findings.map((finding: HipaaPrivacyFinding, idx: number) => (
                          <li key={idx} className="text-sm" style={{ color: '#666666' }}>
                            {finding.message || ''}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <div className="grid gap-4">
            {scanResult.recommendations.map((rec, index) => (
              <Card key={index} style={{ backgroundColor: 'white', color: '#333333' }}>
                <CardHeader>
                  <CardTitle style={{ color: '#333333' }}>{rec.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p style={{ color: '#666666' }}>{rec.description}</p>
                  <div className="mt-3 flex gap-2">
                    <Badge variant="outline" style={{ borderColor: '#663399', color: '#663399' }}>
                      Priority: {rec.priority}
                    </Badge>
                    <Badge variant="outline" style={{ borderColor: '#0055A4', color: '#0055A4' }}>
                      Effort: {rec.effort}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="metadata" className="space-y-4">
          <Card style={{ backgroundColor: 'white', color: '#333333' }}>
            <CardHeader>
              <CardTitle style={{ color: '#333333' }}>Scan Metadata</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span style={{ color: '#666666' }}>Processing Time:</span>
                  <div className="font-semibold" style={{ color: '#333333' }}>
                    {scanResult.metadata.processingTime}ms
                  </div>
                </div>
                <div>
                  <span style={{ color: '#666666' }}>Scanner Version:</span>
                  <div className="font-semibold" style={{ color: '#333333' }}>
                    {scanResult.metadata.scanVersion}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
