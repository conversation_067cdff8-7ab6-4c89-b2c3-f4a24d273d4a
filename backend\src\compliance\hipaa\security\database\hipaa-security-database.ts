import { Pool, PoolClient } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import { env } from '../../../../../../lib/env';
import {
  HipaaSecurityScanConfig,
  HipaaSecurityScanResult,
  HipaaTestDetail,
  HipaaTestFailure,
  FailureEvidence,
  VulnerabilityResult,
  CategoryResult,
  ScanStatus,
} from '../types';

export class HipaaSecurityDatabase {
  private pool: Pool;

  constructor() {
    console.log('🔧 Initializing HIPAA Security Database...');
    console.log('📋 Database URL:', env.DATABASE_URL);
    console.log('📋 Environment:', env.NODE_ENV);

    this.pool = new Pool({
      connectionString: env.DATABASE_URL,
      ssl: env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
  }

  async testConnection(): Promise<boolean> {
    try {
      console.log('🔌 Testing database connection...');
      console.log('📋 Connection string (masked):', env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@'));

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Database connection timeout after 10 seconds')), 10000);
      });

      const queryPromise = this.pool.query('SELECT 1 as test');

      const result = await Promise.race([queryPromise, timeoutPromise]);
      console.log('✅ Database connection successful');
      console.log('📋 Test result:', result.rows[0]);
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: (error as { code?: string })?.code,
        errno: (error as { errno?: number })?.errno,
        syscall: (error as { syscall?: string })?.syscall,
        hostname: (error as { hostname?: string })?.hostname,
        port: (error as { port?: number })?.port,
      });
      return false;
    }
  }

  async checkTableExists(): Promise<boolean> {
    try {
      console.log('🗄️ Checking if hipaa_security_scans table exists...');

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Table check timeout after 10 seconds')), 10000);
      });

      const queryPromise = this.pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'hipaa_security_scans'
        );
      `);

      const result = await Promise.race([queryPromise, timeoutPromise]);
      const exists = result.rows[0].exists;
      console.log(`📋 Table exists: ${exists}`);
      return exists;
    } catch (error) {
      console.error('❌ Error checking table existence:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: (error as { code?: string })?.code,
      });
      return false;
    }
  }

  async createScan(config: HipaaSecurityScanConfig): Promise<string> {
    const scanId = uuidv4();
    const query = `
      INSERT INTO hipaa_security_scans (
        id, target_url, scan_status, created_at
      ) VALUES ($1, $2, $3, NOW())
      RETURNING id
    `;

    try {
      console.log('🗄️ Creating scan record in database...');
      console.log('📋 Scan ID:', scanId);
      console.log('📋 Target URL:', config.targetUrl);
      console.log('📋 SQL Query:', query);

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Create scan timeout after 15 seconds')), 15000);
      });

      const queryPromise = this.pool.query(query, [scanId, config.targetUrl, 'pending']);

      const result = await Promise.race([queryPromise, timeoutPromise]);
      console.log('✅ Scan record created successfully');
      return result.rows[0].id;
    } catch (error) {
      console.error('❌ Database error details:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        code: (error as { code?: string })?.code,
        detail: (error as { detail?: string })?.detail,
        hint: (error as { hint?: string })?.hint,
        position: (error as { position?: number })?.position,
        scanId,
        targetUrl: config.targetUrl,
      });

      throw new Error(
        `Failed to create scan record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async updateScanStatus(scanId: string, status: ScanStatus, errorMessage?: string): Promise<void> {
    const query = `
      UPDATE hipaa_security_scans 
      SET scan_status = $1, error_message = $2, updated_at = NOW()
      WHERE id = $3
    `;

    try {
      await this.pool.query(query, [status, errorMessage || null, scanId]);
    } catch (error) {
      throw new Error(
        `Failed to update scan status: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async saveScanResults(result: HipaaSecurityScanResult): Promise<void> {
    console.log('💾 Saving scan results to database...');
    console.log('📋 Scan data types:', {
      scanId: typeof result.scanId,
      scanDuration: typeof result.scanDuration,
      overallScore: typeof result.overallScore,
      riskLevel: typeof result.riskLevel,
      pagesScanned: typeof result.pagesScanned,
      pagesScannedIsArray: Array.isArray(result.pagesScanned),
      pagesScannedLength: result.pagesScanned?.length,
      toolsUsed: typeof result.toolsUsed,
      toolsUsedIsArray: Array.isArray(result.toolsUsed),
      toolsUsedLength: result.toolsUsed?.length,
      passedTestsCount: result.passedTests?.length,
      failedTestsCount: result.failedTests?.length,
      vulnerabilitiesCount: result.vulnerabilities?.length,
    });

    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Update main scan record
      const updateScanQuery = `
        UPDATE hipaa_security_scans 
        SET 
          scan_duration = $1,
          overall_score = $2,
          risk_level = $3,
          pages_scanned = $4,
          tools_used = $5,
          updated_at = NOW()
        WHERE id = $6
      `;

      await client.query(updateScanQuery, [
        result.scanDuration,
        result.overallScore,
        result.riskLevel,
        Array.isArray(result.pagesScanned) ? result.pagesScanned : [],
        Array.isArray(result.toolsUsed) ? result.toolsUsed : [],
        result.scanId,
      ]);

      // Save test results with improved error handling
      console.log('📝 Saving test results...');
      const allTests = [...(result.passedTests || []), ...(result.failedTests || [])];
      console.log(`📊 Total tests to save: ${allTests.length}`);

      let savedTests = 0;
      let failedTests = 0;

      for (const test of allTests) {
        try {
          // Create a savepoint for each test to allow individual rollbacks
          await client.query('SAVEPOINT test_save');
          await this.saveTestResult(client, result.scanId, test);
          await client.query('RELEASE SAVEPOINT test_save');
          savedTests++;
        } catch (error) {
          failedTests++;
          // Rollback to savepoint to continue with other tests
          await client.query('ROLLBACK TO SAVEPOINT test_save');
          console.error(
            `❌ Failed to save test result ${test.testId}:`,
            error instanceof Error ? error.message : 'Unknown error',
          );
          console.error('📋 Test data:', {
            testId: test.testId,
            testName: test.testName,
            passed: test.passed,
            category: test.category,
          });
          // Continue with other tests instead of failing completely
        }
      }

      console.log(`📊 Test results saved: ${savedTests} successful, ${failedTests} failed`);

      // Save vulnerabilities with improved error handling
      console.log('🔍 Saving vulnerabilities...');
      const vulnerabilities = result.vulnerabilities || [];
      console.log(`📊 Total vulnerabilities to save: ${vulnerabilities.length}`);

      let savedVulns = 0;
      let failedVulns = 0;

      for (const vulnerability of vulnerabilities) {
        try {
          // Create a savepoint for each vulnerability to allow individual rollbacks
          await client.query('SAVEPOINT vuln_save');
          await this.saveVulnerability(client, result.scanId, vulnerability);
          await client.query('RELEASE SAVEPOINT vuln_save');
          savedVulns++;
        } catch (error) {
          failedVulns++;
          // Rollback to savepoint to continue with other vulnerabilities
          await client.query('ROLLBACK TO SAVEPOINT vuln_save');
          console.error(
            `❌ Failed to save vulnerability ${vulnerability.type}:`,
            error instanceof Error ? error.message : 'Unknown error',
          );
          // Continue with other vulnerabilities instead of failing completely
        }
      }

      console.log(`📊 Vulnerabilities saved: ${savedVulns} successful, ${failedVulns} failed`);

      await client.query('COMMIT');
      console.log('✅ Scan results saved successfully');

      // Log summary of any failures
      if (failedTests > 0 || failedVulns > 0) {
        console.warn(
          `⚠️ Some data failed to save: ${failedTests} test results, ${failedVulns} vulnerabilities`,
        );
      }
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('❌ Failed to save scan results:', error);
      throw new Error(
        `Failed to save scan results: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    } finally {
      client.release();
    }
  }

  private async saveTestResult(
    client: PoolClient,
    scanId: string,
    test: HipaaTestDetail | HipaaTestFailure,
  ): Promise<void> {
    console.log('📝 Saving test result:', {
      testId: test.testId,
      testName: test.testName,
      passed: test.passed,
      evidenceType: test.passed ? typeof (test as HipaaTestDetail).evidence : 'N/A',
      pagesTestedType: test.passed ? typeof (test as HipaaTestDetail).pagesTested : 'N/A',
      pagesTestedIsArray: test.passed
        ? Array.isArray((test as HipaaTestDetail).pagesTested)
        : 'N/A',
    });

    // Validate and sanitize data
    const sanitizedTest = this.sanitizeTestData(test);

    const testResultQuery = `
      INSERT INTO hipaa_security_test_results (
        scan_id, test_id, test_name, hipaa_section, category, passed,
        risk_level, description, failure_reason, evidence, pages_tested,
        remediation_priority, recommended_action
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING id
    `;

    const testResultValues = [
      scanId,
      sanitizedTest.testId,
      sanitizedTest.testName,
      sanitizedTest.hipaaSection,
      sanitizedTest.category,
      sanitizedTest.passed,
      sanitizedTest.passed ? null : (sanitizedTest as HipaaTestFailure).riskLevel,
      sanitizedTest.description,
      sanitizedTest.passed ? null : (sanitizedTest as HipaaTestFailure).failureReason,
      sanitizedTest.passed
        ? JSON.stringify((sanitizedTest as HipaaTestDetail).evidence || {})
        : null,
      sanitizedTest.passed ? (sanitizedTest as HipaaTestDetail).pagesTested || [] : null,
      sanitizedTest.passed ? null : (sanitizedTest as HipaaTestFailure).remediationPriority,
      sanitizedTest.passed ? null : (sanitizedTest as HipaaTestFailure).recommendedAction,
    ];

    const testResult = await client.query(testResultQuery, testResultValues);
    const testResultId = testResult.rows[0].id;

    // Save failure evidence if test failed
    if (!sanitizedTest.passed) {
      const failedTest = sanitizedTest as HipaaTestFailure;
      for (const evidence of failedTest.failureEvidence || []) {
        await this.saveFailureEvidence(client, testResultId, evidence);
      }
    }
  }

  private sanitizeTestData(
    test: HipaaTestDetail | HipaaTestFailure,
  ): HipaaTestDetail | HipaaTestFailure {
    // Ensure required fields are strings and not null/undefined
    const sanitized = {
      ...test,
      testId: String(test.testId || ''),
      testName: String(test.testName || ''),
      hipaaSection: String(test.hipaaSection || ''),
      category: test.category || 'technical',
      description: String(test.description || ''),
    };

    if (test.passed) {
      const passedTest = test as HipaaTestDetail;
      return {
        ...sanitized,
        passed: true,
        evidence: passedTest.evidence || '',
        pagesTested: Array.isArray(passedTest.pagesTested) ? passedTest.pagesTested : [],
        timestamp: passedTest.timestamp || new Date(),
      } as HipaaTestDetail;
    } else {
      const failedTest = test as HipaaTestFailure;
      return {
        ...sanitized,
        passed: false,
        failureReason: String(failedTest.failureReason || ''),
        riskLevel: failedTest.riskLevel || 'medium',
        failureEvidence: Array.isArray(failedTest.failureEvidence)
          ? failedTest.failureEvidence
          : [],
        recommendedAction: String(failedTest.recommendedAction || ''),
        remediationPriority: Number(failedTest.remediationPriority) || 3,
        timestamp: failedTest.timestamp || new Date(),
      } as HipaaTestFailure;
    }
  }

  private async saveFailureEvidence(
    client: PoolClient,
    testResultId: string,
    evidence: FailureEvidence,
  ): Promise<void> {
    const evidenceQuery = `
      INSERT INTO hipaa_security_failure_evidence (
        test_result_id, location, element_type, actual_code,
        expected_behavior, line_number, context
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;

    await client.query(evidenceQuery, [
      testResultId,
      evidence.location,
      evidence.elementType,
      evidence.actualCode,
      evidence.expectedBehavior,
      evidence.lineNumber || null,
      evidence.context,
    ]);
  }

  private async saveVulnerability(
    client: PoolClient,
    scanId: string,
    vulnerability: VulnerabilityResult,
  ): Promise<void> {
    const vulnQuery = `
      INSERT INTO hipaa_security_vulnerabilities (
        scan_id, vulnerability_type, severity, location, description,
        evidence, cwe_id, owasp_category, remediation_guidance
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;

    console.log('🔍 Saving vulnerability:', {
      type: vulnerability.type,
      severity: vulnerability.severity,
      evidenceType: typeof vulnerability.evidence,
      evidenceValue: vulnerability.evidence,
    });

    await client.query(vulnQuery, [
      scanId,
      vulnerability.type,
      vulnerability.severity,
      vulnerability.location,
      vulnerability.description,
      JSON.stringify(vulnerability.evidence || {}),
      vulnerability.cweId || null,
      vulnerability.owaspCategory || null,
      vulnerability.remediationGuidance,
    ]);
  }

  async getScanResult(scanId: string): Promise<HipaaSecurityScanResult | null> {
    try {
      // Get main scan record
      const scanQuery = `
        SELECT * FROM hipaa_security_scans WHERE id = $1
      `;
      const scanResult = await this.pool.query(scanQuery, [scanId]);

      if (scanResult.rows.length === 0) {
        return null;
      }

      const scan = scanResult.rows[0];

      // Get test results
      const testQuery = `
        SELECT * FROM hipaa_security_test_results WHERE scan_id = $1
      `;
      const testResults = await this.pool.query(testQuery, [scanId]);

      // Get vulnerabilities
      const vulnQuery = `
        SELECT * FROM hipaa_security_vulnerabilities WHERE scan_id = $1
      `;
      const vulnResults = await this.pool.query(vulnQuery, [scanId]);

      // Build result object (simplified version)
      const result: HipaaSecurityScanResult = {
        scanId: scan.id,
        targetUrl: scan.target_url,
        scanTimestamp: scan.scan_timestamp,
        scanDuration: scan.scan_duration || 0,
        overallScore: parseFloat(scan.overall_score) || 0,
        riskLevel: scan.risk_level || 'low',
        passedTests: testResults.rows
          .filter((t) => t.passed)
          .map(this.mapTestResult)
          .filter((test): test is HipaaTestDetail => test.passed),
        failedTests: testResults.rows
          .filter((t) => !t.passed)
          .map(this.mapTestResult)
          .filter((test): test is HipaaTestFailure => !test.passed),
        technicalSafeguards: this.buildEmptyCategoryResult('technical'),
        administrativeSafeguards: this.buildEmptyCategoryResult('administrative'),
        organizationalSafeguards: this.buildEmptyCategoryResult('organizational'),
        physicalSafeguards: this.buildEmptyCategoryResult('physical'),
        vulnerabilities: vulnResults.rows.map(this.mapVulnerability),
        pagesScanned: scan.pages_scanned || [],
        toolsUsed: scan.tools_used || [],
        scanStatus: scan.scan_status || 'completed',
        errorMessage: scan.error_message,
      };

      return result;
    } catch (error) {
      throw new Error(
        `Failed to get scan result: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async getAllScans(limit: number): Promise<HipaaSecurityScanResult[]> {
    try {
      const query = `
        SELECT * FROM hipaa_security_scans 
        ORDER BY created_at DESC 
        LIMIT $1
      `;
      const result = await this.pool.query(query, [limit]);

      // For simplicity, return basic scan info without full details
      return result.rows.map((scan) => ({
        scanId: scan.id,
        targetUrl: scan.target_url,
        scanTimestamp: scan.scan_timestamp,
        scanDuration: scan.scan_duration || 0,
        overallScore: parseFloat(scan.overall_score) || 0,
        riskLevel: scan.risk_level || 'low',
        passedTests: [],
        failedTests: [],
        technicalSafeguards: this.buildEmptyCategoryResult('technical'),
        administrativeSafeguards: this.buildEmptyCategoryResult('administrative'),
        organizationalSafeguards: this.buildEmptyCategoryResult('organizational'),
        physicalSafeguards: this.buildEmptyCategoryResult('physical'),
        vulnerabilities: [],
        pagesScanned: scan.pages_scanned || [],
        toolsUsed: scan.tools_used || [],
        scanStatus: scan.scan_status || 'completed',
        errorMessage: scan.error_message,
      }));
    } catch (error) {
      throw new Error(
        `Failed to get all scans: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async deleteScan(scanId: string): Promise<boolean> {
    const query = 'DELETE FROM hipaa_security_scans WHERE id = $1';

    try {
      const result = await this.pool.query(query, [scanId]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      throw new Error(
        `Failed to delete scan: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  async close(): Promise<void> {
    await this.pool.end();
  }

  private mapTestResult(row: {
    test_id: string;
    test_name: string;
    hipaa_section: string;
    description: string;
    category: string;
    passed: boolean;
    created_at: Date;
    evidence?: string;
    pages_tested?: string[];
    failure_reason?: string;
    risk_level?: string;
    recommended_action?: string;
    remediation_priority?: number;
  }): HipaaTestDetail | HipaaTestFailure {
    const base = {
      testId: row.test_id,
      testName: row.test_name,
      hipaaSection: row.hipaa_section,
      description: row.description,
      category: row.category as 'technical' | 'administrative' | 'organizational' | 'physical',
      passed: row.passed,
      timestamp: row.created_at,
    };

    if (row.passed) {
      return {
        ...base,
        passed: true,
        evidence: row.evidence || '',
        pagesTested: row.pages_tested || [],
      } as HipaaTestDetail;
    } else {
      return {
        ...base,
        passed: false,
        failureReason: row.failure_reason || '',
        riskLevel: (row.risk_level as 'critical' | 'high' | 'medium' | 'low') || 'medium',
        failureEvidence: [], // Would need additional query to get evidence
        recommendedAction: row.recommended_action || '',
        remediationPriority: row.remediation_priority || 3,
      } as HipaaTestFailure;
    }
  }

  private parseJsonSafely<T>(jsonString: unknown, fallback: T): T {
    if (typeof jsonString !== 'string') {
      return (jsonString as T) || fallback;
    }

    if (!jsonString || jsonString.trim() === '') {
      return fallback;
    }

    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.warn(
        `Failed to parse JSON in security database: ${jsonString.substring(0, 100)}...`,
        error,
      );
      return fallback;
    }
  }

  private mapVulnerability(row: {
    id: string;
    vulnerability_type: string;
    severity: string;
    location: string;
    description: string;
    evidence: string;
    cwe_id?: number;
    owasp_category?: string;
    remediation_guidance: string;
  }): VulnerabilityResult {
    return {
      id: row.id,
      type: row.vulnerability_type,
      severity: row.severity as 'critical' | 'high' | 'medium' | 'low' | 'info',
      location: row.location,
      description: row.description,
      evidence: this.parseJsonSafely<Record<string, unknown>>(row.evidence, {}),
      cweId: row.cwe_id,
      owaspCategory: row.owasp_category,
      remediationGuidance: row.remediation_guidance,
    };
  }

  private buildEmptyCategoryResult(category: string): CategoryResult {
    return {
      category: category as 'technical' | 'administrative' | 'organizational' | 'physical',
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      score: 0,
      riskLevel: 'low',
      criticalIssues: 0,
      highIssues: 0,
      mediumIssues: 0,
      lowIssues: 0,
    };
  }
}
