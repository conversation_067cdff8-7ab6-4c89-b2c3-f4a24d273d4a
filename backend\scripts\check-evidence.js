const db = require('knex')({
  client: 'pg',
  connection: {
    host: 'localhost',
    port: 5432,
    user: 'complyuser',
    password: 'complypassword',
    database: 'complychecker_dev'
  }
});

async function checkEvidence() {
  try {
    // First check what columns exist
    const columns = await db('wcag_automated_results').columnInfo();
    console.log('Available columns:', Object.keys(columns));

    const results = await db('wcag_automated_results')
      .where('scan_id', 'db252a46-cb51-486d-8b41-f8f5e08cc037')
      .limit(3)
      .select('rule_id', 'evidence', 'total_element_count', 'affected_selectors');
    
    console.log('=== EVIDENCE CHECK ===');
    results.forEach(result => {
      console.log(`Rule: ${result.rule_id}`);
      console.log(`Evidence column: ${result.evidence ? 'EXISTS' : 'NULL'}`);
      console.log(`Total elements: ${result.total_element_count}`);
      console.log(`Affected selectors: ${result.affected_selectors}`);
      
      if (result.evidence) {
        try {
          const evidenceData = typeof result.evidence === 'string' ? JSON.parse(result.evidence) : result.evidence;
          console.log(`Evidence items: ${Array.isArray(evidenceData) ? evidenceData.length : 'Not array'}`);
          if (Array.isArray(evidenceData) && evidenceData.length > 0) {
            console.log(`First evidence: ${evidenceData[0].description}`);
          }
        } catch (e) {
          console.log(`Evidence parse error: ${e.message}`);
        }
      }
      

      console.log('---');
    });
    
    await db.destroy();
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkEvidence();
