{"data": {"ruleId": "WCAG-008", "ruleName": "Error Identification", "category": "understandable", "wcagVersion": "2.2", "successCriterion": "0.0.0", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.1, "automated": false, "evidence": [{"type": "text", "description": "Error identification analysis summary", "value": "0/0 checks pass automated tests, 0 require manual review", "severity": "info", "elementCount": 1, "affectedSelectors": ["checks", "pass", "automated", "tests", "require", "manual", "review"], "metadata": {"scanDuration": 139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-008", "ruleName": "Error Identification", "timestamp": "2025-07-13T03:28:17.021Z"}}}, {"type": "text", "description": "No testable forms found on page", "value": "No forms with required fields and submit buttons detected", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "forms", "with", "required", "fields", "and", "submit", "buttons", "detected"], "metadata": {"scanDuration": 139, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-008", "ruleName": "Error Identification", "timestamp": "2025-07-13T03:28:17.021Z"}}}], "recommendations": ["Manual form submission testing required"], "executionTime": 0, "errorMessage": "", "manualReviewItems": []}, "timestamp": 1752377297021, "hash": "53851c16c41eb2fbfe774eea56e1c79e", "accessCount": 1, "lastAccessed": 1752377297021, "size": 1353, "metadata": {"originalKey": "WCAG-008:053b13d2:add92319", "normalizedKey": "wcag-008_053b13d2_add92319", "savedAt": 1752377297021, "version": "1.1", "keyHash": "17e43ddaa0c3a68157ed0096595a1292"}}