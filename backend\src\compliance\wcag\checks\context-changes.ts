/**
 * WCAG-064: Context Changes Check (3.2.5 Level AAA)
 * 70% Automated - Detects unexpected context changes
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

interface ContextChangeDetection {
  unexpectedChanges: number;
  userControlledChanges: number;
  automaticChanges: number;
  warningMechanisms: number;
  changeTypes: string[];
  riskLevel: 'none' | 'low' | 'medium' | 'high';
  detectionConfidence: number;
}

interface UserControlMechanismValidation {
  hasUserControl: boolean;
  hasWarningSystem: boolean;
  hasConfirmationDialogs: boolean;
  hasDisableOption: boolean;
  controlTypes: string[];
  adequateControl: boolean;
}

interface UnexpectedChangeAnalysis {
  autoRedirects: number;
  focusChanges: number;
  formAutoSubmissions: number;
  selectAutoNavigation: number;
  popupWindows: number;
  unexpectedScore: number;
  riskAssessment: 'low' | 'medium' | 'high';
}

interface AnalysisResult {
  totalChecks: number;
  passedChecks: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
}

export interface ContextChangesConfig extends EnhancedCheckConfig {
  enableContextChangeDetection?: boolean;
  enableUserControlValidation?: boolean;
  enableUnexpectedChangeAnalysis?: boolean;
  enableAccessibilityControlTesting?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
  enableComponentLibraryDetection?: boolean;
}

export class ContextChangesCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();

  async performCheck(config: ContextChangesConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized context change detection
    const enhancedConfig: ContextChangesConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000, // Target: <2s performance
      },
      enableContextChangeDetection: true,
      enableUserControlValidation: true,
      enableUnexpectedChangeAnalysis: true,
      enableAccessibilityControlTesting: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
      enableComponentLibraryDetection: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-064',
      'Change on Request',
      'predictable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeContextChangesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with context change analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-064',
        ruleName: 'Change on Request',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.7,
          checkType: 'context-change-analysis',
          behaviorAnalysis: true,
          userInitiatedChanges: true,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
          componentLibraryDetection: enhancedConfig.enableComponentLibraryDetection,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 20,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeContextChangesCheck(
    page: Page,
    _config: ContextChangesConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Context Change Detection Algorithm - Advanced Implementation
    const contextChangeDetection = await this.executeContextChangeDetection(page);

    // User Control Mechanism Validation Algorithm
    const userControlValidation = await this.validateUserControlMechanisms(page);

    // Unexpected Change Analysis Algorithm
    const unexpectedChangeAnalysis = await this.analyzeUnexpectedChanges(page);

    // Accessibility Control Testing Algorithm
    const accessibilityControlTesting = await this.testAccessibilityControls(page);

    // Combine all specialized detection results
    const allAnalyses = [
      contextChangeDetection,
      userControlValidation,
      unexpectedChangeAnalysis,
      accessibilityControlTesting,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 80% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Context Change Detection Algorithm - Core Implementation
   * Target: 80% context change validation accuracy
   */
  private async executeContextChangeDetection(page: Page): Promise<AnalysisResult> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const contextChangeDetection = await page.evaluate((): ContextChangeDetection => {
      // Advanced context change detection
      let unexpectedChanges = 0;
      let userControlledChanges = 0;
      let automaticChanges = 0;
      let warningMechanisms = 0;
      const changeTypes: string[] = [];

      // Analyze meta refresh
      const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
      metaRefresh.forEach((meta) => {
        const content = meta.getAttribute('content') || '';
        const hasUrl = content.includes('url=');
        if (hasUrl) {
          automaticChanges++;
          changeTypes.push('meta-refresh');
        }
      });

      // Analyze form elements with onchange navigation
      const autoNavigateSelects = document.querySelectorAll('select[onchange]');
      autoNavigateSelects.forEach((select) => {
        const onchange = select.getAttribute('onchange') || '';
        if (
          onchange.includes('location') ||
          onchange.includes('submit') ||
          onchange.includes('navigate')
        ) {
          automaticChanges++;
          changeTypes.push('select-auto-navigate');
        }
      });

      // Analyze links that open new windows
      const newWindowLinks = document.querySelectorAll('a[target="_blank"], a[target="_new"]');
      newWindowLinks.forEach((link) => {
        const hasWarning =
          link.textContent?.includes('new window') ||
          link.textContent?.includes('opens in') ||
          link.getAttribute('aria-label')?.includes('new window') ||
          link.getAttribute('title')?.includes('new window');

        if (hasWarning) {
          userControlledChanges++;
          warningMechanisms++;
        } else {
          unexpectedChanges++;
        }
        changeTypes.push('new-window');
      });

      // Analyze JavaScript navigation
      const jsNavigationElements = document.querySelectorAll(
        '[onclick*="location"], [onclick*="window"]',
      );
      jsNavigationElements.forEach((element) => {
        const onclick = element.getAttribute('onclick') || '';
        const hasConfirmation = onclick.includes('confirm') || onclick.includes('alert');

        if (hasConfirmation) {
          userControlledChanges++;
          warningMechanisms++;
        } else {
          unexpectedChanges++;
        }
        changeTypes.push('js-navigation');
      });

      // Analyze focus-triggered changes
      const focusChangeElements = document.querySelectorAll(
        '[onfocus*="location"], [onblur*="location"]',
      );
      focusChangeElements.forEach(() => {
        unexpectedChanges++;
        changeTypes.push('focus-change');
      });

      // Calculate risk level
      const totalChanges = unexpectedChanges + userControlledChanges + automaticChanges;
      const unexpectedRatio = totalChanges > 0 ? unexpectedChanges / totalChanges : 0;

      let riskLevel: ContextChangeDetection['riskLevel'] = 'none';
      let detectionConfidence = 0.8;

      if (totalChanges === 0) {
        riskLevel = 'none';
        detectionConfidence = 0.9;
      } else if (unexpectedRatio <= 0.2) {
        riskLevel = 'low';
        detectionConfidence = 0.85;
      } else if (unexpectedRatio <= 0.5) {
        riskLevel = 'medium';
        detectionConfidence = 0.8;
      } else {
        riskLevel = 'high';
        detectionConfidence = 0.75;
      }

      return {
        unexpectedChanges,
        userControlledChanges,
        automaticChanges,
        warningMechanisms,
        changeTypes: Array.from(new Set(changeTypes)),
        riskLevel,
        detectionConfidence,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (contextChangeDetection.riskLevel === 'none' || contextChangeDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Context change detection: Low or no unexpected context changes',
        value: `Risk level: ${contextChangeDetection.riskLevel}, Confidence: ${(contextChangeDetection.detectionConfidence * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Context change risk: ${contextChangeDetection.riskLevel} risk detected`);
      evidence.push({
        type: 'code',
        description: `Context change detection: ${contextChangeDetection.riskLevel} risk`,
        value: `Unexpected: ${contextChangeDetection.unexpectedChanges}, User-controlled: ${contextChangeDetection.userControlledChanges}, Automatic: ${contextChangeDetection.automaticChanges}, Warnings: ${contextChangeDetection.warningMechanisms}`,
        severity: contextChangeDetection.riskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Add user control and warnings for context changes');
    }

    // Report detected change types
    if (contextChangeDetection.changeTypes.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Detected context change types',
        value: contextChangeDetection.changeTypes.join(', '),
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * User Control Mechanism Validation Algorithm
   */
  private async validateUserControlMechanisms(page: Page): Promise<AnalysisResult> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const userControlValidation = await page.evaluate((): UserControlMechanismValidation => {
      // Check for user control mechanisms
      const confirmationDialogs = document.querySelectorAll(
        '[onclick*="confirm"], [onclick*="alert"], .confirm-dialog, .confirmation',
      );

      const warningElements = document.querySelectorAll(
        '.warning, .notice, [data-warning], [aria-label*="warning"], [title*="warning"]',
      );

      const disableOptions = document.querySelectorAll(
        '.disable-redirect, .no-redirect, [data-disable-redirect], .stop-auto',
      );

      const userControlElements = document.querySelectorAll(
        '.user-control, .manual-control, [data-user-control], .opt-out',
      );

      const hasUserControl = userControlElements.length > 0 || confirmationDialogs.length > 0;
      const hasWarningSystem = warningElements.length > 0;
      const hasConfirmationDialogs = confirmationDialogs.length > 0;
      const hasDisableOption = disableOptions.length > 0;

      const controlTypes: string[] = [];
      if (hasConfirmationDialogs) controlTypes.push('confirmation');
      if (hasWarningSystem) controlTypes.push('warning');
      if (hasDisableOption) controlTypes.push('disable');
      if (hasUserControl) controlTypes.push('user-control');

      // Check for adequate control (at least 2 mechanisms)
      const adequateControl = controlTypes.length >= 2;

      return {
        hasUserControl,
        hasWarningSystem,
        hasConfirmationDialogs,
        hasDisableOption,
        controlTypes,
        adequateControl,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (userControlValidation.adequateControl) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'User control validation: Adequate control mechanisms available',
        value: `Control types: ${userControlValidation.controlTypes.join(', ')}`,
        severity: 'info',
      });
    } else {
      issues.push('Insufficient user control mechanisms for context changes');
      evidence.push({
        type: 'code',
        description: 'User control validation: Inadequate control mechanisms',
        value: `Available controls: ${userControlValidation.controlTypes.join(', ') || 'none'} (need at least 2)`,
        severity: 'error',
      });
      recommendations.push(
        'Add user control mechanisms: confirmations, warnings, or disable options',
      );
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Unexpected Change Analysis Algorithm
   */
  private async analyzeUnexpectedChanges(page: Page): Promise<AnalysisResult> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const unexpectedAnalysis = await page.evaluate((): UnexpectedChangeAnalysis => {
      // Count different types of unexpected changes
      let autoRedirects = 0;
      let focusChanges = 0;
      let formAutoSubmissions = 0;
      let selectAutoNavigation = 0;
      let popupWindows = 0;

      // Auto-redirects (meta refresh)
      const metaRefresh = document.querySelectorAll('meta[http-equiv="refresh"]');
      metaRefresh.forEach((meta) => {
        const content = meta.getAttribute('content') || '';
        if (content.includes('url=')) {
          autoRedirects++;
        }
      });

      // Focus changes that trigger navigation
      const focusElements = document.querySelectorAll('[onfocus], [onblur]');
      focusElements.forEach((element) => {
        const onfocus = element.getAttribute('onfocus') || '';
        const onblur = element.getAttribute('onblur') || '';
        if (
          onfocus.includes('location') ||
          onblur.includes('location') ||
          onfocus.includes('window') ||
          onblur.includes('window')
        ) {
          focusChanges++;
        }
      });

      // Form auto-submissions
      const autoSubmitForms = document.querySelectorAll('form[onload], form[onchange]');
      autoSubmitForms.forEach((form) => {
        const onload = form.getAttribute('onload') || '';
        const onchange = form.getAttribute('onchange') || '';
        if (onload.includes('submit') || onchange.includes('submit')) {
          formAutoSubmissions++;
        }
      });

      // Select auto-navigation
      const autoNavSelects = document.querySelectorAll('select[onchange]');
      autoNavSelects.forEach((select) => {
        const onchange = select.getAttribute('onchange') || '';
        if (onchange.includes('location') || onchange.includes('navigate')) {
          selectAutoNavigation++;
        }
      });

      // Popup windows (JavaScript)
      const popupElements = document.querySelectorAll(
        '[onclick*="window.open"], [onclick*="popup"]',
      );
      popupWindows = popupElements.length;

      // Calculate unexpected score (0-1, where 1 is very unexpected)
      const totalUnexpected =
        autoRedirects + focusChanges + formAutoSubmissions + selectAutoNavigation + popupWindows;
      const unexpectedScore = Math.min(totalUnexpected / 10, 1); // Normalize to 0-1

      // Determine risk assessment
      let riskAssessment: UnexpectedChangeAnalysis['riskAssessment'] = 'low';
      if (unexpectedScore > 0.7) {
        riskAssessment = 'high';
      } else if (unexpectedScore > 0.3) {
        riskAssessment = 'medium';
      }

      return {
        autoRedirects,
        focusChanges,
        formAutoSubmissions,
        selectAutoNavigation,
        popupWindows,
        unexpectedScore,
        riskAssessment,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (unexpectedAnalysis.riskAssessment === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Unexpected change analysis: Low risk of unexpected changes',
        value: `Risk assessment: ${unexpectedAnalysis.riskAssessment}, Score: ${(unexpectedAnalysis.unexpectedScore * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Unexpected change risk: ${unexpectedAnalysis.riskAssessment} risk detected`);
      evidence.push({
        type: 'code',
        description: `Unexpected change analysis: ${unexpectedAnalysis.riskAssessment} risk`,
        value: `Auto-redirects: ${unexpectedAnalysis.autoRedirects}, Focus changes: ${unexpectedAnalysis.focusChanges}, Auto-submissions: ${unexpectedAnalysis.formAutoSubmissions}, Select navigation: ${unexpectedAnalysis.selectAutoNavigation}, Popups: ${unexpectedAnalysis.popupWindows}`,
        severity: unexpectedAnalysis.riskAssessment === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Reduce unexpected context changes and add user control mechanisms');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Accessibility Control Testing Algorithm
   */
  private async testAccessibilityControls(page: Page): Promise<AnalysisResult> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const accessibilityControls = await page.$$eval(
      'button, [role="button"], input[type="button"], input[type="submit"]',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.toLowerCase() || '';
          const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
          const title = element.getAttribute('title')?.toLowerCase() || '';
          const allText = `${text} ${ariaLabel} ${title}`;

          // Check for context change control keywords
          const isContextControl = [
            'confirm',
            'cancel',
            'continue',
            'proceed',
            'navigate',
            'redirect',
            'submit',
          ].some((keyword) => allText.includes(keyword));

          const hasKeyboardAccess =
            element.hasAttribute('tabindex') || ['BUTTON', 'INPUT'].includes(element.tagName);

          const hasAriaLabel =
            element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby');

          const isAccessible = hasKeyboardAccess && (text.length > 0 || hasAriaLabel);

          return {
            index,
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            isContextControl,
            hasKeyboardAccess,
            hasAriaLabel,
            isAccessible,
            text: text.substring(0, 50), // Limit text length
          };
        });
      },
    );

    const contextControls = accessibilityControls.filter((control) => control.isContextControl);
    const totalChecks = contextControls.length;
    let passedChecks = 0;

    contextControls.forEach((control, index) => {
      if (control.isAccessible) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Context control ${index + 1} is accessible`,
          value: `${control.selector} - "${control.text}" - keyboard accessible and properly labeled`,
          severity: 'info',
        });
      } else {
        issues.push(`Context control ${index + 1} lacks accessibility features`);
        evidence.push({
          type: 'code',
          description: `Context control ${index + 1} needs accessibility improvements`,
          value: `${control.selector} - keyboard: ${control.hasKeyboardAccess}, label: ${control.hasAriaLabel}`,
          severity: 'warning',
        });
        recommendations.push(`Improve accessibility of context control ${index + 1}`);
      }
    });

    // If no context controls found, that's generally good
    if (totalChecks === 0) {
      return {
        totalChecks: 1,
        passedChecks: 1,
        evidence: [
          {
            type: 'text',
            description: 'Accessibility control testing: No context change controls detected',
            value: 'No context change controls found to test',
            severity: 'info',
          },
        ],
        issues: [],
        recommendations: [],
      };
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }
}
