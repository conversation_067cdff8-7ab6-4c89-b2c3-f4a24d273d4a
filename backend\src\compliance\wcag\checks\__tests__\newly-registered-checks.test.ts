/**
 * Test Suite for Newly Registered WCAG Checks
 * Comprehensive testing for WCAG-024 through WCAG-038
 */

import { HtmlLangCheck } from '../html-lang';
import { LandmarksCheck } from '../landmarks';
import { LinkPurposeCheck } from '../link-purpose';
import { KeyboardTrapCheck } from '../keyboard-trap';
import { BypassBlocksCheck } from '../bypass-blocks';
import { PageTitledCheck } from '../page-titled';
import { LabelsInstructionsCheck } from '../labels-instructions';
import { ErrorSuggestionCheck } from '../error-suggestion';
import { ErrorPreventionCheck } from '../error-prevention';
import { AudioVideoOnlyCheck } from '../audio-video-only';
import { AudioDescriptionCheck } from '../audio-description';
import { MultipleWaysCheck } from '../multiple-ways';
import { HeadingsLabelsCheck } from '../headings-labels';
import { LanguagePartsCheck } from '../language-parts';
import { CheckConfig } from '../utils/check-template';

// Mock Puppeteer page for testing
const mockPage = {
  evaluate: jest.fn(),
  goto: jest.fn(),
  setViewport: jest.fn(),
  screenshot: jest.fn(),
  close: jest.fn(),
} as any;

const mockConfig: CheckConfig = {
  targetUrl: 'https://example.com',
  page: mockPage,
  scanId: 'test-scan-id',
  userId: 'test-user-id',
  requestId: 'test-request-id',
  timeout: 30000,
};

describe('Newly Registered WCAG Checks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('WCAG-024: HTML Language Check', () => {
    const htmlLangCheck = new HtmlLangCheck();

    it('should detect missing lang attribute', async () => {
      mockPage.evaluate.mockResolvedValue({
        hasLang: false,
        langValue: null,
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html><head><title>Test</title></head>',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('HTML document missing lang attribute');
      expect(result.evidence).toHaveLength(1);
      expect(result.evidence[0].severity).toBe('error');
      expect(result.elementCounts?.failed).toBe(1);
    });

    it('should pass with valid lang attribute', async () => {
      mockPage.evaluate.mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en"><head><title>Test</title></head>',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.score).toBe(100);
      expect(result.issues).toHaveLength(0);
      expect(result.elementCounts?.passed).toBe(1);
    });

    it('should detect invalid lang code format', async () => {
      mockPage.evaluate.mockResolvedValue({
        hasLang: true,
        langValue: 'invalid-code-123',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="invalid-code-123">',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues[0]).toContain('Invalid language code');
      expect(result.evidence[0].fixExample).toBeDefined();
      expect(result.evidence[0].fixExample?.before).toContain('invalid-code-123');
    });
  });

  describe('WCAG-025: Landmarks Check', () => {
    const landmarksCheck = new LandmarksCheck();

    it('should detect content outside landmarks', async () => {
      mockPage.evaluate.mockResolvedValue({
        landmarkCount: 1,
        contentOutsideLandmarks: [
          {
            tagName: 'div',
            selector: 'div:nth-of-type(1)',
            textContent: 'Content without landmarks',
            isVisible: true,
          },
        ],
        hasMainLandmark: false,
      });

      const result = await landmarksCheck.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('1 content elements found outside landmarks');
      expect(result.issues).toContain('Page missing main landmark');
      expect(result.evidence[0].elementCount).toBe(1);
    });

    it('should pass with proper landmark structure', async () => {
      mockPage.evaluate.mockResolvedValue({
        landmarkCount: 3,
        contentOutsideLandmarks: [],
        hasMainLandmark: true,
      });

      const result = await landmarksCheck.performCheck(mockConfig);

      expect(result.score).toBe(100);
      expect(result.issues).toHaveLength(0);
    });
  });

  describe('WCAG-026: Link Purpose Check', () => {
    const linkPurposeCheck = new LinkPurposeCheck();

    it('should detect unclear link text', async () => {
      mockPage.evaluate.mockResolvedValue({
        totalLinks: 3,
        problematicLinks: [
          {
            href: '/page1',
            text: 'click here',
            ariaLabel: '',
            title: '',
            selector: 'a:nth-of-type(1)',
            issue: 'Link text not descriptive',
          },
          {
            href: '/page2',
            text: '',
            ariaLabel: '',
            title: '',
            selector: 'a:nth-of-type(2)',
            issue: 'Link has no accessible text',
          },
        ],
      });

      const result = await linkPurposeCheck.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('2 links with unclear or missing text');
      expect(result.evidence[0].elementCount).toBe(2);
      expect(result.recommendations).toContain(
        'Provide descriptive text that explains the link purpose',
      );
    });

    it('should pass with descriptive link text', async () => {
      mockPage.evaluate.mockResolvedValue({
        totalLinks: 2,
        problematicLinks: [],
      });

      const result = await linkPurposeCheck.performCheck(mockConfig);

      expect(result.score).toBe(100);
      expect(result.issues).toHaveLength(0);
    });
  });

  describe('WCAG-029: Page Titled Check', () => {
    const pageTitledCheck = new PageTitledCheck();

    it('should detect missing page title', async () => {
      mockPage.evaluate.mockResolvedValue({
        hasTitle: false,
        titleText: '',
        titleLength: 0,
        isDescriptive: false,
      });

      const result = await pageTitledCheck.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('Page missing title element');
      expect(result.elementCounts?.failed).toBe(1);
    });

    it('should detect empty title', async () => {
      mockPage.evaluate.mockResolvedValue({
        hasTitle: true,
        titleText: '',
        titleLength: 0,
        isDescriptive: false,
      });

      const result = await pageTitledCheck.performCheck(mockConfig);

      expect(result.score).toBe(0);
      expect(result.issues).toContain('Page title is empty');
    });

    it('should pass with descriptive title', async () => {
      mockPage.evaluate.mockResolvedValue({
        hasTitle: true,
        titleText: 'Contact Us - Company Name',
        titleLength: 25,
        isDescriptive: true,
      });

      const result = await pageTitledCheck.performCheck(mockConfig);

      expect(result.score).toBe(100);
      expect(result.issues).toHaveLength(0);
      expect(result.elementCounts?.passed).toBe(1);
    });
  });

  describe('Enhanced Evidence Features', () => {
    it('should include element counts in all check results', async () => {
      const htmlLangCheck = new HtmlLangCheck();

      mockPage.evaluate.mockResolvedValue({
        hasLang: false,
        langValue: null,
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html>',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.elementCounts).toBeDefined();
      expect(result.elementCounts?.total).toBe(1);
      expect(result.elementCounts?.failed).toBe(1);
      expect(result.elementCounts?.passed).toBe(0);
    });

    it('should include performance metrics', async () => {
      const htmlLangCheck = new HtmlLangCheck();

      mockPage.evaluate.mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en">',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.performance).toBeDefined();
      expect(result.performance?.scanDuration).toBeGreaterThanOrEqual(0);
      expect(result.performance?.elementsAnalyzed).toBe(1);
    });

    it('should include check metadata', async () => {
      const htmlLangCheck = new HtmlLangCheck();

      mockPage.evaluate.mockResolvedValue({
        hasLang: true,
        langValue: 'en',
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html lang="en">',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.checkMetadata).toBeDefined();
      expect(result.checkMetadata?.version).toBe('1.0.0');
      expect(result.checkMetadata?.algorithm).toBe('html-lang-detection');
      expect(result.checkMetadata?.confidence).toBe(1.0);
    });

    it('should include fix examples for failed checks', async () => {
      const htmlLangCheck = new HtmlLangCheck();

      mockPage.evaluate.mockResolvedValue({
        hasLang: false,
        langValue: null,
        hasXmlLang: false,
        xmlLangValue: null,
        htmlOuterHTML: '<html>',
      });

      const result = await htmlLangCheck.performCheck(mockConfig);

      expect(result.evidence[0].fixExample).toBeDefined();
      expect(result.evidence[0].fixExample?.before).toBe('<html>');
      expect(result.evidence[0].fixExample?.after).toBe('<html lang="en">');
      expect(result.evidence[0].fixExample?.description).toContain('Add lang attribute');
      expect(result.evidence[0].fixExample?.resources).toHaveLength(2);
    });
  });
});
