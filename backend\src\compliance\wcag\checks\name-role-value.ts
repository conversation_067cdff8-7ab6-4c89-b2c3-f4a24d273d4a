/**
 * WCAG Rule 9: Name, Role, Value - 4.1.2
 * 90% Automated - Manual review for custom components
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ComponentLibraryDetector } from '../utils/component-library-detector';
import { AdvancedPatternDetector } from '../utils/advanced-pattern-detector';
import { PatternRecognitionEngine } from '../utils/pattern-recognition-engine';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface NameRoleValueConfig extends EnhancedCheckConfig, ManualReviewConfig {
  enableComponentLibraryDetection?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableSemanticValidation?: boolean;
  enableFrameworkOptimization?: boolean;
  enableCustomComponentAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAdvancedComponentAnalysis?: boolean;
  enableAdvancedPatternDetection?: boolean;
  enablePatternRecognition?: boolean;
}

export class NameRoleValueCheck {
  private checkTemplate = new ManualReviewTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private componentLibraryDetector = ComponentLibraryDetector.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  /**
   * Perform name, role, value check - 90% automated with enhanced evidence
   */
  async performCheck(config: NameRoleValueConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration - reduced to focus on core functionality
    const enhancedConfig: NameRoleValueConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: false, // Disable to reduce noise
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement', // Changed from 'enhance' to reduce extra evidence
        maxExecutionTime: 5000,
      },
      enableComponentLibraryDetection: false, // Disable to reduce noise
      enableAccessibilityPatterns: false, // Disable to reduce noise
      enableSemanticValidation: false, // Disable to reduce noise
      enableFrameworkOptimization: false, // Disable to reduce noise
      enableCustomComponentAnalysis: true, // Keep for manual review
      enableAISemanticValidation: false, // Disable to reduce noise
      enableAdvancedComponentAnalysis: false, // Disable to reduce noise
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-009',
      'Name, Role, Value',
      'robust',
      0.11,
      'A',
      enhancedConfig,
      this.executeNameRoleValueCheck.bind(this),
      true, // Requires browser
      true, // Manual review for custom components
    );

    // Enhanced evidence standardization with ARIA and component analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-009',
        ruleName: 'Name, Role, Value',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'aria-component-analysis',
          manualReviewRequired: (result.manualReviewItems?.length ?? 0) > 0,
          interactiveElementAnalysis: true,
          ariaImplementationAnalysis: true,
          customComponentAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.4, // Very low threshold to include all error evidence
        maxEvidenceItems: 100, // Increased limit to capture all evidence
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive name, role, value analysis
   */
  private async executeNameRoleValueCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze interactive elements
    const interactiveAnalysis = await this.analyzeInteractiveElements(page);

    // Analyze ARIA implementation
    const ariaAnalysis = await this.analyzeAriaImplementation(page);

    // Analyze custom components
    const customComponentAnalysis = await this.analyzeCustomComponents(page);

    // Combine all analyses
    const allAnalyses = [interactiveAnalysis, ariaAnalysis, customComponentAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence only if check failed
    if (automatedScore < 100) {
      const failedChecks = automatedChecks - passedChecks;
      evidence.unshift({
        type: 'error',
        description: `Name, role, value violations found`,
        value: `${failedChecks} elements failed accessibility requirements: ${issues.slice(0, 3).join(', ')}${issues.length > 3 ? '...' : ''}`,
        severity: 'error',
      });
    }

    return {
      score: automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze interactive elements for proper name, role, value
   */
  private async analyzeInteractiveElements(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function getAccessibleName(element: Element): string {
        // Check aria-label
        const ariaLabel = element.getAttribute('aria-label');
        if (ariaLabel && ariaLabel.trim()) {
          return ariaLabel.trim();
        }

        // Check aria-labelledby
        const ariaLabelledby = element.getAttribute('aria-labelledby');
        if (ariaLabelledby) {
          const referencedElement = document.getElementById(ariaLabelledby);
          if (referencedElement && referencedElement.textContent?.trim()) {
            return referencedElement.textContent.trim();
          }
        }

        // Check associated label
        if (element.id) {
          const label = document.querySelector(`label[for="${element.id}"]`);
          if (label && label.textContent?.trim()) {
            return label.textContent.trim();
          }
        }

        // Check if inside label
        const parentLabel = element.closest('label');
        if (parentLabel && parentLabel.textContent?.trim()) {
          return parentLabel.textContent.trim();
        }

        // Check title attribute
        const title = element.getAttribute('title');
        if (title && title.trim()) {
          return title.trim();
        }

        // For buttons and links, check text content
        if (element.tagName === 'BUTTON' || element.tagName === 'A') {
          const textContent = element.textContent?.trim();
          if (textContent) {
            return textContent;
          }
        }

        // For images, check alt text
        if (element.tagName === 'IMG') {
          const alt = (element as HTMLImageElement).alt;
          if (alt && alt.trim()) {
            return alt.trim();
          }
        }

        return '';
      }

      function getRole(element: Element): string {
        // Explicit role
        const explicitRole = element.getAttribute('role');
        if (explicitRole) {
          return explicitRole;
        }

        // Implicit role based on tag
        const tagName = element.tagName.toLowerCase();
        const roleMap: { [key: string]: string } = {
          button: 'button',
          a: 'link',
          input: 'textbox', // simplified
          select: 'combobox',
          textarea: 'textbox',
          img: 'img',
          h1: 'heading',
          h2: 'heading',
          h3: 'heading',
          h4: 'heading',
          h5: 'heading',
          h6: 'heading',
        };

        return roleMap[tagName] || '';
      }

      const interactiveElements = Array.from(
        document.querySelectorAll(
          'button, a, input:not([type="hidden"]), select, textarea, [role="button"], [role="link"], [role="textbox"], [role="combobox"], [tabindex]',
        ),
      );

      let totalChecks = 0;
      let passedChecks = 0;

      interactiveElements.forEach((element, index) => {
        const selector = generateSelector(element, index);
        const accessibleName = getAccessibleName(element);
        const role = getRole(element);

        // Check for accessible name
        totalChecks++;
        if (accessibleName) {
          passedChecks++;
          // Only add passed evidence if we're not in failure mode
        } else {
          issues.push(`Element missing accessible name: ${selector}`);
          recommendations.push(`Add aria-label, label, or text content to ${selector}`);

          // Generate code evidence for failed elements
          evidence.push({
            type: 'code',
            description: `Element missing accessible name`,
            value: element.outerHTML || `<${element.tagName.toLowerCase()}${element.id ? ` id="${element.id}"` : ''}${element.className ? ` class="${element.className}"` : ''}>`,
            selector,
            severity: 'error',
          });
        }

        // Check for role
        totalChecks++;
        if (role) {
          passedChecks++;
          // Only add passed evidence if we're not in failure mode
        } else {
          issues.push(`Element missing role: ${selector}`);
          recommendations.push(`Add appropriate role attribute to ${selector}`);

          // Generate code evidence for failed elements
          evidence.push({
            type: 'code',
            description: `Element missing role attribute`,
            value: element.outerHTML || `<${element.tagName.toLowerCase()}${element.id ? ` id="${element.id}"` : ''}${element.className ? ` class="${element.className}"` : ''}>`,
            selector,
            severity: 'error',
          });
        }

        // Check for value (state) if applicable
        totalChecks++;
        const hasState =
          element.hasAttribute('aria-checked') ||
          element.hasAttribute('aria-selected') ||
          element.hasAttribute('aria-expanded') ||
          element.hasAttribute('aria-pressed') ||
          (element as HTMLInputElement).value !== undefined;

        if (hasState || !['button', 'checkbox', 'radio', 'combobox'].includes(role)) {
          passedChecks++;
          // Only add passed evidence if we're not in failure mode
        } else {
          issues.push(`Element missing state information: ${selector}`);
          recommendations.push(`Add appropriate aria-* state attributes to ${selector}`);

          // Generate code evidence for failed elements
          evidence.push({
            type: 'code',
            description: `Element missing state/value information`,
            value: element.outerHTML || `<${element.tagName.toLowerCase()}${element.id ? ` id="${element.id}"` : ''}${element.className ? ` class="${element.className}"` : ''}>`,
            selector,
            severity: 'error',
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze ARIA implementation
   */
  private async analyzeAriaImplementation(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      const ariaElements = Array.from(
        document.querySelectorAll(
          '[role], [aria-label], [aria-labelledby], [aria-describedby], [aria-expanded], [aria-checked], [aria-selected], [aria-pressed]',
        ),
      );
      let totalChecks = 0;
      let passedChecks = 0;

      if (ariaElements.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      ariaElements.forEach((element, index) => {
        const selector = element.id ? `#${element.id}` : `[role]:nth-of-type(${index + 1})`;

        // Validate ARIA roles
        totalChecks++;
        const role = element.getAttribute('role');
        if (role) {
          const validRoles = [
            'alert',
            'alertdialog',
            'application',
            'article',
            'banner',
            'button',
            'cell',
            'checkbox',
            'columnheader',
            'combobox',
            'complementary',
            'contentinfo',
            'definition',
            'dialog',
            'directory',
            'document',
            'feed',
            'figure',
            'form',
            'grid',
            'gridcell',
            'group',
            'heading',
            'img',
            'link',
            'list',
            'listbox',
            'listitem',
            'log',
            'main',
            'marquee',
            'math',
            'menu',
            'menubar',
            'menuitem',
            'menuitemcheckbox',
            'menuitemradio',
            'navigation',
            'none',
            'note',
            'option',
            'presentation',
            'progressbar',
            'radio',
            'radiogroup',
            'region',
            'row',
            'rowgroup',
            'rowheader',
            'scrollbar',
            'search',
            'searchbox',
            'separator',
            'slider',
            'spinbutton',
            'status',
            'switch',
            'tab',
            'table',
            'tablist',
            'tabpanel',
            'term',
            'textbox',
            'timer',
            'toolbar',
            'tooltip',
            'tree',
            'treegrid',
            'treeitem',
          ];

          if (validRoles.includes(role)) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Valid ARIA role used',
              value: `Role: ${role}`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Invalid ARIA role: ${role} in ${selector}`);
            recommendations.push(`Use valid ARIA role for ${selector}`);
          }
        } else {
          passedChecks++; // Elements without role attribute pass this check
        }

        // Validate ARIA references
        const ariaLabelledby = element.getAttribute('aria-labelledby');
        if (ariaLabelledby) {
          totalChecks++;
          const referencedElement = document.getElementById(ariaLabelledby);
          if (referencedElement) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Valid aria-labelledby reference',
              value: `References: #${ariaLabelledby}`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Invalid aria-labelledby reference: ${ariaLabelledby} in ${selector}`);
            recommendations.push(`Fix aria-labelledby reference in ${selector}`);
          }
        }

        const ariaDescribedby = element.getAttribute('aria-describedby');
        if (ariaDescribedby) {
          totalChecks++;
          const referencedElement = document.getElementById(ariaDescribedby);
          if (referencedElement) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Valid aria-describedby reference',
              value: `References: #${ariaDescribedby}`,
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Invalid aria-describedby reference: ${ariaDescribedby} in ${selector}`);
            recommendations.push(`Fix aria-describedby reference in ${selector}`);
          }
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze custom components
   */
  private async analyzeCustomComponents(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      // Look for custom components (elements with custom roles or complex interactions)
      const customComponents = Array.from(
        document.querySelectorAll(
          '[role="combobox"], [role="listbox"], [role="menu"], [role="menubar"], [role="tablist"], [role="tree"], [role="grid"], [role="dialog"]',
        ),
      );

      const totalChecks = customComponents.length;
      const passedChecks = 0;

      if (customComponents.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      customComponents.forEach((element, index) => {
        const selector = element.id ? `#${element.id}` : `[role]:nth-of-type(${index + 1})`;
        const role = element.getAttribute('role');

        // Custom components require manual testing for full functionality
        manualReviewItems.push({
          selector,
          description: 'Custom component accessibility verification needed',
          automatedFindings: `Custom component with role: ${role}`,
          reviewRequired:
            'Manually test keyboard navigation, screen reader interaction, and state management',
          priority: 'high',
          estimatedTime: 10,
        });

        // Basic automated checks
        const hasAccessibleName =
          element.getAttribute('aria-label') ||
          element.getAttribute('aria-labelledby') ||
          element.textContent?.trim();

        if (hasAccessibleName) {
          evidence.push({
            type: 'text',
            description: 'Custom component has accessible name',
            value: `Component with role: ${role}`,
            selector,
            severity: 'info',
          });
        } else {
          issues.push(`Custom component missing accessible name: ${selector}`);
          recommendations.push(`Add aria-label or aria-labelledby to ${selector}`);
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }
}
