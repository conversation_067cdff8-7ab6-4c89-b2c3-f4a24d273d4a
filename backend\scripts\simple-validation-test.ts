/**
 * Simple WCAG System Validation Test
 * Quick validation of key components
 */

import fs from 'fs';
import path from 'path';

function validateWCAGSystem(): void {
  console.log('🧪 WCAG System Validation Test');
  console.log('=' .repeat(40));

  const results: Array<{ test: string; passed: boolean; details: string }> = [];

  // Test 1: Enhanced Evidence Database Schema
  console.log('\n1. 📊 Database Schema Validation');
  try {
    const migrationFile = path.join(__dirname, '../migrations/20250105000001_enhance_wcag_evidence.ts');
    const migrationExists = fs.existsSync(migrationFile);
    
    if (migrationExists) {
      const migrationContent = fs.readFileSync(migrationFile, 'utf8');
      const hasEnhancedFields = migrationContent.includes('total_element_count') &&
                               migrationContent.includes('failed_element_count') &&
                               migrationContent.includes('affected_selectors') &&
                               migrationContent.includes('fix_examples');
      
      results.push({
        test: 'Enhanced Evidence Schema',
        passed: hasEnhancedFields,
        details: hasEnhancedFields ? 'All enhanced fields present' : 'Missing enhanced fields'
      });
      console.log(`   ${hasEnhancedFields ? '✅' : '❌'} Enhanced evidence schema: ${hasEnhancedFields ? 'PASSED' : 'FAILED'}`);
    } else {
      results.push({
        test: 'Enhanced Evidence Schema',
        passed: false,
        details: 'Migration file not found'
      });
      console.log('   ❌ Enhanced evidence schema: FAILED - Migration file not found');
    }
  } catch (error) {
    results.push({
      test: 'Enhanced Evidence Schema',
      passed: false,
      details: `Error: ${error}`
    });
    console.log(`   ❌ Enhanced evidence schema: FAILED - ${error}`);
  }

  // Test 2: Enhanced Database Implementation
  console.log('\n2. 💾 Database Implementation Validation');
  try {
    const dbFile = path.join(__dirname, '../src/compliance/wcag/database/wcag-database.ts');
    const dbContent = fs.readFileSync(dbFile, 'utf8');
    
    const hasExtractMethod = dbContent.includes('extractEnhancedEvidenceData');
    const hasEnhancedFields = dbContent.includes('total_element_count') &&
                             dbContent.includes('failed_element_count') &&
                             dbContent.includes('affected_selectors');
    
    const implementationComplete = hasExtractMethod && hasEnhancedFields;
    
    results.push({
      test: 'Database Implementation',
      passed: implementationComplete,
      details: implementationComplete ? 'Enhanced evidence population implemented' : 'Implementation incomplete'
    });
    console.log(`   ${implementationComplete ? '✅' : '❌'} Database implementation: ${implementationComplete ? 'PASSED' : 'FAILED'}`);
  } catch (error) {
    results.push({
      test: 'Database Implementation',
      passed: false,
      details: `Error: ${error}`
    });
    console.log(`   ❌ Database implementation: FAILED - ${error}`);
  }

  // Test 3: Utility Integration Completion
  console.log('\n3. 🔧 Utility Integration Validation');
  try {
    const utilityFile = path.join(__dirname, '../src/compliance/wcag/utils/utility-integration-manager.ts');
    const utilityContent = fs.readFileSync(utilityFile, 'utf8');
    
    // Check for newly added checks
    const newChecks = ['WCAG-005', 'WCAG-047', 'WCAG-048', 'WCAG-049', 'WCAG-067', 'WCAG-068', 'WCAG-069'];
    const addedChecks = newChecks.filter(check => utilityContent.includes(`'${check}':`));
    
    const integrationComplete = addedChecks.length === newChecks.length;
    
    results.push({
      test: 'Utility Integration',
      passed: integrationComplete,
      details: `${addedChecks.length}/${newChecks.length} missing checks integrated`
    });
    console.log(`   ${integrationComplete ? '✅' : '❌'} Utility integration: ${integrationComplete ? 'PASSED' : 'FAILED'} (${addedChecks.length}/${newChecks.length})`);
  } catch (error) {
    results.push({
      test: 'Utility Integration',
      passed: false,
      details: `Error: ${error}`
    });
    console.log(`   ❌ Utility integration: FAILED - ${error}`);
  }

  // Test 4: AccessibilityChecker.org UI Components
  console.log('\n4. 🎨 UI Components Validation');
  try {
    const frontendPath = path.join(__dirname, '../../frontend/components/wcag');
    const requiredComponents = [
      'RiskMessaging.tsx',
      'FixIssuesButton.tsx',
      'WcagScanOverview.tsx',
      'EnhancedEvidenceDisplay.tsx'
    ];
    
    const existingComponents = requiredComponents.filter(component => {
      const componentPath = path.join(frontendPath, component);
      return fs.existsSync(componentPath);
    });
    
    const uiComplete = existingComponents.length === requiredComponents.length;
    
    results.push({
      test: 'UI Components',
      passed: uiComplete,
      details: `${existingComponents.length}/${requiredComponents.length} components exist`
    });
    console.log(`   ${uiComplete ? '✅' : '❌'} UI components: ${uiComplete ? 'PASSED' : 'FAILED'} (${existingComponents.length}/${requiredComponents.length})`);
  } catch (error) {
    results.push({
      test: 'UI Components',
      passed: false,
      details: `Error: ${error}`
    });
    console.log(`   ❌ UI components: FAILED - ${error}`);
  }

  // Test 5: Enhanced Check Templates
  console.log('\n5. 📋 Enhanced Check Templates Validation');
  try {
    const checksPath = path.join(__dirname, '../src/compliance/wcag/checks');
    const checkFiles = fs.readdirSync(checksPath).filter(file => file.endsWith('.ts') && file !== 'index.ts');
    
    let enhancedCount = 0;
    let totalCount = checkFiles.length;
    
    for (const file of checkFiles.slice(0, 10)) { // Sample first 10 files
      const filePath = path.join(checksPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('EnhancedCheckTemplate') || content.includes('enhancedTemplate')) {
        enhancedCount++;
      }
    }
    
    const enhancementRate = (enhancedCount / 10) * 100; // Based on sample
    const templatesGood = enhancementRate >= 80;
    
    results.push({
      test: 'Enhanced Templates',
      passed: templatesGood,
      details: `${enhancementRate.toFixed(1)}% of sampled checks use enhanced templates`
    });
    console.log(`   ${templatesGood ? '✅' : '❌'} Enhanced templates: ${templatesGood ? 'PASSED' : 'FAILED'} (${enhancementRate.toFixed(1)}%)`);
  } catch (error) {
    results.push({
      test: 'Enhanced Templates',
      passed: false,
      details: `Error: ${error}`
    });
    console.log(`   ❌ Enhanced templates: FAILED - ${error}`);
  }

  // Generate Summary
  console.log('\n📊 VALIDATION SUMMARY');
  console.log('=' .repeat(40));
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;
  const successRate = (passedTests / totalTests) * 100;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${successRate.toFixed(1)}%`);
  
  console.log('\nDetailed Results:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.test}: ${result.passed ? '✅' : '❌'} - ${result.details}`);
  });
  
  if (successRate === 100) {
    console.log('\n🎉 ALL VALIDATION TESTS PASSED!');
    console.log('✅ Enhanced evidence database population: IMPLEMENTED');
    console.log('✅ Utility integration completion: IMPLEMENTED');
    console.log('✅ AccessibilityChecker.org UI: IMPLEMENTED');
    console.log('\n🚀 System is ready for production use!');
  } else if (successRate >= 80) {
    console.log('\n✅ VALIDATION MOSTLY SUCCESSFUL!');
    console.log('Most critical components are working correctly.');
    console.log('Minor issues may need attention.');
  } else {
    console.log('\n⚠️ VALIDATION NEEDS ATTENTION!');
    console.log('Several critical issues need to be resolved.');
  }
}

// Run the validation
validateWCAGSystem();
