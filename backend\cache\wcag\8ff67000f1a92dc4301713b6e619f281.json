{"data": [{"type": "info", "description": "No motion-triggered functionality detected", "value": "<PERSON> does not appear to use device motion for triggering functions", "severity": "info", "metadata": {"scanDuration": 8, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-actuation-analysis", "motionEventDetection": true, "alternativeControlValidation": true, "advancedMotionDetection": true, "accessibilityPatterns": true, "evidenceIndex": 0, "ruleId": "WCAG-056", "ruleName": "Motion Actuation", "timestamp": "2025-07-13T03:29:19.078Z", "qualityMetricsScore": 0.8, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}, "elementCount": 1, "affectedSelectors": ["Page", "does", "not", "appear", "to", "use", "device", "motion", "for", "triggering", "functions"]}], "timestamp": 1752377359078, "hash": "805c5bacf94dbcecc8bbcc0f3a65e992", "accessCount": 1, "lastAccessed": 1752377359078, "size": 847, "metadata": {"originalKey": "WCAG-056:WCAG-056", "normalizedKey": "wcag-056_wcag-056", "savedAt": 1752377359078, "version": "1.1", "keyHash": "1acf038a280dc3d3664b2e2cccd8a05c"}}