/**
 * Phase 3 Integration Test
 * Quick validation test to confirm Phase 3 utilities are working correctly
 */

import logger from '../../../utils/logger';
import { AdvancedPatternDetector } from '../utils/advanced-pattern-detector';
import { PatternRecognitionEngine } from '../utils/pattern-recognition-engine';
import { AdvancedPatternIntegration } from '../utils/advanced-pattern-integration';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';

export interface IntegrationTestResult {
  testName: string;
  passed: boolean;
  executionTime: number;
  error?: string;
  details?: any;
}

export interface Phase3IntegrationTestReport {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  successRate: number;
  testResults: IntegrationTestResult[];
  utilityStatus: {
    advancedPatternDetector: boolean;
    patternRecognitionEngine: boolean;
    advancedPatternIntegration: boolean;
    aiSemanticValidator: boolean;
    contentQualityAnalyzer: boolean;
    accessibilityPatternLibrary: boolean;
  };
  overallStatus: 'PASS' | 'FAIL';
  recommendations: string[];
}

/**
 * Phase 3 Integration Test Runner
 * Quick validation of all Phase 3 utilities
 */
export class Phase3IntegrationTest {
  private static instance: Phase3IntegrationTest;

  private constructor() {}

  static getInstance(): Phase3IntegrationTest {
    if (!Phase3IntegrationTest.instance) {
      Phase3IntegrationTest.instance = new Phase3IntegrationTest();
    }
    return Phase3IntegrationTest.instance;
  }

  /**
   * Run comprehensive Phase 3 integration tests
   */
  async runIntegrationTests(): Promise<Phase3IntegrationTestReport> {
    logger.info('🧪 Starting Phase 3 Integration Tests');

    const testResults: IntegrationTestResult[] = [];

    // Test 1: Advanced Pattern Detector Initialization
    testResults.push(await this.testAdvancedPatternDetector());

    // Test 2: Pattern Recognition Engine Initialization
    testResults.push(await this.testPatternRecognitionEngine());

    // Test 3: Advanced Pattern Integration Initialization
    testResults.push(await this.testAdvancedPatternIntegration());

    // Test 4: AI Semantic Validator Integration
    testResults.push(await this.testAISemanticValidator());

    // Test 5: Content Quality Analyzer Integration
    testResults.push(await this.testContentQualityAnalyzer());

    // Test 6: Accessibility Pattern Library Integration
    testResults.push(await this.testAccessibilityPatternLibrary());

    // Test 7: End-to-End Integration Test
    testResults.push(await this.testEndToEndIntegration());

    // Test 8: Performance Test
    testResults.push(await this.testPerformance());

    // Generate report
    const report = this.generateIntegrationReport(testResults);

    // Log results
    this.logTestResults(report);

    return report;
  }

  /**
   * Test Advanced Pattern Detector
   */
  private async testAdvancedPatternDetector(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('🔍 Testing Advanced Pattern Detector');

      const detector = AdvancedPatternDetector.getInstance();

      // Test basic functionality
      if (!detector) {
        throw new Error('AdvancedPatternDetector instance is null');
      }

      // Test with mock page
      const mockPage = this.createMockPage();
      const result = await detector.performAdvancedPatternDetection(mockPage);

      if (!result) {
        throw new Error('AdvancedPatternDetector returned null result');
      }

      return {
        testName: 'Advanced Pattern Detector',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          semanticPatterns: result.semanticPatterns?.length || 0,
          behavioralPatterns: result.behavioralPatterns?.length || 0,
          crossElementPatterns: result.crossElementPatterns?.length || 0,
        },
      };
    } catch (error) {
      return {
        testName: 'Advanced Pattern Detector',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test Pattern Recognition Engine
   */
  private async testPatternRecognitionEngine(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('🎯 Testing Pattern Recognition Engine');

      const engine = PatternRecognitionEngine.getInstance();

      if (!engine) {
        throw new Error('PatternRecognitionEngine instance is null');
      }

      // Test with mock page
      const mockPage = this.createMockPage();
      const result = await engine.recognizePatterns(mockPage);

      if (!result) {
        throw new Error('PatternRecognitionEngine returned null result');
      }

      return {
        testName: 'Pattern Recognition Engine',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          totalPatterns: result.totalPatterns,
          overallScore: result.overallScore,
          patternCategories: Object.keys(result.patternCategories).length,
        },
      };
    } catch (error) {
      return {
        testName: 'Pattern Recognition Engine',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test Advanced Pattern Integration
   */
  private async testAdvancedPatternIntegration(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('🔧 Testing Advanced Pattern Integration');

      const integration = AdvancedPatternIntegration.getInstance();

      if (!integration) {
        throw new Error('AdvancedPatternIntegration instance is null');
      }

      // Test with mock page
      const mockPage = this.createMockPage();
      const result = await integration.performIntegratedPatternAnalysis(
        mockPage,
        'TEST-001',
        AdvancedPatternIntegration.getDefaultConfig(),
      );

      if (!result) {
        throw new Error('AdvancedPatternIntegration returned null result');
      }

      return {
        testName: 'Advanced Pattern Integration',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          overallScore: result.overallScore,
          evidenceCount: result.evidence.length,
          issuesCount: result.issues.length,
          recommendationsCount: result.recommendations.length,
        },
      };
    } catch (error) {
      return {
        testName: 'Advanced Pattern Integration',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test AI Semantic Validator
   */
  private async testAISemanticValidator(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('🤖 Testing AI Semantic Validator');

      const validator = AISemanticValidator.getAIInstance();

      if (!validator) {
        throw new Error('AISemanticValidator instance is null');
      }

      // Test basic functionality
      const mockPage = this.createMockPage();
      const result = await validator.validateSemanticStructure(mockPage);

      return {
        testName: 'AI Semantic Validator',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          validationScore: result.overallScore || 0,
          issuesFound: result.issues?.length || 0,
        },
      };
    } catch (error) {
      return {
        testName: 'AI Semantic Validator',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test Content Quality Analyzer
   */
  private async testContentQualityAnalyzer(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('📝 Testing Content Quality Analyzer');

      const analyzer = ContentQualityAnalyzer.getInstance();

      if (!analyzer) {
        throw new Error('ContentQualityAnalyzer instance is null');
      }

      // Test basic functionality
      const mockPage = this.createMockPage();
      const result = await analyzer.analyzeContentQuality(mockPage);

      return {
        testName: 'Content Quality Analyzer',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          qualityScore: result.overallScore || 0,
          metricsCount: Object.keys(result.metrics || {}).length,
        },
      };
    } catch (error) {
      return {
        testName: 'Content Quality Analyzer',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test Accessibility Pattern Library
   */
  private async testAccessibilityPatternLibrary(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('📚 Testing Accessibility Pattern Library');

      const library = AccessibilityPatternLibrary.getInstance();

      if (!library) {
        throw new Error('AccessibilityPatternLibrary instance is null');
      }

      // Test basic functionality
      const mockPage = this.createMockPage();
      const result = await library.analyzePatterns(mockPage);

      return {
        testName: 'Accessibility Pattern Library',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          totalPatterns: result.totalPatterns || 0,
          validPatterns: result.validPatterns || 0,
          overallScore: result.overallScore || 0,
        },
      };
    } catch (error) {
      return {
        testName: 'Accessibility Pattern Library',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test End-to-End Integration
   */
  private async testEndToEndIntegration(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('🔄 Testing End-to-End Integration');

      // Test that all utilities can work together
      const mockPage = this.createMockPage();

      // Run multiple utilities in sequence
      const detector = AdvancedPatternDetector.getInstance();
      const engine = PatternRecognitionEngine.getInstance();
      const integration = AdvancedPatternIntegration.getInstance();

      const detectorResult = await detector.performAdvancedPatternDetection(mockPage);
      const engineResult = await engine.recognizePatterns(mockPage);
      const integrationResult = await integration.performIntegratedPatternAnalysis(
        mockPage,
        'E2E-TEST',
        AdvancedPatternIntegration.getDefaultConfig(),
      );

      // Verify all results are valid
      if (!detectorResult || !engineResult || !integrationResult) {
        throw new Error('One or more utilities returned null results');
      }

      return {
        testName: 'End-to-End Integration',
        passed: true,
        executionTime: Date.now() - startTime,
        details: {
          allUtilitiesWorking: true,
          integrationScore: integrationResult.overallScore,
          totalExecutionTime: Date.now() - startTime,
        },
      };
    } catch (error) {
      return {
        testName: 'End-to-End Integration',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Test Performance
   */
  private async testPerformance(): Promise<IntegrationTestResult> {
    const startTime = Date.now();

    try {
      logger.debug('⚡ Testing Performance');

      const mockPage = this.createMockPage();
      const integration = AdvancedPatternIntegration.getInstance();

      // Run multiple iterations to test performance
      const iterations = 3;
      const executionTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const iterationStart = Date.now();
        await integration.performIntegratedPatternAnalysis(
          mockPage,
          `PERF-TEST-${i}`,
          AdvancedPatternIntegration.getDefaultConfig(),
        );
        executionTimes.push(Date.now() - iterationStart);
      }

      const averageTime = executionTimes.reduce((sum, time) => sum + time, 0) / iterations;
      const maxTime = Math.max(...executionTimes);

      // Performance should be under 10 seconds per iteration
      const performanceAcceptable = averageTime < 10000 && maxTime < 15000;

      return {
        testName: 'Performance Test',
        passed: performanceAcceptable,
        executionTime: Date.now() - startTime,
        details: {
          iterations,
          averageTime,
          maxTime,
          performanceAcceptable,
        },
      };
    } catch (error) {
      return {
        testName: 'Performance Test',
        passed: false,
        executionTime: Date.now() - startTime,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Create mock page for testing
   */
  private createMockPage(): any {
    return {
      url: () => Promise.resolve('https://test.example.com'),
      evaluate: (fn: any) =>
        Promise.resolve({
          elements: ['div', 'span', 'button'],
          patterns: ['navigation', 'form', 'content'],
        }),
      goto: (url: string) => Promise.resolve(null),
    };
  }

  /**
   * Generate integration test report
   */
  private generateIntegrationReport(
    testResults: IntegrationTestResult[],
  ): Phase3IntegrationTestReport {
    const totalTests = testResults.length;
    const passedTests = testResults.filter((r) => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = passedTests / totalTests;

    // Determine utility status
    const utilityStatus = {
      advancedPatternDetector:
        testResults.find((r) => r.testName === 'Advanced Pattern Detector')?.passed || false,
      patternRecognitionEngine:
        testResults.find((r) => r.testName === 'Pattern Recognition Engine')?.passed || false,
      advancedPatternIntegration:
        testResults.find((r) => r.testName === 'Advanced Pattern Integration')?.passed || false,
      aiSemanticValidator:
        testResults.find((r) => r.testName === 'AI Semantic Validator')?.passed || false,
      contentQualityAnalyzer:
        testResults.find((r) => r.testName === 'Content Quality Analyzer')?.passed || false,
      accessibilityPatternLibrary:
        testResults.find((r) => r.testName === 'Accessibility Pattern Library')?.passed || false,
    };

    // Generate recommendations
    const recommendations: string[] = [];
    if (successRate === 1.0) {
      recommendations.push('All Phase 3 utilities are working correctly');
      recommendations.push('System is ready for real-world testing');
    } else {
      recommendations.push('Some utilities need attention before production');
      Object.entries(utilityStatus).forEach(([utility, working]) => {
        if (!working) {
          recommendations.push(`Fix ${utility} integration`);
        }
      });
    }

    return {
      totalTests,
      passedTests,
      failedTests,
      successRate,
      testResults,
      utilityStatus,
      overallStatus: successRate >= 0.8 ? 'PASS' : 'FAIL',
      recommendations,
    };
  }

  /**
   * Log test results
   */
  private logTestResults(report: Phase3IntegrationTestReport): void {
    logger.info('📊 PHASE 3 INTEGRATION TEST RESULTS');
    logger.info('=' * 40);

    logger.info('🎯 Overall Results:', {
      status: report.overallStatus,
      successRate: `${(report.successRate * 100).toFixed(1)}%`,
      passed: report.passedTests,
      failed: report.failedTests,
      total: report.totalTests,
    });

    logger.info('🔧 Utility Status:');
    Object.entries(report.utilityStatus).forEach(([utility, working]) => {
      logger.info(`  ${utility}: ${working ? '✅' : '❌'}`);
    });

    logger.info('📋 Test Details:');
    report.testResults.forEach((result) => {
      const status = result.passed ? '✅' : '❌';
      const time = `${result.executionTime}ms`;
      logger.info(`  ${status} ${result.testName} (${time})`);
      if (result.error) {
        logger.warn(`    Error: ${result.error}`);
      }
    });

    logger.info('💡 Recommendations:');
    report.recommendations.forEach((rec) => {
      logger.info(`  - ${rec}`);
    });

    logger.info('=' * 40);

    if (report.overallStatus === 'PASS') {
      logger.info('🎉 PHASE 3 INTEGRATION: SUCCESS!');
    } else {
      logger.warn('⚠️ PHASE 3 INTEGRATION: Issues found.');
    }
  }
}

// Execute test if run directly
if (require.main === module) {
  const test = Phase3IntegrationTest.getInstance();
  test
    .runIntegrationTests()
    .then((report) => {
      logger.info('✅ Phase 3 integration test completed');
      process.exit(report.overallStatus === 'PASS' ? 0 : 1);
    })
    .catch((error) => {
      logger.error('❌ Phase 3 integration test failed:', error);
      process.exit(1);
    });
}

export { Phase3IntegrationTest };
