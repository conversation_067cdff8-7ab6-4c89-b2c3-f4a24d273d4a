module.exports = {
  root: true, // Important: ESLint stops looking in parent folders
  extends: [
    '../.eslintrc.js', // Extend the root ESLint configuration
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@next/next/core-web-vitals', // Using core-web-vitals for Next.js best practices
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    project: ['./tsconfig.json'], // Link to frontend's tsconfig
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  settings: {
    react: {
      version: 'detect', // Automatically detect the React version
    },
  },
  env: {
    browser: true,
    node: true, // For Next.js server-side code
    es6: true,
  },
  rules: {
    // ESLint rule naming convention settings
    '@typescript-eslint/naming-convention': [
      'warn',
      // Default for most things
      {
        selector: 'default',
        format: ['camelCase'],
        leadingUnderscore: 'allow',
        trailingUnderscore: 'allow',
      },
      // Variables: allow camelCase and PascalCase (for const components, etc.)
      {
        selector: 'variable',
        format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
        leadingUnderscore: 'allow',
      },
      // Functions (React components are often functions)
      {
        selector: 'function',
        format: ['camelCase', 'PascalCase'],
      },
      // ObjectLiteralProperty - allow ESLint rule naming pattern
      {
        selector: 'objectLiteralProperty',
        format: null, // Allow any format for ESLint rule names
      },
      // Type properties (interface/type members)
      {
        selector: 'typeProperty',
        format: ['camelCase', 'PascalCase', 'snake_case'],
        leadingUnderscore: 'allow',
      },
      // Parameters
      {
        selector: 'parameter',
        format: ['camelCase'],
        leadingUnderscore: 'allow',
      },
      // Type-like (class, interface, typeAlias, enum)
      {
        selector: 'typeLike',
        format: ['PascalCase'],
      },
      // Enum Members
      {
        selector: 'enumMember',
        format: ['PascalCase', 'UPPER_CASE'], // Allow PascalCase or UPPER_CASE for enums
      },
      // Imports: allow camelCase and PascalCase (for React, Link, Navbar, etc.)
      {
        selector: 'import',
        format: ['camelCase', 'PascalCase'],
      },
    ],

    'react/react-in-jsx-scope': 'off', // Next.js 17+ does not require React to be in scope
    'react/prop-types': 'off', // We use TypeScript for prop types
    '@typescript-eslint/explicit-function-return-type': 'off', // Often too verbose for React components
    '@next/next/no-html-link-for-pages': ['error', './app'], // Point to the app directory for Next.js 13+ App Router
    // Other rules
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off', // Keep existing console rule logic
    '@typescript-eslint/no-explicit-any': 'error', // Strict: no any types allowed
    'react/no-unescaped-entities': ['error', { forbid: ['>', '}', "'"] }], // For the apostrophe issue
    'require-jsdoc': 'off', // Disable JSDoc requirement temporarily
    'valid-jsdoc': 'off', // Disable JSDoc validation temporarily
    'prettier/prettier': 'warn', // Make prettier issues warnings instead of errors
  },
};
