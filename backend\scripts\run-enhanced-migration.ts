/**
 * Run Enhanced WCAG Evidence Migration
 * Directly applies the enhanced evidence migration without environment validation
 */

import knex from 'knex';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

const config = {
  client: 'postgresql',
  connection: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT || '5432'),
    user: process.env.POSTGRES_USER || 'complyuser',
    password: process.env.POSTGRES_PASSWORD || 'complypassword',
    database: process.env.POSTGRES_DB || 'complychecker_dev',
  },
  pool: {
    min: 2,
    max: 10,
  },
};

async function runEnhancedMigration() {
  const db = knex(config);

  try {
    console.log('🚀 Starting WCAG evidence enhancement migration...');
    
    // Check if table exists before attempting to alter it
    const tableExists = await db.schema.hasTable('wcag_automated_results');
    if (!tableExists) {
      console.log('⚠️ wcag_automated_results table does not exist. Skipping migration.');
      return;
    }

    // Check if enhanced columns already exist
    const hasEnhancedColumns = await db.schema.hasColumn('wcag_automated_results', 'total_element_count');
    if (hasEnhancedColumns) {
      console.log('✅ Enhanced evidence columns already exist. Migration not needed.');
      return;
    }

    // Add enhanced columns to wcag_automated_results (all optional with defaults)
    await db.schema.alterTable('wcag_automated_results', (table) => {
      console.log('📊 Adding element count tracking columns...');
      table.integer('total_element_count').nullable().defaultTo(null);
      table.integer('failed_element_count').nullable().defaultTo(null);
      
      console.log('🔍 Adding selector tracking columns...');
      table.jsonb('affected_selectors').nullable().defaultTo(null);
      
      console.log('🛠️ Adding fix guidance columns...');
      table.jsonb('fix_examples').nullable().defaultTo(null);
      
      console.log('📈 Adding metadata columns...');
      table.jsonb('evidence_metadata').nullable().defaultTo(null);
      
      console.log('⏱️ Adding performance tracking columns...');
      table.integer('scan_duration_ms').nullable().defaultTo(null);
      table.integer('elements_analyzed').nullable().defaultTo(null);
      
      console.log('🔧 Adding check metadata column...');
      table.jsonb('check_metadata').nullable().defaultTo(null);
    });

    console.log('📇 Creating performance indexes...');
    
    // Create indexes for performance (non-blocking)
    try {
      await db.raw(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_element_count 
        ON wcag_automated_results(failed_element_count) 
        WHERE failed_element_count IS NOT NULL AND failed_element_count > 0
      `);

      await db.raw(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_scan_duration 
        ON wcag_automated_results(scan_duration_ms) 
        WHERE scan_duration_ms IS NOT NULL
      `);

      await db.raw(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_elements_analyzed 
        ON wcag_automated_results(elements_analyzed) 
        WHERE elements_analyzed IS NOT NULL
      `);

      // Create composite index for enhanced reporting queries
      await db.raw(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wcag_enhanced_reporting 
        ON wcag_automated_results(scan_id, rule_id, failed_element_count) 
        WHERE failed_element_count IS NOT NULL
      `);
    } catch (indexError) {
      console.log('⚠️ Some indexes may already exist, continuing...');
    }

    console.log('✅ WCAG evidence enhancement migration completed successfully');
    
    // Log migration summary
    const tableInfo = await db('wcag_automated_results').count('* as count').first();
    console.log(`📊 Migration applied to ${tableInfo?.count || 0} existing records`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await db.destroy();
  }
}

runEnhancedMigration();
