/**
 * Advanced Form Accessibility Analyzer
 * Comprehensive form validation including complex form patterns, validation messages, and error handling
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import SmartCache from './smart-cache';
import { SessionManager } from './session-manager';

export interface FormFieldAnalysis {
  type: string;
  selector: string;
  label: {
    hasLabel: boolean;
    labelText?: string;
    labelMethod:
      | 'explicit'
      | 'implicit'
      | 'aria-label'
      | 'aria-labelledby'
      | 'placeholder'
      | 'none';
    isAccessible: boolean;
  };
  validation: {
    isRequired: boolean;
    hasValidation: boolean;
    validationMethod: string[];
    errorMessages: string[];
    hasAccessibleErrors: boolean;
  };
  grouping: {
    isInFieldset: boolean;
    fieldsetLegend?: string;
    isInGroup: boolean;
    groupLabel?: string;
  };
  autocomplete: {
    hasAutocomplete: boolean;
    autocompleteValue?: string;
    isValidAutocomplete: boolean;
  };
  accessibility: {
    isKeyboardAccessible: boolean;
    hasProperFocus: boolean;
    hasDescription: boolean;
    ariaAttributes: string[];
  };
  issues: string[];
  recommendations: string[];
  score: number;
}

export interface FormAnalysis {
  selector: string;
  hasAccessibleName: boolean;
  formName?: string;
  fields: FormFieldAnalysis[];
  submitButton: {
    exists: boolean;
    isAccessible: boolean;
    text?: string;
  };
  errorHandling: {
    hasErrorSummary: boolean;
    hasLiveRegion: boolean;
    errorsLinkedToFields: boolean;
  };
  progressIndicator: {
    hasProgress: boolean;
    isAccessible: boolean;
  };
  overallScore: number;
  issues: string[];
  recommendations: string[];
}

export interface FormAccessibilityReport {
  totalForms: number;
  totalFields: number;
  accessibleForms: number;
  accessibleFields: number;
  forms: FormAnalysis[];
  commonIssues: string[];
  bestPractices: string[];
  overallScore: number;
  criticalIssues: string[];
  recommendations: string[];
}

export interface FormAnalysisConfig {
  analyzeLabels: boolean;
  analyzeValidation: boolean;
  analyzeGrouping: boolean;
  analyzeAutocomplete: boolean;
  analyzeErrorHandling: boolean;
  analyzeKeyboardAccess: boolean;
  includeHiddenFields: boolean;
  strictMode: boolean;
}

/**
 * Advanced form accessibility analyzer
 */
export class FormAccessibilityAnalyzer {
  private static instance: FormAccessibilityAnalyzer;
  private smartCache: SmartCache;
  private sessionManager: SessionManager;
  private injectionStateMap: Map<string, boolean> = new Map(); // Track injection state per page
  private injectionPromiseMap: Map<string, Promise<void>> = new Map(); // Prevent concurrent injections

  private constructor() {
    this.smartCache = SmartCache.getInstance();
    this.sessionManager = SessionManager.getInstance();
  }

  static getInstance(): FormAccessibilityAnalyzer {
    if (!FormAccessibilityAnalyzer.instance) {
      FormAccessibilityAnalyzer.instance = new FormAccessibilityAnalyzer();
    }
    return FormAccessibilityAnalyzer.instance;
  }

  /**
   * Clean up injection state for a specific page
   */
  public cleanupPageState(pageUrl: string): void {
    this.injectionStateMap.delete(pageUrl);
    this.injectionPromiseMap.delete(pageUrl);
    logger.debug(`🧹 Cleaned up form analysis injection state for page: ${pageUrl}`);
  }

  /**
   * Clean up all injection states (useful for memory management)
   */
  public cleanupAllStates(): void {
    this.injectionStateMap.clear();
    this.injectionPromiseMap.clear();
    logger.debug('🧹 Cleaned up all form analysis injection states');
  }

  /**
   * Analyze form accessibility with enhanced session management
   */
  async analyzeFormAccessibility(
    page: Page,
    config: Partial<FormAnalysisConfig> = {},
    scanId: string = 'unknown'
  ): Promise<FormAccessibilityReport> {
    const fullConfig: FormAnalysisConfig = {
      analyzeLabels: config.analyzeLabels ?? true,
      analyzeValidation: config.analyzeValidation ?? true,
      analyzeGrouping: config.analyzeGrouping ?? true,
      analyzeAutocomplete: config.analyzeAutocomplete ?? true,
      analyzeErrorHandling: config.analyzeErrorHandling ?? true,
      analyzeKeyboardAccess: config.analyzeKeyboardAccess ?? true,
      includeHiddenFields: config.includeHiddenFields ?? false,
      strictMode: config.strictMode ?? false,
    };

    const url = page.url();
    const configHash = JSON.stringify(fullConfig);

    // Check cache first
    const cachedResult = await this.smartCache.getSiteAnalysis<FormAccessibilityReport>(
      url,
      `form-analysis-${configHash}`,
    );
    if (cachedResult) {
      logger.debug('📝 Using cached form accessibility analysis result');
      return cachedResult;
    }

    logger.debug('📝 Starting advanced form accessibility analysis');

    // Use session manager for coordinated access
    return await this.sessionManager.lockSession(page, scanId, async () => {
      // Enhanced session management - check page state before proceeding
      const isSessionSafe = await this.sessionManager.isSessionSafe(page, scanId);
      if (!isSessionSafe) {
        throw new Error('Page session is not safe for form accessibility analysis');
      }

      return await this.performFormAnalysis(page, fullConfig, url, configHash);
    });
  }

  /**
   * Perform the actual form analysis
   */
  private async performFormAnalysis(
    page: Page,
    config: FormAnalysisConfig,
    url: string,
    configHash: string
  ): Promise<FormAccessibilityReport> {

    // Inject form analysis functions
    await this.injectFormAnalysisFunctions(page);

    // Get all forms using direct page evaluation
    const forms = await this.getForms(page, config);

    // Analyze each form
    const formAnalyses: FormAnalysis[] = [];
    for (const form of forms) {
      try {
        const analysis = await this.analyzeForm(page, form, config);
        formAnalyses.push(analysis);
      } catch (error) {
        logger.warn(`Error analyzing form ${form.selector}`, { error: error instanceof Error ? error.message : String(error) });
      }
    }

    // Generate comprehensive report
    const report = this.generateReport(formAnalyses);

    // Cache the result
    await this.smartCache.cacheSiteAnalysis(url, `form-analysis-${configHash}`, report);

    logger.info(`✅ Form accessibility analysis completed`, {
      totalForms: report.totalForms,
      accessibleForms: report.accessibleForms,
      totalFields: report.totalFields,
      overallScore: report.overallScore,
    });

    return report;
  }

  /**
   * Inject form analysis functions into the page with enhanced error handling and session management
   */
  private async injectFormAnalysisFunctions(page: Page): Promise<void> {
    try {
      // Check if page is still valid and not closed
      if (page.isClosed()) {
        throw new Error('Page is closed, cannot inject form analysis functions');
      }

      // Generate unique page identifier for state tracking
      const pageId = await page.evaluate(() => window.location.href).catch(() => 'unknown');

      // Check if injection is already in progress for this page
      const existingPromise = this.injectionPromiseMap.get(pageId);
      if (existingPromise) {
        logger.debug('⏳ Form analysis injection already in progress for this page, waiting...');
        await existingPromise;
        return;
      }

      // Check if injection already exists to avoid redundant injections
      if (this.injectionStateMap.get(pageId)) {
        const injectionExists = await page.evaluate(() => {
          return typeof (window as any).formAnalysis === 'object' &&
                 typeof (window as any).formAnalysis.getElementSelector === 'function';
        }).catch(() => false);

        if (injectionExists) {
          logger.debug('✅ Form analysis functions already injected, skipping injection');
          return;
        } else {
          // State is stale, reset it
          this.injectionStateMap.delete(pageId);
        }
      }

      // Create injection promise to prevent concurrent injections
      const injectionPromise = this.performInjection(page, pageId);
      this.injectionPromiseMap.set(pageId, injectionPromise);

      try {
        await injectionPromise;
      } finally {
        this.injectionPromiseMap.delete(pageId);
      }
    } catch (error) {
      logger.error('❌ Form analysis injection process failed:', { error: (error as Error).message });
      throw new Error(`Form analysis injection process failed: ${(error as Error).message}`);
    }
  }

  /**
   * Perform the actual injection with enhanced error handling
   */
  private async performInjection(page: Page, pageId: string): Promise<void> {

    try {
      // Use evaluate instead of evaluateOnNewDocument for immediate injection
      await page.evaluate(() => {
        // Ensure window is available
        if (typeof window === 'undefined') {
          console.error('❌ Window object not available for form analysis injection');
          return;
        }

        (window as unknown as Record<string, unknown>).formAnalysis = {
          /**
           * Get element selector with enhanced error handling
           */
          getElementSelector(element: HTMLElement): string {
            try {
              if (!element) {
                console.warn('⚠️ getElementSelector: element is null or undefined');
                return 'unknown-element';
              }

              // Try ID first
              if (element.id) {
                return `#${element.id}`;
              }

              // Try unique class combination
              if (element.className) {
                const classes = element.className.trim().split(/\s+/);
                if (classes.length > 0) {
                  const classSelector = `.${classes.join('.')}`;
                  try {
                    const matches = document.querySelectorAll(classSelector);
                    if (matches.length === 1) {
                      return classSelector;
                    }
                  } catch (e) {
                    // Invalid selector, continue to path-based approach
                  }
                }
              }

              // Generate path-based selector
              const path: string[] = [];
              let current: Element | null = element;

              while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
                let selector = current.nodeName.toLowerCase();

                if (current.id) {
                  selector += `#${current.id}`;
                  path.unshift(selector);
                  break;
                }

                // Add nth-child if needed for uniqueness
                if (current.parentElement) {
                  const siblings = Array.from(current.parentElement.children)
                    .filter(sibling => sibling.tagName === current!.tagName);

                  if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += `:nth-child(${index})`;
                  }
                }

                path.unshift(selector);
                current = current.parentElement;

                if (path.length > 6) break; // Prevent overly long selectors
              }

              const result = path.join(' > ') || 'unknown-element';
              console.log(`✅ Generated selector for element: ${result}`);
              return result;
            } catch (error) {
              console.error('❌ Error generating element selector:', error);
              return 'error-element';
            }
          },

          /**
           * Check if element is visible
           */
          isVisible: function(element: HTMLElement): boolean {
            const style = window.getComputedStyle(element);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
          },

        /**
         * Analyze form field label
         */
          analyzeFieldLabel: function(field: HTMLElement): {
            hasLabel: boolean;
            labelText: string;
            labelMethod: string;
            isAccessible: boolean;
            issues: string[];
          } {
          const issues: string[] = [];
          let labelText = '';
          let labelMethod = 'none';
          let isAccessible = false;

          // Check for explicit label
          if (field.id) {
            const explicitLabel = document.querySelector(`label[for="${field.id}"]`);
            if (explicitLabel) {
              labelText = explicitLabel.textContent?.trim() || '';
              labelMethod = 'explicit';
              isAccessible = labelText.length > 0;
            }
          }

          // Check for implicit label (field inside label)
          if (!isAccessible) {
            const implicitLabel = field.closest('label');
            if (implicitLabel) {
              labelText = implicitLabel.textContent?.trim() || '';
              labelMethod = 'implicit';
              isAccessible = labelText.length > 0;
            }
          }

          // Check for aria-label
          if (!isAccessible) {
            const ariaLabel = field.getAttribute('aria-label');
            if (ariaLabel) {
              labelText = ariaLabel.trim();
              labelMethod = 'aria-label';
              isAccessible = labelText.length > 0;
            }
          }

          // Check for aria-labelledby
          if (!isAccessible) {
            const ariaLabelledby = field.getAttribute('aria-labelledby');
            if (ariaLabelledby) {
              const labelElement = document.getElementById(ariaLabelledby);
              if (labelElement) {
                labelText = labelElement.textContent?.trim() || '';
                labelMethod = 'aria-labelledby';
                isAccessible = labelText.length > 0;
              }
            }
          }

          // Check for placeholder (not recommended as primary label)
          if (!isAccessible) {
            const placeholder = field.getAttribute('placeholder');
            if (placeholder) {
              labelText = placeholder.trim();
              labelMethod = 'placeholder';
              isAccessible = false; // Placeholder alone is not accessible
              issues.push('Using placeholder as primary label is not accessible');
            }
          }

          if (!isAccessible) {
            issues.push('Field lacks accessible label');
          }

          return {
            hasLabel: labelMethod !== 'none',
            labelText: labelText || '',
            labelMethod,
            isAccessible,
            issues,
          };
        },

        /**
         * Analyze field validation
         */
          analyzeFieldValidation: function(field: HTMLElement): {
            isRequired: boolean;
            hasValidation: boolean;
            validationMethod: string[];
            errorMessages: string[];
            hasAccessibleErrors: boolean;
            issues: string[];
          } {
          const issues: string[] = [];
          const validationMethod: string[] = [];
          const errorMessages: string[] = [];

          const isRequired =
            field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';

          if (isRequired) {
            validationMethod.push('required');
          }

          // Check for HTML5 validation
          const inputElement = field as HTMLInputElement;
          if (inputElement.pattern) {
            validationMethod.push('pattern');
          }
          if (inputElement.min || inputElement.max) {
            validationMethod.push('min-max');
          }
          if (inputElement.minLength || inputElement.maxLength) {
            validationMethod.push('length');
          }

          // Check for ARIA validation
          if (field.getAttribute('aria-invalid')) {
            validationMethod.push('aria-invalid');
          }

          // Check for error messages
          const ariaDescribedby = field.getAttribute('aria-describedby');
          if (ariaDescribedby) {
            const errorElement = document.getElementById(ariaDescribedby);
            if (errorElement) {
              const errorText = errorElement.textContent?.trim();
              if (errorText) {
                errorMessages.push(errorText);
              }
            }
          }

          // Look for nearby error messages
          const fieldContainer =
            field.closest('.field, .form-group, .input-group') || field.parentElement;
          if (fieldContainer) {
            const errorElements = fieldContainer.querySelectorAll(
              '.error, .invalid, [role="alert"]',
            );
            errorElements.forEach((errorEl: Element) => {
              const errorText = errorEl.textContent?.trim();
              if (errorText && !errorMessages.includes(errorText)) {
                errorMessages.push(errorText);
              }
            });
          }

          const hasValidation = validationMethod.length > 0;
          const hasAccessibleErrors = errorMessages.length > 0 && ariaDescribedby !== null;

          if (isRequired && !hasAccessibleErrors && field.getAttribute('aria-invalid') === 'true') {
            issues.push('Required field with errors lacks accessible error message');
          }

          return {
            isRequired,
            hasValidation,
            validationMethod,
            errorMessages,
            hasAccessibleErrors,
            issues,
          };
        },

        /**
         * Analyze field grouping
         */
          analyzeFieldGrouping: function(field: HTMLElement): {
            isInFieldset: boolean;
            fieldsetLegend?: string;
            isInGroup: boolean;
            groupLabel?: string;
          } {
          const fieldset = field.closest('fieldset');
          const isInFieldset = !!fieldset;
          let fieldsetLegend = '';

          if (fieldset) {
            const legend = fieldset.querySelector('legend');
            fieldsetLegend = legend?.textContent?.trim() || '';
          }

          // Check for role="group"
          const group = field.closest('[role="group"]');
          const isInGroup = !!group;
          let groupLabel = '';

          if (group) {
            groupLabel =
              group.getAttribute('aria-label') ||
              (group.getAttribute('aria-labelledby') &&
                document.getElementById(group.getAttribute('aria-labelledby')!)?.textContent) ||
              '';
          }

          return {
            isInFieldset,
            fieldsetLegend: fieldsetLegend || undefined,
            isInGroup,
            groupLabel: groupLabel || undefined,
          };
        },

        /**
         * Analyze autocomplete
         */
          analyzeAutocomplete: function(field: HTMLElement): {
            hasAutocomplete: boolean;
            autocompleteValue?: string;
            isValidAutocomplete: boolean;
          } {
          const autocompleteValue = field.getAttribute('autocomplete');
          const hasAutocomplete = !!autocompleteValue;

          // Valid autocomplete values (simplified list)
          const validValues = [
            'name',
            'given-name',
            'family-name',
            'email',
            'username',
            'new-password',
            'current-password',
            'tel',
            'address-line1',
            'address-line2',
            'country',
            'postal-code',
            'cc-name',
            'cc-number',
            'cc-exp',
            'cc-csc',
            'off',
          ];

          const isValidAutocomplete =
            !hasAutocomplete ||
            validValues.includes(autocompleteValue!) ||
            autocompleteValue === 'off';

          return {
            hasAutocomplete,
            autocompleteValue: autocompleteValue || undefined,
            isValidAutocomplete,
          };
        },

        /**
         * Analyze accessibility features
         */
          analyzeAccessibility: function(field: HTMLElement): {
            isKeyboardAccessible: boolean;
            hasProperFocus: boolean;
            hasDescription: boolean;
            ariaAttributes: string[];
            issues: string[];
          } {
          const issues: string[] = [];
          const ariaAttributes: string[] = [];

          // Check keyboard accessibility
          const isKeyboardAccessible =
            field.tabIndex >= 0 ||
            ['INPUT', 'SELECT', 'TEXTAREA', 'BUTTON'].includes(field.tagName);

          // Check focus indicator
          field.focus();
          const style = window.getComputedStyle(field, ':focus');
          const hasProperFocus = style.outline !== 'none' && style.outline !== '0px';

          if (!hasProperFocus) {
            issues.push('Field lacks visible focus indicator');
          }

          // Check for description
          const hasDescription = !!(
            field.getAttribute('aria-describedby') || field.getAttribute('title')
          );

          // Collect ARIA attributes
          Array.from(field.attributes).forEach((attr: Attr) => {
            if (attr.name.startsWith('aria-')) {
              ariaAttributes.push(attr.name);
            }
          });

          return {
            isKeyboardAccessible,
            hasProperFocus,
            hasDescription,
            ariaAttributes,
            issues,
          };
        },

        /**
         * Get all form fields
         */
          getFormFields: function(form: HTMLElement, includeHidden: boolean): HTMLElement[] {
          const formAnalysis = (window as unknown as Record<string, unknown>).formAnalysis as {
            isVisible: (element: HTMLElement) => boolean;
          };

          const fieldSelectors = [
            'input:not([type="hidden"])',
            'select',
            'textarea',
            'button[type="submit"]',
            '[role="textbox"]',
            '[role="combobox"]',
            '[role="listbox"]',
            '[role="checkbox"]',
            '[role="radio"]',
          ];

          if (includeHidden) {
            fieldSelectors.push('input[type="hidden"]');
          }

          const fields: HTMLElement[] = [];
          fieldSelectors.forEach((selector: string) => {
            const elements = form.querySelectorAll(selector);
            elements.forEach((el: Element) => {
              const element = el as HTMLElement;
              if (includeHidden || formAnalysis.isVisible(element)) {
                fields.push(element);
              }
            });
          });

          return fields;
        },

        /**
         * Analyze form error handling
         */
          analyzeErrorHandling: function(form: HTMLElement): {
            hasErrorSummary: boolean;
            hasLiveRegion: boolean;
            errorsLinkedToFields: boolean;
          } {
          // Check for error summary
          const errorSummary = form.querySelector('.error-summary, [role="alert"], .alert-error');
          const hasErrorSummary = !!errorSummary;

          // Check for live region
          const liveRegion = form.querySelector('[aria-live]');
          const hasLiveRegion = !!liveRegion;

          // Check if errors are linked to fields
          const errorElements = form.querySelectorAll('.error, .invalid, [role="alert"]');
          let errorsLinkedToFields = true;

          errorElements.forEach((errorEl: Element) => {
            const errorElement = errorEl as HTMLElement;
            const errorId = errorElement.id;

            if (errorId) {
              const linkedField = form.querySelector(`[aria-describedby*="${errorId}"]`);
              if (!linkedField) {
                errorsLinkedToFields = false;
              }
            } else {
              errorsLinkedToFields = false;
            }
          });

          return {
            hasErrorSummary,
            hasLiveRegion,
            errorsLinkedToFields,
          };
        },

        /**
         * Analyze progress indicator
         */
          analyzeProgressIndicator: function(form: HTMLElement): {
            hasProgress: boolean;
            isAccessible: boolean;
          } {
          const progressElements = form.querySelectorAll(
            '[role="progressbar"], .progress, .step-indicator',
          );
          const hasProgress = progressElements.length > 0;

          let isAccessible = false;
          if (hasProgress) {
            progressElements.forEach((progress: Element) => {
              const progressElement = progress as HTMLElement;
              const hasLabel = !!(
                progressElement.getAttribute('aria-label') ||
                progressElement.getAttribute('aria-labelledby')
              );
              const hasValue = !!(
                progressElement.getAttribute('aria-valuenow') ||
                progressElement.getAttribute('aria-valuetext')
              );

              if (hasLabel && hasValue) {
                isAccessible = true;
              }
            });
          }

          return {
            hasProgress,
            isAccessible,
          };
        },

        /**
         * Main form element analysis function
         */
        analyzeFormElement: function(element: HTMLElement): any {
          try {
            const tagName = element.tagName.toLowerCase();
            const type = element.getAttribute('type')?.toLowerCase() || '';

            if (tagName === 'form') {
              // Basic form analysis
              const fields = element.querySelectorAll('input, textarea, select');
              return {
                type: 'form',
                fieldCount: fields.length,
                hasSubmit: !!element.querySelector('[type="submit"], button[type="submit"]'),
                hasValidation: !!element.getAttribute('novalidate')
              };
            } else if (['input', 'textarea', 'select'].includes(tagName)) {
              // Basic field analysis
              return {
                type: 'field',
                tagName: tagName,
                fieldType: type,
                hasLabel: !!element.getAttribute('aria-label') || !!element.closest('label'),
                isRequired: element.hasAttribute('required') || element.getAttribute('aria-required') === 'true'
              };
            } else {
              return { type: 'unknown', element: tagName };
            }
          } catch (error) {
            console.error('❌ Error analyzing form element:', error);
            return { type: 'error', error: (error as Error).message || 'Unknown error' };
          }
        },

        /**
         * Get form validation information
         */
        getFormValidation: function(form: HTMLElement): any {
          try {
            const requiredFields = form.querySelectorAll('[required], [aria-required="true"]');
            const validationMessages = form.querySelectorAll('[role="alert"], .error, .invalid');

            return {
              hasRequiredFields: requiredFields.length > 0,
              requiredFieldsCount: requiredFields.length,
              hasValidationMessages: validationMessages.length > 0,
              validationMessagesCount: validationMessages.length,
              hasClientSideValidation: !!form.getAttribute('novalidate')
            };
          } catch (error) {
            console.error('❌ Error getting form validation:', error);
            return { error: (error as Error).message || 'Unknown error' };
          }
        },

        /**
         * Get form labels information
         */
        getFormLabels: function(form: HTMLElement): any {
          try {
            const fields = form.querySelectorAll('input, textarea, select');
            const labels = form.querySelectorAll('label');
            let labeledFields = 0;

            fields.forEach((field: Element) => {
              const fieldElement = field as HTMLElement;
              // Simple label check without calling missing method
              const hasLabel = !!(
                fieldElement.getAttribute('aria-label') ||
                fieldElement.getAttribute('aria-labelledby') ||
                fieldElement.closest('label') ||
                (fieldElement.id && form.querySelector(`label[for="${fieldElement.id}"]`))
              );
              if (hasLabel) {
                labeledFields++;
              }
            });

            return {
              totalFields: fields.length,
              totalLabels: labels.length,
              labeledFields: labeledFields,
              labelCoverage: fields.length > 0 ? (labeledFields / fields.length) * 100 : 0
            };
          } catch (error) {
            console.error('❌ Error getting form labels:', error);
            return { error: (error as Error).message || 'Unknown error' };
          }
        },

        /**
         * Get form errors information
         */
        getFormErrors: function(form: HTMLElement): any {
          try {
            const errorElements = form.querySelectorAll('[role="alert"], .error, .invalid, [aria-invalid="true"]');

            // Simple error handling analysis without calling missing method
            const errorSummary = form.querySelector('.error-summary, [role="alert"], .alert-error');
            const liveRegion = form.querySelector('[aria-live]');

            return {
              hasErrors: errorElements.length > 0,
              errorCount: errorElements.length,
              hasErrorSummary: !!errorSummary,
              hasLiveRegion: !!liveRegion,
              errorsLinkedToFields: true // Simplified for injection
            };
          } catch (error) {
            console.error('❌ Error getting form errors:', error);
            return { error: (error as Error).message || 'Unknown error' };
          }
        },

        /**
         * Get form grouping information
         */
        getFormGrouping: function(form: HTMLElement): any {
          try {
            const fieldsets = form.querySelectorAll('fieldset');
            const groups = form.querySelectorAll('[role="group"], [role="radiogroup"]');

            return {
              hasFieldsets: fieldsets.length > 0,
              fieldsetCount: fieldsets.length,
              hasGroups: groups.length > 0,
              groupCount: groups.length,
              totalGroupingElements: fieldsets.length + groups.length
            };
          } catch (error) {
            console.error('❌ Error getting form grouping:', error);
            return { error: (error as Error).message || 'Unknown error' };
          }
        },

        /**
         * Get keyboard access information
         */
        getKeyboardAccess: function(form: HTMLElement): any {
          try {
            const focusableElements = form.querySelectorAll(
              'input, textarea, select, button, [tabindex]:not([tabindex="-1"]), [role="button"]'
            );

            let accessibleElements = 0;
            focusableElements.forEach((element: Element) => {
              const el = element as HTMLElement;
              // Check for disabled property safely
              const isDisabled = (el as any).disabled === true || el.getAttribute('disabled') !== null;
              if (el.tabIndex >= 0 && !isDisabled) {
                accessibleElements++;
              }
            });

            return {
              totalFocusableElements: focusableElements.length,
              accessibleElements: accessibleElements,
              keyboardAccessibility: focusableElements.length > 0 ? (accessibleElements / focusableElements.length) * 100 : 0,
              hasTabIndex: form.querySelectorAll('[tabindex]').length > 0
            };
          } catch (error) {
            console.error('❌ Error getting keyboard access:', error);
            return { error: (error as Error).message || 'Unknown error' };
          }
        },
      };

      // Validate injection
      try {
        console.log('✅ Form analysis functions injected successfully');
      } catch (error) {
        console.error('❌ Failed to inject form analysis functions:', error);
      }
    });

    // Enhanced validation with retry mechanism and better timeout handling
    let validationAttempts = 0;
    const maxValidationAttempts = 3;
    let injectionSuccessful = false;

    while (validationAttempts < maxValidationAttempts && !injectionSuccessful) {
      try {
        validationAttempts++;

        // Check if page is still valid before validation
        if (page.isClosed()) {
          throw new Error('Page closed during injection validation');
        }

        // Wait for injection to be available with progressive timeout
        const timeoutMs = 3000 + (validationAttempts * 2000); // 3s, 5s, 7s
        await page.waitForFunction(
          () => {
            const formAnalysis = (window as any).formAnalysis;
            return formAnalysis && typeof formAnalysis.getElementSelector === 'function';
          },
          { timeout: timeoutMs }
        );

        // Validate injection worked with comprehensive checks
        const injectionValidation = await page.evaluate(() => {
          const formAnalysis = (window as any).formAnalysis;
          return {
            hasGetElementSelector: typeof formAnalysis?.getElementSelector === 'function',
            hasAnalyzeFormElement: typeof formAnalysis?.analyzeFormElement === 'function',
            hasGetFormValidation: typeof formAnalysis?.getFormValidation === 'function',
            hasGetFormLabels: typeof formAnalysis?.getFormLabels === 'function',
            hasGetFormErrors: typeof formAnalysis?.getFormErrors === 'function',
            hasGetFormGrouping: typeof formAnalysis?.getFormGrouping === 'function',
            hasGetKeyboardAccess: typeof formAnalysis?.getKeyboardAccess === 'function',
          };
        });

        if (!injectionValidation.hasGetElementSelector || !injectionValidation.hasAnalyzeFormElement) {
          throw new Error(`Form analysis injection validation failed - missing functions: ${JSON.stringify(injectionValidation)}`);
        }

        injectionSuccessful = true;
        // Mark injection as successful in state map
        this.injectionStateMap.set(pageId, true);
        logger.debug(`✅ Form analysis functions injected and validated successfully (attempt ${validationAttempts})`);

      } catch (error) {
        logger.warn(`⚠️ Form analysis injection validation attempt ${validationAttempts} failed:`, {
          error: error instanceof Error ? error.message : String(error),
          willRetry: validationAttempts < maxValidationAttempts
        });

        if (validationAttempts >= maxValidationAttempts) {
          logger.error('❌ Form analysis injection validation failed after all attempts:', { error: error instanceof Error ? error.message : String(error) });
          throw new Error(`Form analysis injection failed after ${maxValidationAttempts} attempts: ${error instanceof Error ? error.message : String(error)}`);
        }

        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, 1000 * validationAttempts));
      }
    }
  } catch (injectionError: unknown) {
    logger.error('❌ Form analysis injection process failed:', { error: injectionError instanceof Error ? injectionError.message : String(injectionError) });
    throw new Error(`Form analysis injection process failed: ${injectionError instanceof Error ? injectionError.message : String(injectionError)}`);
  }
  }

  /**
   * Get all forms from the page
   */
  private async getForms(
    page: Page,
    _config: FormAnalysisConfig,
  ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
    return await page.evaluate(() => {
      const forms: {
        selector: string;
        element: { tagName: string; action: string; method: string };
      }[] = [];
      const formElements = document.querySelectorAll('form');
      const formAnalysis = (window as unknown as Record<string, unknown>).formAnalysis as {
        getElementSelector: (element: HTMLElement) => string;
      };

      formElements.forEach((form) => {
        forms.push({
          selector: formAnalysis.getElementSelector(form),
          element: {
            tagName: form.tagName,
            action: form.action,
            method: form.method,
          },
        });
      });

      return forms;
    });
  }

  /**
   * Analyze individual form
   */
  private async analyzeForm(
    page: Page,
    formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
    config: FormAnalysisConfig,
  ): Promise<FormAnalysis> {
    return await page.evaluate(
      (selector: string, analysisConfig: FormAnalysisConfig) => {
        const formAnalysis = (window as unknown as Record<string, unknown>).formAnalysis as {
          getFormFields: (form: HTMLFormElement, includeHidden: boolean) => HTMLElement[];
          analyzeFieldLabel: (field: HTMLElement) => {
            hasLabel: boolean;
            labelText: string;
            labelMethod:
              | 'explicit'
              | 'implicit'
              | 'aria-label'
              | 'aria-labelledby'
              | 'placeholder'
              | 'none';
            isAccessible: boolean;
            issues: string[];
          };
          analyzeFieldValidation: (field: HTMLElement) => {
            isRequired: boolean;
            hasValidation: boolean;
            validationMethod: string[];
            errorMessages: string[];
            hasAccessibleErrors: boolean;
            issues: string[];
          };
          analyzeFieldGrouping: (field: HTMLElement) => {
            isInFieldset: boolean;
            fieldsetLegend?: string;
            isInGroup: boolean;
            groupLabel?: string;
          };
          analyzeAutocomplete: (field: HTMLElement) => {
            hasAutocomplete: boolean;
            autocompleteValue?: string;
            isValidAutocomplete: boolean;
          };
          analyzeAccessibility: (field: HTMLElement) => {
            isKeyboardAccessible: boolean;
            hasProperFocus: boolean;
            hasDescription: boolean;
            ariaAttributes: string[];
            issues: string[];
          };
          analyzeErrorHandling: (form: HTMLFormElement) => {
            hasErrorSummary: boolean;
            hasLiveRegion: boolean;
            errorsLinkedToFields: boolean;
          };
          analyzeProgressIndicator: (form: HTMLFormElement) => {
            hasProgress: boolean;
            isAccessible: boolean;
          };
          getElementSelector: (element: HTMLElement) => string;
        };
        const form = document.querySelector(selector) as HTMLFormElement;

        if (!form) {
          throw new Error(`Form not found: ${selector}`);
        }

        const issues: string[] = [];
        const recommendations: string[] = [];

        // Analyze form name/label
        const hasAccessibleName = !!(
          form.getAttribute('aria-label') ||
          form.getAttribute('aria-labelledby') ||
          form.querySelector('h1, h2, h3, h4, h5, h6')
        );

        const formName =
          form.getAttribute('aria-label') ||
          (form.getAttribute('aria-labelledby') &&
            document.getElementById(form.getAttribute('aria-labelledby')!)?.textContent) ||
          form.querySelector('h1, h2, h3, h4, h5, h6')?.textContent;

        if (!hasAccessibleName) {
          issues.push('Form lacks accessible name');
          recommendations.push('Add aria-label or associate with heading');
        }

        // Get and analyze fields
        const fieldElements = formAnalysis.getFormFields(form, analysisConfig.includeHiddenFields);
        const fields: FormFieldAnalysis[] = [];

        fieldElements.forEach((field: HTMLElement) => {
          const fieldIssues: string[] = [];
          const fieldRecommendations: string[] = [];

          // Analyze label
          const labelAnalysis = analysisConfig.analyzeLabels
            ? formAnalysis.analyzeFieldLabel(field)
            : {
                hasLabel: true,
                labelText: '',
                labelMethod: 'none' as const,
                isAccessible: true,
                issues: [],
              };
          fieldIssues.push(...labelAnalysis.issues);

          // Analyze validation
          const validationAnalysis = analysisConfig.analyzeValidation
            ? formAnalysis.analyzeFieldValidation(field)
            : {
                isRequired: false,
                hasValidation: false,
                validationMethod: [],
                errorMessages: [],
                hasAccessibleErrors: true,
                issues: [],
              };
          fieldIssues.push(...validationAnalysis.issues);

          // Analyze grouping
          const groupingAnalysis = analysisConfig.analyzeGrouping
            ? formAnalysis.analyzeFieldGrouping(field)
            : { isInFieldset: false, isInGroup: false };

          // Analyze autocomplete
          const autocompleteAnalysis = analysisConfig.analyzeAutocomplete
            ? formAnalysis.analyzeAutocomplete(field)
            : { hasAutocomplete: false, isValidAutocomplete: true };

          // Analyze accessibility
          const accessibilityAnalysis = analysisConfig.analyzeKeyboardAccess
            ? formAnalysis.analyzeAccessibility(field)
            : {
                isKeyboardAccessible: true,
                hasProperFocus: true,
                hasDescription: false,
                ariaAttributes: [],
                issues: [],
              };
          fieldIssues.push(...accessibilityAnalysis.issues);

          // Calculate field score
          let fieldScore = 100;
          if (!labelAnalysis.isAccessible) fieldScore -= 30;
          if (validationAnalysis.isRequired && !validationAnalysis.hasAccessibleErrors)
            fieldScore -= 25;
          if (!accessibilityAnalysis.isKeyboardAccessible) fieldScore -= 20;
          if (!accessibilityAnalysis.hasProperFocus) fieldScore -= 15;
          fieldScore -= fieldIssues.length * 5;
          fieldScore = Math.max(0, fieldScore);

          // Generate recommendations
          if (!labelAnalysis.isAccessible) {
            fieldRecommendations.push('Add accessible label to field');
          }
          if (validationAnalysis.isRequired && !validationAnalysis.hasAccessibleErrors) {
            fieldRecommendations.push('Link error messages to field with aria-describedby');
          }
          if (!accessibilityAnalysis.hasProperFocus) {
            fieldRecommendations.push('Add visible focus indicator');
          }

          fields.push({
            type: field.tagName.toLowerCase(),
            selector: formAnalysis.getElementSelector(field),
            label: labelAnalysis,
            validation: validationAnalysis,
            grouping: groupingAnalysis,
            autocomplete: autocompleteAnalysis,
            accessibility: accessibilityAnalysis,
            issues: fieldIssues,
            recommendations: fieldRecommendations,
            score: fieldScore,
          });
        });

        // Analyze submit button
        const submitButton = form.querySelector(
          'button[type="submit"], input[type="submit"]',
        ) as HTMLElement;
        const submitAnalysis = {
          exists: !!submitButton,
          isAccessible: false,
          text: undefined as string | undefined,
        };

        if (submitButton) {
          const buttonText =
            submitButton.textContent?.trim() ||
            submitButton.getAttribute('value') ||
            submitButton.getAttribute('aria-label');
          submitAnalysis.text = buttonText || undefined;
          submitAnalysis.isAccessible = !!buttonText;

          if (!submitAnalysis.isAccessible) {
            issues.push('Submit button lacks accessible text');
            recommendations.push('Add text content or aria-label to submit button');
          }
        } else {
          issues.push('Form lacks submit button');
          recommendations.push('Add submit button to form');
        }

        // Analyze error handling
        const errorHandling = analysisConfig.analyzeErrorHandling
          ? formAnalysis.analyzeErrorHandling(form)
          : { hasErrorSummary: false, hasLiveRegion: false, errorsLinkedToFields: true };

        // Analyze progress indicator
        const progressIndicator = formAnalysis.analyzeProgressIndicator(form);

        // Calculate overall form score
        const fieldScores = fields.map((f) => f.score);
        const averageFieldScore =
          fieldScores.length > 0
            ? fieldScores.reduce((sum, score) => sum + score, 0) / fieldScores.length
            : 100;

        let overallScore = averageFieldScore;
        if (!hasAccessibleName) overallScore -= 15;
        if (!submitAnalysis.isAccessible) overallScore -= 10;
        if (!errorHandling.errorsLinkedToFields) overallScore -= 15;
        overallScore = Math.max(0, overallScore);

        return {
          selector,
          hasAccessibleName,
          formName: formName || undefined,
          fields,
          submitButton: submitAnalysis,
          errorHandling,
          progressIndicator,
          overallScore: Math.round(overallScore),
          issues,
          recommendations,
        };
      },
      formInfo.selector,
      config,
    );
  }

  /**
   * Generate comprehensive report
   */
  private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
    const totalForms = forms.length;
    const totalFields = forms.reduce((sum: number, form: FormAnalysis) => sum + form.fields.length, 0);
    const accessibleForms = forms.filter((form: FormAnalysis) => form.overallScore >= 80).length;
    const accessibleFields = forms.reduce(
      (sum: number, form: FormAnalysis) => sum + form.fields.filter((field: FormFieldAnalysis) => field.score >= 80).length,
      0,
    );

    // Calculate overall score
    const formScores = forms.map((form: FormAnalysis) => form.overallScore);
    const overallScore =
      formScores.length > 0
        ? Math.round(formScores.reduce((sum: number, score: number) => sum + score, 0) / formScores.length)
        : 100;

    // Collect common issues
    const allIssues: string[] = [];
    forms.forEach((form: FormAnalysis) => {
      allIssues.push(...form.issues);
      form.fields.forEach((field: FormFieldAnalysis) => {
        allIssues.push(...field.issues);
      });
    });

    const issueCounts: { [key: string]: number } = {};
    allIssues.forEach((issue: string) => {
      issueCounts[issue] = (issueCounts[issue] || 0) + 1;
    });

    const commonIssues = Object.entries(issueCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 5)
      .map(([issue]) => issue);

    // Generate critical issues
    const criticalIssues: string[] = [];
    if (accessibleForms / totalForms < 0.5) {
      criticalIssues.push('Less than 50% of forms are accessible');
    }
    if (accessibleFields / totalFields < 0.7) {
      criticalIssues.push('Less than 70% of form fields are accessible');
    }

    // Generate recommendations
    const recommendations: string[] = [
      'Ensure all form fields have accessible labels',
      'Link error messages to fields using aria-describedby',
      'Add visible focus indicators to all form controls',
      'Use fieldsets and legends for related form controls',
      'Implement proper error handling and validation',
    ];

    // Best practices
    const bestPractices: string[] = [
      'Use semantic HTML form elements',
      'Provide clear instructions and help text',
      'Use autocomplete attributes for common fields',
      'Test forms with keyboard navigation',
      'Validate forms both client-side and server-side',
    ];

    return {
      totalForms,
      totalFields,
      accessibleForms,
      accessibleFields,
      forms,
      commonIssues,
      bestPractices,
      overallScore,
      criticalIssues,
      recommendations,
    };
  }
}

export default FormAccessibilityAnalyzer;
