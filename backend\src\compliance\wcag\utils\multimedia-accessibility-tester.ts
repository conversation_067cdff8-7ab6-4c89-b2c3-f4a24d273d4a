/**
 * Multimedia Accessibility Tester
 * Enhanced multimedia testing for video captions, audio descriptions, and media controls
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface MediaAccessibilityResult {
  type: 'video' | 'audio' | 'iframe' | 'object' | 'embed';
  selector: string;
  src?: string;
  captions: {
    hasTextTracks: boolean;
    trackTypes: string[];
    languages: string[];
    isAccessible: boolean;
  };
  audioDescription: {
    hasAudioDescription: boolean;
    method: string;
    isAccessible: boolean;
  };
  controls: {
    hasControls: boolean;
    areAccessible: boolean;
    keyboardAccessible: boolean;
    customControls: string[];
  };
  transcript: {
    hasTranscript: boolean;
    location: string;
    isLinked: boolean;
  };
  autoplay: {
    autoplays: boolean;
    hasUserControl: boolean;
    isProblematic: boolean;
  };
  issues: string[];
  recommendations: string[];
  score: number;
}

export interface MultimediaAccessibilityReport {
  totalMediaElements: number;
  accessibleElements: number;
  videoElements: MediaAccessibilityResult[];
  audioElements: MediaAccessibilityResult[];
  embeddedElements: MediaAccessibilityResult[];
  overallScore: number;
  criticalIssues: string[];
  recommendations: string[];
  complianceLevel: 'A' | 'AA' | 'AAA' | 'fail';
}

/**
 * Enhanced multimedia accessibility tester
 */
export class MultimediaAccessibilityTester {
  private static instance: MultimediaAccessibilityTester;

  private constructor() {}

  static getInstance(): MultimediaAccessibilityTester {
    if (!MultimediaAccessibilityTester.instance) {
      MultimediaAccessibilityTester.instance = new MultimediaAccessibilityTester();
    }
    return MultimediaAccessibilityTester.instance;
  }

  /**
   * Test multimedia accessibility
   */
  async testMultimediaAccessibility(page: Page): Promise<MultimediaAccessibilityReport> {
    logger.debug('🎬 Starting multimedia accessibility testing');

    // Inject testing functions
    await this.injectMultimediaTestingFunctions(page);

    // Test different media types
    const [videoElements, audioElements, embeddedElements] = await Promise.all([
      this.testVideoElements(page),
      this.testAudioElements(page),
      this.testEmbeddedElements(page),
    ]);

    // Generate report
    const report = this.generateReport(videoElements, audioElements, embeddedElements);

    logger.info(`✅ Multimedia accessibility testing completed`, {
      totalElements: report.totalMediaElements,
      accessibleElements: report.accessibleElements,
      overallScore: report.overallScore,
      complianceLevel: report.complianceLevel,
    });

    return report;
  }

  /**
   * Inject multimedia testing functions
   */
  private async injectMultimediaTestingFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as unknown as { multimediaTesting: Record<string, unknown> }).multimediaTesting = {
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          let current = element;

          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();

            if (current.className) {
              const classes = current.className.split(' ').filter((c) => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }

            path.unshift(selector);
            current = current.parentElement!;

            if (path.length > 4) break;
          }

          return path.join(' > ');
        },

        /**
         * Test video element accessibility
         */
        testVideoElement(video: HTMLVideoElement): MediaAccessibilityResult {
          const issues: string[] = [];
          const recommendations: string[] = [];

          // Test captions/subtitles
          const textTracks = Array.from(video.textTracks || []);
          const trackElements = video.querySelectorAll('track');

          const captionTracks = textTracks.filter(
            (track) => track.kind === 'captions' || track.kind === 'subtitles',
          );

          const hasTextTracks = captionTracks.length > 0 || trackElements.length > 0;
          const trackTypes = textTracks.map((track) => track.kind);
          const languages = textTracks.map((track) => track.language).filter((lang) => lang);

          if (!hasTextTracks) {
            issues.push('Video lacks captions or subtitles');
            recommendations.push('Add caption tracks for video content');
          }

          // Test audio description
          const audioDescriptionTracks = textTracks.filter(
            (track) => track.kind === 'descriptions',
          );
          const hasAudioDescription = audioDescriptionTracks.length > 0;

          if (!hasAudioDescription) {
            issues.push('Video lacks audio description');
            recommendations.push('Provide audio descriptions for visual content');
          }

          // Test controls
          const hasControls = video.controls || video.hasAttribute('controls');
          const customControlsContainer = video.parentElement?.querySelector(
            '.video-controls, .media-controls',
          );
          const hasCustomControls = !!customControlsContainer;

          if (!hasControls && !hasCustomControls) {
            issues.push('Video lacks accessible controls');
            recommendations.push('Provide accessible media controls');
          }

          // Test keyboard accessibility of custom controls
          let keyboardAccessible = true;
          const customControls: string[] = [];

          if (hasCustomControls) {
            const controlButtons =
              customControlsContainer!.querySelectorAll('button, [role="button"]');
            controlButtons.forEach((button) => {
              const btn = button as HTMLElement;
              customControls.push(
                btn.textContent?.trim() || btn.getAttribute('aria-label') || 'unlabeled',
              );

              if (!btn.getAttribute('aria-label') && !btn.textContent?.trim()) {
                keyboardAccessible = false;
                issues.push('Custom control lacks accessible label');
              }
            });
          }

          // Test transcript
          const transcriptSelectors = [
            '.transcript',
            '.video-transcript',
            '[data-transcript]',
            'a[href*="transcript"]',
            'button[data-transcript]',
          ];

          let hasTranscript = false;
          let transcriptLocation = '';
          let isLinked = false;

          for (const selector of transcriptSelectors) {
            const transcriptElement =
              video.parentElement?.querySelector(selector) || document.querySelector(selector);
            if (transcriptElement) {
              hasTranscript = true;
              transcriptLocation = selector;
              isLinked =
                transcriptElement.hasAttribute('href') ||
                transcriptElement.hasAttribute('data-transcript');
              break;
            }
          }

          if (!hasTranscript) {
            issues.push('Video lacks transcript');
            recommendations.push('Provide a complete transcript for video content');
          }

          // Test autoplay
          const autoplays = video.autoplay;
          const hasUserControl = hasControls || hasCustomControls;
          const isProblematic = autoplays && (!hasUserControl || video.duration > 3);

          if (isProblematic) {
            issues.push('Video autoplays without user control');
            recommendations.push('Avoid autoplay or provide pause controls');
          }

          // Calculate score
          let score = 100;
          if (!hasTextTracks) score -= 30;
          if (!hasAudioDescription) score -= 20;
          if (!hasControls && !hasCustomControls) score -= 25;
          if (!hasTranscript) score -= 15;
          if (isProblematic) score -= 10;
          score = Math.max(0, score);

          return {
            type: 'video',
            selector: (
              window as unknown as {
                multimediaTesting: { getElementSelector: (el: HTMLElement) => string };
              }
            ).multimediaTesting.getElementSelector(video),
            src: video.currentSrc || video.src,
            captions: {
              hasTextTracks,
              trackTypes,
              languages,
              isAccessible: hasTextTracks,
            },
            audioDescription: {
              hasAudioDescription,
              method: hasAudioDescription ? 'text-track' : 'none',
              isAccessible: hasAudioDescription,
            },
            controls: {
              hasControls: hasControls || hasCustomControls,
              areAccessible: keyboardAccessible,
              keyboardAccessible,
              customControls,
            },
            transcript: {
              hasTranscript,
              location: transcriptLocation,
              isLinked,
            },
            autoplay: {
              autoplays,
              hasUserControl,
              isProblematic,
            },
            issues,
            recommendations,
            score,
          };
        },

        /**
         * Test audio element accessibility
         */
        testAudioElement(audio: HTMLAudioElement): MediaAccessibilityResult {
          const issues: string[] = [];
          const recommendations: string[] = [];

          // Test transcript (more critical for audio)
          const transcriptSelectors = [
            '.transcript',
            '.audio-transcript',
            '[data-transcript]',
            'a[href*="transcript"]',
            'button[data-transcript]',
          ];

          let hasTranscript = false;
          let transcriptLocation = '';
          let isLinked = false;

          for (const selector of transcriptSelectors) {
            const transcriptElement =
              audio.parentElement?.querySelector(selector) || document.querySelector(selector);
            if (transcriptElement) {
              hasTranscript = true;
              transcriptLocation = selector;
              isLinked =
                transcriptElement.hasAttribute('href') ||
                transcriptElement.hasAttribute('data-transcript');
              break;
            }
          }

          if (!hasTranscript) {
            issues.push('Audio lacks transcript');
            recommendations.push('Provide a complete transcript for audio content');
          }

          // Test controls
          const hasControls = audio.controls || audio.hasAttribute('controls');
          const customControlsContainer = audio.parentElement?.querySelector(
            '.audio-controls, .media-controls',
          );
          const hasCustomControls = !!customControlsContainer;

          if (!hasControls && !hasCustomControls) {
            issues.push('Audio lacks accessible controls');
            recommendations.push('Provide accessible media controls');
          }

          // Test autoplay
          const autoplays = audio.autoplay;
          const hasUserControl = hasControls || hasCustomControls;
          const isProblematic = autoplays && (!hasUserControl || audio.duration > 3);

          if (isProblematic) {
            issues.push('Audio autoplays without user control');
            recommendations.push('Avoid autoplay or provide pause controls');
          }

          // Calculate score
          let score = 100;
          if (!hasTranscript) score -= 40; // More critical for audio
          if (!hasControls && !hasCustomControls) score -= 30;
          if (isProblematic) score -= 20;
          score = Math.max(0, score);

          return {
            type: 'audio',
            selector: (
              window as unknown as {
                multimediaTesting: { getElementSelector: (el: HTMLElement) => string };
              }
            ).multimediaTesting.getElementSelector(audio),
            src: audio.currentSrc || audio.src,
            captions: {
              hasTextTracks: true, // N/A for audio
              trackTypes: [],
              languages: [],
              isAccessible: true,
            },
            audioDescription: {
              hasAudioDescription: true, // N/A for audio
              method: 'n/a',
              isAccessible: true,
            },
            controls: {
              hasControls: hasControls || hasCustomControls,
              areAccessible: true, // Simplified for audio
              keyboardAccessible: true,
              customControls: [],
            },
            transcript: {
              hasTranscript,
              location: transcriptLocation,
              isLinked,
            },
            autoplay: {
              autoplays,
              hasUserControl,
              isProblematic,
            },
            issues,
            recommendations,
            score,
          };
        },

        /**
         * Test embedded media accessibility
         */
        testEmbeddedElement(element: HTMLElement): MediaAccessibilityResult {
          const issues: string[] = [];
          const recommendations: string[] = [];

          // Test for accessible name
          const hasAccessibleName = !!(
            element.getAttribute('title') ||
            element.getAttribute('aria-label') ||
            element.getAttribute('aria-labelledby')
          );

          if (!hasAccessibleName) {
            issues.push('Embedded content lacks accessible name');
            recommendations.push('Add title or aria-label to describe embedded content');
          }

          // Test for keyboard accessibility
          const isKeyboardAccessible =
            element.tabIndex >= 0 || element.getAttribute('tabindex') !== '-1';

          if (!isKeyboardAccessible) {
            issues.push('Embedded content not keyboard accessible');
            recommendations.push('Ensure embedded content is keyboard accessible');
          }

          // Calculate score
          let score = 100;
          if (!hasAccessibleName) score -= 40;
          if (!isKeyboardAccessible) score -= 30;
          score = Math.max(0, score);

          return {
            type: element.tagName.toLowerCase() as
              | 'video'
              | 'audio'
              | 'iframe'
              | 'object'
              | 'embed',
            selector: (
              window as unknown as {
                multimediaTesting: { getElementSelector: (el: HTMLElement) => string };
              }
            ).multimediaTesting.getElementSelector(element),
            src: element.getAttribute('src') || undefined,
            captions: {
              hasTextTracks: false, // Unknown for embedded
              trackTypes: [],
              languages: [],
              isAccessible: false,
            },
            audioDescription: {
              hasAudioDescription: false, // Unknown for embedded
              method: 'unknown',
              isAccessible: false,
            },
            controls: {
              hasControls: false, // Unknown for embedded
              areAccessible: false,
              keyboardAccessible: isKeyboardAccessible,
              customControls: [],
            },
            transcript: {
              hasTranscript: false, // Unknown for embedded
              location: '',
              isLinked: false,
            },
            autoplay: {
              autoplays: false, // Unknown for embedded
              hasUserControl: false,
              isProblematic: false,
            },
            issues,
            recommendations,
            score,
          };
        },
      };
    });
  }

  /**
   * Test video elements
   */
  private async testVideoElements(page: Page): Promise<MediaAccessibilityResult[]> {
    return await page.evaluate(() => {
      const results: MediaAccessibilityResult[] = [];
      const videos = document.querySelectorAll('video');
      const testing = (window as unknown as Record<string, unknown>).multimediaTesting as {
        testVideoElement: (video: HTMLVideoElement) => MediaAccessibilityResult;
      };

      videos.forEach((video) => {
        try {
          const result = testing.testVideoElement(video);
          results.push(result);
        } catch (error) {
          console.warn('Error testing video element:', error);
        }
      });

      return results;
    });
  }

  /**
   * Test audio elements
   */
  private async testAudioElements(page: Page): Promise<MediaAccessibilityResult[]> {
    return await page.evaluate(() => {
      const results: MediaAccessibilityResult[] = [];
      const audios = document.querySelectorAll('audio');
      const testing = (window as unknown as Record<string, unknown>).multimediaTesting as {
        testAudioElement: (audio: HTMLAudioElement) => MediaAccessibilityResult;
      };

      audios.forEach((audio) => {
        try {
          const result = testing.testAudioElement(audio);
          results.push(result);
        } catch (error) {
          console.warn('Error testing audio element:', error);
        }
      });

      return results;
    });
  }

  /**
   * Test embedded elements
   */
  private async testEmbeddedElements(page: Page): Promise<MediaAccessibilityResult[]> {
    return await page.evaluate(() => {
      const results: MediaAccessibilityResult[] = [];
      const embedded = document.querySelectorAll('iframe, object, embed');
      const testing = (window as unknown as Record<string, unknown>).multimediaTesting as {
        testEmbeddedElement: (element: HTMLElement) => MediaAccessibilityResult;
      };

      embedded.forEach((element) => {
        try {
          // Only test if it might contain media
          const src = element.getAttribute('src') || '';
          const isMediaEmbed =
            src.includes('youtube') ||
            src.includes('vimeo') ||
            src.includes('video') ||
            src.includes('audio') ||
            element.tagName === 'OBJECT' ||
            element.tagName === 'EMBED';

          if (isMediaEmbed) {
            const result = testing.testEmbeddedElement(element as HTMLElement);
            results.push(result);
          }
        } catch (error) {
          console.warn('Error testing embedded element:', error);
        }
      });

      return results;
    });
  }

  /**
   * Generate comprehensive report
   */
  private generateReport(
    videoElements: MediaAccessibilityResult[],
    audioElements: MediaAccessibilityResult[],
    embeddedElements: MediaAccessibilityResult[],
  ): MultimediaAccessibilityReport {
    const allElements = [...videoElements, ...audioElements, ...embeddedElements];
    const totalMediaElements = allElements.length;
    const accessibleElements = allElements.filter((element) => element.score >= 80).length;

    // Calculate overall score
    const overallScore =
      totalMediaElements > 0
        ? Math.round(
            allElements.reduce((sum, element) => sum + element.score, 0) / totalMediaElements,
          )
        : 100;

    // Determine compliance level
    let complianceLevel: 'A' | 'AA' | 'AAA' | 'fail' = 'fail';
    if (overallScore >= 95) complianceLevel = 'AAA';
    else if (overallScore >= 85) complianceLevel = 'AA';
    else if (overallScore >= 75) complianceLevel = 'A';

    // Collect critical issues
    const criticalIssues: string[] = [];
    allElements.forEach((element) => {
      if (element.score < 50) {
        criticalIssues.push(`Critical accessibility issues in ${element.type} element`);
      }
      element.issues.forEach((issue) => {
        if (issue.includes('lacks captions') || issue.includes('lacks transcript')) {
          if (!criticalIssues.includes(issue)) {
            criticalIssues.push(issue);
          }
        }
      });
    });

    // Generate recommendations
    const recommendations: string[] = [
      'Provide captions for all video content',
      'Include transcripts for audio and video content',
      'Ensure media controls are keyboard accessible',
      'Add audio descriptions for video content with visual information',
      'Avoid autoplay or provide user controls',
      'Test multimedia content with assistive technologies',
    ];

    return {
      totalMediaElements,
      accessibleElements,
      videoElements,
      audioElements,
      embeddedElements,
      overallScore,
      criticalIssues,
      recommendations,
      complianceLevel,
    };
  }
}

export default MultimediaAccessibilityTester;
