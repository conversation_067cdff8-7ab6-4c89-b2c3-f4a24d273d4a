'use client';

import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/context/AuthContext';
import { getScanDetails } from '@/lib/api';
import { Scan } from '@/backend-types/index'; // Assuming Scan might be from a shared types location
import { EnhancedHipaaResults } from '@/types/hipaa';

// Type for recommendation in scan results
interface ScanRecommendation {
  id?: string;
  title: string;
  description?: string;
  category?: string | object;
  [key: string]: unknown;
}
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import FindingDetailsDisplay from '@/components/compliance/FindingDetailsDisplay';
import EnhancedHipaaResultsComponent from '@/components/compliance/EnhancedHipaaResults';
import { QuickGuidanceLinks } from '@/components/guidance/GuidanceRecommendations';
import { ArrowLeft, Loader2 } from 'lucide-react';

// Use the existing Scan type which already has enhancedHipaaResults

// Types for enhanced results transformation
interface AnalysisLevel {
  level: number;
  score: string | number;
  [key: string]: unknown;
}

interface CheckResult {
  checkId: string;
  analysisLevels?: AnalysisLevel[];
  levelResults?: {
    level1?: Record<string, unknown>;
    level2?: Record<string, unknown>;
    level3?: Record<string, unknown>;
  };
  metadata?: {
    analysisLevels?: {
      level1?: Record<string, unknown>;
      level2?: Record<string, unknown>;
      level3?: Record<string, unknown>;
    };
  };
  details?: {
    findings?: Array<{
      type?: string;
      severity?: string;
      message?: string;
      description?: string;
      content?: string;
      suggestion?: string;
    }>;
  };
  [key: string]: unknown;
}

interface EnhancedResults {
  checksBreakdown?: CheckResult[];
  checks?: CheckResult[];
  recommendations?: Array<{
    id?: string;
    title?: string;
    description?: string;
    priority?: number;
    category?: string;
  }>;
  overallScore?: number | string;
  metadata?: {
    processingTime?: number;
  };
  timestamp?: string;
  [key: string]: unknown;
}

const DetailItem: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
  <div>
    <p className="text-sm font-medium text-muted-foreground">{label}</p>
    <div className="mt-1 text-sm text-foreground">{value || 'N/A'}</div>
  </div>
);

const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return isNaN(date.getTime()) ? 'Invalid Date' : date.toLocaleString();
};

// Transform backend enhanced HIPAA results to frontend format (unused but kept for reference)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const transformEnhancedResults = (enhancedResults: EnhancedResults, url: string) => {
  if (!enhancedResults) {
    return null;
  }

  // Use the enhanced results structure - handle both direct results and nested check results
  // Backend now sends data in checksBreakdown format, not checks format
  const hipaaCheck: CheckResult =
    enhancedResults.checksBreakdown?.find((check) => check.checkId === 'HIPAA-COMPREHENSIVE-001') ||
    enhancedResults.checks?.[0] ||
    (enhancedResults as CheckResult);

  // Extract scores and data from the enhanced backend response
  const overallScore = enhancedResults.overallScore || hipaaCheck.overallScore || 0;

  // Extract level results from multiple possible locations
  let level1Data = {};
  let level2Data = {};
  let level3Data = {};

  // FIXED: Try to get level data from different possible locations
  if (hipaaCheck.analysisLevels && Array.isArray(hipaaCheck.analysisLevels)) {
    // New format: analysisLevels is an array of level objects
    level1Data = hipaaCheck.analysisLevels.find((level) => level.level === 1) || {};
    level2Data = hipaaCheck.analysisLevels.find((level) => level.level === 2) || {};
    level3Data = hipaaCheck.analysisLevels.find((level) => level.level === 3) || {};
  } else if (hipaaCheck.levelResults) {
    level1Data = hipaaCheck.levelResults.level1 || {};
    level2Data = hipaaCheck.levelResults.level2 || {};
    level3Data = hipaaCheck.levelResults.level3 || {};
  } else if (hipaaCheck.metadata?.analysisLevels) {
    level1Data = hipaaCheck.metadata.analysisLevels.level1 || {};
    level2Data = hipaaCheck.metadata.analysisLevels.level2 || {};
    level3Data = hipaaCheck.metadata.analysisLevels.level3 || {};
  } else {
    // Try to extract from individual checks if they have level-specific data
    enhancedResults.checks?.forEach((check) => {
      if (check.levelResults) {
        level1Data = { ...level1Data, ...check.levelResults.level1 };
        level2Data = { ...level2Data, ...check.levelResults.level2 };
        level3Data = { ...level3Data, ...check.levelResults.level3 };
      }
    });
  }

  // FIXED: Parse scores correctly from string format
  const level1Score = parseFloat(String((level1Data as Record<string, unknown>).score || '0')) || 0;
  const level2Score = parseFloat(String((level2Data as Record<string, unknown>).score || '0')) || 0;
  const level3Score = parseFloat(String((level3Data as Record<string, unknown>).score || '0')) || 0;

  // Create findings array from various sources
  const findings: Array<{
    id: string;
    type: string;
    severity: string;
    title: string;
    description: string;
    recommendation?: string;
  }> = [];

  // Add findings from checks
  if (enhancedResults.checksBreakdown) {
    enhancedResults.checksBreakdown.forEach((check, index: number) => {
      if (check.details?.findings) {
        check.details.findings.forEach((finding, findingIndex: number) => {
          findings.push({
            id: `finding-${index}-${findingIndex}`,
            type: finding.type || 'non_compliant',
            severity: finding.severity || 'MEDIUM',
            title: finding.message || `Finding ${findingIndex + 1}`,
            description: finding.content || finding.suggestion || 'No description available',
            recommendation: finding.suggestion,
          });
        });
      }
    });
  }

  // Create recommendations array
  const recommendations: Array<{
    id: string;
    title: string;
    description: string;
    priority: string;
    category: string;
  }> = [];
  if (enhancedResults.recommendations) {
    enhancedResults.recommendations.forEach((rec, index: number) => {
      recommendations.push({
        id: rec.id || `rec-${index}`,
        title: rec.title || `Recommendation ${index + 1}`,
        description: rec.description || 'No description available',
        priority: rec.priority === 1 ? 'HIGH' : rec.priority === 2 ? 'MEDIUM' : 'LOW',
        category: rec.category || 'privacy_policy',
      });
    });
  }

  const transformedResult = {
    overallScore,
    privacyPolicyPresent: hipaaCheck.passed || false,
    privacyPolicyUrl: url.includes('privacy') ? url : undefined,
    contentAnalysisScore: overallScore,
    contactInformationScore: Math.max(0, Number(overallScore) - 10),

    analysisLevels: {
      level1: {
        score: level1Score, // Use parsed score
        method: 'pattern_matching' as const,
        findings: (level1Data as Record<string, unknown>).findings || [],
        processingTime: Number((level1Data as Record<string, unknown>).processingTime) || 0,
      },
      level2: {
        score: level2Score, // Use parsed score
        method: 'nlp_analysis' as const,
        entities: (level2Data as Record<string, unknown>).entities || {
          people: [],
          organizations: [],
          phoneNumbers: [],
          emails: [],
        },
        statements: (level2Data as Record<string, unknown>).statements || {
          privacy: 0,
          rights: 0,
        },
        processingTime: Number((level2Data as Record<string, unknown>).processingTime) || 0,
      },
      level3: {
        score: level3Score, // Use parsed score
        method: 'ai_analysis' as const,
        complianceGaps:
          (level3Data as Record<string, unknown>).identifiedGaps ||
          (level3Data as Record<string, unknown>).complianceGaps ||
          [],
        recommendations: (level3Data as Record<string, unknown>).recommendations || [],
        processingTime: Number((level3Data as Record<string, unknown>).processingTime) || 0,
      },
    },

    findings,
    recommendations,
    totalProcessingTime: Number(enhancedResults.metadata?.processingTime) || 0,
    analysisTimestamp: enhancedResults.timestamp || new Date().toISOString(),
  };

  return transformedResult;
};
/* eslint-enable @typescript-eslint/no-explicit-any */

const ScanDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const { authenticated, loading: authLoading } = useAuth();
  const scanId = params?.scanId as string | undefined;
  const [scan, setScan] = useState<Scan | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (authLoading) return;
    if (!authenticated) {
      router.push('/auth/login');
      return;
    }
    if (scanId) {
      setIsLoading(true);
      getScanDetails(scanId)
        .then((data: Scan) => {
          // Debug logging for scan details page

          setScan(data);
        })
        .catch((err) => {
          setError(err.response?.data?.error || err.message || 'Failed to load scan details.');
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [scanId, authenticated, authLoading, router]);

  if (isLoading || authLoading) {
    return (
      <div className="flex justify-center items-center h-screen" role="status">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2">Loading scan details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p role="alert">{error}</p>
            <Button onClick={() => router.back()} className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!scan) {
    return (
      <div className="container mx-auto p-4 text-center">
        <p role="status">Scan not found.</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Go Back
        </Button>
      </div>
    );
  }

  let statusVariant: 'default' | 'secondary' | 'destructive' | 'outline' = 'secondary';
  if (scan.status === 'completed') statusVariant = 'default'; // Or a success-like variant
  if (scan.status === 'failed') statusVariant = 'destructive';
  if (scan.status === 'pending') statusVariant = 'outline';

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Button variant="outline" onClick={() => router.back()} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Scans
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>Scan Details</CardTitle>
          <CardDescription>Detailed information for scan ID: {scan.id}</CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="overflow-x-auto">
            {' '}
            {/* Added responsive wrapper */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 min-w-[600px] md:min-w-full">
              {' '}
              {/* Added min-width */}
              <DetailItem label="Scan ID" value={scan.id} />
              <DetailItem label="URL Scanned" value={scan.url} />
              <DetailItem
                label="Status"
                value={<Badge variant={statusVariant}>{scan.status.toUpperCase()}</Badge>}
              />
              <DetailItem
                label="Standards Scanned"
                value={
                  Array.isArray(scan.standards_scanned)
                    ? scan.standards_scanned.join(', ')
                    : String(scan.standards_scanned)
                }
              />
              <DetailItem label="Scan Initiated" value={formatDate(scan.created_at)} />
              <DetailItem label="Last Updated" value={formatDate(scan.updated_at)} />
            </div>
          </div>{' '}
          {/* Closing responsive wrapper */}
          {scan.findings && scan.findings.length > 0 && !scan.enhancedHipaaResults && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="text-lg font-semibold mb-4">Basic Findings</h3>
              <div className="space-y-4">
                {scan.findings.map((finding) => (
                  <Card key={finding.id} className="bg-muted/50">
                    <CardHeader className="pb-2 pt-4">
                      <CardTitle className="text-base">
                        Standard:{' '}
                        <Badge variant="secondary">{String(finding.standard).toUpperCase()}</Badge>{' '}
                        | Check: <Badge variant="outline">{finding.check_id}</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      <p
                        className={`font-semibold ${finding.passed ? 'text-success-foreground' : 'text-destructive'}`}
                      >
                        Status: {finding.passed ? 'Passed' : 'Failed'}
                      </p>
                      <p className="mt-1 text-muted-foreground">
                        Description: {finding.description}
                      </p>
                      {finding.severity && <p className="mt-1">Severity: {finding.severity}</p>}
                      <FindingDetailsDisplay
                        standard={finding.standard}
                        details={typeof finding.details === 'object' ? finding.details || {} : {}}
                      />
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
          {scan.summary_report && Object.keys(scan.summary_report).length > 0 && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="text-lg font-semibold mb-2">Summary Report (Raw)</h3>
              {typeof scan.summary_report === 'object' ? (
                <pre className="bg-muted p-3 rounded-md text-sm overflow-x-auto">
                  {JSON.stringify(scan.summary_report, null, 2)}
                </pre>
              ) : (
                <p>{String(scan.summary_report)}</p>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter>{/* Add any actions here, e.g., Re-scan, Download Report */}</CardFooter>
      </Card>

      {/* Enhanced HIPAA Results Display */}
      {scan.enhancedHipaaResults ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">Enhanced HIPAA Analysis Results</h2>
            <Link
              href={`/dashboard/scans/${scanId}/findings`}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              View Detailed Findings & Recommendations
            </Link>
          </div>
          <EnhancedHipaaResultsComponent
            enhancedHipaaResults={scan.enhancedHipaaResults as EnhancedHipaaResults}
          />

          {/* Quick Guidance Links */}
          {scan.enhancedHipaaResults?.recommendations && (
            <div className="mt-6">
              <QuickGuidanceLinks
                findings={scan.enhancedHipaaResults.recommendations.map(
                  (rec: ScanRecommendation) => ({
                    category: typeof rec.category === 'string' ? rec.category : 'privacy_policy',
                    title: rec.title || 'Recommendation',
                    type: 'missing' as const,
                  }),
                )}
                maxLinks={3}
              />
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-600">Enhanced HIPAA Analysis</h2>
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-sm text-gray-600">
              No enhanced HIPAA analysis results available for this scan. This may be an older scan
              or the enhanced analysis may have failed.
            </p>
            <p className="text-xs text-gray-500 mt-2">
              Try running a new scan to see the enhanced 3-level analysis results with positive
              findings and detailed recommendations.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScanDetailPage;
