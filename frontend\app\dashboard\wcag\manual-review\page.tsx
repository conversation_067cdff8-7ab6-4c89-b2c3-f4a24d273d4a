'use client';

/**
 * WCAG Manual Review Page
 * Lists all scans that have manual review items requiring attention
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  ClipboardCheck,
  Calendar,
  ExternalLink,
  AlertTriangle,
  CheckCircle,
  Clock,
} from 'lucide-react';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

/**
 * WCAG Manual Review Content Component
 */
const WcagManualReviewContent: React.FC = () => {
  const router = useRouter();
  const state = useWcagState();
  const actions = useWcagActions();
  const [mounted, setMounted] = useState(false);

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Load scans with manual review items
  useEffect(() => {
    const loadScans = async () => {
      try {
        await actions.fetchScans({ status: 'completed', limit: 50 });
      } catch (error) {
        console.error('Error loading scans:', error);
      }
    };

    if (mounted) {
      loadScans();
    }
  }, [mounted, actions]);

  const handleBack = () => {
    router.push('/dashboard/wcag');
  };

  const handleViewScan = (scanId: string) => {
    router.push(`/dashboard/wcag/manual-review/submit?scanId=${scanId}`);
  };

  // Filter scans that have manual review items
  const scansWithManualReview = state.scans.filter(
    (scan) => scan.status === 'completed' && scan.summary?.manualReviewItems > 0,
  );

  const getStatusBadge = (manualReviewItems: number) => {
    if (manualReviewItems === 0) {
      return <Badge variant="default">Complete</Badge>;
    }
    return <Badge variant="outline">{manualReviewItems} Pending</Badge>;
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return <Badge variant="default">Excellent</Badge>;
    if (score >= 70) return <Badge variant="secondary">Good</Badge>;
    if (score >= 50) return <Badge variant="outline">Fair</Badge>;
    return <Badge variant="destructive">Poor</Badge>;
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Manual Review</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Complete manual accessibility reviews for your scans
            </p>
          </div>
        </div>
        <Button
          onClick={() => router.push('/dashboard/wcag/manual-review/submit')}
          className="flex items-center gap-2"
        >
          <ClipboardCheck className="h-4 w-4" />
          Submit Reviews
        </Button>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <ClipboardCheck className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {scansWithManualReview.length}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Scans Requiring Review</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {scansWithManualReview.reduce(
                    (total, scan) => total + (scan.summary?.manualReviewItems || 0),
                    0,
                  )}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Items Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {Math.round(
                    scansWithManualReview.reduce(
                      (total, scan) => total + (scan.overallScore || 0),
                      0,
                    ) / Math.max(scansWithManualReview.length, 1),
                  )}
                  %
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Average Score</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scans List */}
      <Card>
        <CardHeader>
          <CardTitle>Scans Requiring Manual Review</CardTitle>
        </CardHeader>
        <CardContent>
          {state.loading.fetching ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : scansWithManualReview.length === 0 ? (
            <div className="text-center py-8">
              <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400 mb-2">No manual reviews required</p>
              <p className="text-sm text-gray-400 mb-4">
                All completed scans have been fully automated or reviews are complete.
              </p>
              <div className="flex gap-2">
                <Button onClick={() => router.push('/dashboard/wcag/manual-review/submit')}>
                  Submit Manual Review
                </Button>
                <Button variant="outline" onClick={() => router.push('/dashboard/wcag/scan')}>
                  Start New Scan
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {scansWithManualReview.map((scan) => (
                <div
                  key={scan.scanId}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium text-gray-900 dark:text-white truncate">
                        {scan.url || scan.targetUrl}
                      </p>
                      {getStatusBadge(scan.summary?.manualReviewItems || 0)}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(scan.scanTimestamp).toLocaleDateString()}
                      </span>
                      <span>WCAG {scan.wcagVersion}</span>
                      <span>Level {scan.complianceLevel}</span>
                      <span>{scan.summary?.manualReviewItems || 0} items pending</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {scan.overallScore ? Number(scan.overallScore).toFixed(1) : '0.0'}%
                      </p>
                      {getScoreBadge(Number(scan.overallScore) || 0)}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewScan(scan.scanId)}
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="h-4 w-4" />
                      Review
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Main WCAG Manual Review Page with Provider
 */
export default function WcagManualReviewPage() {
  return (
    <WcagProvider>
      <WcagManualReviewContent />
    </WcagProvider>
  );
}
