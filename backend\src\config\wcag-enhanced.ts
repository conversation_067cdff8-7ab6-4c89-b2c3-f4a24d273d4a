/**
 * WCAG Enhanced Configuration for Phase 1 + Phase 2 Features
 * Production-ready settings for all enhanced WCAG scanning capabilities
 */

export const wcagEnhancedConfig = {
  // Phase 1: Performance Optimizations
  browserPool: {
    maxBrowsers: parseInt(process.env.WCAG_MAX_BROWSERS || '3'),
    maxPagesPerBrowser: parseInt(process.env.WCAG_MAX_PAGES_PER_BROWSER || '3'),
    browserTimeout: parseInt(process.env.WCAG_BROWSER_TIMEOUT || '300000'), // 5 minutes
    pageTimeout: parseInt(process.env.WCAG_PAGE_TIMEOUT || '120000'), // 2 minutes
    memoryThresholdMB: parseInt(process.env.WCAG_MEMORY_THRESHOLD_MB || '4800'), // 80% of 6GB
    enableHealthChecks: true,
    recycleAfterUses: parseInt(process.env.WCAG_PAGE_RECYCLE_LIMIT || '10'),
    enableMemoryMonitoring: true,
    memoryCheckInterval: 5000, // 5 seconds
  },

  smartCache: {
    maxSizeMB: parseInt(process.env.WCAG_CACHE_SIZE_MB || '100'),
    maxEntries: parseInt(process.env.WCAG_MAX_CACHE_ENTRIES || '10000'),
    defaultTTL: parseInt(process.env.WCAG_CACHE_EXPIRY_MINUTES || '60') * 60 * 1000,
    cleanupInterval: parseInt(process.env.WCAG_CLEANUP_INTERVAL_MS || '300000'), // 5 minutes
    enableCompression: process.env.WCAG_ENABLE_CACHE_COMPRESSION !== 'false',
    enableMetrics: true,
  },

  performanceMonitoring: {
    enabled: process.env.WCAG_ENABLE_PERFORMANCE_MONITORING !== 'false',
    enableBaseline: process.env.WCAG_PERFORMANCE_BASELINE_ENABLED !== 'false',
    metricsRetentionDays: parseInt(process.env.WCAG_METRICS_RETENTION_DAYS || '30'),
    enableDetailedMetrics: true,
    enableRecommendations: true,
    performanceThresholds: {
      scanDurationWarning: 45000, // 45 seconds
      scanDurationCritical: 60000, // 60 seconds
      memoryWarning: 2500, // 2.5GB
      memoryCritical: 3500, // 3.5GB
      cacheHitRateWarning: 70, // 70%
      cacheHitRateCritical: 50, // 50%
    },
  },

  dynamicContentMonitoring: {
    enabled: true,
    monitoringDuration: 30000, // 30 seconds
    stabilityThreshold: 5, // changes per second
    enableNetworkMonitoring: true,
    enableSPADetection: true,
    enableFrameworkDetection: true,
    maxChangesToTrack: 1000,
  },

  // Phase 2: Website Type Detection
  cmsDetection: {
    enabled: process.env.WCAG_ENABLE_CMS_DETECTION !== 'false',
    enableWordPressOptimization: true,
    enableDrupalOptimization: true,
    enableJoomlaOptimization: true,
    enableShopifyOptimization: true,
    enableWixOptimization: true,
    enableSquarespaceOptimization: true,
    enableCustomCMSDetection: true,
    deepAnalysis: true,
    cacheResults: true,
    cacheTTL: 3600000, // 1 hour
  },

  ecommerceOptimization: {
    enabled: process.env.WCAG_ENABLE_ECOMMERCE_ANALYSIS !== 'false',
    analyzeProductListings: true,
    analyzeProductDetails: true,
    analyzeShoppingCart: true,
    analyzeCheckoutFlow: true,
    analyzePaymentForms: true,
    analyzeSearchAndFilters: true,
    analyzeReviews: true,
    testCheckoutProcess: false, // Disabled for safety in production
    deepAnalysis: true,
    cacheResults: true,
    cacheTTL: 1800000, // 30 minutes
  },

  mediaAnalysis: {
    enabled: process.env.WCAG_ENABLE_MEDIA_ANALYSIS !== 'false',
    analyzeVideos: true,
    analyzeAudio: true,
    analyzeImages: true,
    analyzeGalleries: true,
    analyzeEmbeddedContent: true,
    checkTranscripts: true,
    checkCaptions: true,
    checkAudioDescriptions: true,
    checkKeyboardNavigation: true,
    deepAnalysis: true,
    cacheResults: true,
    cacheTTL: 3600000, // 1 hour
  },

  frameworkOptimization: {
    enabled: process.env.WCAG_ENABLE_FRAMEWORK_DETECTION !== 'false',
    enableReactOptimization: true,
    enableVueOptimization: true,
    enableAngularOptimization: true,
    enableSvelteOptimization: true,
    enableNextJSOptimization: true,
    enableNuxtOptimization: true,
    enableGatsbyOptimization: true,
    enableCustomFrameworkDetection: true,
    analyzeStateManagement: true,
    analyzeRouting: true,
    analyzeUILibraries: true,
    deepAnalysis: true,
    cacheResults: true,
    cacheTTL: 7200000, // 2 hours
  },

  // Enhanced Features
  colorAnalysis: {
    useEnhancedAnalysis: true,
    enableGradientDetection: true,
    enableCustomPropertyResolution: true,
    enableImageBackgroundAnalysis: true,
    cacheResults: true,
    cacheTTL: 3600000, // 1 hour
  },

  uiComponentDetection: {
    enabled: true,
    enableModalDetection: true,
    enableDropdownDetection: true,
    enableCarouselDetection: true,
    enableAccordionDetection: true,
    enableTabDetection: true,
    enableTooltipDetection: true,
    enableMenuDetection: true,
    enableCustomWidgetDetection: true,
    testKeyboardInteraction: true,
    testFocusManagement: true,
    deepAnalysis: true,
  },

  // WCAG 2.2 Configuration
  wcag22: {
    enableAuthenticationRules: true,
    enableEnhancedAuthenticationRules: true,
    manualReviewIntegration: true,
    enhancedManualReviewGuidance: true,
  },

  // Concurrency and Resource Management
  concurrency: {
    maxConcurrentScans: parseInt(process.env.WCAG_MAX_CONCURRENT_SCANS || '8'),
    maxQueueSize: parseInt(process.env.WCAG_MAX_QUEUE_SIZE || '50'),
    scanTimeout: parseInt(process.env.WCAG_SCAN_TIMEOUT || '300000'), // 5 minutes
    enableQueuePrioritization: true,
    enableResourceThrottling: true,
  },

  // Monitoring and Alerting
  monitoring: {
    enableHealthChecks: true,
    healthCheckInterval: 30000, // 30 seconds
    enableMetricsCollection: true,
    metricsInterval: 60000, // 1 minute
    enableAlerting: true,
    alertThresholds: {
      scanFailureRate: 5, // 5%
      memoryUsage: 3000, // 3GB
      scanDuration: 60000, // 60 seconds
      cacheHitRate: 70, // 70%
    },
  },

  // Feature Flags
  features: {
    enablePhase1Enhancements: true,
    enablePhase2Enhancements: true,
    enableExperimentalFeatures: process.env.ENABLE_EXPERIMENTAL_FEATURES === 'true',
    enableBetaFeatures: process.env.ENABLE_BETA_FEATURES === 'true',
    enableDebugMode: process.env.NODE_ENV === 'development',
  },

  // Graceful Degradation
  gracefulDegradation: {
    enableFallbackMode: true,
    disablePhase2OnError: true,
    maxConsecutiveErrors: 5,
    errorRecoveryTimeout: 300000, // 5 minutes
    enablePartialResults: true,
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableStructuredLogging: true,
    enablePerformanceLogging: true,
    enablePhase2Logging: true,
    logRetentionDays: 30,
    enableLogAggregation: true,
  },
};

/**
 * Get configuration for specific environment
 */
export function getWcagEnhancedConfig(environment: string = process.env.NODE_ENV || 'development') {
  const baseConfig = { ...wcagEnhancedConfig };

  switch (environment) {
    case 'production':
      return {
        ...baseConfig,
        logging: {
          ...baseConfig.logging,
          level: 'warn',
          enableDebugMode: false,
        },
        features: {
          ...baseConfig.features,
          enableExperimentalFeatures: false,
          enableBetaFeatures: false,
          enableDebugMode: false,
        },
      };

    case 'staging':
      return {
        ...baseConfig,
        logging: {
          ...baseConfig.logging,
          level: 'info',
        },
        features: {
          ...baseConfig.features,
          enableExperimentalFeatures: true,
          enableBetaFeatures: true,
        },
      };

    case 'development':
    default:
      return {
        ...baseConfig,
        logging: {
          ...baseConfig.logging,
          level: 'debug',
          enableDebugMode: true,
        },
        features: {
          ...baseConfig.features,
          enableExperimentalFeatures: true,
          enableBetaFeatures: true,
          enableDebugMode: true,
        },
        browserPool: {
          ...baseConfig.browserPool,
          maxBrowsers: 1, // Reduced for development
          maxPagesPerBrowser: 2,
        },
      };
  }
}

export default wcagEnhancedConfig;
