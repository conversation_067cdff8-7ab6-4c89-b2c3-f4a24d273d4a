/**
 * Test Enhanced Evidence Database Population
 * Validates that enhanced evidence fields are properly populated
 */

import { WcagDatabase } from '../src/compliance/wcag/database/wcag-database';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../src/compliance/wcag/types-enhanced';
import db from '../src/lib/db';
import logger from '../src/utils/logger';

async function testEnhancedEvidencePopulation(): Promise<void> {
  console.log('🧪 Testing Enhanced Evidence Database Population');
  console.log('=' .repeat(60));

  const wcagDatabase = new WcagDatabase();
  let testScanId: string | null = null;

  try {
    // Create a test scan
    console.log('📝 Creating test scan...');
    testScanId = await wcagDatabase.createScan({
      userId: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
      targetUrl: 'https://test-enhanced-evidence.example.com',
      options: {
        includeLevel: ['A', 'AA'],
        includeCategories: ['perceivable', 'operable'],
        timeout: 30000,
      },
    });
    console.log(`✅ Test scan created: ${testScanId}`);

    // Create enhanced evidence
    const enhancedEvidence: WcagEvidenceEnhanced[] = [
      {
        type: 'error',
        description: 'Missing alt text on image',
        value: '<img src="test.jpg" />',
        selector: 'img[src="test.jpg"]',
        severity: 'error',
        elementCount: 3,
        affectedSelectors: ['img[src="test.jpg"]', 'img.hero-image', 'img.gallery-item'],
        fixExample: {
          before: '<img src="test.jpg" />',
          after: '<img src="test.jpg" alt="Test image description" />',
          description: 'Add descriptive alt text to the image',
          codeExample: 'alt="Descriptive text about the image content"',
          resources: ['https://www.w3.org/WAI/tutorials/images/'],
        },
        metadata: {
          scanDuration: 1500,
          elementsAnalyzed: 10,
          checkSpecificData: {
            imageType: 'content',
            hasAriaLabel: false,
          },
        },
      },
    ];

    // Create enhanced check result
    const enhancedCheckResult: WcagCheckResultEnhanced = {
      ruleId: 'WCAG-001',
      ruleName: 'Non-text Content',
      category: 'perceivable',
      wcagVersion: '2.2',
      successCriterion: '1.1.1',
      level: 'A',
      status: 'failed',
      score: 0,
      maxScore: 100,
      weight: 0.1,
      evidence: enhancedEvidence,
      recommendations: ['Add alt text to all images'],
      executionTime: 1500,
      elementCounts: {
        total: 8,
        failed: 3,
        passed: 5,
      },
      performance: {
        scanDuration: 1500,
        elementsAnalyzed: 8,
        cacheHitRate: 0.75,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'enhanced-image-analysis',
        confidence: 0.95,
        additionalData: {
          imageAnalysisEnabled: true,
        },
      },
    };

    // Create scan result
    const scanResult = {
      scanId: testScanId,
      targetUrl: 'https://test-enhanced-evidence.example.com',
      scanTimestamp: new Date(),
      scanDuration: 5000,
      overallScore: 75,
      levelAchieved: 'A' as const,
      riskLevel: 'medium' as const,
      checks: [enhancedCheckResult],
      summary: {
        totalAutomatedChecks: 1,
        passedAutomatedChecks: 0,
        failedAutomatedChecks: 1,
        manualReviewItems: 0,
        categoryScores: {
          perceivable: 75,
          operable: 80,
          understandable: 85,
          robust: 90,
        },
      },
      metadata: {
        scanDuration: 5000,
        checksExecuted: 1,
        automationRate: 1.0,
        performanceMetrics: {
          totalMemoryUsage: 150,
          peakMemoryUsage: 200,
          averageResponseTime: 1500,
        },
      },
    };

    // Save the scan result
    console.log('💾 Saving enhanced scan result...');
    await wcagDatabase.saveScanResult(scanResult);
    console.log('✅ Scan result saved successfully');

    // Verify enhanced evidence fields were populated
    console.log('🔍 Verifying enhanced evidence population...');
    const savedResults = await db('wcag_automated_results')
      .where('scan_id', testScanId)
      .first();

    if (!savedResults) {
      throw new Error('No saved results found');
    }

    console.log('\n📊 Enhanced Evidence Fields Verification:');
    console.log(`  total_element_count: ${savedResults.total_element_count}`);
    console.log(`  failed_element_count: ${savedResults.failed_element_count}`);
    console.log(`  scan_duration_ms: ${savedResults.scan_duration_ms}`);
    console.log(`  elements_analyzed: ${savedResults.elements_analyzed}`);

    // Verify affected_selectors
    if (savedResults.affected_selectors) {
      const affectedSelectors = JSON.parse(savedResults.affected_selectors);
      console.log(`  affected_selectors: ${affectedSelectors.length} selectors`);
      console.log(`    - ${affectedSelectors.slice(0, 2).join(', ')}`);
    }

    // Verify fix_examples
    if (savedResults.fix_examples) {
      const fixExamples = JSON.parse(savedResults.fix_examples);
      console.log(`  fix_examples: ${fixExamples.length} examples`);
      console.log(`    - ${fixExamples[0]?.description || 'N/A'}`);
    }

    // Verify evidence_metadata
    if (savedResults.evidence_metadata) {
      const evidenceMetadata = JSON.parse(savedResults.evidence_metadata);
      console.log(`  evidence_metadata: ${evidenceMetadata.totalEvidenceItems} items`);
    }

    // Verify check_metadata
    if (savedResults.check_metadata) {
      const checkMetadata = JSON.parse(savedResults.check_metadata);
      console.log(`  check_metadata: v${checkMetadata.version}, confidence: ${checkMetadata.confidence}`);
    }

    // Validation checks
    const validations = [
      { name: 'total_element_count', expected: 8, actual: savedResults.total_element_count },
      { name: 'failed_element_count', expected: 3, actual: savedResults.failed_element_count },
      { name: 'scan_duration_ms', expected: 1500, actual: savedResults.scan_duration_ms },
      { name: 'elements_analyzed', expected: 8, actual: savedResults.elements_analyzed },
    ];

    console.log('\n✅ Validation Results:');
    let allPassed = true;
    for (const validation of validations) {
      const passed = validation.actual === validation.expected;
      console.log(`  ${passed ? '✅' : '❌'} ${validation.name}: ${validation.actual} (expected: ${validation.expected})`);
      if (!passed) allPassed = false;
    }

    if (allPassed) {
      console.log('\n🎉 All enhanced evidence fields populated correctly!');
    } else {
      console.log('\n⚠️ Some validations failed - check implementation');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    // Clean up test data
    if (testScanId) {
      console.log('\n🧹 Cleaning up test data...');
      await db('wcag_automated_results').where('scan_id', testScanId).del();
      await db('wcag_scans').where('id', testScanId).del();
      console.log('✅ Test data cleaned up');
    }
    await db.destroy();
  }
}

// Run the test
testEnhancedEvidencePopulation()
  .then(() => {
    console.log('\n🎯 Enhanced evidence population test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Enhanced evidence population test failed:', error);
    process.exit(1);
  });
